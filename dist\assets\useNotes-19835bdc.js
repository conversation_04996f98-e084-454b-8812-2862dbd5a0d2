import{A as d,G as p,M as f,t as h,s as x}from"./index-f2ad9142.js";import{r as t}from"./vendor-4cdf2bd1.js";function k(o){const[r,e]=t.useState(!1),[c,n]=t.useState([]),{dispatch:i}=t.useContext(d),{dispatch:u}=t.useContext(p),a=t.useCallback(async()=>{e(!0);try{const l=await new f().callRawAPI(`/v4/api/records/notes?filter=update_id,eq,${o}&order=\`order\`,asc`);n(l.list)}catch(s){h(i,s.message),x(u,s.message,5e3,"error")}e(!1)},[]);return t.useEffect(()=>{a()},[]),{loading:r,notes:c,refetch:a}}export{k as u};
