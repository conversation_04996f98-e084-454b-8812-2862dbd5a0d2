import{A as C,G as d,T as S,t as E,s as T}from"./index-f2ad9142.js";import{r as s,h as k}from"./vendor-4cdf2bd1.js";function D(a){const[h,m]=s.useState(!1),[p,l]=s.useState([]),[t]=k(),{dispatch:b}=s.useContext(C),{dispatch:$}=s.useContext(d),[_,x]=s.useState(0),f=t.get("limit")||30,g=t.get("page")??1,o=t.get("email"),r=t.get("first_name"),n=t.get("last_name"),c=t.get("role");async function u(){m(!0);try{const e=await new S().getPaginate("company_member",{join:["user|member_id"],filter:[`company_id,eq,${a}`,`${o?`&filter=email,cs,${o}`:[]}`,`${r?`&filter=first_name,cs,${r}`:[]}`,`${n?`&filter=last_name,cs,${n}`:[]}`,`${c?`&filter=member_role,cs,${c}`:[]}`],limit:f,page:g});l(()=>e==null?void 0:e.list),x(e==null?void 0:e.total)}catch(i){E(b,i.message),T($,i.message,5e3,"error")}m(!1)}return s.useEffect(()=>{a&&u()},[f,g,o,r,n,c,a]),{loading:h,members:p,refetch:u,setMembers:l,totalCount:_}}export{D as u};
