import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{r as m,i as X,b as Z,u as D}from"./vendor-4cdf2bd1.js";import{a as ee,u as te,O as se,L as E,i as oe,bD as B,bU as re}from"./index-f2ad9142.js";import{u as ne}from"./useComments-7e9daaa3.js";import{A as me}from"./AddButton-51d1b2cd.js";import{c as ie}from"./UpdateSection-24e02fd2.js";import{A as de}from"./index-afef2e72.js";import"./MkdInput-d37679e9.js";import{u as ce}from"./useUpdateCollaborator-1187c43b.js";import{u as le}from"./react-popper-9a65a9b6.js";import{u as ae}from"./useMentions-2c8c5eca.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./@popperjs/core-f3391c26.js";const fe=({children:o})=>{try{return o}catch(n){return console.error("Comment Error:",n),null}},Ge=({update:o=null,note:n=null})=>{var $,R,P,O,q;const{create:I,setLoading:S,showToast:A}=ee(),[t,g]=m.useState({html:null,data:null,modal:null,comment:"",showModal:!1,errors:{comment:{message:""}}}),{loading:v,comments:f,getComments:U}=ne(),{public_link_id:b}=X(),{profile:c}=te(),z=Z(),l=D(),[x,w]=m.useState(!1),p=m.useRef(null),{data:h,fetchUpdateContributors:F}=ce(),{markAllMentionsAsSeen:Y}=ae(),N=m.useRef(null),M=m.useRef(null),[K,V]=m.useState(null),{styles:L,attributes:G}=le(N.current,M.current,{placement:"bottom-start",modifiers:[{name:"arrow",options:{element:K}},{name:"offset",options:{offset:[0,8]}},{name:"preventOverflow",options:{padding:8}},{name:"flip",options:{padding:8}}]});m.useEffect(()=>{o!=null&&o.id&&F(o==null?void 0:o.id)},[o==null?void 0:o.id]);const y=(e,s)=>{g(i=>({...i,[e]:s,showModal:s,modal:s?e:null}))},C=()=>{U({note_id:n==null?void 0:n.id,update_id:o==null?void 0:o.id,exposure:b?"public":"private",filter:[`update_id,eq,${o==null?void 0:o.id}`,`note_id,eq,${n==null?void 0:n.id}`]})},H=()=>{oe(t==null?void 0:t.comment)?g(e=>({...e,errors:{...e==null?void 0:e.errors,comment:{message:"Comment is required"}}})):k()},k=async()=>{var e,s;try{S((e=B)==null?void 0:e.createModel,!0);const i={note_id:n==null?void 0:n.id,user_id:c==null?void 0:c.id,update_id:o==null?void 0:o.id,comment:t==null?void 0:t.comment,create_at:re(new Date)},d=await I("update_comments",i);g(a=>({...a,comment:""})),d!=null&&d.error||C()}catch(i){console.error("error >> ",i)}finally{y("add_comment",!1),S((s=B)==null?void 0:s.createModel,!1)}},j=()=>{const e=p.current;e&&(e.style.height="auto",e.style.height=e.scrollHeight+"px")};m.useEffect(()=>{t!=null&&t.add_comment&&j()},[t==null?void 0:t.add_comment]),m.useEffect(()=>{if(x){const e=window.scrollY,s=window.getComputedStyle(document.body).overflow;return document.body.style.position="fixed",document.body.style.top=`-${e}px`,document.body.style.width="100%",document.body.style.overflow="hidden",()=>{document.body.style.position="",document.body.style.top="",document.body.style.width="",document.body.style.overflow=s,window.scrollTo(0,e)}}},[x]);const J=e=>{const s=e.target.value,i=s[s.length-1],d=p.current;j(),e.nativeEvent.inputType==="deleteContentBackward"&&x&&w(!1),i==="@"&&(N.current=d,w(!0)),g(a=>({...a,comment:s,errors:{...a==null?void 0:a.errors,comment:{message:""}}}))},Q=e=>{const s=p.current,i=s.selectionEnd,d=t.comment.substring(0,i),a=t.comment.substring(i),_=`@${e.first_name} ${e.last_name} `;g(u=>({...u,comment:d.slice(0,-1)+_+a})),w(!1),setTimeout(()=>{s.focus();const u=i-1+_.length;s.setSelectionRange(u,u)},0)};m.useEffect(()=>{const e=s=>{x&&!s.target.closest(".mention-modal")&&w(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[x]),m.useEffect(()=>{o!=null&&o.id&&(n!=null&&n.id)&&(C(),o!=null&&o.id&&!b&&Y(o.id).then(e=>{e&&setGlobalState("mentionsChange",!(globalState!=null&&globalState.mentionsChange))}))},[o==null?void 0:o.id,n==null?void 0:n.id,b]),m.useEffect(()=>{const e=s=>{t!=null&&t.add_comment&&p.current&&!p.current.contains(s.target)&&!s.target.closest(".mention-modal")&&y(t==null?void 0:t.modal,!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[t==null?void 0:t.add_comment]),m.useEffect(()=>{var e;if(l!=null&&l.hash&&((e=f==null?void 0:f.list)!=null&&e.length)){const[s,i]=l==null?void 0:l.hash.split("#"),d=(()=>{try{return atob(i)}catch{return i}})();if(d.includes("comment:")){const[a,_]=d==null?void 0:d.split(":"),u=document.getElementById(_);u&&(u==null||u.scrollIntoView({behavior:"smooth",block:"center"}))}}},[l==null?void 0:l.hash,($=f==null?void 0:f.list)==null?void 0:$.length]),m.useEffect(()=>{},[f,n,o]);const W=e=>Array.isArray(e==null?void 0:e.list)&&e.list.every(s=>s&&typeof s=="object"&&typeof s.id=="number"&&typeof s.comment=="string"),T=m.useMemo(()=>!(h!=null&&h.updateContributors)||!(c!=null&&c.id)?[]:h.updateContributors.filter(e=>e.id!==c.id),[h==null?void 0:h.updateContributors,c==null?void 0:c.id]);return r.jsxs(m.Fragment,{children:[r.jsxs("div",{className:"relative  flex flex-col gap-[1rem]",children:[v!=null&&v.list?r.jsx(se,{loading:!0}):null,W(f)?[...f.list].sort((e,s)=>new Date(e.update_at)-new Date(s.update_at)).map((e,s)=>r.jsx(fe,{children:r.jsx(E,{children:r.jsx(ie,{loadComments:C,comment:e,update:o,note:n,onSuccess:()=>{C()}})})},s)):null]}),t!=null&&t.add_comment?r.jsxs("div",{className:`relative w-full ${(P=(R=t==null?void 0:t.errors)==null?void 0:R.comment)!=null&&P.message?"mb-5":""}`,children:[r.jsx("textarea",{ref:p,className:"border-bborder-t-0 h-auto min-h-[32px] w-full resize-none appearance-none overflow-hidden rounded-sm border-x-0 border-t-0 border-[#1f1d1a] bg-brown-main-bg p-[12px_16px_12px_16px] px-0 text-sm font-normal leading-tight text-[#1f1d1a] placeholder:text-base placeholder:text-[#79716C] focus:border-x-0 focus:border-t-0 focus:shadow-none focus:outline-none focus:outline-0 focus:ring-0",rows:"1",name:"comment",placeholder:"Comment on this section... (Press @ to mention someone)",onChange:J,value:t==null?void 0:t.comment,onKeyDown:e=>{var s;e.key==="Enter"&&(e.shiftKey?setTimeout(j,0):(e.preventDefault(),(s=t==null?void 0:t.comment)!=null&&s.trim()&&H()))}}),((q=(O=t==null?void 0:t.errors)==null?void 0:O.comment)==null?void 0:q.message)&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:t.errors.comment.message}),x&&T.length>0&&r.jsxs("div",{ref:M,style:L.popper,...G.popper,className:"mention-modal z-50 max-h-[200px] w-[250px] overflow-y-auto rounded-[.125rem] border-[.125rem] border-[#1f1d1a] bg-brown-main-bg px-3 shadow-lg",children:[r.jsx("div",{ref:V,style:L.arrow}),T.map(e=>r.jsxs("div",{className:"flex cursor-pointer items-center gap-2 border-b border-[#1f1d1a]/10 p-3 font-iowan text-[#1f1d1a] last:border-b-0 hover:bg-[#1f1d1a]/5",onClick:()=>Q(e),children:[e.photo?r.jsx("img",{src:e.photo,alt:`${e.first_name} ${e.last_name}`,className:"h-7 w-7 rounded-full border border-[#1f1d1a]/20 object-cover"}):r.jsx("div",{className:"flex h-7 w-7 items-center justify-center rounded-full border border-[#1f1d1a]/20 bg-[#1f1d1a]/5 font-iowan text-sm text-[#1f1d1a]",children:e.first_name[0]}),r.jsxs("span",{className:"font-medium",children:[e.first_name," ",e.last_name]})]},e.id))]})]}):null,r.jsx("div",{className:` flex gap-5 ${t!=null&&t.add_comment?"mt-8":"mt-0"}`,children:!(t!=null&&t.add_comment)&&r.jsx(E,{children:r.jsx(me,{onClick:()=>{b?(A("Please Signup to interact with the update",3e3),z("/member/sign-up")):l.search.includes("mode=preview")?A("You are in preview mode"):y("add_comment",!0)},className:"!h-[36px] !w-[174px] !min-w-[10.875rem] !gap-[.625rem] !rounded-[.125rem] !border-[.0625rem] !border-black !bg-brown-main-bg !py-[.5rem] px-[1rem] font-iowan !text-[1rem] !font-bold !leading-[1.25rem] !text-black",children:"Add Comment"})})}),r.jsx(E,{children:r.jsx(de,{action:"Add",mode:"manual",multiple:!1,title:"Add Comment",onSuccess:k,inputConfirmation:!1,onClose:()=>y("add_comment",!0),customMessage:r.jsx(r.Fragment,{children:"Are you sure you want to add this comment?"}),isOpen:(t==null?void 0:t.showModal)&&(t==null?void 0:t.modal)==="confirm_comment"})})]})};export{Ge as default};
