import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{S as l}from"./index-f1d75e77.js";import{u as y}from"./index-f2ad9142.js";import{u as f}from"./useCompanyMember-0033d2de.js";import{r as D}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function v({onSelect:p,value:c,refreshRef:u,clearRef:d}){var i,m;const{profile:t}=y(),{companyMember:{myMembers:o},loading:e,getMyCompanyMembers:h}=f({filter:[`company_id,eq,${(m=(i=t==null?void 0:t.companies)==null?void 0:i[0])==null?void 0:m.id}`]}),x=async()=>{var a,r;await h({filter:[`company_id,eq,${(r=(a=t==null?void 0:t.companies)==null?void 0:a[0])==null?void 0:r.id}`]})};return D.useEffect(()=>{t!=null&&t.id&&x()},[t==null?void 0:t.id]),s.jsx(s.Fragment,{children:s.jsx(l,{className:"",value:c,useExternalData:!0,display:"name",label:"Members:",onSelect:p,showSearchIcon:!0,uniqueKey:"member_id",placeholder:"Search Members",inputClassName:"!h-[2.5rem] !max-h-[2.5rem] !min-h-[2.5rem]",externalDataOptions:o==null?void 0:o.map(a=>{var r,n;return{...a,name:`${(r=a==null?void 0:a.user)==null?void 0:r.first_name} ${(n=a==null?void 0:a.user)==null?void 0:n.last_name}`}}),externalDataLoading:e==null?void 0:e.myMembers,refreshRef:u,clearRef:d})})}export{v as default};
