import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{S as l}from"./index-f1d75e77.js";import{u as h}from"./index-f2ad9142.js";import{u as x}from"./useRecipientGroup-ff89a0bd.js";import{r as f}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function P({onSelect:m,value:s,refreshRef:n,clearRef:u}){var p;const{profile:r}=h(),{recipientGroup:o,loading:a,getRecipientGroups:c}=x(),d=async()=>{await c({filter:[`goodbadugly_recipient_group.user_id,eq,${r==null?void 0:r.id}`]})};return f.useEffect(()=>{r!=null&&r.id&&d()},[r==null?void 0:r.id]),e.jsx(e.Fragment,{children:e.jsx(l,{className:"",value:s,useExternalData:!0,display:"name",label:"Groups:",onSelect:m,showSearchIcon:!0,uniqueKey:"group_id",placeholder:"Search Groups",externalDataLoading:a==null?void 0:a.list,inputClassName:"!h-[2.5rem] !max-h-[2.5rem] !min-h-[2.5rem]",externalDataOptions:(p=o==null?void 0:o.list)==null?void 0:p.map(t=>{var i;return{...t,name:(i=t==null?void 0:t.group)==null?void 0:i.group_name}}),refreshRef:n,clearRef:u})})}export{P as default};
