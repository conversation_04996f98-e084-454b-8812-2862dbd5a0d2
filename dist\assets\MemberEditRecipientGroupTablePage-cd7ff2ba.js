import{j as a}from"./@nextui-org/listbox-0f38ca19.js";import{r as u,b as k,i as N}from"./vendor-4cdf2bd1.js";import{u as I}from"./react-hook-form-a383372b.js";import{o as V}from"./yup-0917e80c.js";import{c as $,a as _,d as C}from"./yup-342a5df4.js";import{G as q,A as U,M as b,p as B,t as h,s as F}from"./index-f2ad9142.js";import{S as H}from"./SelectGroupType-2b6e1a07.js";import{S as K}from"./CreateGroupModal-d6bb962a.js";import{L as M}from"./index-b8adfdf8.js";import{InteractiveButton2 as O}from"./InteractiveButton-060359e0.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./MkdCustomInput-aaf80542.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./XMarkIcon-cfb26fe7.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";const Le=()=>{const{dispatch:l}=u.useContext(q),{dispatch:d,state:S}=u.useContext(U),w=k(),{id:m}=N(),[Y,E]=u.useState([]),T=$({group_id:_().required("This field is required"),members:C().min(1,"You must add at least one member").of(_())}),{register:z,handleSubmit:y,setError:j,setValue:p,formState:{errors:P,isSubmitting:c,isLoading:v,defaultValues:J},control:f,clearErrors:R,watch:A}=I({resolver:V(T)});async function D(){var t,i;try{let o=new b;o.setTable("recipient_group");const n=await o.callRawAPI(`/v4/api/records/recipient_group/${m}`,void 0,"GET"),e=await B(l,d,"recipient_member",{filter:[`recipient_group_id,eq,${m}`],join:["user"]});e.error||E(()=>{var r;return(r=e==null?void 0:e.data)==null?void 0:r.map(s=>{var x;return(x=s==null?void 0:s.user)==null?void 0:x.id})}),p("group_id",n.model.group_id),p("members",(t=e==null?void 0:e.data)==null?void 0:t.map(r=>{var s;return(s=r==null?void 0:r.user)==null?void 0:s.id})),p("old_members",(i=e==null?void 0:e.data)==null?void 0:i.map(r=>{var s;return(s=r==null?void 0:r.user)==null?void 0:s.id}))}catch(o){return h(d,o.message),{group_id:"",members:[]}}}const G=async t=>{let i=new b;try{i.setTable("recipient_group"),await i.callRestAPI({id:m,group_id:t.group_id,members:t.members.join(","),user_id:S.user},"PUT");const o=t.members.filter(e=>!t.old_members.includes(e)),n=t.old_members.filter(e=>!t.members.includes(e));i.setTable("recipient_member"),await Promise.all(o.map(e=>i.callRestAPI({user_id:e,recipient_group_id:m},"POST"))),await Promise.all(n.map(e=>i.callRawAPI("/v4/api/records/recipient_member",{user_id:e,recipient_group_id:m},"DELETE"))),F(l,"Edit successful"),w("/member/recipient_group")}catch(o){h(d,o.message),j("group_id",{type:"manual",message:o.message})}};u.useEffect(()=>{l({type:"SETPATH",payload:{path:"recipient_group"}})},[]);const[g,L]=A(["members","group_id"]);return u.useEffect(()=>{R(["members","group_id"])},[g,L]),v?a.jsx(M,{}):a.jsxs("div",{className:"p-5 pt-8 mx-auto rounded md:px-8",children:[a.jsx("h4",{className:"text-xl font-semibold text-[#1f1d1a]",children:"Edit Recipient Group"}),a.jsxs("form",{className:"mt-5 w-full max-w-[500px] pb-80",onSubmit:y(G),children:[a.jsx(H,{control:f,name:"group_id",errors:P,onReady:()=>D(),setValue:t=>p("group_id",t),allowedRoles:["investor","stakeholder","NULL"]}),a.jsx(K,{defaultSelectedMembers:g,control:f,name:"members",setValue:t=>p("members",t)}),a.jsx(O,{type:"submit",loading:c,disabled:c,className:"focus:shadow-outline mt-6 h-[40px] w-[90px] rounded bg-primary-black px-4 py-2 text-sm font-bold text-white focus:outline-none",children:c?"":"Submit"})]})]})};export{Le as default};
