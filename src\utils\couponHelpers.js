// Coupon helper functions for easy integration
import MkdSDK from "./MkdSDK";

const sdk = new MkdSDK();

/**
 * Create a VIP coupon with 100% discount
 * @returns {Promise} API response
 */
export const createVIPCoupon = async () => {
  try {
    const response = await sdk.createStripeCoupon({
      couponId: 'vip-access-100',
      name: 'VIP Access - 100% Off',
      percent_off: 100,
      duration: 'forever'
    });
    return response;
  } catch (error) {
    console.error('Error creating VIP coupon:', error);
    throw error;
  }
};

/**
 * Get all available coupons
 * @returns {Promise} API response with coupons list
 */
export const getAllCoupons = async () => {
  try {
    const response = await sdk.getStripeCoupons();
    return response;
  } catch (error) {
    console.error('Error fetching coupons:', error);
    throw error;
  }
};

/**
 * Create a subscription with optional coupon
 * @param {string} planId - The plan ID to subscribe to
 * @param {string|null} couponId - Optional coupon ID to apply
 * @returns {Promise} API response
 */
export const createSubscriptionWithCoupon = async (planId, couponId = null) => {
  try {
    const subscriptionData = {
      planId: planId,
    };
    
    if (couponId) {
      subscriptionData.coupon = couponId;
    }
    
    const response = await sdk.createStripeSubscription(subscriptionData);
    return response;
  } catch (error) {
    console.error('Error creating subscription with coupon:', error);
    throw error;
  }
};

/**
 * Create a custom coupon
 * @param {Object} couponData - Coupon configuration
 * @param {string} couponData.couponId - Unique coupon identifier
 * @param {string} couponData.name - Display name for the coupon
 * @param {number} couponData.percent_off - Percentage discount (1-100)
 * @param {string} couponData.duration - Duration type ('forever', 'once', 'repeating')
 * @returns {Promise} API response
 */
export const createCustomCoupon = async (couponData) => {
  try {
    const response = await sdk.createStripeCoupon(couponData);
    return response;
  } catch (error) {
    console.error('Error creating custom coupon:', error);
    throw error;
  }
};

// Usage examples:
/*
// Create VIP coupon
const vipCoupon = await createVIPCoupon();

// Get all coupons
const coupons = await getAllCoupons();

// Apply coupon to subscription
const subscription = await createSubscriptionWithCoupon('plan_123', 'vip-access-100');

// Create custom coupon
const customCoupon = await createCustomCoupon({
  couponId: 'SAVE50',
  name: '50% Off Special',
  percent_off: 50,
  duration: 'forever'
});
*/
