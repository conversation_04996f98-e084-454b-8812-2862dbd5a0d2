import{j as l}from"./@nextui-org/listbox-0f38ca19.js";import{r as f,u as a,i as D,b as ss,R as os}from"./vendor-4cdf2bd1.js";import{a as rs,u as is,O as es,L as A,i as ds,bU as ls}from"./index-f2ad9142.js";import{u as fs}from"./useComments-7e9daaa3.js";import{A as ns}from"./AddButton-51d1b2cd.js";import{U as cs}from"./UpdateSectionNoteComment-626a6891.js";import{A as ms}from"./index-afef2e72.js";import"./MkdInput-d37679e9.js";import{u as ps}from"./useUpdateCollaborator-1187c43b.js";import{u as us}from"./react-popper-9a65a9b6.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./UpdateSection-24e02fd2.js";import"./index-45396f34.js";import"./lucide-react-0b94883e.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./@popperjs/core-f3391c26.js";const ys=e=>e&&typeof e=="object"&&typeof e.id=="number"&&typeof e.reply=="string",xs=({note:e=null,update:r=null,comment:d=null,externalData:o=null,setExternalData:c=null})=>{var U,q,z,F;const{showToast:N,tokenExpireError:ts,custom:hs,setLoading:M,create:Y,RequestItems:u}=rs(),h=f.useRef(null),b=f.useRef(null),S=f.useRef(!1),K=a();f.useState({html:null,data:null,modal:null,showModal:!1});const{loading:_,comments:E,getCommentReplies:V}=fs(),{public_link_id:C}=D(),{profile:n}=is(),H=ss();f.useState(!1);const{data:p,fetchUpdateContributors:G}=ps(),[y,g]=f.useState(!1);f.useState({x:0,y:0});const L=f.useRef(null),k=f.useRef(null),[J,Q]=f.useState(null),{styles:$,attributes:W}=us(L.current,k.current,{placement:"bottom-start",modifiers:[{name:"arrow",options:{element:J}},{name:"offset",options:{offset:[0,8]}},{name:"preventOverflow",options:{padding:8}},{name:"flip",options:{padding:8}}]});f.useEffect(()=>{r!=null&&r.id&&G(r==null?void 0:r.id)},[r==null?void 0:r.id]);const j=()=>{const s=h.current;s&&(s.style.height="auto",s.style.height=s.scrollHeight+"px")};f.useEffect(()=>{o!=null&&o.add_reply&&j()},[o==null?void 0:o.add_reply]),f.useEffect(()=>{S.current=o==null?void 0:o.add_reply,console.log("isAddingReply updated:",S.current)},[o==null?void 0:o.add_reply]),f.useEffect(()=>{const s=i=>{o!=null&&o.add_reply&&b.current&&!b.current.contains(i.target)&&(c==null||c(m=>({...m,modal:null,add_reply:!1,showModal:!1,reply:"",errors:{...m==null?void 0:m.errors,reply:{message:""}}})))};return document.addEventListener("mousedown",s),()=>document.removeEventListener("mousedown",s)},[o==null?void 0:o.add_reply]);const T=(s,i)=>{c&&c(m=>({...m,[s]:i,showModal:i,modal:i?s:null}))},X=()=>{c&&(ds(o==null?void 0:o.reply)?c(s=>({...s,errors:{...s==null?void 0:s.errors,reply:{message:"Reply is required"}}})):P())},P=async()=>{try{if(!(d!=null&&d.id)||!(e!=null&&e.id)||!(r!=null&&r.id)||!(n!=null&&n.id)){console.error("Missing required data for adding reply");return}M(u==null?void 0:u.createModel,!0);const s={note_id:e==null?void 0:e.id,user_id:n==null?void 0:n.id,update_id:r==null?void 0:r.id,reply:o==null?void 0:o.reply,update_comment_id:d==null?void 0:d.id,create_at:ls(new Date)},i=await Y("update_comment_replies",s);i!=null&&i.error||R()}catch(s){console.error("Error adding reply:",s)}finally{c&&c(s=>({...s,modal:null,add_reply:!1,showModal:!1,reply:"",errors:{...s==null?void 0:s.errors,reply:{message:""}}})),M(u==null?void 0:u.createModel,!1)}},R=async()=>{if(!(d!=null&&d.id)||!(e!=null&&e.id)||!(r!=null&&r.id)){console.error("Missing required data for loading replies");return}try{await V({filter:[`update_id,eq,${r==null?void 0:r.id}`,`note_id,eq,${e==null?void 0:e.id}`,`update_comment_id,eq,${d==null?void 0:d.id}`],note_id:e==null?void 0:e.id,update_id:r==null?void 0:r.id,comment_id:d==null?void 0:d.id,exposure:C?"public":"private"})}catch(s){console.error("Error loading replies:",s)}};f.useEffect(()=>{r!=null&&r.id&&(e!=null&&e.id)&&(d!=null&&d.id)&&R()},[r==null?void 0:r.id,e==null?void 0:e.id,d==null?void 0:d.id,C]);const Z=s=>Array.isArray(s==null?void 0:s.replies)&&s.replies.every(ys),I=s=>{const i=s.target.value,m=i[i.length-1],v=h.current;j(),s.nativeEvent.inputType==="deleteContentBackward"&&y&&g(!1),m==="@"&&(L.current=v,g(!0)),c&&c(t=>({...t,reply:i,errors:{...t==null?void 0:t.errors,reply:{message:""}}}))},x=s=>{const i=h.current,m=i.selectionEnd,v=o.reply.substring(0,m),t=o.reply.substring(m),B=`@${s.first_name} ${s.last_name} `;c(w=>({...w,reply:v.slice(0,-1)+B+t})),g(!1),setTimeout(()=>{i.focus();const w=m-1+B.length;i.setSelectionRange(w,w)},0)};f.useEffect(()=>{const s=i=>{y&&!i.target.closest(".mention-modal")&&g(!1)};return document.addEventListener("mousedown",s),()=>document.removeEventListener("mousedown",s)},[y]);const O=os.useMemo(()=>!(p!=null&&p.updateContributors)||!(n!=null&&n.id)?[]:p.updateContributors.filter(s=>s.id!==(n==null?void 0:n.id)),[p==null?void 0:p.updateContributors,n==null?void 0:n.id]);return f.useEffect(()=>{if(y){const s=window.scrollY,i=window.getComputedStyle(document.body).overflow;return document.body.style.position="fixed",document.body.style.top=`-${s}px`,document.body.style.width="100%",document.body.style.overflow="hidden",()=>{document.body.style.position="",document.body.style.top="",document.body.style.width="",document.body.style.overflow=i,window.scrollTo(0,s)}}},[y]),l.jsxs(f.Fragment,{children:[l.jsxs("div",{className:`relative ml-[1.125rem] flex flex-col  space-y-[20px] border-l-[.125rem] border-l-[#1F1D1A1A] pl-[1.875rem] ${o!=null&&o.showReply?"":"hidden"}`,children:[_!=null&&_.replies?l.jsx(es,{loading:!0}):null,Z(E)&&[...E.replies].sort((s,i)=>new Date(i.update_at)-new Date(s.update_at)).map((s,i)=>l.jsx(A,{children:l.jsx(cs,{comment:d,update:r,reply:s,note:e,loadReplies:R})},i)),(o==null?void 0:o.add_reply)&&l.jsxs("div",{ref:b,className:`reply-form-area relative w-full ${(q=(U=o==null?void 0:o.errors)==null?void 0:U.reply)!=null&&q.message?"mb-5":""}`,children:[l.jsx("textarea",{ref:h,className:"border-bborder-t-0 h-auto min-h-[32px] w-full resize-none appearance-none overflow-hidden rounded-sm border-x-0 border-t-0 border-[#1f1d1a] bg-brown-main-bg p-[12px_16px_12px_16px] px-0 text-sm font-normal leading-tight text-[#1f1d1a] placeholder:text-base placeholder:text-[#79716C] focus:border-x-0 focus:border-t-0 focus:border-t-0 focus:shadow-none focus:outline-none focus:outline-0 focus:ring-0",rows:"1",name:"reply",placeholder:"Reply to this comment... (Press @ to mention someone)",onChange:I,value:(o==null?void 0:o.reply)||"",onKeyDown:s=>{var i;s.key==="Enter"&&(s.shiftKey?setTimeout(j,0):(s.preventDefault(),(i=o==null?void 0:o.reply)!=null&&i.trim()&&X()))}}),((F=(z=o==null?void 0:o.errors)==null?void 0:z.reply)==null?void 0:F.message)&&l.jsx("p",{className:"mt-1 text-sm text-red-500",children:o.errors.reply.message}),y&&O.length>0&&l.jsxs("div",{ref:k,style:$.popper,...W.popper,className:"mention-modal z-50 max-h-[200px] w-[250px] overflow-y-auto rounded-[.125rem] border-[.125rem] border-[#1f1d1a] bg-brown-main-bg px-3 shadow-lg",children:[l.jsx("div",{ref:Q,style:$.arrow}),O.map(s=>l.jsxs("div",{className:"flex cursor-pointer items-center gap-2 border-b border-[#1f1d1a]/10 p-3 font-iowan text-[#1f1d1a] last:border-b-0 hover:bg-[#1f1d1a]/5",onClick:()=>x(s),children:[s.photo?l.jsx("img",{src:s.photo,alt:`${s.first_name} ${s.last_name}`,className:"h-7 w-7 rounded-full border border-[#1f1d1a]/20 object-cover"}):l.jsx("div",{className:"flex h-7 w-7 items-center justify-center rounded-full border border-[#1f1d1a]/20 bg-[#1f1d1a]/5 font-iowan text-sm text-[#1f1d1a]",children:s.first_name[0]}),l.jsxs("span",{className:"font-medium",children:[s.first_name," ",s.last_name]})]},s.id))]})]}),l.jsx("div",{className:"flex gap-5",children:!(o!=null&&o.add_reply)&&l.jsx(A,{children:l.jsx(ns,{onClick:()=>{C?(H("/member/sign-up"),N("Please Signup to interact with the update",3e3)):K.search.includes("mode=preview")?N("You are in preview mode"):T("add_reply",!0)},className:"!h-[36px] !w-[174px] !min-w-[10.875rem] !gap-[.625rem] !rounded-[.125rem] !border-[.0625rem] !border-black !bg-brown-main-bg !py-[.5rem] px-[1rem] font-iowan !text-[1rem] !font-bold !leading-[1.25rem] !text-black",children:"Add Reply"})})})]}),l.jsx(A,{children:l.jsx(ms,{title:"Reply",mode:"manual",action:"Add",multiple:!1,onSuccess:P,inputConfirmation:!1,onClose:()=>T("add_reply",!0),customMessage:l.jsx(l.Fragment,{children:"Are you sure you want to add this reply?"}),isOpen:(o==null?void 0:o.showModal)&&(o==null?void 0:o.modal)==="confirm_reply",className:"action-confirmation-modal"})})]})};export{xs as default};
