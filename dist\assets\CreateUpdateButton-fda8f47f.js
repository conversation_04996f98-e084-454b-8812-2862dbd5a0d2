import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{a as R,u as P,O as U,L}from"./index-f2ad9142.js";import{u as k}from"./useSubscription-dc563085.js";import{M as I}from"./index-49e40c51.js";import{r as n,b as M}from"./vendor-4cdf2bd1.js";import{P as O}from"./PlusIcon-26cedb5d.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const _={pro:5,"pro yearly":5,business:10,"business yearly":10,enterprise:1/0},se=({setComposeUpdate:h})=>{const b=n.useRef(null),j=n.useRef(null),[u,c]=n.useState(!1),[y,l]=n.useState(!1),{globalState:i,setGlobalState:g}=R(),{loading:t,data:e,processRegisteredDate:w,getSubscription:v,getCustomerSubscription:p,getSentUpdates:S}=k(),{profile:r}=P();M();const N=()=>{var m,f,d,x;if(t!=null&&t.processRegisteredDate||t!=null&&t.subscription)return;if(e!=null&&e.trial_expired&&!(e!=null&&e.subscription)){c(!0);return}const o=(x=(d=(f=(m=e==null?void 0:e.object)==null?void 0:m.plan)==null?void 0:f.nickname)==null?void 0:d.split(" ")[0])==null?void 0:x.trim(),a=_[o]||(o!=null&&o.includes("enterprise")?1/0:0);(e==null?void 0:e.sentUpdates)>=a&&a!==1/0&&c(!0)},C=()=>{c(!1)};return n.useEffect(()=>{r!=null&&r.id&&(p(),v({filter:[`user_id,eq,${r==null?void 0:r.id}`]}),w(r==null?void 0:r.create_at),S(r),i!=null&&i.refreshSubscription&&g("refreshSubscription",!1))},[r==null?void 0:r.id,i==null?void 0:i.refreshSubscription]),s.jsxs(s.Fragment,{children:[s.jsxs("div",{onMouseOver:N,onMouseLeave:C,className:"relative grid h-[15.125rem] min-h-[16rem] grid-cols-1 grid-rows-12 rounded border-[.125rem] border-dashed border-[#1f1d1a] bg-[#F2DFCE] font-iowan text-lg font-medium shadow",children:[t!=null&&t.processRegisteredDate||t!=null&&t.subscription?s.jsx(U,{loading:!0}):null,u?null:s.jsx("div",{className:"flex justify-center items-center py-12 w-full h-full min-h-full max-h-full row-span-12",children:s.jsxs("button",{ref:b,onClick:()=>h(!0),className:"flex flex-col gap-5 justify-center items-center",children:[s.jsx(O,{className:"w-6 h-6"}),s.jsx("span",{className:"font-iowan text-[1rem] font-[700] capitalize leading-5",children:"Create an update"})]})}),u?s.jsxs("div",{className:"grid grid-cols-1 justify-center items-center p-5 w-full h-full min-h-full max-h-full row-span-12 grid-rows-12",children:[s.jsx("div",{className:"text-center row-span-8",children:e!=null&&e.trial_expired&&!(e!=null&&e.subscription)?s.jsx("p",{children:"Please Upgrade your account to create an update!"}):s.jsx("p",{children:"You have reached your monthly update limit for your current plan."})}),s.jsx("div",{className:"row-span-4 w-full",children:s.jsx("button",{ref:j,className:"flex w-full flex-col items-center justify-center gap-5 rounded-[.125rem] bg-primary-black px-4 py-2 font-iowan text-[1rem] font-[700] leading-5 text-white",onClick:()=>{l(!0)},children:e!=null&&e.trial_expired&&!(e!=null&&e.subscription)?"Subscribe":"Upgrade Plan"})})]}):null]}),s.jsx(L,{children:s.jsx(I,{isOpen:y,onClose:()=>{l(!1)},currentPlan:e==null?void 0:e.subscription,onSuccess:()=>{p()}})})]})};export{se as default};
