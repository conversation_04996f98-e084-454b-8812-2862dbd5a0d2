import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{G as a,A as m}from"./index-f2ad9142.js";import{r as o,j as i,O as l}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function k(){o.useContext(a);const{state:r}=o.useContext(m);return t.jsxs("div",{children:[t.jsx("h3",{className:"mt-6 px-4 text-3xl font-normal"}),t.jsx("ul",{className:"custom-overflow mt-8 flex w-full overflow-x-auto border-y border-[#0003] px-8 py-1",children:t.jsx("li",{children:t.jsx(i,{to:"/collaborator/upgrade",end:!0,className:({isActive:e})=>`font block rounded-md px-6 py-2 font-medium text-[#1f1d1a] ${e?"bg-[#1f1d1a] text-white":"font-iowan text-[#1f1d1a]"}`,children:"Upgrade"})})}),r.profile.email?t.jsx(l,{}):null]})}export{k as default};
