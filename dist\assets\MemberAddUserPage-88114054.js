import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{r as l,h as y,b as j}from"./vendor-4cdf2bd1.js";import{u as v}from"./react-hook-form-a383372b.js";import{o as w}from"./yup-0917e80c.js";import{c as S,a as o}from"./yup-342a5df4.js";import{G as E,A as N,M as R,g as m,s as c}from"./index-f2ad9142.js";import{InteractiveButton2 as _}from"./InteractiveButton-060359e0.js";import{M as n}from"./MkdInput-d37679e9.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const mt=({onSuccess:p=null})=>{const{dispatch:r}=l.useContext(E),{dispatch:T,state:d}=l.useContext(N),[f]=y();f.get("next");const h=S({first_name:o().nullable(),last_name:o().nullable(),email:o().email("Invalid Email").required("This field is required")});j();const{register:s,handleSubmit:x,setError:A,formState:{errors:i,isSubmitting:u}}=v({resolver:w(h),defaultValues:{first_name:"",last_name:"",email:"",role:""}}),g=async a=>{const b=new R;try{const e=await b.callRawAPI(`/v3/api/custom/goodbadugly/member/${d.company.id}/invite-to-team`,{email:m(a.email),first_name:m(a.first_name),last_name:m(a.last_name)},"POST");console.log("RESULT >>",e),e.error||(c(r,"Invite Sent",500,"success"),p&&p()),console.log("RESULT >>",e)}catch(e){console.log("ERROR >>",e),c(r,(e==null?void 0:e.message)||"Email already exist",5e3,"error")}};return l.useEffect(()=>{r({type:"SETPATH",payload:{path:"users"}})},[]),t.jsx("div",{className:"grid grid-cols-1 grid-rows-1 p-5 mx-auto w-full h-full min-h-full max-h-full rounded",children:t.jsxs("form",{className:"w-full max-w-full h-full min-h-full max-h-full",onSubmit:x(g,a=>{console.error("ERROR >>",a)}),children:[t.jsxs("div",{className:"space-y-5 w-full",children:[t.jsxs("div",{className:"grid grid-cols-1 gap-2 w-full md:grid-cols-2",children:[t.jsx("div",{children:t.jsx(n,{type:"text",register:s,errors:i,name:"first_name",label:"First Name"})}),t.jsx("div",{children:t.jsx(n,{type:"text",register:s,errors:i,name:"last_name",label:"Last Name"})})]}),t.jsx("div",{className:"w-full",children:t.jsx(n,{type:"text",register:s,errors:i,name:"email",required:!0,label:"Email Address"})})]}),t.jsx(_,{type:"submit",loading:u,disabled:u,className:"mt-6 !w-full !rounded-[.125rem] !bg-primary-black !px-4 !py-4 !font-bold !text-white ",children:"Submit"})]})})};export{mt as default};
