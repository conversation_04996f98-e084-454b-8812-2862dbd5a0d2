import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as n,i as T,b as q}from"./vendor-4cdf2bd1.js";import{u as z}from"./react-hook-form-a383372b.js";import{o as $}from"./yup-0917e80c.js";import{c as P,a as l}from"./yup-342a5df4.js";import{G as A,A as C,M as w,t as j,s as N}from"./index-f2ad9142.js";import{InteractiveButton2 as I}from"./InteractiveButton-060359e0.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";const R=[{label:"Manager/Founder",value:"member"},{label:"Collaborator",value:"collaborator"},{label:"Fund manager",value:"investor"},{label:"Stakeholder",value:"stakeholder"}],me=()=>{var c,p,u,x,f,h,b,g;const{dispatch:o}=n.useContext(A),{dispatch:m,state:D}=n.useContext(C),{id:d}=T(),v=P({first_name:l().required("This field is required"),last_name:l().required("This field is required"),email:l().required("This field is required"),role:l().required("This field is required")});async function _(){try{const t=await new w().callRawAPI(`/v4/api/records/user/${d}`,{},"GET");return console.log(t),{first_name:t.model.first_name,last_name:t.model.last_name,email:t.model.email,role:t.model.role}}catch(a){return j(m,a.message),N(o,a.message,5e3,"error"),{first_name:"",last_name:"",email:"",role:""}}}const y=q(),{register:r,handleSubmit:k,setError:S,formState:{errors:s,isSubmitting:i}}=z({resolver:$(v),defaultValues:_}),E=async a=>{try{const F=await new w().callRawAPI(`/v4/api/records/user/${d}`,{email:a.email,first_name:a.first_name,last_name:a.last_name,role:a.role},"PUT");N(o,"User Information Updated"),y("/member/users")}catch(t){j(m,t.message),S("first_name",{type:"manual",message:t.message})}};return n.useEffect(()=>{o({type:"SETPATH",payload:{path:"users"}})},[]),e.jsxs("div",{className:"mx-auto rounded p-5 pt-8 md:px-8",children:[e.jsx("h4",{className:"text-[16px] font-semibold md:text-xl",children:"Edit user"}),e.jsxs("form",{className:"w-full max-w-lg pb-24",onSubmit:k(E),children:[e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Select user type"}),e.jsxs("select",{className:`focus:shadow-outline h-[41.5px] w-full max-w-[500px] appearance-none rounded border border border-[#1f1d1a] bg-transparent py-2 pl-6 pr-8 font-Inter text-sm font-normal leading-tight text-[#1d1f1a] shadow shadow-none focus:outline-none ${(c=s.role)!=null&&c.message?"border-red-500":""}`,...r("role"),children:[e.jsx("option",{value:"",children:"-Select Role-"}),R.map(a=>e.jsx("option",{value:a.value,children:a.label},a.value))]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(p=s.role)==null?void 0:p.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"First name"}),e.jsx("input",{className:`focus:shadow-outline h-[41.6px] w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a]   shadow focus:outline-none ${(u=s.first_name)!=null&&u.message?"border-red-500":""}`,...r("first_name")}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(x=s.first_name)==null?void 0:x.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Last name"}),e.jsx("input",{className:`focus:shadow-outline h-[41.6px] w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a]   shadow focus:outline-none ${(f=s.last_name)!=null&&f.message?"border-red-500":""}`,...r("last_name")}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(h=s.last_name)==null?void 0:h.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Email address"}),e.jsx("input",{className:`focus:shadow-outline h-[41.6px] w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a]   shadow focus:outline-none ${(b=s.email)!=null&&b.message?"border-red-500":""}`,...r("email")}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(g=s.email)==null?void 0:g.message})]}),e.jsx(I,{type:"submit",loading:i,disabled:i,className:"focus:shadow-outline mt-6 h-[40px] w-[90px] rounded bg-primary-black px-4 py-2 text-sm font-bold text-white focus:outline-none",children:i?"":"Submit"})]})]})};export{me as default};
