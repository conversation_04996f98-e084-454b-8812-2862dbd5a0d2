import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{b as a}from"./vendor-4cdf2bd1.js";import{c as m}from"./index.esm-bb52b9ca.js";import{a as o,u as s}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./react-icons-36ae72b7.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const R=()=>{const{authState:r}=o();s();const t=a(),i=()=>{r!=null&&r.isAuthenticated?t("/member/updates?availability=available"):t("/member/login?redirect_uri=/member/updates?availability=available")};return e.jsx("div",{className:"mx-auto w-full",children:e.jsxs("div",{onClick:i,className:"flex cursor-pointer items-center gap-3 ",children:[e.jsx(m,{className:"h-[2rem] w-[2rem] rounded-[.125rem]"}),e.jsx("p",{className:"text-priamry-black font-inter text-[1rem] font-semibold leading-[1.21rem]",children:"Updatestack Dashboard"})]})})};export{R as default};
