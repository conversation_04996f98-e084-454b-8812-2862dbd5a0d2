import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{R as e}from"./vendor-4cdf2bd1.js";import{L as s}from"./index-b8adfdf8.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";const u=()=>{const[o,r]=e.useState(!0);return e.useEffect(()=>{setTimeout(()=>{r(!1)},5e3)},[]),t.jsx(t.Fragment,{children:o?t.jsx(s,{}):t.jsx("div",{className:"flex h-full w-full items-center justify-center text-7xl  text-gray-700 ",children:"Not Found"})})};export{u as default};
