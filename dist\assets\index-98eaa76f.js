import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as m,b as R}from"./vendor-4cdf2bd1.js";import{o as G}from"./yup-0917e80c.js";import{A as M}from"./AddButton-51d1b2cd.js";import{b as O,L as l,aN as B,aO as L,aP as U,aQ as P,aR as $,aS as z,aT as K,aU as H,i as q,a as V,u as Q,aV as W,aW as Y}from"./index-f2ad9142.js";import{M as u}from"./MkdInput-d37679e9.js";import{u as Z}from"./react-hook-form-a383372b.js";import{c as J,a as d}from"./yup-342a5df4.js";import{M as X}from"./index-d07d87ac.js";import{u as ee}from"./useSubscription-dc563085.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const se=({data:g,setFilter:c,filter:y,selectedFundManagers:f=[],onAddToGroup:N})=>{const{sdk:h}=O(),S=h.getProjectId(),j=J({search:d(),niches:d(),check_size_buckets:d(),based_in_city:d(),based_in_state:d(),based_in:d(),geographies_investing_in:d(),leads_round:d(),title:d(),stages:d()}),{register:o,handleSubmit:b,reset:_,formState:{errors:i}}=Z({resolver:G(j)}),n=Object.values(g==null?void 0:g.reduce((r,s)=>(r[s.stages]=s,r),{}));function t(r){const s=Object.entries(r).map(([w,p])=>!q(p)&&!["All","all"].includes(p)?`${S}_fund_manager.${w},cs,${p}`:null).filter(Boolean);if(!(s!=null&&s.length))return c(()=>"");c(()=>s==null?void 0:s.join("|"))}return e.jsx(e.Fragment,{children:e.jsx("div",{className:"py-6 space-y-10",children:e.jsx("div",{className:"flex flex-col",children:e.jsx("form",{className:"flex flex-row flex-wrap gap-6 mt-1",onSubmit:b(t),children:e.jsxs("div",{className:"relative grid h-fit w-full grid-cols-[repeat(auto-fill,minmax(11rem,1fr))] gap-[.75rem]",children:[e.jsx("div",{className:"",children:e.jsx(l,{children:e.jsx(u,{type:"select",errors:i,register:o,name:"stages",label:"Stages",noneText:"All",options:n==null?void 0:n.map(r=>r==null?void 0:r.title),className:"!h-[2.25rem] !w-full !rounded-[0.125rem] !border !py-[0.5rem]"})})}),e.jsxs("div",{children:[e.jsx(l,{children:e.jsx(u,{type:"select",errors:i,register:o,name:"title",label:"Title",noneText:"All",options:Object.entries(B).map(([r,s])=>s),className:"!h-[2.25rem] !w-full !rounded-[0.125rem] !border !py-[0.5rem]"})})," "]}),e.jsxs("div",{children:[e.jsx(l,{children:e.jsx(u,{type:"select",errors:i,register:o,name:"niches",label:"Niches",noneText:"All",options:Object.entries(L).map(([r,s])=>s),className:"!h-[2.25rem] !w-full !rounded-[0.125rem] !border !py-[0.5rem]"})})," "]}),e.jsxs("div",{children:[e.jsx(l,{children:e.jsx(u,{type:"select",errors:i,register:o,name:"geographies_investing_in",label:"investing in",noneText:"All",options:Object.entries(U).map(([r,s])=>s),className:"!h-[2.25rem] !w-full !rounded-[0.125rem] !border !py-[0.5rem]"})})," "]}),e.jsxs("div",{children:[e.jsx(l,{children:e.jsx(u,{type:"select",errors:i,register:o,name:"based_in",label:"Based in",noneText:"All",options:Object.entries(P).map(([r,s])=>s),className:"!h-[2.25rem] !w-full !rounded-[0.125rem] !border !py-[0.5rem]"})})," "]}),e.jsxs("div",{children:[e.jsx(l,{children:e.jsx(u,{type:"select",errors:i,register:o,name:"based_in_state",label:"Based in state",noneText:"All",options:Object.entries($).map(([r,s])=>s),className:"!h-[2.25rem] !w-full !rounded-[0.125rem] !border !py-[0.5rem]"})})," "]}),e.jsxs("div",{children:[e.jsx(l,{children:e.jsx(u,{type:"select",errors:i,register:o,name:"based_in_city",label:"Based in city",noneText:"All",options:Object.entries(z).map(([r,s])=>s),className:"!h-[2.25rem] !w-full !rounded-[0.125rem] !border !py-[0.5rem]"})})," "]}),e.jsxs("div",{children:[e.jsx(l,{children:e.jsx(u,{type:"select",errors:i,register:o,name:"leads_round",label:"Leads round",noneText:"All",options:Object.entries(K).map(([r,s])=>s),className:"!h-[2.25rem] !w-full !rounded-[0.125rem] !border !py-[0.5rem]"})})," "]}),e.jsxs("div",{children:[e.jsx(l,{children:e.jsx(u,{type:"select",errors:i,register:o,name:"check_size_buckets",label:"Check size buckets",noneText:"All",options:Object.entries(H).map(([r,s])=>s),className:"!h-[2.25rem] !w-full !rounded-[0.125rem] !border !py-[0.5rem]"})})," "]}),e.jsxs("div",{className:"flex !w-fit items-end gap-[.75rem]",children:[e.jsx(l,{children:e.jsx(M,{showPlus:!1,type:"submit",className:"!rounded-[.125rem]",children:"Search"})}),e.jsx("button",{type:"button",onClick:()=>{_(),b(t)()},className:"font-semibold text-[#1f1d1a]",children:"Clear"})]}),e.jsx("div",{className:"absolute -bottom-[3.125rem] right-0 m-auto mt-4 flex w-fit items-center gap-4 md:bottom-0",children:e.jsx(l,{children:e.jsx(M,{type:"button",showPlus:!1,disabled:f.length===0,onClick:N,className:"!border-none !bg-[#F2DFCE] p-1 px-4 font-iowan text-[1rem] font-[700] leading-[1.25rem] !text-primary-black",children:"Add to Group"})})})]})})})})})},te=[{header:"Company",accessor:"fund_name",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"fund_manager"},{header:"Stage",accessor:"stages",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"fund_manager"},{header:"First Name",accessor:"first_name",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"fund_manager"},{header:"Last Name",accessor:"last_name",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"fund_manager"},{header:"Title",accessor:"title",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"fund_manager"},{header:"Country",accessor:"based_in_country",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"fund_manager"},{header:"City",accessor:"based_in_city",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"fund_manager"},{header:"State",accessor:"based_in_state",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"fund_manager"}],Re=()=>{var v,A,E,k,D,F,T;const g=m.useRef(null),c=m.useRef(null),{setGlobalState:y}=V(),[f,N]=m.useState(""),[h,S]=m.useState([]),[j,o]=m.useState([]),[b,_]=m.useState({maxSelection:0}),i=R(),{profile:n}=Q({}),{data:t,getSubscription:r,getAccessedFundManager:s,loading:w}=ee();console.log("data",t);const p=(F=W)==null?void 0:F[(D=(k=(E=(A=(v=t==null?void 0:t.object)==null?void 0:v.plan)==null?void 0:A.nickname)==null?void 0:E.split(" "))==null?void 0:k[0])==null?void 0:D.trim()],C=()=>{const a=j==null?void 0:j.map(x=>{if(h!=null&&h.includes(x==null?void 0:x.id))return x}).filter(Boolean);y("fundManagersToAdd",a),i("/member/add-recipient_group")},I=()=>{if(!(t!=null&&t.accessedFundManagers))return _(x=>({...x,maxSelection:p}));const a=p-(t==null?void 0:t.accessedFundManagers);return _(x=>({...x,maxSelection:a}))};return m.useEffect(()=>{n!=null&&n.id&&(r({filter:[`user_id,eq,${n==null?void 0:n.id}`,"cancelled,eq,0","status,eq,'active'"],join:[]}),s(n))},[n==null?void 0:n.id]),m.useEffect(()=>{var a;c!=null&&c.current&&((a=c==null?void 0:c.current)==null||a.click())},[f]),m.useEffect(()=>{I()},[t==null?void 0:t.accessedFundManagers,p]),e.jsx(e.Fragment,{children:e.jsxs(l,{children:[e.jsxs("div",{className:"flex items-center justify-between gap-5 p-7",children:[e.jsxs("button",{className:" flex h-[2.25rem] w-[5.1875rem] items-center justify-center gap-2 ",onClick:()=>i(-1),children:[e.jsx("div",{className:"flex min-h-[32px] min-w-[32px] items-center justify-center  gap-2 rounded border border-[#1f1d1a] ",children:e.jsxs("svg",{className:"min-h-5 min-w-5",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.5 10.0001L2.5 10.0001",stroke:"black","stroke-width":"1.66667","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M8.33203 15.8335L2.4987 10.0002L8.33203 4.16683",stroke:"black","stroke-width":"1.66667","stroke-linecap":"round","stroke-linejoin":"round"})]})}),e.jsx("span",{className:"font-inter font-[400]",children:" Back"})]}),((T=t==null?void 0:t.subscription)==null?void 0:T.status)==="active"?e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[t!=null&&t.accessedFundManagers?e.jsxs("span",{children:[t==null?void 0:t.accessedFundManagers," /"]}):null,e.jsx("span",{children:p}),e.jsx("span",{children:"Available investors "})]}):null]}),e.jsxs("div",{className:"flex items-center justify-between gap-5 px-7",children:[e.jsx("p",{className:"font-iowan text-[2rem] font-bold leading-[2.4863rem]",children:"Investors"}),e.jsx("div",{children:e.jsxs("div",{className:"flex h-[2.25rem] cursor-pointer items-center justify-between gap-3 rounded-[.125rem] border border-black px-2 py-1",children:[e.jsx(Y,{className:"!h-[1.25rem] !w-[1.25rem] text-black"}),e.jsx("input",{type:"text",placeholder:"Search Fund Managers",className:"w-full border-none bg-transparent p-0 placeholder:text-left placeholder:text-black focus:outline-none",style:{boxShadow:"0 0 transparent"},onKeyDown:a=>{a.key==="Enter"&&a.preventDefault()},onKeyUp:a=>{}})]})})]}),e.jsxs("div",{className:"custom-overflow w-full space-y-6 overflow-x-auto p-7",children:[e.jsx(se,{filter:f,setFilter:N,selectedFundManagers:h,data:j,onAddToGroup:()=>{C()}}),e.jsx(l,{children:e.jsx(X,{showSearch:!1,useDefaultColumns:!0,defaultColumns:te,noDataComponent:{use:!0,component:e.jsx(l,{})},onReady:a=>{o(a)},hasFilter:!1,tableRole:"member",table:"fund_manager",actionId:"id",tableTitle:e.jsx(e.Fragment,{}),defaultFilter:f==null?void 0:f.split("|"),actions:{view:{show:!1,action:null,multiple:!1},select:{show:!0,action:a=>{S(()=>[...a])},multiple:!0,max:b==null?void 0:b.maxSelection},export:{show:!1,action:null,multiple:!0},delete:{show:!1,action:null,multiple:!0}},defaultPageSize:10,showPagination:!0,updateRef:g,refreshRef:c,showScrollbar:!1,maxHeight:"md:grid-rows-[inherit] grid-rows-[inherit]"})})]})]})})};export{Re as default};
