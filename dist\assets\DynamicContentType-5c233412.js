import{j as a}from"./@nextui-org/listbox-0f38ca19.js";import{R as h}from"./vendor-4cdf2bd1.js";import{M as v,i as f}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const b=new v,x="https://via.placeholder.com/150?text=%20",z=({contentType:c,contentValue:o,setContentValue:d})=>{const[u,i]=h.useState(x),y=async m=>{const p=new FormData;p.append("file",m.target.files[0]);try{const e=await b.uploadImage(p);i(e.url),d(e.url)}catch(e){console.error(e)}};switch(c){case"text":return a.jsx(a.Fragment,{children:a.jsx("textarea",{className:"shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",rows:15,placeholder:"Content",onChange:m=>d(m.target.value),defaultValue:o})});case"image":return a.jsxs(a.Fragment,{children:[a.jsx("img",{src:f(o)?u:o,alt:"preview",height:150,width:150}),a.jsx("input",{type:"file",onChange:y,className:"shadow appearance-none border block  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"})]});case"number":return a.jsx("input",{type:"number",className:"shadow appearance-none border block  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",onChange:m=>d(m.target.value),defaultValue:o});case"team-list":return a.jsx(N,{setContentValue:d,contentValue:o});case"image-list":return a.jsx(k,{setContentValue:d,contentValue:o});case"captioned-image-list":return a.jsx(j,{setContentValue:d,contentValue:o});case"kvp":return a.jsx(I,{setContentValue:d,contentValue:o})}},k=({contentValue:c,setContentValue:o})=>{let d=[{key:"",value_type:"image",value:null}];f(c)||(d=JSON.parse(c));const[u,i]=h.useState(d),y=async p=>{const e=p.target.getAttribute("listkey"),t=new FormData;t.append("file",p.target.files[0]);try{const s=await b.uploadImage(t);i(l=>l.map((n,g)=>(g==e&&(n.value=s.url),n))),o(JSON.stringify(u))}catch(s){console.error(s)}},m=p=>{const e=p.target.getAttribute("listkey");i(t=>t.map((l,r)=>(r==e&&(l.key=p.target.value),l))),o(JSON.stringify(u))};return a.jsxs("div",{className:"block",children:[u.map((p,e)=>a.jsxs("div",{children:[a.jsx("img",{src:p.value!==null?p.value:x,alt:"preview",height:150,width:150}),a.jsxs("div",{className:"flex",children:[a.jsx("input",{className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"key",listkey:e,onChange:m,defaultValue:p.key}),a.jsx("input",{listkey:e,type:"file",accept:"image/*",onChange:y,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"})]})]},e*.23)),a.jsx("button",{type:"button",className:"bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline",onClick:p=>i(e=>[...e,{key:"",value_type:"image",value:null}]),children:"+"})]})},j=({setContentValue:c,contentValue:o})=>{let d=[{key:"",value_type:"image",value:null,caption:""}];f(o)||(d=JSON.parse(o));const[u,i]=h.useState(d),y=async e=>{const t=e.target.getAttribute("listkey"),s=new FormData;s.append("file",e.target.files[0]);try{const l=await b.uploadImage(s);i(r=>r.map((g,w)=>(w==t&&(g.value=l.url),g))),c(JSON.stringify(u))}catch(l){console.error(l)}},m=e=>{const t=e.target.getAttribute("listkey");i(s=>s.map((r,n)=>(n==t&&(r.key=e.target.value),r))),c(JSON.stringify(u))},p=e=>{const t=e.target.getAttribute("listkey");i(s=>s.map((r,n)=>(n==t&&(r.caption=e.target.value),r))),c(JSON.stringify(u))};return a.jsxs("div",{className:"block",children:[u.map((e,t)=>a.jsxs("div",{children:[a.jsx("img",{src:e.value!==null?e.value:x,alt:"preview",height:150,width:150}),a.jsxs("div",{className:"flex",children:[a.jsx("input",{className:"shadow appearance-none border rounded w-full py-2 px-3 mr-2 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Key",listkey:t,onChange:m,defaultValue:e.key}),a.jsx("input",{listkey:t,type:"file",accept:"image/*",onChange:y,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"})]}),a.jsx("input",{className:"shadow block appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Caption",listkey:t,onChange:p,defaultValue:e.caption})]},t*.23)),a.jsx("button",{type:"button",className:"bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline",onClick:e=>i(t=>[...t,{key:"",value_type:"image",value:null,caption:""}]),children:"+"})]})},N=({setContentValue:c,contentValue:o})=>{let d=[{name:"",image:null,title:""}];f(o)||(d=JSON.parse(o));const[u,i]=h.useState(d),y=async e=>{const t=e.target.getAttribute("listkey"),s=new FormData;s.append("file",e.target.files[0]);try{const l=await b.uploadImage(s);i(r=>r.map((g,w)=>(w==t&&(g.image=l.url),g))),c(JSON.stringify(u))}catch(l){console.error(l)}},m=e=>{const t=e.target.getAttribute("listkey");i(s=>s.map((r,n)=>(n==t&&(r.name=e.target.value),r))),c(JSON.stringify(u))},p=e=>{const t=e.target.getAttribute("listkey");i(s=>s.map((r,n)=>(n==t&&(r.title=e.target.value),r))),c(JSON.stringify(u))};return a.jsxs("div",{className:"block my-4",children:[u.map((e,t)=>a.jsxs("div",{className:"my-4",children:[a.jsx("input",{className:"shadow block appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Title",listkey:t,onChange:p,defaultValue:e.title}),a.jsx("input",{className:"shadow appearance-none border rounded w-full py-2 px-3 mr-2 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Name",listkey:t,onChange:m,defaultValue:e.name}),a.jsx("img",{src:e.image!==null?e.image:x,alt:"preview",height:150,width:150}),a.jsx("input",{listkey:t,type:"file",accept:"image/*",onChange:y,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"})]},t*.23)),a.jsx("button",{type:"button",className:"bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline",onClick:e=>i(t=>[...t,{name:"",image:null,title:""}]),children:"+"})]})},I=({setContentValue:c,contentValue:o})=>{let d=[{key:"",value_type:"text",value:""}];f(o)||(d=JSON.parse(o));const[u,i]=h.useState(d),y=[{key:"text",value:"Text"},{key:"number",value:"Number"},{key:"json",value:"JSON Object"},{key:"url",value:"URL"}],m=t=>{const s=t.target.getAttribute("listkey");i(l=>l.map((n,g)=>(g==s&&(n.key=t.target.value),n))),c(JSON.stringify(u))},p=t=>{const s=t.target.getAttribute("listkey");i(l=>l.map((n,g)=>(g==s&&(n.value=t.target.value),n))),c(JSON.stringify(u))},e=t=>{const s=t.target.getAttribute("listkey");i(l=>l.map((n,g)=>(g==s&&(n.value_type=t.target.value),n))),c(JSON.stringify(u))};return a.jsxs("div",{className:"block",children:[u.map((t,s)=>a.jsxs("div",{className:"my-4",children:[a.jsx("input",{className:"shadow appearance-none border rounded w-full py-2 px-3 mr-2 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Key",listkey:s,onChange:m,defaultValue:t.key}),a.jsx("select",{className:"shadow block border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",listkey:s,onChange:e,defaultValue:t.value_type,children:y.map((l,r)=>a.jsx("option",{value:l.key,children:l.value},r*122))}),a.jsx("input",{className:"shadow block appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",required:!0,placeholder:"Value",listkey:s,onChange:p,defaultValue:t.value})]},s*.23)),a.jsx("button",{type:"button",className:"bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline",onClick:t=>i(s=>[...s,{key:"",value_type:"text",value:""}]),children:"+"})]})};export{z as default};
