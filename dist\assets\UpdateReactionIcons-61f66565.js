import{j as a}from"./@nextui-org/listbox-0f38ca19.js";import"./vendor-4cdf2bd1.js";import{u as g,L as C,bY as j,bX as r,bZ as D,bW as S,b_ as E,b$ as R,c0 as A,c1 as w}from"./index-f2ad9142.js";import{M as y}from"./index-713720be.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const q=({onClick:s,className:k,reactions:m=[]})=>{var i,l,p,c,d,I,h;const{profile:t}=g(),o=(e=null)=>{s&&e&&s(e)},x=[{icon:j,name:(i=r)==null?void 0:i.SMILE,onClick:()=>{var e;o((e=r)==null?void 0:e.SMILE)}},{icon:D,name:(l=r)==null?void 0:l.SAD,onClick:()=>{var e;o((e=r)==null?void 0:e.SAD)}},{icon:S,name:(p=r)==null?void 0:p.GLAD,onClick:()=>{var e;o((e=r)==null?void 0:e.GLAD)}},{icon:E,name:(c=r)==null?void 0:c.LIKE,onClick:()=>{var e;o((e=r)==null?void 0:e.LIKE)}},{icon:R,name:(d=r)==null?void 0:d.DISLIKE,onClick:()=>{var e;o((e=r)==null?void 0:e.DISLIKE)}},{icon:A,name:(I=r)==null?void 0:I.INSIGHTFUL,onClick:()=>{var e;o((e=r)==null?void 0:e.INSIGHTFUL)}},{icon:w,name:(h=r)==null?void 0:h.ROCKET,onClick:()=>{var e;o((e=r)==null?void 0:e.ROCKET)}}];return a.jsx("div",{className:`relative flex !h-[3rem] max-h-[3rem] min-h-[3rem] items-center justify-between gap-[.5rem] rounded-[.125rem] border border-primary-black bg-brown-main-bg px-[1rem]  ${k}`,children:x.map((e,b)=>{const L=e==null?void 0:e.icon,f=m==null?void 0:m.find(n=>{var u;return((u=n==null?void 0:n.user)==null?void 0:u.id)===(t==null?void 0:t.id)&&(e==null?void 0:e.name)===(n==null?void 0:n.reaction)});return a.jsx(C,{children:a.jsx(y,{display:a.jsx("button",{type:"button",className:"flex !h-[1.5rem] max-h-[1.5rem] min-h-[1.5rem] !w-[1.5rem] min-w-[1.5rem] max-w-[1.5rem] cursor-pointer items-center gap-2",onClick:e==null?void 0:e.onClick,children:a.jsx(C,{children:a.jsx(L,{hoverColor:"#1F1D1A",highlightColor:"#1F1D1A",highlight:!!f})})}),openOnClick:!1,backgroundColor:"#1F1D1A",tooltipClasses:"!p-2",place:"bottom-end",children:a.jsx("span",{className:"font-inter capitalize text-white",children:e==null?void 0:e.name})},b)},b)})})};export{q as default};
