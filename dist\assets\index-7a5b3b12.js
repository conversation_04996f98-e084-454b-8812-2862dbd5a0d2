import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as a,h as A,b as R,L as F}from"./vendor-4cdf2bd1.js";import{A as C,G as P,M,t as D,s as L,U as k,S as T}from"./index-f2ad9142.js";import{I as U}from"./react-infinite-scroll-component-02637779.js";import{c as z,a as j}from"./yup-342a5df4.js";import{a as I,u as _}from"./react-hook-form-a383372b.js";import{o as B}from"./yup-0917e80c.js";import{G as H,L as N,t as G}from"./@headlessui/react-cdd9213e.js";import{S as E}from"./react-loading-skeleton-f57eafae.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-b50d6e2a.js";function O({title:t,titleId:r,...n},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},n),t?a.createElement("title",{id:r},t):null,a.createElement("path",{fillRule:"evenodd",d:"M12 2.25a.75.75 0 0 1 .75.75v16.19l6.22-6.22a.75.75 0 1 1 1.06 1.06l-7.5 7.5a.75.75 0 0 1-1.06 0l-7.5-7.5a.75.75 0 1 1 1.06-1.06l6.22 6.22V3a.75.75 0 0 1 .75-.75Z",clipRule:"evenodd"}))}const V=a.forwardRef(O),q=V;function Z({title:t,titleId:r,...n},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},n),t?a.createElement("title",{id:r},t):null,a.createElement("path",{fillRule:"evenodd",d:"M9.53 2.47a.75.75 0 0 1 0 1.06L4.81 8.25H15a6.75 6.75 0 0 1 0 13.5h-3a.75.75 0 0 1 0-1.5h3a5.25 5.25 0 1 0 0-10.5H4.81l4.72 4.72a.75.75 0 1 1-1.06 1.06l-6-6a.75.75 0 0 1 0-1.06l6-6a.75.75 0 0 1 1.06 0Z",clipRule:"evenodd"}))}const W=a.forwardRef(Z),K=W;function J({title:t,titleId:r,...n},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},n),t?a.createElement("title",{id:r},t):null,a.createElement("path",{d:"M21.731 2.269a2.625 2.625 0 0 0-3.712 0l-1.157 1.157 3.712 3.712 1.157-1.157a2.625 2.625 0 0 0 0-3.712ZM19.513 8.199l-3.712-3.712-12.15 12.15a5.25 5.25 0 0 0-1.32 2.214l-.8 2.685a.75.75 0 0 0 .933.933l2.685-.8a5.25 5.25 0 0 0 2.214-1.32L19.513 8.2Z"}))}const Q=a.forwardRef(J),X=Q;function Y({title:t,titleId:r,...n},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},n),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"}))}const ee=a.forwardRef(Y),te=ee;function ae({control:t,name:r,setValue:n,disabled:s}){const{field:i}=I({control:t,name:r}),c=i.value===1;return e.jsxs(e.Fragment,{children:[e.jsx("input",{disabled:s,type:"checkbox",checked:i.value===1,onChange:()=>{n(i.value===1?0:1)},onBlur:i.onBlur,name:i.name,ref:i.ref,id:r,className:"!hidden"}),e.jsx("div",{className:"relative",children:e.jsxs(H,{disabled:s,checked:c,onChange:()=>{n(i.value===1?0:1)},className:`${c?"bg-primary-black":"bg-gray-300"}
          relative inline-flex h-[20px] w-[36px] shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus-visible:ring-2  focus-visible:ring-white focus-visible:ring-opacity-75`,children:[e.jsx("span",{className:"sr-only",children:"Use setting"}),e.jsx("span",{"aria-hidden":"true",className:`${c?"translate-x-[16px]":"translate-x-0"}
            pointer-events-none inline-block h-[16px] w-[16px] transform rounded-full bg-brown-main-bg shadow-lg ring-0 transition duration-200 ease-in-out`})]})})]})}function se(t){const[r,n]=a.useState(!1),[s,i]=a.useState([]),{dispatch:c}=a.useContext(C),{dispatch:o}=a.useContext(P),[f]=A(),l=f.get("date"),p=f.get("status"),w=new M,[v,m]=a.useState({page:1,total:0,num_pages:0});async function b(x,h){n(!0);try{h=h??1,x=x??10;const d=await w.callRawAPI(`/v4/api/records/update_collaborators?join=updates|update_id&filter=collaborator_id,eq,${t}&filter=status,neq,-1&order=id,desc&page=${h},${x}${l?`&filter=date,eq,'${l}'`:""}${p?`&filter=status,eq,${p}`:""}`);i(d.list.map(g=>({...g,...g.updates}))),m({total:d.total,page:d.page,num_pages:d.num_pages})}catch(d){D(c,d.message),L(o,d.message,5e3,"error")}n(!1)}return a.useEffect(()=>{b()},[l,p]),{loading:r,updates:s,refetch:b,paginationData:v}}function re(){const[t,r]=A(),n=z({date:j(),availability:j(),status:j(),recipients:j()}),{register:s,handleSubmit:i,formState:{isSubmitting:c,errors:o,defaultValues:f},reset:l,setValue:p,control:w}=_({resolver:B(n),defaultValues:async()=>({date:t.get("date")??"",availability:t.get("availability")??"",status:t.get("status")??"",recipients:""})});async function v(m){t.set("date",m.date),t.set("availability",m.availability),t.set("status",m.status),t.set("recipients",m.recipients),r(t)}return e.jsx(N,{className:"relative",children:({open:m})=>{var b,x,h,d,g,S,$;return e.jsxs(e.Fragment,{children:[e.jsxs(N.Button,{className:`
              group inline-flex items-center rounded-md border-2 px-3 py-2 text-base font-medium focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75`,children:[e.jsx("span",{children:"Filters"}),e.jsx(te,{className:"h-5"})]}),e.jsx(G,{as:a.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 translate-y-1",children:e.jsx(N.Panel,{className:"absolute right-[-30px] z-10 mt-3 w-screen max-w-[250px] transform rounded-md border-2 border-[#1f1d1a] bg-brown-main-bg p-4 sm:right-0 sm:max-w-md",children:e.jsxs("form",{onSubmit:i(v),children:[e.jsxs("div",{className:"grid max-w-lg grid-cols-2 gap-6",children:[e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block cursor-pointer text-left text-sm font-bold capitalize capitalize text-[#1f1d1a]",children:"Date"}),e.jsx("input",{type:"date",className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 pr-5 text-sm font-normal leading-tight   text-[#1d1f1a] shadow focus:outline-none sm:pr-3 ${(b=o.date)!=null&&b.message?"border-red-500":""}`,...s("date")}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(x=o.date)==null?void 0:x.message})]}),e.jsxs("div",{className:"mt-6 flex items-center justify-end gap-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Availability"}),e.jsx(ae,{control:w,name:"availability",setValue:u=>p("availability",u)}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(h=o.availability)==null?void 0:h.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Status"}),e.jsxs("select",{className:`focus:shadow-outline w-full max-w-[200px] appearance-none rounded border border border-[#1f1d1a] bg-transparent py-2 pl-6 pr-8 font-Inter text-sm font-normal leading-tight text-[#1d1f1a] shadow shadow-none focus:outline-none ${(d=o.status)!=null&&d.message?"border-red-500":""}`,...s("status"),children:[e.jsx("option",{value:"",children:"-select-"}),Object.entries(k).map(([u,y])=>e.jsx("option",{value:u,children:y},u))]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(g=o.status)==null?void 0:g.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Recipients"}),e.jsxs("select",{className:`focus:shadow-outline w-full max-w-[200px] appearance-none rounded border border border-[#1f1d1a] bg-transparent py-2 pl-6 pr-8 leading-tight text-[#1d1f1a] shadow focus:outline-none ${(S=o.status)!=null&&S.message?"border-red-500":""}`,...s("status"),children:[e.jsx("option",{value:"",children:"-select-"}),[].map(([u,y])=>e.jsx("option",{value:`${u}`,children:y},u))]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:($=o.status)==null?void 0:$.message})]})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-6",children:[e.jsx("button",{type:"submit",className:"disabled:bg-disabledblack rounded-lg bg-primary-black/80 py-2 text-center font-semibold text-white transition-colors duration-100",children:"Apply Filters"}),e.jsxs("button",{type:"button",onClick:()=>{l({date:"",availability:"",status:"",recipients:""}),t.delete("date"),t.delete("availability"),t.delete("status"),t.delete("recipients"),r(t)},className:"flex items-center justify-center gap-3 rounded-lg border py-2 text-center",children:[e.jsx(K,{className:"h-4",strokeWidth:2}),"Reset"]})]})]})})})]})}})}const ne={0:e.jsxs("span",{className:"rounded-3xl bg-blue-900 px-2 py-1 text-xs font-semibold text-white",children:["Status: ",k[0]]}),1:e.jsxs("span",{className:"rounded-3xl bg-green-900 px-2 py-1 text-xs font-semibold text-white",children:["Status: ",k[1]]})},Ce=()=>{const{state:t,dispatch:r}=a.useContext(C),{dispatch:n}=a.useContext(P),{updates:s,refetch:i,loading:c,paginationData:o}=se(t.user),f=R();return a.useEffect(()=>{n({type:"SETPATH",payload:{path:"updates"}})},[]),console.log(s),e.jsx("div",{className:"mx-auto rounded p-5",children:e.jsxs("div",{className:"overflow-x-auto rounded bg-brown-main-bg p-5",children:[e.jsx("h2",{className:"font-normal",children:t.company.name}),e.jsxs("div",{className:"mb-3 mt-4 flex w-full justify-between text-center ",children:[e.jsx("h4",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"Updates"}),e.jsx(re,{})]}),e.jsx("div",{className:" mt-6 border-b border-[#0003]",children:e.jsxs("div",{className:"min-w-full",children:[e.jsxs(U,{className:`grid ${s.length<11?"!h-fit":""} grid-cols-1 gap-5 bg-brown-main-bg pb-12 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-4`,dataLength:s.length,next:()=>i(s.length+5,1),hasMore:o.num_pages>o.page,loader:e.jsx(T,{}),endMessage:e.jsx("p",{className:"flex items-center justify-center",children:s.length<10?"":e.jsx("b",{children:"End"})}),height:"700px",scrollableTarget:"body",children:[s.map((l,p)=>e.jsxs("div",{className:"group relative flex h-full min-h-[9.475rem] items-center justify-center truncate rounded-[.625rem] border border-[#0003] bg-brown-main-bg p-5 shadow-md",children:[e.jsx("div",{className:"absolute right-0 top-[-100%] m-auto flex gap-2 whitespace-nowrap bg-brown-main-bg px-6 py-4 transition-all group-hover:top-0",children:e.jsxs("button",{className:"text-xs",onClick:()=>{f("/collaborator/edit-updates/"+l.id,{state:l})},children:[" ",e.jsx(X,{title:"Edit updates details",className:"h-4 w-4 cursor-pointer ",pathClasses:" text-[#1f1d1a]",stroke:"#29282990"})]})}),e.jsxs("div",{className:"flex flex-col items-center justify-center gap-2",children:[e.jsx("div",{className:"whitespace-nowrap px-6 py-4 text-sm",children:l.name}),ne[l.status],l.status==1?e.jsx(F,{to:`/collaborator/view-updates/${l.id}`,className:"text-sm font-medium text-primary-black",children:"View insights"}):null]})]},l.id)),c&&e.jsx(E,{height:150}),c&&e.jsx(E,{height:150})]}),e.jsx("div",{className:"my-4 flex justify-center",onClick:()=>i(s.length+5,1),children:e.jsx("button",{className:"animate-pulse rounded-[50%] border-2 p-2 text-primary-black",children:e.jsx(q,{className:"h-7",strokeWidth:2})})})]})})]})})};export{Ce as default};
