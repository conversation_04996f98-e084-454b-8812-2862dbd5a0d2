import{A as D,G as I,M,t as G,s as L}from"./index-f2ad9142.js";import{r as s,h as R}from"./vendor-4cdf2bd1.js";function O(p,u){const[x,l]=s.useState(!1),[y,C]=s.useState([]),[m,w]=s.useState(0),[t,g]=R(),{dispatch:b,state:T}=s.useContext(D),{dispatch:E}=s.useContext(I),o=parseInt(t.get("limit")||"30"),e=parseInt(t.get("page")||"1"),d=t.get("company_name")||"",P=t.get("status")||"";async function h(){var r,f;l(!0);try{const c=new M,U=z=>Object.entries(z).reduce((S,[A,i])=>(i!=null&&i!==""&&(S[A]=i),S),{}),k=new URLSearchParams(U({page:e,limit:o,request:p,update_id:u,company_name:d,status:P})),a=await c.callRawAPI(`/v3/api/goodbadugly/customer/fund-update-requests?${k.toString()}`);C(a==null?void 0:a.list),console.log(a==null?void 0:a.total),w((f=(r=a==null?void 0:a.total)==null?void 0:r[0])==null?void 0:f.total)}catch(c){G(b,c.message),L(E,c.message,5e3,"error")}l(!1)}s.useEffect(()=>{h()},[o,e,d,P,p,u]);const n=Math.ceil(m/o);return console.log(m,o),console.log(n),{loading:x,updates:y,refetch:h,currentPage:e,pageCount:n,pageSize:o,updatePageSize:r=>{t.set("limit",r.toString()),t.set("page","1"),g(t)},previousPage:()=>{e>1&&(t.set("page",(e-1).toString()),g(t))},nextPage:()=>{e<n&&(t.set("page",(e+1).toString()),g(t))},canPreviousPage:e>1,canNextPage:e<n}}export{O as u};
