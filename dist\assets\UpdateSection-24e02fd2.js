import{j as _}from"./@nextui-org/listbox-0f38ca19.js";import{r as e}from"./vendor-4cdf2bd1.js";import{L as p}from"./index-f2ad9142.js";import{_ as o}from"./qr-scanner-cf010ec4.js";const l=e.lazy(()=>o(()=>import("./UpdateSectionNote-afbc3af3.js"),["assets/UpdateSectionNote-afbc3af3.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-45396f34.js"])),U=e.lazy(()=>o(()=>import("./UpdateSectionNoteTitle-5123a13a.js"),["assets/UpdateSectionNoteTitle-5123a13a.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js"])),x=e.lazy(()=>o(()=>import("./UpdateSectionNoteContent-b36859b7.js"),["assets/UpdateSectionNoteContent-b36859b7.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"])),f=e.lazy(()=>o(()=>import("./UpdateSectionNoteActivities-0a3b053e.js"),["assets/UpdateSectionNoteActivities-0a3b053e.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-d20ea84b.js","assets/qr-scanner-cf010ec4.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-31067895.js","assets/useReactions-581cd573.js","assets/useNote-ea33f376.js","assets/useUpdateCollaborator-1187c43b.js","assets/index-afef2e72.js"])),E=e.lazy(()=>o(()=>import("./UpdateSectionNoteComments-4922ed9c.js"),["assets/UpdateSectionNoteComments-4922ed9c.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/useComments-7e9daaa3.js","assets/AddButton-51d1b2cd.js","assets/index-afef2e72.js","assets/MkdInput-d37679e9.js","assets/react-toggle-6478c5c4.js","assets/@uppy/dashboard-51133bb7.js","assets/@fullcalendar/core-085b11ae.js","assets/core-b9802b0d.css","assets/@uppy/core-a4ba4b97.js","assets/@uppy/aws-s3-a6b02742.js","assets/@craftjs/core-a2cdaeb4.js","assets/@uppy/compressor-4bcbc734.js","assets/MkdInput-5e6afe8d.css","assets/useUpdateCollaborator-1187c43b.js","assets/react-popper-9a65a9b6.js","assets/@popperjs/core-f3391c26.js","assets/useMentions-2c8c5eca.js"])),z=e.lazy(()=>o(()=>import("./UpdateSectionNoteComment-626a6891.js").then(s=>s.b),["assets/UpdateSectionNoteComment-626a6891.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/moment-a9aaa855.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/useComments-7e9daaa3.js","assets/index-45396f34.js","assets/index-afef2e72.js","assets/lucide-react-0b94883e.js"])),A=e.lazy(()=>o(()=>import("./UpdateSectionNoteCommentActivities-d4e258da.js"),["assets/UpdateSectionNoteCommentActivities-d4e258da.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-d20ea84b.js","assets/qr-scanner-cf010ec4.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-31067895.js","assets/useReactions-581cd573.js"])),C=e.lazy(()=>o(()=>import("./UpdateSectionNoteCommentContent-4552603d.js"),["assets/UpdateSectionNoteCommentContent-4552603d.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/useUpdateCollaborator-1187c43b.js","assets/react-popper-9a65a9b6.js","assets/@popperjs/core-f3391c26.js"])),N=e.lazy(()=>o(()=>import("./UpdateSectionNoteCommentUser-a608c846.js"),["assets/UpdateSectionNoteCommentUser-a608c846.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-713720be.js"])),u=({update:s=null,note:t=null,onSuccess:a})=>{const[i,r]=e.useState({hideComment:!1}),m=(d,c)=>{r(n=>({...n,[d]:c}))};return _.jsx(p,{children:_.jsxs("div",{id:t==null?void 0:t.type,className:"flex flex-col gap-5","data-section-type":t==null?void 0:t.type,"data-note-id":t==null?void 0:t.id,children:[_.jsx(l,{note:t,update:s,onSuccess:a,toggleComments:m,commentVisibility:i==null?void 0:i.hideComment}),_.jsx("div",{className:`relative  mt-[5px]  ${i!=null&&i.hideComment?"hidden":""}`,children:_.jsx(E,{update:s,note:t})})]})})},O=Object.freeze(Object.defineProperty({__proto__:null,default:u},Symbol.toStringTag,{value:"Module"}));export{U,x as a,f as b,z as c,N as d,C as e,A as f,O as g};
