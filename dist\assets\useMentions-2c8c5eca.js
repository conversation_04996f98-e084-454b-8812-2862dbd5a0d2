import{r as n}from"./vendor-4cdf2bd1.js";import{A as E,G as M,M as g,s as y,t as k}from"./index-f2ad9142.js";const v=()=>{const[w,o]=n.useState({list:!1,count:!1,markSeen:!1,markAllSeen:!1}),[p,m]=n.useState([]),[A,d]=n.useState(0),{dispatch:r}=n.useContext(E),{dispatch:i}=n.useContext(M),C=n.useCallback(async()=>{var a,t;o(s=>({...s,list:!0}));try{const e=await new g().callRawAPI("/v3/api/custom/goodbadugly/mentions",void 0,"GET");!e.error&&e.data&&m(e.data)}catch(s){console.error("Error fetching mentions:",s);const e=((t=(a=s==null?void 0:s.response)==null?void 0:a.data)==null?void 0:t.message)??s.message;y(i,e,5e3,"error"),k(r,e)}finally{o(s=>({...s,list:!1}))}},[r,i]),S=n.useCallback(async()=>{var a,t;o(s=>({...s,count:!0}));try{const e=await new g().callRawAPI("/v3/api/custom/goodbadugly/mentions/count",void 0,"GET");e.error||d(e.count||0)}catch(s){console.error("Error fetching unread mentions count:",s);const e=((t=(a=s==null?void 0:s.response)==null?void 0:a.data)==null?void 0:t.message)??s.message;k(r,e)}finally{o(s=>({...s,count:!1}))}},[r]),b=n.useCallback(async a=>{var t,s;o(e=>({...e,markSeen:!0}));try{return(await new g().callRawAPI(`/v3/api/custom/goodbadugly/mentions/${a}/mark-seen`,{},"PUT")).error?!1:(m(f=>f.map(u=>u.id===a?{...u,seen:1}:u)),d(f=>Math.max(0,f-1)),!0)}catch(e){console.error("Error marking mention as seen:",e);const l=((s=(t=e==null?void 0:e.response)==null?void 0:t.data)==null?void 0:s.message)??e.message;return y(i,l,5e3,"error"),k(r,l),!1}finally{o(e=>({...e,markSeen:!1}))}},[r,i]),x=n.useCallback(async(a=null)=>{var t,s;o(e=>({...e,markAllSeen:!0}));try{const e=new g,l=a?{update_id:a}:{};if(!(await e.callRawAPI("/v3/api/custom/goodbadugly/mentions/mark-all-seen",l,"PUT")).error){if(a){m(c=>c.map(h=>h.update_id===a?{...h,seen:1}:h));const u=p.filter(c=>c.update_id===a&&c.seen===0).length;d(c=>Math.max(0,c-u))}else m(u=>u.map(c=>({...c,seen:1}))),d(0);return!0}return!1}catch(e){console.error("Error marking all mentions as seen:",e);const l=((s=(t=e==null?void 0:e.response)==null?void 0:t.data)==null?void 0:s.message)??e.message;return y(i,l,5e3,"error"),k(r,l),!1}finally{o(e=>({...e,markAllSeen:!1}))}},[r,i,p]);return{loading:w,mentions:p,unreadCount:A,fetchMentions:C,fetchUnreadCount:S,markMentionAsSeen:b,markAllMentionsAsSeen:x}};export{v as u};
