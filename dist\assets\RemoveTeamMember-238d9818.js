import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{A as l}from"./index-8c774937.js";import{O as f,L as u}from"./index-f2ad9142.js";import{u as d}from"./useCompanyMember-0033d2de.js";import{r as s}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const N=({id:t,onClose:a,onSuccess:n})=>{var o,i;const{companyMember:{single:r},getCompanyMember:p,loading:m}=d({filter:[`member_id,eq,${t}`]});return s.useEffect(()=>{p(t)},[t]),e.jsx(s.Fragment,{children:e.jsxs("div",{className:"relative h-fit max-h-fit min-h-[6.25rem] w-[25rem] min-w-[25rem] ",children:[m!=null&&m.single?e.jsx(f,{loading:!0}):null,r?e.jsx(u,{children:e.jsx(l,{customMessage:e.jsxs(e.Fragment,{children:["Are you sure you want to remove"," ",e.jsxs("b",{children:[(o=r==null?void 0:r.user)==null?void 0:o.first_name," ",(i=r==null?void 0:r.user)==null?void 0:i.last_name]})," ","from the team?"]}),onClose:a,onSuccess:n,action:"Remove",mode:"delete",data:{id:r==null?void 0:r.id},table:"company_member",inputConfirmation:!1})}):null]})})};export{N as default};
