import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{b as g,u as b,L as f,E as w,d as j,C as y}from"./index-f2ad9142.js";import{r as c}from"./vendor-4cdf2bd1.js";import{M as S}from"./index-d07d87ac.js";import{C}from"./ClipboardDocumentIcon-f03b0627.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const D=[{header:"Status",accessor:"status",selected_column:!0,isSorted:!0,isSortedDesc:!1,mappingExist:!0,mappings:{paid:{text:"Paid",bg:"#9DD321",color:"black"},unpaid:{text:"Unpaid",bg:"#F6A13C",color:"black"},void:{text:"Void",bg:"#F6A13C",color:"black"}}},{header:"Currency",accessor:"currency",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"uppercase"},{header:"Amount due",accessor:"amount_due",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"currency"},{header:"Amount paid",accessor:"amount_paid",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"currency"},{header:"Amount remaining",accessor:"amount_remaining",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"currency"},{header:"Created at",accessor:"created",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"timestamp"},{header:"Action",accessor:""}],E={usd:"$",eur:"€",gbp:"£",cad:"$",aud:"$",inr:"₹"},N=s=>[0].includes(s)?"0.00":Number(s/100).toFixed(2),_=(s,r,i)=>(r==null?void 0:r.type)==="timestamp"?e.jsxs("span",{className:"flex items-center gap-2",children:[e.jsx(y,{}),new Date(s*1e3).toLocaleString("en-US")]}):(r==null?void 0:r.type)==="currency"?`${E[i==null?void 0:i.currency]||(i==null?void 0:i.currency)}${N(s)}`:(r==null?void 0:r.type)==="uppercase"?s==null?void 0:s.toUpperCase():s,B=()=>{var u;const s=c.useRef(null),r=c.useRef(null),{operations:i}=g(),[n,h]=c.useState({filter:[],refresh:!1}),{profile:t}=b(),x=(l,p)=>l==null?void 0:l.map(a=>{const d=a!=null&&a.object&&JSON.parse(a==null?void 0:a.object)?JSON.parse(a==null?void 0:a.object):null,m={};return p==null||p.forEach(o=>{m[o==null?void 0:o.accessor]=_(d==null?void 0:d[o==null?void 0:o.accessor],o,d)}),{...a,...m}});return c.useEffect(()=>{t!=null&&t.id&&h(l=>({...l,filter:[`user_id,${i.EQUAL},${t==null?void 0:t.id}`]}))},[t==null?void 0:t.id]),e.jsx(c.Fragment,{children:e.jsxs("div",{className:" grid h-full max-h-full min-h-full w-full grid-rows-[auto_auto_1fr] rounded bg-brown-main-bg px-5 md:px-8 ",children:[e.jsxs("div",{className:" flex pt-3 w-full items-center justify-between gap-5 ",children:[e.jsx("h4",{className:"font-iowan text-[26px] font-bold leading-[2.486rem]",children:"Invoices"}),e.jsx("div",{className:"flex w-fit items-center justify-start gap-5",children:e.jsx(f,{})})]}),e.jsx("div",{className:"h-[.125rem] mt-4 w-full border-[.125rem] border-black bg-black "}),t!=null&&t.id&&((u=n==null?void 0:n.filter)==null?void 0:u.length)>0?e.jsx(S,{showSearch:!1,useDefaultColumns:!0,defaultColumns:[...D],noDataComponent:{use:!0,component:e.jsx(f,{children:e.jsxs("div",{className:"mb-[20px] mt-24 flex flex-col items-center",children:[e.jsx(C,{className:"h-8 w-8 text-gray-700",strokeWidth:2}),e.jsx("p",{className:"mt-4 text-center text-base font-medium",children:"No Invoices"})]})})},onReady:()=>{},processes:[x],hasFilter:!1,tableRole:"admin",actionId:"id",table:"stripe_invoice",join:["user"],defaultFilter:n==null?void 0:n.filter,actions:{view:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},update:{show:!1,action:null,multiple:!0,children:"Edit",showChildren:!0,icon:e.jsx(w,{}),locations:["dropdown"]},remove:{show:!1,action:null,multiple:!0,children:"Delete",icon:e.jsx(j,{fill:"#292D32"}),locations:["dropdown"]},view_all:{show:!1,type:"static",action:()=>{},children:e.jsx(e.Fragment,{children:"View All"}),className:"!gap-2 !bg-transparent !text-black !underline !shadow-none !border-0"},add:{show:!1,multiple:!0,children:"+ Add"},export:{show:!1,action:null,multiple:!0}},defaultPageSize:20,showPagination:!0,maxHeight:"md:grid-rows-[inherit] grid-rows-[inherit]",actionPostion:["dropdown"],refreshRef:s,updateRef:r}):null]})})};export{B as default};
