/**
 * Bulk Stripe Products Creation Script
 * 
 * This script creates multiple Stripe products at once using the existing API endpoints.
 * Run this in the browser console while logged into the admin panel.
 * 
 * Usage:
 * 1. Copy this entire script
 * 2. Paste it in the browser console
 * 3. Call: await bulkCreateStripeProducts()
 * 4. The script will return an array of created products with their IDs
 */

const bulkCreateStripeProducts = async () => {
  // Define the products to create
  const products = [
    {
      name: "free",
      description: "30 days free plan for all new users"
    },
    {
      name: "pro", 
      description: "Professional plan with advanced features"
    },
    {
      name: "business",
      description: "Business plan for growing teams"
    },
    {
      name: "enterprise",
      description: "Enterprise plan for large organizations"
    }
  ];

  console.log('🚀 Starting bulk product creation...');
  console.log(`📦 Creating ${products.length} products`);
  
  const createdProducts = [];
  const errors = [];
  
  // Get auth token and project encoding
  const token = localStorage.getItem('token') || localStorage.getItem('admin_token');
  const projectId = 'goodbadugly';
  const secret = 'i3k2c2k8kjp9ook0m14mhrtcwpgdd8g';
  const base64Encode = btoa(projectId + ':' + secret);
  
  if (!token) {
    console.error('❌ No authentication token found. Please log in first.');
    return { success: false, error: 'No authentication token found' };
  }
  
  for (let i = 0; i < products.length; i++) {
    const product = products[i];
    
    try {
      console.log(`\n📝 Creating product ${i + 1}/${products.length}: "${product.name}"`);
      
      const response = await fetch('https://api.updatestack.com/v2/api/lambda/stripe/product', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-project': base64Encode,
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(product)
      });
      
      const result = await response.json();
      
      if (result.error) {
        console.error(`❌ Error creating "${product.name}":`, result.message);
        errors.push({
          product: product.name,
          error: result.message,
          validation: result.validation
        });
      } else {
        console.log(`✅ Successfully created "${product.name}" with ID: ${result.model}`);
        createdProducts.push({
          name: product.name,
          id: result.model,
          description: product.description,
          stripe_id: result.stripe_id || null
        });
      }
      
      // Add a small delay to avoid rate limiting
      if (i < products.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
    } catch (error) {
      console.error(`❌ Network error creating "${product.name}":`, error.message);
      errors.push({
        product: product.name,
        error: error.message
      });
    }
  }
  
  // Summary
  console.log('\n📊 BULK CREATION SUMMARY');
  console.log('========================');
  console.log(`✅ Successfully created: ${createdProducts.length} products`);
  console.log(`❌ Failed: ${errors.length} products`);
  
  if (createdProducts.length > 0) {
    console.log('\n🎉 Created Products:');
    createdProducts.forEach(product => {
      console.log(`  • ${product.name} (ID: ${product.id})`);
    });
  }
  
  if (errors.length > 0) {
    console.log('\n⚠️  Errors:');
    errors.forEach(error => {
      console.log(`  • ${error.product}: ${error.error}`);
    });
  }
  
  console.log('\n💡 Next Steps:');
  console.log('1. Copy the product IDs above');
  console.log('2. Use them in the bulk price creation script');
  console.log('3. Update the productMapping object with the actual IDs');
  
  return {
    success: errors.length === 0,
    created: createdProducts,
    errors: errors,
    summary: {
      total: products.length,
      successful: createdProducts.length,
      failed: errors.length
    }
  };
};

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { bulkCreateStripeProducts };
}

console.log('📋 Bulk Stripe Products Creation Script Loaded');
console.log('💻 Run: await bulkCreateStripeProducts()');
