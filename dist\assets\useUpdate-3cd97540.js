import{A as m,G as x,u as y,p as k,M as w,t as C,s as E}from"./index-f2ad9142.js";import{r as s}from"./vendor-4cdf2bd1.js";function S(o){const{dispatch:r}=s.useContext(m),{dispatch:i}=s.useContext(x),[d,n]=s.useState({list:!1,single:!1,update:!1,create:!1,delete:!1}),[u,f]=s.useState({}),[p,g]=s.useState({list:[],single:null}),{profile:a}=y({isPublic:!1});async function l(){try{n(c=>({...c,single:!0}));const t=await new w().callRawAPI(`/v4/api/records/updates/${o}?join=companies|company_id`,void 0,"GET");console.log(t.model),f(t.model)}catch(e){C(r,e.message),E(i,e.message,5e3,"error")}n(e=>({...e,single:!1}))}const h=s.useCallback((e={filter:[],join:[]})=>(async()=>{n(t=>({...t,list:!0}));try{const t=await k(i,r,"updates",{...e});return t!=null&&t.errors?[]:(g(c=>({...c,list:t==null?void 0:t.data})),t==null?void 0:t.data)}catch(t){return console.log(t),[]}finally{n(t=>({...t,list:!1}))}})(),[]);return s.useEffect(()=>{o&&(a!=null&&a.id)&&l()},[o,a==null?void 0:a.id]),{loading:d,update:u,updates:p,refetch:l,getUpdates:h}}export{S as u};
