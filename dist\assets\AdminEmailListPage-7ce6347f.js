import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as t,b as v}from"./vendor-4cdf2bd1.js";import{M as N,A as M,G as T,L as m,t as k}from"./index-f2ad9142.js";import{S as D}from"./react-loading-skeleton-f57eafae.js";import{M as p}from"./index-d526f96e.js";import{c as C,a as n}from"./yup-342a5df4.js";import{u as I}from"./react-hook-form-a383372b.js";import{o as L}from"./yup-0917e80c.js";import R from"./AddAdminEmailPage-5fff9e79.js";import F from"./EditAdminEmailPage-0345c03a.js";import{A as P}from"./AddButton-51d1b2cd.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-b50d6e2a.js";let u=new N;const h=[{header:"ID",accessor:"id"},{header:"Email Type",accessor:"slug"},{header:"Subject",accessor:"subject"},{header:"Tags",accessor:"tag"},{header:"Action",accessor:""}],ne=()=>{const{dispatch:x}=t.useContext(M),[i,f]=t.useState([]),[j,l]=t.useState(!1),[w,o]=t.useState(!1),[b,d]=t.useState(!1),[g,S]=t.useState();t.useState(!1),t.useState(!1),t.useState([]),t.useState([]),t.useState(""),t.useState("eq"),v();const{dispatch:E}=t.useContext(T),y=C({page:n(),key:n(),type:n()});I({resolver:L(y)});async function A(){try{u.setTable("email");const s=await u.callRestAPI({},"GETALL"),{list:a}=s;f(a)}catch(s){console.log("ERROR",s),k(x,s.message)}}return t.useEffect(()=>{E({type:"SETPATH",payload:{path:"email"}}),async function(){l(!0),await A(),l(!1)}()},[]),e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"overflow-x-auto rounded bg-brown-main-bg p-5",children:[e.jsxs("div",{className:"mb-6 flex w-full justify-between text-center",children:[e.jsx("div",{}),e.jsx(P,{onClick:()=>o(!0)})]}),e.jsx("div",{className:"overflow-x-auto shadow",children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10",children:[e.jsx("thead",{children:e.jsx("tr",{children:h.map((s,a)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[s.header,e.jsx("span",{children:s.isSorted?s.isSortedDesc?" ▼":" ▲":""})]},a))})}),e.jsxs("tbody",{className:"font-iowan-regular divide-y divide-[#1f1d1a]/10",children:[i.length==0?e.jsx("tr",{children:e.jsx("td",{colSpan:5,children:j&&e.jsx(D,{count:4})})}):null,console.log(i),i.map((s,a)=>e.jsx("tr",{className:"  md:h-[60px]",children:h.map((r,c)=>r.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("button",{className:"cursor-pointer text-xs text-[#4F46E5]",onClick:()=>{S(s.id),d(!0)},children:[" ","Edit"]})},c):r.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:r.mapping[s[r.accessor]]},c):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s[r.accessor]},c))},a))]})]})})]}),e.jsx(m,{children:e.jsx(p,{isModalActive:w,showHeader:!0,title:"Add Email",closeModalFn:()=>o(!1),customMinWidthInTw:"md:w-[25%] w-full !bg-brown-main-bg",children:e.jsx(R,{setSidebar:o})})}),e.jsx(m,{children:e.jsx(p,{showHeader:!0,title:"Edit Email",isModalActive:b,closeModalFn:()=>d(!1),customMinWidthInTw:"md:w-[25%] w-full !bg-brown-main-bg",children:e.jsx(F,{activeId:g,setSidebar:d})})})]})};export{ne as default};
