import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{b as A,r as n,u as R}from"./vendor-4cdf2bd1.js";import{A as L,G as Y,u as F,T as D,M as U,t as V,s as H,a1 as l,aX as $,aY as B,aZ as K,a_ as q,a$ as G,b0 as X,b1 as z,b2 as W,b3 as Z}from"./index-f2ad9142.js";import{h as S}from"./moment-a9aaa855.js";import{P as J}from"./index-590fd997.js";import{b as Q}from"./index.esm-6fcccbfe.js";import{B as ee}from"./react-spinners-b860a5a3.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-icons-36ae72b7.js";const te=["Investor","Marketing","Product","Finance","Team/HR","Operations"],a={Investor:"Investor",Marketing:"Marketing",Product:"Product",Finance:"Finance",TeamHR:"Team/HR",Operations:"Operations"},g=[{id:1,title:"Standard",img:"standard",url:"",template:l,type:a.Investor,preview:"/assets/standard-preview2.png"},{id:2,img:"techstars",title:"Techstars",url:"(https://www.techstars.com)",preview:"/assets/techstar-preview.png",template:$,type:a.Investor},{id:3,title:"Kima Ventures",url:"(https://www.kimaventures.com)",img:"kima",template:B,type:a.Investor,preview:"/assets/kima-ventures-preview.png"},{id:4,title:"Y Combinator",img:"yc-combinator",url:"(https://www.ycombinator.com)",template:K,type:a.Investor,preview:"/assets/y-combinator-preview.png"},{id:5,title:"Underscore VC",img:"underscore",url:"(https://underscore.vc)",template:q,type:a.Investor,preview:"/assets/underscore-preview.png"},{id:6,title:"Founder Institute",img:"founder",url:"(https://www.fi.co)",template:G,type:a.Investor,preview:"/assets/founder-preview.png"},{id:7,title:"Emergence ",url:"(https://www.emcap.com)",img:"emergence",template:X,type:a.Investor,preview:"/assets/emergence-preview.png"},{id:8,title:"NFX Capital",img:"nfx",url:"(https://www.nfx.com)",template:z,type:a.Investor,preview:"/assets/nfx-preview.png"},{id:9,title:"Hackernoon",url:"(https://hackernoon.com)",img:"hacker",template:W,type:a.Investor,preview:"/assets/hackernoon-preview.png"},{id:10,img:"techcrunch",title:"Techcrunch",url:"(https://techcrunch.com)",template:Z,type:a.Investor,preview:"/assets/techchrunch-preview2.svg"},{id:11,title:"Standard",img:"standard",url:"",template:l,type:a.Marketing,preview:"/assets/standard-preview.png"},{id:12,title:"Standard",img:"standard",url:"",categories:[{id:121,title:"Standard",img:"standard",type:"Product",template:l,preview:"/assets/standard-preview.png"},{id:122,title:"Standard",img:"standard",type:"Engineering",template:l,preview:"/assets/standard-preview.png"},{id:123,title:"Standard",img:"standard",type:"Design",template:l,preview:"/assets/standard-preview.png"}],template:l,type:a.Product,preview:"/assets/standard-preview.png"},{id:13,title:"Standard",img:"standard",url:"",template:l,type:a.Finance,preview:"/assets/standard-preview.png"},{id:14,title:"Standard",img:"standard",url:"",template:l,type:a.TeamHR,preview:"/assets/standard-preview.png"},{id:15,title:"Standard",img:"standard",url:"",template:l,type:a.Operations,preview:"/assets/standard-preview.png"}],Se=()=>{const h=A(),{dispatch:y,state:u}=n.useContext(L),{dispatch:f}=n.useContext(Y),[T,E]=n.useState(!1),j=R(),[p,b]=n.useState("Investor"),[x,_]=n.useState({isOpen:!1,imageUrl:"",header:""}),[se,I]=n.useState({});F();const w=new URLSearchParams(j.search).get("id");n.useEffect(()=>{if(w){const e=g.find(s=>s.id==w);v(e.template,e.id,e.title)}},[w]),n.useEffect(()=>{const e=localStorage.getItem("template-tab")||"Investor";b(e)},[]),n.useEffect(()=>{const e={},s=r=>new Promise((i,d)=>{const c=new Image;c.src=r,c.onload=()=>{e[r]=c,i(r)},c.onerror=d});(async()=>{try{const r=g.map(i=>i.preview?s(i.preview):i.categories?i.categories.map(d=>s(d.preview)):null).flat().filter(Boolean);await Promise.all(r),I(e)}catch(r){console.error("Error preloading images:",r)}})()},[]);function P(e){return p==="Investor"?`${e} UpdateStack Investor Update Template`:`${e} UpdateStack ${p} Update Template`}function N(e,s){_({isOpen:!0,imageUrl:e,header:s})}function k(){_({isOpen:!1,imageUrl:"",header:""})}async function v(e,s,o){E(s);try{const r=new D,i=localStorage.getItem("requested_update_id");if(i){const d=await r.update("updates",i,{name:"Update Title",template_name:o,user_id:u.user,mrr:0,arr:0,cash:0,burnrate:0,date:S().format("MMM D, YYYY"),public_link_enabled:0,private_link_open:1,company_id:u.company.id});for(let c=0;c<e.length;c++){const m=e[c];await r.create("notes",d.data,{type:m,status:0})}h(`/member/edit-updates/${i}?autofocus=true`,{state:{templateTitle:o}})}else{const d=await r.create("updates",{name:"Update Title",template_name:o,user_id:u.user,mrr:0,arr:0,cash:0,burnrate:0,date:S().format("MMM D, YYYY"),public_link_enabled:0,private_link_open:1,company_id:u.company.id});await new U().callRawAPI("/v3/api/custom/goodbadugly/activities/draft",{update_id:d.data},"POST");for(let m=0;m<e.length;m++){const C=e[m];await r.create("notes",{update_id:d.data,type:C,status:0})}h(`/member/edit-updates/${d.data}?autofocus=true`,{state:{templateTitle:o}})}}catch(r){V(y,r.message),H(f,r.message,5e3,"error")}finally{E(!1),localStorage.removeItem("requested_update_id")}}n.useEffect(()=>{f({type:"SETPATH",payload:{path:"templates"}})},[]);const O=({tab:e,isSelected:s,onClick:o})=>t.jsx("button",{className:` border-b-[6px] px-4 py-2 pb-3 text-[18px] text-lg font-semibold transition-all duration-200 ease-out ${s?"border-b-[6px] border-b-[#1F1D1A]":"font-Ionwan-regular border-b-transparent"}`,onClick:()=>o(e),children:e}),M=({template:e,onSelect:s,creating:o})=>t.jsxs("div",{className:"flex h-[209px] max-w-[100%] flex-col justify-between rounded-[4px] border border-[#1f1D1A] bg-[#F2DFCE] p-6 sm:max-w-[388px] ",children:[t.jsxs("div",{className:"items-center",children:[t.jsx("img",{src:`/assets/${e.img}.svg`,className:"mb-4",alt:""}),t.jsx("h3",{className:"text-[18px] font-semibold",children:P(e.title)})]}),t.jsxs("div",{className:"flex justify-between mt-4",children:[t.jsx("button",{className:"flex gap-3 items-center",onClick:()=>N(e.preview,e.title),children:t.jsx("span",{className:"text-sm font-medium font-Inter",children:"Preview"})}),t.jsx("button",{className:"flex gap-3 items-center",onClick:()=>s(e),children:o===e.id?t.jsxs(t.Fragment,{children:[t.jsx("span",{className:"hidden text-sm font-medium font-Inter sm:block",children:"Creating"}),t.jsx(ee,{size:12})]}):t.jsxs(t.Fragment,{children:[t.jsx("span",{className:"text-sm font-medium font-Inter",children:"Select"}),t.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsx("path",{d:"M2.5 10L17.5 10",stroke:"black",strokeWidth:"1.66667",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M11.668 15.8335L17.5013 10.0002L11.668 4.16683",stroke:"black",strokeWidth:"1.66667",strokeLinecap:"round",strokeLinejoin:"round"})]})]})})]})]});return t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"flex flex-col p-5 pt-8 space-y-4 w-full md:px-8",children:[t.jsx("span",{className:"text-[24px] font-semibold sm:text-lgheader",children:"Select A Template"}),t.jsxs("div",{className:"w-full md:mx-auto",children:[t.jsx("div",{id:"tabContainer",className:"scrollbar-hide mb-6 flex w-full overflow-x-auto  border-b-[1px] border-b-[#1f1d1a]/20 md:overflow-hidden",children:te.map(e=>t.jsx(O,{tab:e,isSelected:e===p,onClick:s=>{b(s),localStorage.setItem("template-tab",s)}},e))}),g.map(e=>{if([a.Product].includes(e==null?void 0:e.type)&&[p].includes(e==null?void 0:e.type))return t.jsx(J,{template:e,openPreviewModal:N,onSelect:s=>v(s.template,s.id,s.title),creating:T},e.id)}),t.jsx("div",{className:"grid grid-cols-1 justify-center gap-4 sm:grid-cols-2 md:grid-cols-3 md:justify-normal 2xl:gap-[80px]",children:g.map(e=>{if(![a.Product].includes(e==null?void 0:e.type)&&[p].includes(e==null?void 0:e.type))return t.jsx(M,{template:e,onSelect:()=>v(e.template,e.id,e.title),creating:T},e.id)})})]})]}),x.isOpen&&t.jsx("div",{className:"pt-80px fixed inset-0 z-[10000] mx-auto flex max-h-[100vh] w-[100%] items-center justify-center bg-black bg-opacity-50",children:t.jsx("div",{className:"flex h-[100%] items-center justify-center",children:t.jsxs("div",{className:"h-[90%] w-full overflow-y-auto rounded-lg border border-primary-black bg-brown-main-bg  md:max-w-[860px]",children:[t.jsxs("div",{className:"flex justify-between items-center p-6 mb-4",children:[t.jsxs("div",{className:"flex gap-2 items-center",children:[t.jsx("span",{className:"font-iowan text-[20px] font-medium",children:"Template Preview:"}),t.jsxs("h2",{className:"font-iowan text-[20px] font-semibold",children:[x.header," Investor Update"]})]}),t.jsx("button",{onClick:k,className:"text-xl",children:t.jsx(Q,{className:"text-xl"})})]}),t.jsx("div",{className:"bg-[#F2DFCE] px-[60px] py-[40px]",children:t.jsx("img",{src:x.imageUrl,alt:"Template Preview",className:"object-cover w-full h-auto"})})]})})})]})};export{Se as default};
