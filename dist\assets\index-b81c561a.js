import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{o as h}from"./yup-0917e80c.js";import{A as b,G as j,M as N,s as p,t as g}from"./index-f2ad9142.js";import{InteractiveButton2 as y}from"./InteractiveButton-060359e0.js";import{T as S}from"./TwoFactorAuthenticate-cf55d555.js";import{r as t}from"./vendor-4cdf2bd1.js";import{u as k}from"./react-hook-form-a383372b.js";import{c as P,a as v}from"./yup-342a5df4.js";import{E,a as C}from"./EyeIcon-b7c71a85.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";function as(){var l,d;const{dispatch:n}=t.useContext(b),{dispatch:o}=t.useContext(j);t.useState(!1);const[r,c]=t.useState(!1),x=P({password:v().required("This field is required")}),{register:u,handleSubmit:w,setError:A,reset:T,formState:{errors:i,isSubmitting:m}}=k({resolver:h(x),defaultValues:{password:""}});async function f(e){try{await new N().updatePassword(e.password),p(o,"Password updated successfully")}catch(a){g(n,a.message),p(o,a.message,5e3,"error")}}return s.jsx("div",{className:"p-5 px-4 pt-8 md:px-8",children:s.jsxs("div",{className:"w-full max-w-7xl ",children:[s.jsx("h3",{className:"text-xl ",children:"Password"}),s.jsx("p",{className:"font-iowan-regular  mt-1 text-base",children:"Update your account's password"}),s.jsxs("form",{onSubmit:w(f),className:"mt-8 flex flex-col gap-4 sm:flex-row sm:items-end",children:[s.jsxs("div",{className:"",children:[s.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"New Password"}),s.jsxs("div",{className:"relative w-[400px] max-w-full rounded-md",children:[s.jsx("input",{type:r?"text":"password",autoComplete:"off",...u("password"),className:`no-box-shadow w-[400px] max-w-full appearance-none  rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(l=i.password)!=null&&l.message?"border-red-500":""}`}),s.jsx("button",{className:"absolute right-2 translate-y-1/2",type:"button",onClick:()=>c(e=>!e),children:r?s.jsx(E,{className:"h-5"}):s.jsx(C,{className:"h-5"})})]}),s.jsx("p",{className:"text-field-error italic text-red-500",children:(d=i.password)==null?void 0:d.message})]}),s.jsx(y,{loading:m,disabled:m,type:"submit",className:" disabled:bg-disabledblack block h-[41.6px] w-[160px] whitespace-nowrap rounded-md bg-primary-black/90 px-3 py-1 text-center text-sm font-semibold text-white transition-colors duration-100",children:"Update Password"})]}),s.jsx("div",{className:"mt-8 max-w-[576px]",children:s.jsx(S,{})})]})})}export{as as default};
