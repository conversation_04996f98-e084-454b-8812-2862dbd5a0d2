import React, { Fragment, useState, useContext } from "react";
import { useNavigate } from "react-router-dom";
import { useProfile } from "Hooks/useProfile";
import { LazyLoad } from "Components/LazyLoad";
import { ModalSidebar } from "Components/ModalSidebar";
import { TrashIcon } from "Assets/svgs";
import { ActionConfirmationModal } from "Components/ActionConfirmationModal";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { AddButton } from "Components/AddButton";
import { SkeletonLoader } from "Components/Skeleton";

const AdminCouponsListPage = () => {
  const { profile } = useProfile();
  const navigate = useNavigate();
  const { dispatch: authDispatch } = useContext(AuthContext);
  const { dispatch: globalDispatch } = useContext(GlobalContext);

  // State management
  const [coupons, setCoupons] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedCoupon, setSelectedCoupon] = useState(null);
  const [newCoupon, setNewCoupon] = useState({
    couponId: "",
    name: "",
    percent_off: "",
    duration: "forever",
  });

  const sdk = new MkdSDK();

  const fetchCoupons = async () => {
    setLoading(true);
    try {
      const result = await sdk.getStripeCoupons();
      console.log("Coupons API response:", result);

      if (!result.error) {
        // Handle the response structure: { error: false, coupons: [...] }
        setCoupons(result.coupons || []);
      } else {
        showToast(
          globalDispatch,
          result.message || "Failed to fetch coupons",
          5000,
          "error"
        );
      }
    } catch (error) {
      console.error("Error fetching coupons:", error);
      showToast(
        globalDispatch,
        error.message || "Failed to fetch coupons",
        5000,
        "error"
      );
      tokenExpireError(authDispatch, error.message);
    } finally {
      setLoading(false);
    }
  };

  const createVIPCoupon = async () => {
    try {
      const result = await sdk.createStripeCoupon({
        couponId: "vip-access-100",
        name: "VIP Access - 100% Off",
        percent_off: 100,
        duration: "forever",
      });

      if (!result.error) {
        showToast(
          globalDispatch,
          "VIP Coupon created successfully!",
          5000,
          "success"
        );
        fetchCoupons();
      } else {
        showToast(globalDispatch, result.message, 5000, "error");
      }
    } catch (error) {
      console.error("Error creating VIP coupon:", error);
      showToast(globalDispatch, error.message, 5000, "error");
      tokenExpireError(authDispatch, error.message);
    }
  };

  const createCustomCoupon = async () => {
    if (!newCoupon.couponId || !newCoupon.name || !newCoupon.percent_off) {
      showToast(
        globalDispatch,
        "Please fill in all required fields",
        5000,
        "error"
      );
      return;
    }

    try {
      const result = await sdk.createStripeCoupon({
        couponId: newCoupon.couponId,
        name: newCoupon.name,
        percent_off: parseInt(newCoupon.percent_off),
        duration: newCoupon.duration,
      });

      if (!result.error) {
        showToast(
          globalDispatch,
          "Coupon created successfully!",
          5000,
          "success"
        );
        setShowAddModal(false);
        setNewCoupon({
          couponId: "",
          name: "",
          percent_off: "",
          duration: "forever",
        });
        fetchCoupons();
      } else {
        showToast(globalDispatch, result.message, 5000, "error");
      }
    } catch (error) {
      console.error("Error creating coupon:", error);
      showToast(globalDispatch, error.message, 5000, "error");
      tokenExpireError(authDispatch, error.message);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "coupons",
      },
    });
    fetchCoupons();
  }, []);

  if (loading) {
    return (
      <div className="mx-auto rounded p-5 shadow-md">
        <div className="min-h-screen">
          <SkeletonLoader />
        </div>
      </div>
    );
  }

  return (
    <Fragment>
      <div className="px-8">
        <div className="items-center py-3 pt-10">
          <div className="mb-6 flex w-full items-center justify-between gap-5">
            <h4 className="text-[1rem] font-semibold md:text-xl">
              Coupon Management
            </h4>
            <div className="flex gap-3">
              <button
                onClick={createVIPCoupon}
                disabled={loading}
                className="rounded bg-green-600 px-4 py-2 text-white hover:bg-green-700 disabled:opacity-50"
              >
                {loading ? "Creating..." : "Create VIP Coupon (100% Off)"}
              </button>
              <AddButton onClick={() => setShowAddModal(true)} />
            </div>
          </div>
        </div>

        {loading ? (
          <SkeletonLoader />
        ) : (
          <div className="overflow-x-auto shadow">
            <table className="min-w-full">
              <thead>
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Coupon ID
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Name
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Discount
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Duration
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Times Redeemed
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Status
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="font-iowan-regular divide-y divide-[#1f1d1a]/10">
                {coupons.length > 0 ? (
                  coupons.map((coupon, index) => (
                    <tr className="md:h-[60px]" key={coupon.id || index}>
                      <td className="whitespace-nowrap px-6 py-4 text-sm font-medium">
                        {coupon.id}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm">
                        {coupon.name}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm">
                        {coupon.percent_off}% off
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm">
                        {coupon.duration}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm">
                        {coupon.times_redeemed || 0}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm">
                        {coupon.valid ? (
                          <span className="rounded-md bg-[#D1FAE5] px-3 py-1 text-[#065F46]">
                            Active
                          </span>
                        ) : (
                          <span className="rounded-md bg-[#F4F4F4] px-3 py-1 text-[#393939]">
                            Inactive
                          </span>
                        )}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm font-medium">
                        <button
                          onClick={() => {
                            setSelectedCoupon(coupon);
                            setShowDeleteModal(true);
                          }}
                          className="text-red-600 hover:text-red-900"
                        >
                          <TrashIcon />
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan="7"
                      className="px-6 py-4 text-center text-gray-500"
                    >
                      No coupons found. Create your first coupon!
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
            {!loading && coupons.length === 0 && (
              <p className="px-10 py-3 text-xl capitalize">No coupons found</p>
            )}
          </div>
        )}
      </div>

      {/* Add Coupon Modal */}
      <ModalSidebar
        isModalActive={showAddModal}
        showHeader
        title="Create Custom Coupon"
        closeModalFn={() => setShowAddModal(false)}
        customMinWidthInTw="md:w-[25%] w-full !bg-brown-main-bg"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Coupon ID *
            </label>
            <input
              type="text"
              value={newCoupon.couponId}
              onChange={(e) =>
                setNewCoupon({ ...newCoupon, couponId: e.target.value })
              }
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              placeholder="e.g., SAVE50"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Coupon Name *
            </label>
            <input
              type="text"
              value={newCoupon.name}
              onChange={(e) =>
                setNewCoupon({ ...newCoupon, name: e.target.value })
              }
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              placeholder="e.g., 50% Off Special"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Percent Off *
            </label>
            <input
              type="number"
              min="1"
              max="100"
              value={newCoupon.percent_off}
              onChange={(e) =>
                setNewCoupon({ ...newCoupon, percent_off: e.target.value })
              }
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              placeholder="50"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Duration
            </label>
            <select
              value={newCoupon.duration}
              onChange={(e) =>
                setNewCoupon({ ...newCoupon, duration: e.target.value })
              }
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            >
              <option value="forever">Forever</option>
              <option value="once">Once</option>
              <option value="repeating">Repeating</option>
            </select>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setShowAddModal(false)}
              className="rounded-md bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-300"
            >
              Cancel
            </button>
            <button
              onClick={createCustomCoupon}
              className="rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700"
            >
              Create Coupon
            </button>
          </div>
        </div>
      </ModalSidebar>

      {/* Delete Confirmation Modal */}
      <ActionConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={() => {
          // Add delete functionality here if needed
          setShowDeleteModal(false);
          showToast(
            globalDispatch,
            "Delete functionality not implemented yet",
            3000,
            "info"
          );
        }}
        title="Delete Coupon"
        message={`Are you sure you want to delete the coupon "${selectedCoupon?.name}"?`}
        confirmText="Delete"
        cancelText="Cancel"
      />
    </Fragment>
  );
};

export default AdminCouponsListPage;
