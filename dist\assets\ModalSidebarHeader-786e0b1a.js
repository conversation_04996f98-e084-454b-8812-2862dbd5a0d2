import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{r as m}from"./vendor-4cdf2bd1.js";import{A as p}from"./AddButton-51d1b2cd.js";import{L as s}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const e=({onToggleModal:r,onSaveTrigger:o,cancelText:i="Cancel"})=>t.jsx(s,{className:"flex w-full justify-end gap-3",children:t.jsxs("div",{className:"flex w-full justify-end gap-3",children:[r&&t.jsx("button",{type:"button",onClick:r,children:i}),o&&t.jsx(p,{onClick:o,showPlus:!1,children:"Save"})]})}),H=m.memo(e);export{H as default};
