import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{R as q,r as D,b as I,L as M}from"./vendor-4cdf2bd1.js";import{u as O}from"./react-hook-form-a383372b.js";import{o as T}from"./yup-0917e80c.js";import{c as B,a as n,e as K}from"./yup-342a5df4.js";import{A as U,I as Y,M as z,s as G,t as H}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const be=()=>{var m,c,p,x,u,b;const{dispatch:i}=q.useContext(U),[d,a]=D.useState(!1),F=window.location.search,S=new URLSearchParams(F).get("token"),R=B({code:n().required(),password:n().required(),confirmPassword:n().oneOf([K("password"),null],"Passwords must match")}).required(),L=I(),{register:o,handleSubmit:$,setError:l,formState:{errors:t}}=O({resolver:T(R)}),C=async f=>{var h,g,w,y,j,N,k,v;let A=new z;try{a(!0);const e=await A.reset(S,f.code,f.password);if(!e.error)G(i,"Password Reset"),setTimeout(()=>{L("/admin/login")},2e3);else if(e.validation){const P=Object.keys(e.validation);for(let r=0;r<P.length;r++){const E=P[r];l(E,{type:"manual",message:e.validation[E]})}}a(!1)}catch(e){a(!1),console.log("Error",e),l("code",{type:"manual",message:(g=(h=e==null?void 0:e.response)==null?void 0:h.data)!=null&&g.message?(y=(w=e==null?void 0:e.response)==null?void 0:w.data)==null?void 0:y.message:e==null?void 0:e.message}),H(i,(N=(j=e==null?void 0:e.response)==null?void 0:j.data)!=null&&N.message?(v=(k=e==null?void 0:e.response)==null?void 0:k.data)==null?void 0:v.message:e==null?void 0:e.message)}};return s.jsx("div",{className:"flex min-h-screen w-full items-center justify-center",children:s.jsxs("div",{className:"mx-auto w-full max-w-xs",children:[s.jsxs("form",{onSubmit:$(C),className:"mb-4 mt-8 rounded bg-[#FFF4EC] px-8 pb-8 pt-6 shadow-md ",children:[s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"code",children:"Code"}),s.jsx("input",{type:"text",placeholder:"Enter code sent to your email",...o("code"),className:`"shadow focus:shadow-outline w-[100px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] focus:outline-none   sm:w-[180px] ${(m=t.code)!=null&&m.message?"border-red-500":""}`}),s.jsx("p",{className:"text-xs italic text-red-500",children:(c=t.code)==null?void 0:c.message})]}),s.jsxs("div",{className:"mb-6",children:[s.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"password",children:"Password"}),s.jsx("input",{type:"password",placeholder:"******************",...o("password"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none ${(p=t.password)!=null&&p.message?"border-red-500":""}`}),s.jsx("p",{className:"text-xs italic text-red-500",children:(x=t.password)==null?void 0:x.message})]}),s.jsxs("div",{className:"mb-6",children:[s.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"confirmPassword",children:"Confirm Password"}),s.jsx("input",{type:"password",placeholder:"******************",...o("confirmPassword"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none ${(u=t.confirmPassword)!=null&&u.message?"border-red-500":""}`}),s.jsx("p",{className:"text-xs italic text-red-500",children:(b=t.confirmPassword)==null?void 0:b.message})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(Y,{className:"focus:shadow-outline rounded bg-primary-black px-4 py-2 font-bold text-white focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:d,disabled:d,children:"Reset Password"}),s.jsx(M,{className:"inline-block align-baseline text-sm font-bold text-primary-black",to:"/admin/login",children:"Login?"})]})]}),s.jsxs("p",{className:"text-center text-xs text-gray-500",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]})})};export{be as default};
