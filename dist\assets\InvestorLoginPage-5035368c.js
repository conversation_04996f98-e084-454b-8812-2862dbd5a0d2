import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R,r as o,u as T,b as J,L as Z}from"./vendor-4cdf2bd1.js";import{u as W}from"./react-hook-form-a383372b.js";import{o as X}from"./yup-0917e80c.js";import{c as q,a as D}from"./yup-342a5df4.js";import{M as z,A as _,G as $,I as e1,s as P}from"./index-f2ad9142.js";import{P as s1}from"./popup-1f338261.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./MoonLoader-6f2b5db4.js";let m=new z;const M1=()=>{var w,A;const G=q({email:D().email().required(),password:D().required()}).required(),{dispatch:I}=R.useContext(_),{dispatch:u}=R.useContext($),[k,B]=o.useState(!1),[n,d]=o.useState(!1),[x,O]=o.useState(!1),[U,t1]=o.useState([]),[E,a1]=o.useState(),Y=T(),K=new URLSearchParams(Y.search).get("redirect_uri"),l=J(),{register:h,handleSubmit:Q,setError:p,formState:{errors:t}}=W({resolver:X(G)}),C=async i=>{var r,c,j,b,N,H,L,y,V,M,v,S;try{d(!0);const s=await m.login(i.email,i.password,"investor");if(s.error){if(d(!1),s.validation){const f=Object.keys(s.validation);for(let a=0;a<f.length;a++){const F=f[a];p(F,{type:"manual",message:s.validation[F]})}}}else{if(s.two_factor_enabled){localStorage.setItem("2FA",s.two_factor_enabled);let a=btoa(JSON.stringify(JSON.stringify(s)));l(`/fundmanager/2FA/?t=${a}`)}if(I({type:"LOGIN",payload:s}),(await m.callRawAPI(`/v4/api/records/user/${s.user_id}`,void 0,"GET",s.token)).model.is_onboarded!=1){if((await m.callRawAPI(`/v4/api/records/startup_member?filter=member_id,eq,${s.user_id}&filter=member_role,cs,investor`,void 0,"GET",s.token)).list.length>0){l("/fundmanager/get-started");return}l("/fundmanager/get-started",{state:{first_login:!0}});return}P(u,"Successfully Logged In",4e3,"success"),l(K??"/fundmanager/dashboard")}}catch(s){d(!1),P(u,(c=(r=s==null?void 0:s.response)==null?void 0:r.data)!=null&&c.message?(b=(j=s==null?void 0:s.response)==null?void 0:j.data)==null?void 0:b.message:s==null?void 0:s.message,4e3,"error"),console.log("Error",s),(s==null?void 0:s.message)=="Invalid Password"?p("password",{type:"manual",message:(H=(N=s==null?void 0:s.response)==null?void 0:N.data)!=null&&H.message?(y=(L=s==null?void 0:s.response)==null?void 0:L.data)==null?void 0:y.message:s==null?void 0:s.message}):p("email",{type:"manual",message:(M=(V=s==null?void 0:s.response)==null?void 0:V.data)!=null&&M.message?(S=(v=s==null?void 0:s.response)==null?void 0:v.data)==null?void 0:S.message:s==null?void 0:s.message})}},g=async i=>{let r="investor";const c=await m.oauthLoginApi(i,r);window.open(c,"_self")};return e.jsxs("main",{className:"min-h-screen bg-brown-main-bg",children:[e.jsxs("div",{className:"flex min-h-full flex-col items-center justify-center",children:[e.jsxs("div",{className:"my-3 mb-3 mt-3 flex w-[300px] flex-col items-center rounded-lg p-4 sm:w-[520px] lg:max-w-[700px]",children:[e.jsx("div",{className:"my-2 text-xl font-semibold text-[#262626]",children:e.jsxs("svg",{onClick:()=>window.location.href="https://updatestack.com",className:"h-[32px] w-[180px] cursor-pointer sm:h-auto sm:w-auto",width:"294",height:"40",viewBox:"0 0 294 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M56.011 3.94476V29.6915H8.29451V9.07431H17.8659H32.1526H51.5972V3.94226L3.91203 3.92002V3.94771H3.13672V34.8457H61.1688V3.94771L56.011 3.94476Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M12.707 21.101V26.2552H51.5962V12.5105H32.1516H17.8648H12.707V17.6648H46.4386V21.101H12.707Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M72.2383 20.2053V5.81549H77.2012V19.9748C77.2012 22.8572 79.0662 24.6829 81.7057 24.6829C84.3387 24.6829 86.2044 22.8572 86.2044 19.9748V5.81549H91.1674V20.2053C91.1674 25.6144 87.1177 29.3931 81.7057 29.3931C76.288 29.3931 72.2383 25.6144 72.2383 20.2053Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M105.12 10.5858C110.443 10.5858 114.379 14.5954 114.379 19.9895C114.379 25.3773 110.443 29.4202 105.12 29.4202C103.29 29.4202 101.628 28.8765 100.259 27.9216V35.3057H95.4805V11.0896H98.6402L99.4561 12.7078C100.957 11.3687 102.917 10.5865 105.121 10.5865L105.12 10.5858ZM109.541 19.9887C109.541 17.1934 107.506 15.1105 104.73 15.1105C101.955 15.1105 99.913 17.1993 99.913 19.9887C99.913 22.7789 101.955 24.8673 104.73 24.8673C107.506 24.8673 109.541 22.7847 109.541 19.9887Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M135.561 4.67397V28.8897H132.032L131.468 27.4079C129.984 28.679 128.075 29.421 125.933 29.421C120.571 29.421 116.641 25.378 116.641 19.9902C116.641 14.5961 120.571 10.5865 125.933 10.5865C127.758 10.5865 129.415 11.1203 130.783 12.061V4.67397H135.561ZM131.139 19.9902C131.139 17.2 129.099 15.1116 126.322 15.1116C123.546 15.1116 121.511 17.1942 121.511 19.9902C121.511 22.7862 123.546 24.8687 126.322 24.8687C129.098 24.8687 131.139 22.7738 131.139 19.9902Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M158.147 11.0629V28.8634H154.401L153.99 27.2703C152.472 28.6087 150.492 29.3931 148.263 29.3931C142.947 29.3931 138.992 25.351 138.992 19.9631C138.992 14.5961 142.947 10.5865 148.263 10.5865C150.532 10.5865 152.541 11.3918 154.069 12.7634L154.58 11.0629H158.147ZM153.463 19.9631C153.463 17.1671 151.428 15.0845 148.652 15.0845C145.877 15.0845 143.835 17.173 143.835 19.9631C143.835 22.7533 145.877 24.8417 148.652 24.8417C151.428 24.8417 153.463 22.7591 153.463 19.9631Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M173.544 24.6046V28.8897H170.149C166.261 28.8897 163.872 26.4837 163.872 22.5337V14.9587H160.676V13.9185L167.656 6.44479H168.57V11.0896H173.446V14.9587H168.65V21.8957C168.65 23.6153 169.632 24.6046 171.373 24.6046L173.544 24.6046Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M193.704 21.3381H180.328C180.743 23.7675 182.329 25.1051 184.647 25.1051C186.307 25.1051 187.67 24.3141 188.396 23.0401H193.418C192.133 26.9206 188.75 29.3931 184.647 29.3931C179.428 29.3931 175.48 25.3239 175.48 19.9895C175.48 14.6287 179.402 10.5861 184.647 10.5861C190.074 10.5861 193.796 14.7995 193.796 19.9265C193.796 20.397 193.764 20.8675 193.704 21.3381ZM180.454 18.0511H189.019C188.343 15.8748 186.791 14.6945 184.647 14.6945C182.52 14.6945 181.009 15.9443 180.454 18.0511Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M196.238 21.4127H201.233C201.233 23.6423 203.063 24.7187 205.09 24.7187C206.955 24.7187 208.779 23.7265 208.779 22.0061C208.779 20.2178 206.69 19.726 204.206 19.1421C200.752 18.2852 196.557 17.2747 196.557 12.3035C196.557 7.87756 199.793 5.38412 204.867 5.38412C210.133 5.38412 213.126 8.21599 213.126 12.8128H208.228C208.228 10.8261 206.601 9.89425 204.747 9.89425C203.141 9.89425 201.514 10.5803 201.514 12.091C201.514 13.7147 203.504 14.2064 205.929 14.7907C209.421 15.6809 213.807 16.7778 213.807 21.9469C213.807 26.9301 209.865 29.3485 205.122 29.3485C199.862 29.3485 196.238 26.3725 196.238 21.4127Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M228.032 24.6046V28.8897H224.637C220.749 28.8897 218.36 26.4837 218.36 22.5337V14.9587H215.164V13.9185L222.145 6.44479H223.058V11.0896H227.934V14.9587H223.139V21.8957C223.139 23.6153 224.12 24.6046 225.861 24.6046L228.032 24.6046Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M249.123 11.0629V28.8634H245.377L244.967 27.2703C243.449 28.6087 241.469 29.3931 239.239 29.3931C233.923 29.3931 229.969 25.351 229.969 19.9631C229.969 14.5961 233.923 10.5865 239.239 10.5865C241.508 10.5865 243.518 11.3918 245.046 12.7634L245.557 11.0629H249.123ZM244.44 19.9631C244.44 17.1671 242.405 15.0845 239.629 15.0845C236.853 15.0845 234.812 17.173 234.812 19.9631C234.812 22.7533 236.853 24.8417 239.629 24.8417C242.405 24.8417 244.44 22.7591 244.44 19.9631Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M261.935 29.3924C256.618 29.3924 252.551 25.269 252.551 19.9353C252.551 14.6012 256.591 10.5858 261.962 10.5858C266.538 10.5858 270.09 13.5076 270.95 17.8886H266.205C265.431 16.139 263.839 15.1109 261.935 15.1109C259.343 15.1109 257.394 17.2059 257.394 19.9624C257.394 22.7196 259.376 24.868 261.935 24.868C263.866 24.868 265.392 23.7865 266.199 21.8832H271.009C270.177 26.3579 266.558 29.3931 261.935 29.3931L261.935 29.3924Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M285.032 28.8897L279.376 20.3743V28.8897H274.598V4.67397H279.376V18.9987L284.681 11.0896H290.185L284.207 19.5658L290.822 28.8897H285.032Z",fill:"#1F1D1A"})]})}),e.jsxs("div",{className:"mb-3 flex flex-col items-center justify-center gap-2",children:[e.jsxs("span",{className:"whitespace-nowrap text-center font-iowan text-[24px] font-semibold sm:text-[40px]",children:["Welcome to Your ",e.jsx("br",{})," Update Center"]}),e.jsx("span",{className:"text-center text-[14px] font-[500] lg:text-base",children:"To get started, please sign in to your account"})]}),e.jsxs("form",{className:"w-[350px] max-w-[96%] space-y-2  md:min-w-[70%] md:space-y-3",onSubmit:Q(C),children:[e.jsxs("div",{className:"mt-[14px] flex flex-col space-y-1 text-sm",children:[e.jsx("label",{htmlFor:"",className:"font-iowan text-[16px] font-[700]",children:"Email"}),e.jsx("input",{className:"h-[44px] rounded-sm border-[2px] border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] outline-none focus:border-[#1f1d1a] focus:shadow-none focus:outline-none",type:"text",placeholder:"<EMAIL>",...h("email")}),e.jsx("p",{className:"text-xs italic text-red-500",children:(w=t==null?void 0:t.email)==null?void 0:w.message})]}),e.jsxs("div",{className:"flex flex-col space-y-2 text-sm",children:[e.jsx("label",{htmlFor:"",className:"font-iowan text-[16px] font-[700]",children:"Password"}),e.jsxs("div",{className:"flex h-[44px] items-center rounded-sm border-[2px] border-[#1f1d1a] bg-transparent px-2 py-1 ",children:[e.jsx("input",{className:"w-[95%] border-none bg-transparent p-1 text-sm font-normal text-[#1f1d1a] shadow-[0] outline-none focus:border-none focus:shadow-none focus:outline-none",type:x?"text":"password",placeholder:"********",...h("password"),style:{boxShadow:"0 0 transparent"},autoComplete:"false"}),e.jsx("span",{className:"w-[5%] cursor-pointer pr-6",onClick:()=>O(!x),children:x?e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.99998 3.33337C13.5326 3.33335 16.9489 5.50937 19.0735 9.61715L19.2715 10L19.0735 10.3828C16.9489 14.4906 13.5326 16.6667 10 16.6667C6.46737 16.6667 3.05113 14.4907 0.926472 10.3829L0.728455 10.0001L0.926472 9.61724C3.05113 5.50946 6.46736 3.3334 9.99998 3.33337ZM7.08333 10C7.08333 8.38921 8.38917 7.08337 10 7.08337C11.6108 7.08337 12.9167 8.38921 12.9167 10C12.9167 11.6109 11.6108 12.9167 10 12.9167C8.38917 12.9167 7.08333 11.6109 7.08333 10Z",fill:"#A8A8A8"})}):e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.28033 2.21967C2.98744 1.92678 2.51256 1.92678 2.21967 2.21967C1.92678 2.51256 1.92678 2.98744 2.21967 3.28033L5.38733 6.44799C4.04329 7.533 2.8302 8.97021 1.81768 10.7471C1.37472 11.5245 1.37667 12.4782 1.81881 13.2539C3.74678 16.6364 6.40456 18.789 9.29444 19.6169C12.0009 20.3923 14.8469 19.9857 17.3701 18.4308L20.7197 21.7803C21.0126 22.0732 21.4874 22.0732 21.7803 21.7803C22.0732 21.4874 22.0732 21.0126 21.7803 20.7197L3.28033 2.21967ZM14.2475 15.3082L13.1559 14.2166C12.81 14.3975 12.4167 14.4995 11.9991 14.4995C10.6184 14.4995 9.49911 13.3802 9.49911 11.9995C9.49911 11.5819 9.60116 11.1886 9.78207 10.8427L8.69048 9.75114C8.25449 10.3917 7.99911 11.1662 7.99911 11.9995C7.99911 14.2087 9.78998 15.9995 11.9991 15.9995C12.8324 15.9995 13.6069 15.7441 14.2475 15.3082Z",fill:"#A8A8A8"}),e.jsx("path",{d:"M19.7234 16.5416C20.5189 15.7335 21.2556 14.7869 21.9145 13.7052C22.5512 12.66 22.5512 11.34 21.9145 10.2948C19.3961 6.16075 15.7432 4.00003 11.9999 4C10.6454 3.99999 9.30281 4.28286 8.02148 4.83974L19.7234 16.5416Z",fill:"#A8A8A8"})]})})]}),e.jsx("p",{className:"text-xs italic text-red-500",children:(A=t==null?void 0:t.password)==null?void 0:A.message})]}),e.jsxs("div",{className:"my-3 flex justify-between text-sm",children:[e.jsxs("div",{className:"flex items-center text-[#1f1d1a]",children:[e.jsx("input",{className:"mr-2 h-[15px] w-[15px] border-[2px] bg-brown-main-bg",type:"checkbox"}),e.jsx("span",{className:"text-[14px] font-[600] text-[#1f1d1a] sm:text-[16px]",children:"Remember me"})]}),e.jsx(Z,{to:"/fundmanager/forgot",className:"text-[14px] font-[600] text-[#1F1D1A] underline sm:text-[16px]",children:"Forgot password"})]}),e.jsx(e1,{type:"submit",className:"my-12 flex h-[44px] w-full items-center justify-center rounded-sm bg-[#1f1d1a] py-2 tracking-wide text-white outline-none focus:outline-none",loading:n,disabled:n,children:e.jsx("span",{className:"capitalize transition-all duration-150 ease-out",children:n?"Logging In":"Log In"})})]}),e.jsxs("div",{className:"mt-4 flex w-[350px] max-w-[96%] flex-row items-center md:min-w-[70%]",children:[e.jsx("hr",{className:"w-full border-[1px] border-[#1f1d1a]"}),e.jsxs("span",{className:"mx-2 w-full max-w-[100px] whitespace-nowrap text-[16px] font-[500]",children:[" ","Or login with"]}),e.jsx("hr",{className:"w-full border-[1px] border-[#1f1d1a]"})]}),e.jsxs("div",{className:"oauth mt-2 flex w-[350px] max-w-[96%] grow flex-col gap-2 text-[#344054] md:min-w-[70%]",children:[e.jsxs("button",{onClick:()=>g("google"),className:"my-2 flex h-[44px] min-w-[70%] cursor-pointer items-center justify-center gap-3 rounded-sm border-2 border-[#1f1d1a] px-4",children:[e.jsx("img",{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAALpSURBVHgBtVbNTxNBFH8zuy3QoN0YJMEQs8QQP05LAsbEg4uRxMSD4AeaeLB6xEPhpIkm4MF4IsG/oODF4Edajgahy8UDxbAcjDEc2IORCIlUhVK6u/OcKbVpaZdWxN+lkzd9v9+b9968WQK7YEnXlYPSxm0GqCMQjZtUYScASUSw+NJwGU40GXOGFwfxIg7IqX6KGEYABSqCWBmKPc2TCbOiwEpXhwaMRAFQhb+Ei/i4aXpuyFNAkBMG8eqiLoVIG2N2Z5NhWiUCyxfPqLLtznuTYxKQWIRk869wT60SuYD8ZyHZrGzk3NGkCP3r6Cy0GGYyH5CuqRL1DXKhkBd5/gRrfa0h+7MSKQ0aRhqnEwOwC1YvtOuO41jlyPMCzpRvKT3boKbeNRdsYOzw1FwP/COoPSnriKjWdKxCsO8j0GAmm0/HdQZgHyADhXM8FdtqnPzArUVIv280gsOWVc5BH9xUoWrUJkWRi7pBiAQufRmF4fIukt+N8Hh0qAYsNUoBSztHRtmCfQASVCn8Z1BCiLXT6DJbg32CzPhFKpwXv9AHkY3jOoA5Uc6B53+Mn90o2SBi0mKo2MS5RZvyVVwYFp0g3P95GpbdQNJJuy3mnVgSqsT5JxuRnQKMQYj6uhyDr5Pjm8fg3o+zsMwCQlqR66RIteT6082S6LNw7BlJ/EpX22ufp1r1DEiF2yeOXDupfH396W0lcopMZKCoG/llNYzB4LN8+tvHr8zz3JYUl48MPkHJ0OyNN2NFxJFuZb1W7pfSp8J1K3cV6jQU+aHk1+IP/At5Ae3FTVWm9ny5e5FT4uMasi8WL7RKcs+nALUboO5bGKStozl2GJl+VD+w7VaAjpfXNRTHxb09OP61Hqj53m3GH9a35cUL/5DofWU6zNfGI7RgD9g6FI1hxu4stJV99LVotyJnaJjXZAiqAPI6Aa/Thx118hTIC/G6UMjolJLL2Y+AXBMgr4coPmc2CMVYojc648XxG0ZrPRAMMnAhAAAAAElFTkSuQmCC",className:"h-[18px] w-[18px]"}),e.jsx("span",{className:"text-[16px] font-[600]",children:"Sign in With Google"})]}),e.jsxs("button",{onClick:()=>g("microsoft"),className:"oauth flex h-[44px] items-center justify-center gap-2 rounded-sm border-2 border-[#1f1d1a]",children:[e.jsx("img",{src:"https://companieslogo.com/img/orig/MSFT-a203b22d.png?t=**********",className:"h-[18px] w-[18px]"}),e.jsx("span",{className:"text-[16px] font-[600]",children:"Sign in With Microsoft"})]})]})]}),e.jsxs("div",{className:"flex items-center pb-10 text-sm lg:text-base",children:[e.jsxs("span",{className:"mr-1 font-medium text-[#1f1d1a]",children:["Don’t have account?"," "]})," ",e.jsx(Z,{to:"/fundmanager/sign-up",className:"font-bold text-[#1f1d1a] underline",children:"Sign up here"})]})]}),e.jsx(s1,{open:k,setOpen:B,accounts:U,loading:n,login:C,data:E})]})};export{M1 as default};
