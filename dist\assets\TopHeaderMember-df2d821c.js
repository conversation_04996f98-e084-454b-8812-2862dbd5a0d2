import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as l,b as ge,i as _e,R as ue,u as Ce}from"./vendor-4cdf2bd1.js";import{U as ke,c as $e}from"./index-f08e5be1.js";import{A as ae,G as we,M as W,s as xe,t as Ee,a as Ae,b as Re,au as qe,L as te,T as he}from"./index-f2ad9142.js";import"./lodash-82bd9112.js";import{M as Me}from"./index.esm-6fcccbfe.js";import{_ as Te}from"./MoonLoader-6f2b5db4.js";import{l as Ue}from"./logo5-2e16f0f2.js";import{h as pe}from"./moment-a9aaa855.js";import{A as Ie}from"./index-afef2e72.js";import{e as De}from"./AuthAction-52ee0934.js";import{u as Fe}from"./useMentions-2c8c5eca.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-icons-36ae72b7.js";function Le({title:u,titleId:r,...d},c){return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:c,"aria-labelledby":r},d),u?l.createElement("title",{id:r},u):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))}const Pe=l.forwardRef(Le),Z=Pe,Be=()=>{var T,C,b;const{state:u,dispatch:r}=l.useContext(ae),d=ge();if(console.log(u,"bar"),!((T=u.impersonation)!=null&&T.active))return null;const c=()=>{De(r),window.location.href="/admin/users",d("/admin/users")};return e.jsxs("div",{className:" z-50 flex items-center justify-between bg-[#1f1d1a] px-4 py-2 text-white lg:w-[600px]",children:[e.jsxs("div",{className:" hidden items-center gap-4 lg:flex",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs("span",{className:"text-sm",children:["Admin: ",(C=u.impersonation.originalAdmin)==null?void 0:C.first_name," ",(b=u.impersonation.originalAdmin)==null?void 0:b.last_name]})}),e.jsx("div",{className:"h-4 w-px bg-gray-500"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("span",{className:"text-sm",children:["Viewing as: ",u.userDetails.firstName," ",u.userDetails.lastName]}),e.jsx("span",{className:"rounded bg-yellow-500 px-2 py-0.5 text-xs text-black",children:u.role})]})]}),e.jsx("button",{onClick:c,className:"rounded bg-white px-3 py-1 text-sm text-black hover:bg-gray-100",children:"Exit Impersonation"})]})};function Oe({update:u,afterEdit:r}){const{dispatch:d,state:c}=l.useContext(ae),{dispatch:T}=l.useContext(we),[C,b]=l.useState(!1),[k,P]=l.useState("");_e();const[J,S]=l.useState(null);console.log(k,"profilek",c),l.useEffect(()=>{P(c.company.name||"Company Name")},[c.company.name]);async function U(p){b(!0);try{await new W().callRawAPI(`/v4/api/records/companies/${c.company.id}`,{name:p},"PUT"),d({type:"REFETCH_COMPANY"}),xe(T,"Company name saved")}catch(A){Ee(d,A.message),xe(T,A.message,5e3,"error")}b(!1)}return e.jsx("div",{className:"flex flex-row items-center",children:C?e.jsx(Te,{size:14}):e.jsxs(e.Fragment,{children:[e.jsx("input",{className:"no-box-shadow focus:shadow-outline appearance-none border-none bg-brown-main-bg bg-inherit p-0 text-3xl text-[18px] font-bold capitalize focus:outline-none",defaultValue:"Company name",value:k,onChange:p=>{P(p.target.value),J&&clearTimeout(J);const A=setTimeout(()=>U(p.target.value),2e3);S(A)},readOnly:C,style:{width:`${k?(k==null?void 0:k.length)+1:14}ch`}}),k?null:e.jsx(Me,{size:16})]})})}const gs=()=>{const{dispatch:u}=ue.useContext(we),{globalState:r,showToast:d,setGlobalState:c}=Ae();ue.useContext(ae);const[T,C]=l.useState(""),{isOpen:b,showBackButton:k}=r,P=Ce(),{tdk:J,sdk:S}=Re(),[U,p]=l.useState(!1),[A,g]=l.useState(!1),[B,R]=l.useState(null),[N,v]=l.useState(""),[j,be]=l.useState([]),[I,ne]=l.useState(!1),[z,D]=l.useState(null),[X,G]=l.useState("member"),[y,re]=l.useState([]),[Q,oe]=l.useState(!1),[je,F]=l.useState(null),[$,ie]=l.useState([]),[He,le]=l.useState(!1),{loading:Se,mentions:V,unreadCount:Y,fetchMentions:ee,fetchUnreadCount:se,markMentionAsSeen:ce,markAllMentionsAsSeen:ze}=Fe();console.log(z,"currentCollaboration",j,y);const{invitations:_,loading:L,handleInvitation:Ne,refetchInvitations:de}=qe();console.log(_,"invite");const O=async()=>{var n,a;console.log("Fetching collaboration requests"),ne(!0);const s=new he;try{const t=await S.callRawAPI("/v3/api/custom/goodbadugly/team-updates",{},"GET");if(!t.error&&t.data){console.log("Team updates data received:",t.data);const i=Array.from(t.data.filter(h=>h.collaborator_status==2));console.log("Filtered collaboration updates:",i);const x=(await Promise.all(i.map(async h=>{var m;const f=await s.getList("update_collaborators",{filter:[`id,eq,${h==null?void 0:h.update_collaborator_id}`]}),w=(m=f==null?void 0:f.list)==null?void 0:m[0];return w?{...h,collaborator_details:w}:null}))).filter(Boolean);console.log("Processed collaborator data:",x),be(x)}}catch(t){console.error("Error fetching collaboration requests:",t);const i=((a=(n=t==null?void 0:t.response)==null?void 0:n.data)==null?void 0:a.message)??t.message;d(i,5e3),tokenExpireError(i)}finally{ne(!1)}},ve=async(s,n)=>{var a,t,i,o;try{const x="/v3/api/custom/goodbadugly/collaborator/response",h=n==="accept"?1:2,f=j.find(m=>m.id===s);console.log("Handling collaboration action",{updateId:s,action:n,collaborator_id:(a=f==null?void 0:f.collaborator_details)==null?void 0:a.collaborator_id});const w=await S.callRawAPI(x,{collaborator_id:(t=f==null?void 0:f.collaborator_details)==null?void 0:t.collaborator_id,update_id:s,status:h},"POST");console.log("Collaboration action result:",w),c("collaborationChange",!(r!=null&&r.collaborationChange)),w.error?d(`Failed to ${n} collaboration request`,5e3):(d(`Collaboration request ${n}ed successfully`),await O(),await H(),n==="accept"&&setTimeout(()=>{q(`/member/edit-updates/${z}`)},100))}catch(x){console.error("Error handling collaboration:",x);const h=((o=(i=x==null?void 0:x.response)==null?void 0:i.data)==null?void 0:o.message)??x.message;d(h,5e3),tokenExpireError(h)}},H=async()=>{var s,n;console.log("Fetching update requests");try{oe(!0);const a=new W,t=new he,i=await a.callRawAPI("/v3/api/custom/goodbadugly/users/update-requests",void 0,"GET");if(console.log("Update requests data received:",i),!i.error&&((s=i.data)==null?void 0:s.length)>0){const o=[...new Set(i.data.map(m=>m.requesting_user_id))];console.log("Requesting user IDs:",o);const x=await t.getPaginate("user",{filter:[`id,in,${o.join(",")}`],page:1,limit:o.length});console.log("User details:",x);const h=x.list.reduce((m,M)=>(m[M.id]=M,m),{}),f={};for(const m of o){a.setTable("companies");const M=await a.callRawAPI(`/v4/api/records/companies?filter=user_id,eq,${m}`,void 0,"GET");(n=M==null?void 0:M.list)!=null&&n[0]&&(f[m]=M.list[0])}console.log("User company map:",f);const w=i.data.map(m=>({...m,requesting_user:h[m.requesting_user_id]||null,requesting_user_company:f[m.requesting_user_id]||null}));console.log("Updates with user details:",w),re(w),c("requestedUpdates",w==null?void 0:w.length)}else console.log("No update requests found or error in response"),re([]),c("requestedUpdates",0)}catch(a){console.error("Error fetching update requests:",a),d(a.message||"Error fetching update requests",5e3,"error")}finally{oe(!1)}},me=async()=>{var s;try{le(!0);const a=await new W().callRawAPI("/v3/api/custom/goodbadugly/user/unanswered-questions",[],"GET");!a.error&&((s=a.data)==null?void 0:s.length)>0?(ie(a.data),c("unansweredQuestions",a.data.length)):(ie([]),c("unansweredQuestions",0))}catch(n){console.error("Error fetching unanswered questions:",n),d(n.message||"Error fetching unanswered questions",5e3,"error")}finally{le(!1)}},ye=async(s,n,a)=>{try{console.log("Handling update request action",{requestId:s,updateId:n,action:a});const i=await new W().callRawAPI("/v3/api/custom/goodbadugly/update-requests/acceptance",{ids:[s],request:a==="accept"?1:2,update_id:n,data:[{id:s,status:a==="accept"?1:2}]},"POST");console.log("Update request action result:",i),c("updateRequestChange",!(r!=null&&r.updateRequestChange)),i.error?d(`Failed to ${a} update request`,5e3,"error"):(d(`Update request ${a}ed successfully`),await H(),await O(),await de(),a==="accept"&&setTimeout(()=>{q(`/member/edit-updates/${n}?autofocus=true`)},100))}catch(t){console.error("Error handling update request:",t),d(t.message||`Error ${a}ing update request`,5e3,"error")}};l.useEffect(()=>{O(),H(),me(),ee(),se();const s=setInterval(()=>{O(),H(),me(),ee(),se()},18e4);return()=>clearInterval(s)},[]);const E=s=>{const n=pe(),a=pe(s),t=n.diff(a,"seconds");if(t<60)return"just now";if(t<3600){const o=Math.floor(t/60);return`${o} ${o===1?"min":"mins"} ago`}if(t<86400){const o=Math.floor(t/3600);return`${o} ${o===1?"hour":"hours"} ago`}if(t<604800){const o=Math.floor(t/86400);return`${o} ${o===1?"day":"days"} ago`}if(t<2592e3){const o=Math.floor(t/604800);return`${o} ${o===1?"week":"weeks"} ago`}if(t<31536e3){const o=Math.floor(t/2592e3);return`${o} ${o===1?"month":"months"} ago`}const i=Math.floor(t/31536e3);return`${i} ${i===1?"year":"years"} ago`},K=(s,n)=>{R(s),v(n),c("memberChange",!(r!=null&&r.memberChange)),g(!0)};l.useEffect(()=>{de()},[r==null?void 0:r.memberChange]),l.useEffect(()=>{console.log("collaborationChange triggered",r==null?void 0:r.collaborationChange),(async()=>{await O(),console.log("Collaboration requests fetched")})()},[r==null?void 0:r.collaborationChange]),l.useEffect(()=>{console.log("updateRequestChange triggered",r==null?void 0:r.updateRequestChange),(async()=>{await H(),console.log("Update requests fetched")})()},[r==null?void 0:r.updateRequestChange]),l.useEffect(()=>{console.log("mentionsChange triggered",r==null?void 0:r.mentionsChange),(async()=>{await ee(),await se(),console.log("Mentions refetched")})()},[r==null?void 0:r.mentionsChange]),l.useEffect(()=>{const s=n=>{n.target.closest(".notifications-container")||p(!1)};return document.addEventListener("mousedown",s),()=>document.removeEventListener("mousedown",s)},[]);let fe=s=>{console.log(s,b),u({type:"OPEN_SIDEBAR",payload:{isOpen:s}})};const q=ge();return l.useEffect(()=>{const s=P.pathname.split("/");s[1]!=="user"&&s[1]!=="admin"?C(s[1]):C(s[2])},[P]),e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"sticky right-0 top-0 z-[9] flex h-[4.5rem] w-full flex-row items-center justify-between bg-[#1f1d1a] px-4 md:hidden md:px-8",children:[e.jsx("img",{onClick:()=>q("/member/dashboard"),src:Ue,alt:"logo",className:"h-10 w-[180px] cursor-pointer sm:invisible"}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"notifications-container relative md:hidden",children:[e.jsxs("button",{className:"relative rounded-[50%] border border-[#E9DBD2] p-2",onClick:()=>p(!U),disabled:L||I,children:[e.jsx(Z,{className:"h-4 w-4 fill-brown-main-bg text-brown-main-bg"}),_.length+j.length+y.length+$.length+Y>0&&e.jsx("div",{className:"absolute -right-2 -top-[0.25rem] flex h-4 w-4 items-center justify-center rounded-full bg-[#F6A03C] text-xs text-[black]",children:e.jsx("span",{className:"text-xs",children:_.length+j.length+y.length+$.length+Y})})]}),U&&e.jsxs("div",{className:"custom-overscroll absolute right-[-42px] top-12 max-h-[252px] min-w-[300px] overflow-y-auto rounded-sm border border-[#1f1d1a]/10 bg-brown-main-bg p-4 shadow-lg sm:right-0 md:w-[445px]",children:[e.jsx("div",{className:"absolute -top-2 right-4 h-4 w-4 rotate-45 transform border-l border-t border-[#1f1d1a]/10 bg-brown-main-bg"}),_.length>0||j.length>0||y.length>0||$.length>0||V.length>0?e.jsxs(e.Fragment,{children:[_.map(s=>e.jsxs("div",{className:"mb-4 last:mb-0",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"h-8 max-h-8 min-h-8 w-8 min-w-8 max-w-8 overflow-hidden rounded-full bg-gray-200",children:s.photo?e.jsx("img",{src:s.photo,alt:s.inviter,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium",children:s.inviter.charAt(0)})}),e.jsxs("div",{className:"font-iowan-regular flex flex-grow items-center justify-between gap-6",children:[e.jsxs("p",{className:"font-iowan-regular whitespace-nowrap  text-[11px] sm:text-sm",children:[e.jsx("span",{className:"font-iowan font-semibold",children:s.inviter})," ",e.jsxs("span",{className:"font-iowan-regular text-base",children:["has invited you to"," "]}),e.jsx("span",{className:"font-iowan font-semibold",children:s.team}),e.jsx("span",{className:"ml-[5px] font-iowan font-semibold",children:"team"})]}),e.jsx("p",{className:"whitespace-nowrap font-inter text-xs font-medium text-black",children:s.timeAgo})]})]}),e.jsxs("div",{className:"flex w-[50%] gap-2",children:[e.jsx("button",{onClick:()=>K(s.id,"reject"),disabled:L,className:"= w-[85px] flex-1 rounded-sm border border-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium disabled:opacity-50",children:"Reject"}),e.jsx("button",{onClick:()=>K(s.id,"accept"),disabled:L,className:"w-[85px] flex-1 rounded-sm bg-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium text-white hover:bg-[#2a2724] disabled:opacity-50",children:"Accept"})]})]},s.id)),j.map(s=>{var n,a,t,i,o;return e.jsxs("div",{className:"mb-4 last:mb-0",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200",children:(n=s.user)!=null&&n.photo?e.jsx("img",{src:s.user.photo,alt:`${s.user.first_name} ${s.user.last_name}`,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium",children:(t=(a=s.user)==null?void 0:a.first_name)==null?void 0:t.charAt(0)})}),e.jsxs("div",{className:"font-iowan-regular flex flex-grow items-center justify-between gap-6",children:[e.jsxs("p",{className:"font-iowan-regular whitespace-nowrap text-[11px] sm:text-sm",children:[e.jsx("span",{className:"font-iowan font-semibold",children:`${(i=s.user)==null?void 0:i.first_name} ${(o=s.user)==null?void 0:o.last_name}`})," ",e.jsx("span",{className:"font-iowan-regular text-base",children:"requested collaboration on"})," ",e.jsx("span",{className:"font-iowan font-semibold",children:s.name}),s.company_name&&e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"font-iowan-regular text-base",children:[" ","for"," "]}),e.jsx("span",{className:"font-iowan font-semibold",children:s.company_name})]})]}),e.jsx("p",{className:"whitespace-nowrap font-inter text-xs font-medium text-black",children:E(s.collaborator_details.update_at)})]})]}),e.jsxs("div",{className:"flex w-[50%] gap-2",children:[e.jsx("button",{onClick:()=>{D(s.id),v("reject"),g(!0)},disabled:I,className:"w-[85px] flex-1 rounded-sm border border-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium disabled:opacity-50",children:"Reject"}),e.jsx("button",{onClick:()=>{D(s.id),v("accept"),g(!0)},disabled:I,className:"w-[85px] flex-1 rounded-sm bg-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium text-white hover:bg-[#2a2724] disabled:opacity-50",children:"Accept"})]})]},s.id)}),y.map(s=>{var n,a,t,i,o;return e.jsxs("div",{className:"mb-4 last:mb-0",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200",children:(n=s.requesting_user)!=null&&n.photo?e.jsx("img",{src:s.requesting_user.photo,alt:`${s.requesting_user.first_name} ${s.requesting_user.last_name}`,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium",children:((t=(a=s.requesting_user)==null?void 0:a.first_name)==null?void 0:t[0])||""})}),e.jsxs("div",{className:"font-iowan-regular flex flex-grow items-center justify-between gap-6",children:[e.jsxs("p",{className:"font-iowan-regular whitespace-normal text-sm",children:[e.jsx("span",{className:"font-iowan font-semibold",children:`${((i=s.requesting_user)==null?void 0:i.first_name)||""} ${((o=s.requesting_user)==null?void 0:o.last_name)||""}`})," ",s.requesting_user_company&&e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"font-iowan-regular text-base",children:["from"," "]}),e.jsx("span",{className:"font-iowan font-semibold",children:s.requesting_user_company.name})," "]}),e.jsx("span",{className:"font-iowan-regular text-base",children:"requested an update"})," "]}),e.jsx("p",{className:"whitespace-nowrap font-inter text-xs font-medium text-black",children:E(s.update_at)})]})]}),e.jsxs("div",{className:"flex w-[50%] gap-2",children:[e.jsx("button",{onClick:()=>{R(s.id),G("update_request"),v("reject"),g(!0),F(s.update_id)},disabled:Q,className:"w-[85px] flex-1 rounded-sm border border-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium disabled:opacity-50",children:"Reject"}),e.jsx("button",{onClick:()=>{R(s.id),G("update_request"),v("accept"),g(!0),F(s.update_id)},disabled:Q,className:"w-[85px] flex-1 rounded-sm bg-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium text-white hover:bg-[#2a2724] disabled:opacity-50",children:"Accept"})]})]},s.id)}),$.map(s=>{var n,a,t,i,o;return e.jsxs("div",{className:"mb-4 last:mb-0",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200",children:(n=s.creator)!=null&&n.photo?e.jsx("img",{src:s.creator.photo,alt:`${s.creator.first_name} ${s.creator.last_name}`,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium",children:((t=(a=s.creator)==null?void 0:a.first_name)==null?void 0:t[0])||""})}),e.jsxs("div",{className:"font-iowan-regular flex flex-grow items-center justify-between gap-6",children:[e.jsxs("p",{className:"font-iowan-regular whitespace-normal text-sm",children:[e.jsx("span",{className:"font-iowan font-semibold",children:`${((i=s.creator)==null?void 0:i.first_name)||""} ${((o=s.creator)==null?void 0:o.last_name)||""}`})," ",e.jsxs("span",{className:"font-iowan-regular text-base",children:["asked a question in"," "]}),e.jsx("span",{className:"font-iowan font-semibold",children:s.update_name})]}),e.jsx("p",{className:"whitespace-nowrap font-inter text-xs font-medium text-black",children:E(s.sent_at)})]})]}),e.jsx("div",{className:"",children:e.jsx("button",{onClick:()=>{q(`/member/update/private/view/${s.update_id}?question=${s.id}`),p(!1)},className:"w-[92px] flex-1 rounded-sm bg-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium text-white hover:bg-[#2a2724]",children:"Respond"})})]},s.id)}),V.map(s=>{var n;return e.jsxs("div",{className:"mb-4 last:mb-0",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200",children:s.mentioner_photo?e.jsx("img",{src:s.mentioner_photo,alt:`${s.mentioner_first_name} ${s.mentioner_last_name}`,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium",children:((n=s.mentioner_first_name)==null?void 0:n[0])||""})}),e.jsxs("div",{className:"font-iowan-regular flex flex-grow items-center justify-between gap-6",children:[e.jsxs("p",{className:"font-iowan-regular whitespace-normal text-sm",children:[e.jsx("span",{className:"font-iowan font-semibold",children:`${s.mentioner_first_name||""} ${s.mentioner_last_name||""}`})," ",e.jsxs("span",{className:"font-iowan-regular text-base",children:["mentioned you in"," "]}),e.jsx("span",{className:"font-iowan font-semibold",children:s.update_name})]}),e.jsx("p",{className:"whitespace-nowrap font-inter text-xs font-medium text-black",children:E(s.created_at)})]})]}),e.jsx("div",{className:"",children:e.jsx("button",{onClick:()=>{ce(s.id).then(t=>{t&&c("mentionsChange",!(r!=null&&r.mentionsChange))});let a=`/member/update/private/view/${s.update_id}`;s.comment_id&&(a+=`#${btoa(`comment:${s.comment_id}`)}`),q(a),p(!1)},className:`w-[92px] flex-1 rounded-sm ${s.seen===0?"bg-[#1f1d1a] text-white hover:bg-[#2a2724]":"border border-[#1f1d1a] bg-transparent text-[#1f1d1a]"} px-4 py-1.5 font-iowan text-sm font-medium`,children:s.seen===0?"View":"Viewed"})})]},s.id)})]}):e.jsxs("div",{className:"flex flex-col items-center justify-center py-8",children:[e.jsx("div",{className:"mb-4 rounded-full bg-[#1f1d1a]/5 p-4",children:e.jsx(Z,{className:"h-8 w-8 text-[#1f1d1a]/40"})}),e.jsx("h3",{className:"mb-2 text-lg font-semibold text-[#1f1d1a]",children:"You have no notifications"}),e.jsxs("p",{className:"text-center text-sm text-[#1f1d1a]/60",children:["Recent activity within your account that requires you to take action",e.jsx("br",{}),"will be shown here"]})]})]})]}),b?e.jsx("svg",{onClick:()=>fe(!b),className:"cursor-pointer",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M7 7L12 12M12 12L17 17M12 12L17 7M12 12L7 17",stroke:"#FCF1E6",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}):e.jsx("svg",{className:"cursor-pointer",width:"24",onClick:()=>fe(!b),height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M21 12C21 12.1989 20.921 12.3897 20.7803 12.5303C20.6397 12.671 20.4489 12.75 20.25 12.75H3.75C3.55109 12.75 3.36032 12.671 3.21967 12.5303C3.07902 12.3897 3 12.1989 3 12C3 11.8011 3.07902 11.6103 3.21967 11.4697C3.36032 11.329 3.55109 11.25 3.75 11.25H20.25C20.4489 11.25 20.6397 11.329 20.7803 11.4697C20.921 11.6103 21 11.8011 21 12ZM3.75 6.75H20.25C20.4489 6.75 20.6397 6.67098 20.7803 6.53033C20.921 6.38968 21 6.19891 21 6C21 5.80109 20.921 5.61032 20.7803 5.46967C20.6397 5.32902 20.4489 5.25 20.25 5.25H3.75C3.55109 5.25 3.36032 5.32902 3.21967 5.46967C3.07902 5.61032 3 5.80109 3 6C3 6.19891 3.07902 6.38968 3.21967 6.53033C3.36032 6.67098 3.55109 6.75 3.75 6.75ZM20.25 17.25H3.75C3.55109 17.25 3.36032 17.329 3.21967 17.4697C3.07902 17.6103 3 17.8011 3 18C3 18.1989 3.07902 18.3897 3.21967 18.5303C3.36032 18.671 3.55109 18.75 3.75 18.75H20.25C20.4489 18.75 20.6397 18.671 20.7803 18.5303C20.921 18.3897 21 18.1989 21 18C21 17.8011 20.921 17.6103 20.7803 17.4697C20.6397 17.329 20.4489 17.25 20.25 17.25Z",fill:"#FEF1E5"})})]})]}),e.jsxs("div",{className:"sticky right-0 top-0 z-[9] hidden h-[4.5rem] w-full flex-row items-center justify-between border-b-[2px] border-b-[#1F1D1A] bg-brown-main-bg px-8  shadow-sm sm:h-[4.5rem] sm:max-h-[4.5rem]  sm:flex-row sm:px-6 sm:pl-[20px] md:flex md:pl-[100px] lg:pl-[20px] xl:pl-[20px]",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Oe,{}),e.jsx(te,{children:e.jsx(ke,{})}),e.jsx(Be,{})]}),e.jsxs("div",{className:"flex items-center gap-4 md:self-auto",children:[e.jsx("div",{className:"hidden md:block",children:e.jsx(te,{children:e.jsx($e,{})})}),e.jsxs("div",{className:"notifications-container relative",children:[e.jsxs("button",{className:"relative rounded-[50%] border border-[#E9DBD2] p-2",onClick:()=>p(!U),disabled:L||I,children:[e.jsx(Z,{className:"h-6 w-6 fill-black text-[#1f1d1a]"}),_.length+j.length+y.length+$.length+Y>0&&e.jsx("div",{className:"absolute -right-2 -top-[0.25rem] flex h-5 w-5 items-center justify-center rounded-full bg-[#F6A03C] text-xs text-black",children:e.jsx("span",{className:"text-xs",children:_.length+j.length+y.length+$.length+Y})})]}),U&&e.jsxs("div",{className:"custom-overscroll absolute right-0 top-12 max-h-[252px] w-[420px] overflow-y-auto rounded-sm border border-[#1f1d1a]/10 bg-brown-main-bg p-4 shadow-lg sm:w-[460px]",children:[e.jsx("div",{className:"absolute -top-2 right-4 h-4 w-4 rotate-45 transform border-l border-t border-[#1f1d1a]/10 bg-brown-main-bg"}),_.length>0||j.length>0||y.length>0||$.length>0||V.length>0?e.jsxs(e.Fragment,{children:[_.map(s=>e.jsxs("div",{className:"mb-4 last:mb-0",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"h-8 max-h-8 min-h-8 w-8 min-w-8 max-w-8 overflow-hidden rounded-full bg-gray-200",children:s.photo?e.jsx("img",{src:s.photo,alt:s.inviter,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium",children:s.inviter.charAt(0)})}),e.jsxs("div",{className:"font-iowan-regular flex flex-grow items-center justify-between gap-6",children:[e.jsxs("p",{className:"font-iowan-regular text-sm",children:[e.jsx("span",{className:"font-iowan font-semibold",children:s.inviter})," ",e.jsxs("span",{className:"font-iowan-regular text-base",children:["has invited you to"," "]}),e.jsx("span",{className:"font-iowan font-semibold",children:s.team}),e.jsx("span",{className:"ml-[5px] font-iowan font-semibold",children:"team"})]}),e.jsx("p",{className:"whitespace-nowrap font-inter text-xs font-medium text-black",children:s.timeAgo})]})]}),e.jsxs("div",{className:"flex w-[50%] gap-2",children:[e.jsx("button",{onClick:()=>K(s.id,"reject"),disabled:L,className:"= w-[85px] flex-1 rounded-sm border border-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium disabled:opacity-50",children:"Reject"}),e.jsx("button",{onClick:()=>K(s.id,"accept"),disabled:L,className:"w-[85px] flex-1 rounded-sm bg-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium text-white hover:bg-[#2a2724] disabled:opacity-50",children:"Accept"})]})]},s.id)),j.map(s=>{var n,a,t,i,o;return e.jsxs("div",{className:"mb-4 last:mb-0",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200",children:(n=s.user)!=null&&n.photo?e.jsx("img",{src:s.user.photo,alt:`${s.user.first_name} ${s.user.last_name}`,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium",children:(t=(a=s.user)==null?void 0:a.first_name)==null?void 0:t.charAt(0)})}),e.jsxs("div",{className:"font-iowan-regular flex flex-grow items-center justify-between gap-6",children:[e.jsxs("p",{className:"font-iowan-regular text-sm",children:[e.jsx("span",{className:"font-iowan font-semibold",children:`${(i=s.user)==null?void 0:i.first_name} ${(o=s.user)==null?void 0:o.last_name}`})," ",e.jsx("span",{className:"font-iowan-regular text-base",children:"requested collaboration on"})," ",e.jsx("span",{className:"font-iowan font-semibold",children:s.name}),s.company_name&&e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"font-iowan-regular text-base",children:[" ","for"," "]}),e.jsx("span",{className:"font-iowan font-semibold",children:s.company_name})]})]}),e.jsx("p",{className:"whitespace-nowrap font-inter text-xs font-medium text-black",children:E(s.collaborator_details.update_at)})]})]}),e.jsxs("div",{className:"flex w-[50%] gap-2",children:[e.jsx("button",{onClick:()=>{D(s.id),v("reject"),g(!0)},disabled:I,className:"w-[85px] flex-1 rounded-sm border border-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium disabled:opacity-50",children:"Reject"}),e.jsx("button",{onClick:()=>{D(s.id),v("accept"),g(!0)},disabled:I,className:"w-[85px] flex-1 rounded-sm bg-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium text-white hover:bg-[#2a2724] disabled:opacity-50",children:"Accept"})]})]},s.id)}),y.map(s=>{var n,a,t,i,o;return e.jsxs("div",{className:"mb-4 last:mb-0",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200",children:(n=s.requesting_user)!=null&&n.photo?e.jsx("img",{src:s.requesting_user.photo,alt:`${s.requesting_user.first_name} ${s.requesting_user.last_name}`,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium",children:((t=(a=s.requesting_user)==null?void 0:a.first_name)==null?void 0:t[0])||""})}),e.jsxs("div",{className:"font-iowan-regular flex flex-grow items-center justify-between gap-6",children:[e.jsxs("p",{className:"font-iowan-regular text-sm",children:[e.jsx("span",{className:"font-iowan font-semibold",children:`${((i=s.requesting_user)==null?void 0:i.first_name)||""} ${((o=s.requesting_user)==null?void 0:o.last_name)||""}`})," ",s.requesting_user_company&&e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"font-iowan-regular text-base",children:["from"," "]}),e.jsx("span",{className:"font-iowan font-semibold",children:s.requesting_user_company.name})," "]}),e.jsx("span",{className:"font-iowan-regular text-base",children:"requested an update."})," "]}),e.jsx("p",{className:"whitespace-nowrap font-inter text-xs font-medium text-black",children:E(s.update_at)})]})]}),e.jsxs("div",{className:"flex w-[50%] gap-2",children:[e.jsx("button",{onClick:()=>{R(s.id),G("update_request"),v("reject"),g(!0),F(s.update_id)},disabled:Q,className:"w-[85px] flex-1 rounded-sm border border-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium disabled:opacity-50",children:"Reject"}),e.jsx("button",{onClick:()=>{R(s.id),G("update_request"),v("accept"),g(!0),F(s.update_id)},disabled:Q,className:"w-[85px] flex-1 rounded-sm bg-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium text-white hover:bg-[#2a2724] disabled:opacity-50",children:"Accept"})]})]},s.id)}),$.map(s=>{var n,a,t,i,o;return e.jsxs("div",{className:"mb-4 last:mb-0",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200",children:(n=s.creator)!=null&&n.photo?e.jsx("img",{src:s.creator.photo,alt:`${s.creator.first_name} ${s.creator.last_name}`,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium",children:((t=(a=s.creator)==null?void 0:a.first_name)==null?void 0:t[0])||""})}),e.jsxs("div",{className:"font-iowan-regular flex flex-grow items-center justify-between gap-6",children:[e.jsxs("p",{className:"font-iowan-regular text-sm",children:[e.jsx("span",{className:"font-iowan font-semibold",children:`${((i=s.creator)==null?void 0:i.first_name)||""} ${((o=s.creator)==null?void 0:o.last_name)||""}`})," ",e.jsxs("span",{className:"font-iowan-regular text-base",children:["asked a question in"," "]}),e.jsx("span",{className:"font-iowan font-semibold",children:s.update_name}),s.company_name&&e.jsx(e.Fragment,{})]}),e.jsx("p",{className:"whitespace-nowrap font-inter text-xs font-medium text-black",children:E(s.sent_at)})]})]}),e.jsx("div",{className:"",children:e.jsx("button",{onClick:()=>{q(`/member/update/private/view/${s.update_id}?question=${s.id}`),p(!1)},className:"w-[92px] flex-1 rounded-sm bg-[#1f1d1a] px-4 py-1.5 font-iowan text-sm font-medium text-white hover:bg-[#2a2724]",children:"Respond"})})]},s.id)}),V.map(s=>{var n;return e.jsxs("div",{className:"mb-4 last:mb-0",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("div",{className:"flex max-h-8 min-h-8 min-w-8 max-w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200",children:s.mentioner_photo?e.jsx("img",{src:s.mentioner_photo,alt:`${s.mentioner_first_name} ${s.mentioner_last_name}`,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-[#1f1d1a]/5 text-sm font-medium",children:((n=s.mentioner_first_name)==null?void 0:n[0])||""})}),e.jsxs("div",{className:"font-iowan-regular flex flex-grow items-center justify-between gap-6",children:[e.jsxs("p",{className:"font-iowan-regular whitespace-normal text-sm",children:[e.jsx("span",{className:"font-iowan font-semibold",children:`${s.mentioner_first_name||""} ${s.mentioner_last_name||""}`})," ",e.jsxs("span",{className:"font-iowan-regular text-base",children:["mentioned you in"," "]}),e.jsx("span",{className:"font-iowan font-semibold",children:s.update_name})]}),e.jsx("p",{className:"whitespace-nowrap font-inter text-xs font-medium text-black",children:E(s.created_at)})]})]}),e.jsx("div",{className:"",children:e.jsx("button",{onClick:()=>{ce(s.id).then(t=>{t&&c("mentionsChange",!(r!=null&&r.mentionsChange))});let a=`/member/update/private/view/${s.update_id}`;s.comment_id&&(a+=`#${btoa(`comment:${s.comment_id}`)}`),q(a),p(!1)},className:`w-[92px] flex-1 rounded-sm ${s.seen===0?"bg-[#1f1d1a] text-white hover:bg-[#2a2724]":"border border-[#1f1d1a] bg-transparent text-[#1f1d1a]"} px-4 py-1.5 font-iowan text-sm font-medium`,children:s.seen===0?"View":"Viewed"})})]},s.id)})]}):e.jsxs("div",{className:"flex flex-col items-center justify-center py-8",children:[e.jsx("div",{className:"mb-4 rounded-full bg-[#1f1d1a]/5 p-4",children:e.jsx(Z,{className:"h-8 w-8 text-[#1f1d1a]/40"})}),e.jsx("h3",{className:"mb-2 text-lg font-semibold text-[#1f1d1a]",children:"You have no notifications"}),e.jsxs("p",{className:"text-center text-sm text-[#1f1d1a]/60",children:["Recent activity within your account that requires you to take action",e.jsx("br",{}),"will be shown here"]})]})]})]})]})]}),e.jsx(te,{children:e.jsx(Ie,{mode:"manual",action:N,multiple:!1,inputConfirmation:!1,className:"action-confirmation-modal",isOpen:A,onClose:()=>{g(!1),R(null),D(null),F(null)},onSuccess:async()=>{X==="update_request"?await ye(B,je,N):B?await Ne(B,N):z&&await ve(z,N),g(!1),R(null),D(null),F(null)},title:X==="update_request"?"Update Request":B?"Team Invitation":"Collaboration Request",customMessage:X==="update_request"?`Are you sure you want to ${N} this update request?${N==="accept"?" You will be redirected to the update page after accepting.":""}`:B?`This action will ${N} the team invitation.`:`This action will ${N} the collaboration request. ${N==="accept"?"You will be redirected to the update page after accepting.":""}`})})]})};export{gs as default};
