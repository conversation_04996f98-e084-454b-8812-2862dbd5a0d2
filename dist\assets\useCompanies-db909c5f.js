import{A as x,G as C,M as _,t as b,s as k}from"./index-f2ad9142.js";import{r as e,h as y}from"./vendor-4cdf2bd1.js";function E(p){const[l,n]=e.useState(!1),[d,u]=e.useState([]),[a]=y(),{dispatch:h}=e.useContext(x),{dispatch:f}=e.useContext(C),r=a.get("limit")||30,i=a.get("page"),t=a.get("name");async function c(){n(!0);try{const g=await new _().callRawAPI(`/v4/api/records/company_member?join=companies|company_id&filter=member_id,eq,${p}&filter=member_role,cs,stakeholder${t?`&filter=name,cs,${t}`:""}&page=${i??1},${r??10}&order=id,asc`);u(g.list.map(s=>{var m;return{id:s.company_id,logo:(m=s.companies)==null?void 0:m.logo,name:s.companies.name,user_id:s.companies.user_id}}))}catch(o){b(h,o.message),k(f,o.message,5e3,"error")}n(!1)}return e.useEffect(()=>{c()},[r,i,t]),{loading:l,companies:d,refetch:c}}export{E as u};
