import{j as o}from"./@nextui-org/listbox-0f38ca19.js";import{A as h,ab as x,L as f}from"./index-f2ad9142.js";import{M as l}from"./index-713720be.js";import{h as i}from"./moment-a9aaa855.js";import{r as c}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const P=({update:j=null,note:N=null,reply:t=null,comment:M=null})=>{var s,r,a,m,e;const{state:u}=c.useContext(h),d=c.useMemo(()=>{try{return u.user}catch(n){return console.error("Error parsing user from localStorage:",n),null}},[])===((s=t==null?void 0:t.user)==null?void 0:s.id);return o.jsxs("div",{className:"flex !h-[2.25rem] !max-h-[2.25rem] !min-h-[2.25rem] w-full gap-[.75rem]",children:[o.jsx("div",{className:"h-[2.25rem] w-[2.25rem] rounded-[50%]",children:(r=t==null?void 0:t.user)!=null&&r.photo?o.jsx("img",{className:"rounded-[50%] object-cover",src:(a=t==null?void 0:t.user)==null?void 0:a.photo,alt:"avatar"}):o.jsx(x,{className:"h-[2.25rem] w-[2.25rem] rounded-[50%]"})}),o.jsxs("div",{className:"flex !h-full !max-h-full !min-h-full flex-col gap-[.125rem]",children:[o.jsx("span",{className:"font-inter text-[.875rem] font-[600] leading-[1.0588rem]",children:d?"Me":((m=t==null?void 0:t.user)==null?void 0:m.first_name)+" "+((e=t==null?void 0:t.user)==null?void 0:e.last_name)}),o.jsx(f,{children:o.jsx(l,{display:o.jsx("p",{children:i(t==null?void 0:t.create_at).fromNow()}),openOnClick:!1,backgroundColor:"#1f1d1a",children:o.jsxs("span",{className:"text-white",children:[" ",i(t==null?void 0:t.create_at).format("MMMM D, YYYY [at] h:mm A")]})})})]})]})};export{P as default};
