import{j as o}from"./@nextui-org/listbox-0f38ca19.js";import"./vendor-4cdf2bd1.js";import{o as w,L as A}from"./index-f2ad9142.js";import{A as L}from"./index-8c774937.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const K=({data:i={id:null},options:a={endpoint:null,method:"GET"},onSuccess:l,onClose:t,multiple:p=!1,action:e="",mode:s="create",table:d="",title:n="",input:f="input",isOpen:r=!1,inputConfirmation:u=!0,disableCancel:m=!1,modalClasses:x={modalDialog:"max-h-[90%] min-h-[12rem] overflow-y-auto !w-full md:!w-[29.0625rem]",modal:"h-full"},customMessage:c="",inputType:h="text",initialValue:j=""})=>o.jsx(w,{isOpen:r,modalCloseClick:t,title:n,modalHeader:!0,classes:x,disableCancel:m,children:r&&o.jsx(A,{children:o.jsx(L,{data:i,mode:s,input:f,table:d,action:e,onClose:t,options:a,multiple:p,onSuccess:l,inputType:h,initialValue:j,disableCancel:m,customMessage:c,inputConfirmation:u})})});export{K as ActionConfirmationModal,K as default};
