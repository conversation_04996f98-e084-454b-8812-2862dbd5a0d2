import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{A as Q,G as J,u as Y,L as O,$ as Z,I as ee,o as se,a0 as ae,M as U,s as v,t as D}from"./index-f2ad9142.js";import{u as te,r as o,i as oe,b as le}from"./vendor-4cdf2bd1.js";import{u as ne}from"./useUpdateCollaborators-677ec5ee.js";import"./yup-342a5df4.js";import"./InteractiveButton-060359e0.js";import{M as re}from"./index-713720be.js";import{u as ie}from"./useCompanyMember-0033d2de.js";import{M as de}from"./index-dc002f62.js";import{P as ce}from"./PlusIcon-26cedb5d.js";import{X as me}from"./XMarkIcon-cfb26fe7.js";import{C as ue}from"./ChevronUpDownIcon-e0f342e0.js";import{L as C,t as m,S as k,W as u}from"./@headlessui/react-cdd9213e.js";function Me({note_id:y,isOwner:f=!1}){var P,E,R;const q=te(),[B,r]=o.useState(!1),{dispatch:M}=o.useContext(Q),{dispatch:x}=o.useContext(J),[S,I]=o.useState(!1),[j,h]=o.useState(!1),[i,F]=o.useState(""),[n,p]=o.useState({}),[H,g]=o.useState(!1),[A,_]=o.useState(!1),[T,z]=o.useState(null),{id:w}=oe(),{profile:l}=Y({isPublic:!1}),{updateCollaborators:b,refetch:W}=ne(w,y),{companyMember:{myMembers:N},getMyCompanyMembers:L}=ie({filter:[`goodbadugly_company_member.company_id,eq,${(E=(P=l==null?void 0:l.companies)==null?void 0:P[0])==null?void 0:E.id}`]});le();async function G(s){var a;I(!0);try{await new U().callRawAPI("/v3/api/custom/goodbadugly/add-collaborator",{update_id:w,collaborator_id:(a=n==null?void 0:n.user)==null?void 0:a.id,note_id:y},"POST"),p({}),F(""),W(),r(!1),v(x,"Successful")}catch(t){D(M,t.message),v(x,t.message,5e3,"error")}I(!1)}async function V(s){_(!0),z(s.user.id);try{await new U().callRawAPI("/v3/api/custom/goodbadugly/add-collaborator",{update_id:w,collaborator_id:s.user.id,note_id:y},"POST"),v(x,"Invite resent successfully")}catch(a){D(M,a.message),v(x,a.message,5e3,"error")}_(!1),z(null)}const X=()=>{q.pathname==="/member/get-started"?(console.log("hi"),h(!0)):(r(!1),h(!0))},d=i===""?N.filter(s=>!b.some(a=>{var t;return(a==null?void 0:a.collaborator_id)==((t=s==null?void 0:s.user)==null?void 0:t.id)})):N.filter(s=>!b.some(a=>{var t;return(a==null?void 0:a.collaborator_id)==((t=s==null?void 0:s.user)==null?void 0:t.id)})).filter(s=>{var a;return(a=s==null?void 0:s.user)==null?void 0:a.email.toLowerCase().replace(/\s+/g,"").includes(i.toLowerCase().replace(/\s+/g,""))});return o.useEffect(()=>{if(i==""){p({});return}const s=N.find(a=>{var t;return((t=a==null?void 0:a.user)==null?void 0:t.email)==i});s&&p(s)},[i,j]),o.useEffect(()=>{var s,a;l!=null&&l.id&&L({filter:[`goodbadugly_company_member.company_id,eq,${(a=(s=l==null?void 0:l.companies)==null?void 0:s[0])==null?void 0:a.id}`]})},[l==null?void 0:l.id]),e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mt-6 text-end",children:e.jsxs("div",{className:"flex relative flex-col gap-2 items-center font-inter",children:[e.jsx("span",{className:"absolute top-0",children:e.jsx(O,{children:e.jsx(re,{display:e.jsx("button",{className:"p-0 w-full h-0 section-collaborators"}),openOnClick:!0,backgroundColor:"#1f1d1a",place:"top",children:e.jsxs("span",{className:"flex gap-2 items-center text-white",children:["Add collaborator"," ",e.jsx(Z,{className:"w-4 h-4 rotate-180",stroke:"white"})]})})})}),e.jsx("button",{disabled:!f,onClick:()=>r(!0),children:b.length==0&&f?e.jsx("span",{className:"text-base font-semibold underline font-inter underline-offset-2",children:"+ Add collaborator"}):e.jsxs("div",{className:"flex gap-4 items-center",children:[f?e.jsx(ce,{className:"h-4",strokeWidth:2}):null,e.jsx("div",{className:"flex -space-x-1",children:b.map(s=>e.jsx(C,{className:"relative",children:({open:a})=>{var t,c,$;return e.jsxs(e.Fragment,{children:[e.jsx(C.Button,{as:"div",className:"cursor-pointer",onMouseEnter:()=>g(s.user.id),onMouseLeave:()=>g(!1),children:e.jsx("img",{className:"h-7 min-h-7 w-7 min-w-7 rounded-[50%] object-cover",src:((t=s.user)==null?void 0:t.photo)||"/default.png"})}),e.jsx(m,{as:o.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",show:s.user.id===H,onMouseEnter:()=>g(s.user.id),onMouseLeave:()=>g(!1),children:e.jsx(C.Panel,{className:"absolute left-0 z-10 mt-3 w-fit -translate-x-[20%] transform whitespace-nowrap bg-brown-main-bg px-4 sm:-translate-x-full",children:e.jsx("div",{className:"overflow-hidden rounded-lg bg-[#1f1d1a] p-4 px-4 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsxs("div",{className:"flex flex-row gap-2 items-center text-sm font-medium",children:[(c=s.user)==null?void 0:c.first_name," ",($=s.user)==null?void 0:$.last_name]}),f&&e.jsx("button",{onClick:K=>{K.stopPropagation(),V(s)},disabled:A&&T===s.user.id,className:"flex gap-2 items-center px-2 py-1 text-xs rounded border border-white hover:bg-white/10",children:A&&T===s.user.id?e.jsx(de,{loading:!0,color:"#ffffff",size:12}):e.jsxs(e.Fragment,{children:[e.jsx("svg",{className:"w-3 h-3",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",clipRule:"evenodd"})}),"Resend Invite"]})})]})})})})]})}},s.id))})]})})]})}),e.jsx(m,{appear:!0,show:B,as:o.Fragment,children:e.jsxs(k,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>r(!1),children:[e.jsx(m.Child,{as:o.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"overflow-y-auto fixed inset-0",children:e.jsx("div",{className:"flex justify-center items-center p-4 min-h-full text-center",children:e.jsx(m.Child,{as:o.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(k.Panel,{className:"p-5 w-full max-w-md h-auto text-base text-left rounded-md shadow-xl transition-all transform bg-brown-main-bg",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(k.Title,{as:"h3",className:"text-xl font-semibold leading-6 text-gray-900",children:"Invite a collaborator"}),e.jsx("button",{onClick:()=>r(!1),type:"button",children:e.jsx(me,{className:"w-6 h-6"})})]}),e.jsx("p",{className:"my-4 font-iowan-regular",children:"Type their email to add them to this update only"}),e.jsx(u,{value:n,onChange:p,children:e.jsxs("div",{className:"relative mt-6",children:[e.jsxs(u.Button,{className:"relative w-full text-left rounded-md cursor-default focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 sm:text-sm",children:[e.jsx(u.Input,{className:"focus:shadow-outline w-full appearance-none rounded border  bg-brown-main-bg px-3 py-2 leading-tight text-[#1f1d1a] shadow focus:outline-none ",placeholder:"Type to search",displayValue:s=>{var a;return((a=s==null?void 0:s.user)==null?void 0:a.email)??""},onChange:s=>F(s.target.value)}),e.jsx("div",{className:"flex absolute inset-y-0 right-0 items-center pr-2",children:e.jsx(ue,{className:"w-5 h-5 text-gray-400","aria-hidden":"true"})})]}),e.jsx(m,{as:o.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx(u.Options,{className:"absolute z-[99999] mt-1 max-h-60 w-full overflow-auto rounded-md bg-brown-main-bg py-1 text-base shadow-lg  focus:outline-[#1f1d1a] sm:text-sm",children:(d==null?void 0:d.length)===0&&i!==""?e.jsx("div",{className:"relative px-4 py-2 text-gray-700 cursor-default select-none",children:"Nothing found."}):d==null?void 0:d.map(s=>e.jsx(u.Option,{className:({active:a})=>`relative cursor-default select-none py-2 pl-10 pr-4 ${a?"bg-[#1f1d1a] text-white":"text-gray-900"}`,value:s,children:({selected:a,active:t})=>{var c;return e.jsxs(e.Fragment,{children:[e.jsx("span",{className:`block truncate ${a?"font-medium":"font-normal"}`,children:(c=s==null?void 0:s.user)==null?void 0:c.email}),a?e.jsx("span",{className:`absolute inset-y-0 left-0 flex items-center pl-3 ${t?"text-white":"text-teal-600"}`}):null]})}},s.id))})})]})}),e.jsxs("div",{className:"flex justify-between items-center mt-6",children:[e.jsx("button",{className:"text-base font-semibold text-[#1f1d1a] underline",onClick:X,children:"New collaborator?"}),e.jsx(ee,{loading:S,disabled:S||!((R=n==null?void 0:n.user)!=null&&R.id),onClick:G,className:"rounded-sm bg-[#1f1d1a] px-4 py-2 text-center font-semibold !text-white transition-colors duration-100  disabled:bg-disabled-black",children:"Add"})]})]})})})})]})}),e.jsx(se,{modalHeader:!0,title:"Add New Collaborator",isOpen:j,modalCloseClick:()=>h(!1),classes:{modalDialog:"h-fit max-h-fit min-h-fit z-[51] md:!w-fit !w-full",modalContent:"!bg-brown-main-bg !z-[51] !mt-0 overflow-hidden !py-0",modal:"h-full z-[51]"},children:j?e.jsx(O,{children:e.jsx(ae,{onSuccess:()=>{var s,a;L({filter:[`goodbadugly_company_member.company_id,eq,${(a=(s=l==null?void 0:l.companies)==null?void 0:s[0])==null?void 0:a.id}`]}),h(!1),r(!0)}})}):null})]})}export{Me as C};
