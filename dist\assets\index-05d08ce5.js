import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{o as G}from"./yup-0917e80c.js";import{A as I,G as D,y as H,M as P,s as S,t as T}from"./index-f2ad9142.js";import{InteractiveButton2 as B}from"./InteractiveButton-060359e0.js";import{r as i}from"./vendor-4cdf2bd1.js";import{u as O}from"./react-hook-form-a383372b.js";import{c as X,a as s}from"./yup-342a5df4.js";import{G as Y}from"./react-icons-36ae72b7.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";function J({title:r,titleId:n,...l},d){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":n},l),r?i.createElement("title",{id:n},r):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))}const V=i.forwardRef(J),K=V;function W(r){return Y({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z"}}]})(r)}function ve(){var x,p,b,f,u,h,g,j,w,N,y,v,k,C,z,L,_,R,$,A;const{dispatch:r,state:n}=i.useContext(I),{dispatch:l}=i.useContext(D),d=X({linkedin:s(),github:s(),x:s(),medium:s(),facebook:s(),youtube:s(),instagram:s(),reddit:s(),angel_list:s(),crunch_base:s(),calendar:s()}),{register:o,handleSubmit:U,setError:q,reset:E,formState:{errors:a,isSubmitting:m,isDirty:M}}=O({resolver:G(d),defaultValues:async()=>{const t=H(n.company.socials,{});return{linkedin:t.linkedin,github:t.github,x:t.x,medium:t.medium,facebook:t.facebook,youtube:t.youtube,instagram:t.instagram,reddit:t.reddit,angel_list:t.angel_list,crunch_base:t.crunch_base,calendar:t.calendar}}});console.log(n);async function F(t){try{await new P().callRawAPI(`/v4/api/records/companies/${n.company.id}`,{socials:JSON.stringify({linkedin:t.linkedin,github:t.github,x:t.x,medium:t.medium,facebook:t.facebook,youtube:t.youtube,instagram:t.instagram,reddit:t.reddit,angel_list:t.angel_list,crunch_base:t.crunch_base,calendar:t.calendar})},"PUT"),r({type:"REFETCH_COMPANY"}),S(l,"Changes saved")}catch(c){T(r,c.message),S(l,c.message,5e3,"error")}}return e.jsx("div",{className:"w-full max-w-[1000px] items-start gap-12 p-4 sm:p-8 md:flex md:p-12",children:e.jsxs("form",{className:"flex-grow",onSubmit:U(F),children:[e.jsxs("div",{className:"grid grid-cols-2 gap-8",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"Linkedin"}),e.jsxs("div",{className:"flex items-center gap-3 rounded-md border border-[#1f1d1a]  px-3",children:[e.jsx("img",{src:"/linkedin.png",alt:"linkedin",className:"h-6 w-6 object-cover"}),e.jsx("input",{type:"text",autoComplete:"off",...o("linkedin"),className:`no-box-shadow w-full appearance-none border-none bg-transparent py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(x=a.linkedin)!=null&&x.message?"border-red-500":""}`,placeholder:"LinkedIn URL"})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(p=a.linkedin)==null?void 0:p.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"Github"}),e.jsxs("div",{className:"flex items-center gap-3 rounded-md border border-[#1f1d1a]  px-3",children:[e.jsx("img",{src:"/github.png",alt:"github",className:"h-6 w-6 object-cover"}),e.jsx("input",{type:"text",autoComplete:"off",...o("github"),className:`no-box-shadow w-full appearance-none border-none bg-transparent py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(b=a.github)!=null&&b.message?"border-red-500":""}`,placeholder:"Github URL"})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(f=a.github)==null?void 0:f.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"X"}),e.jsxs("div",{className:"flex items-center gap-3 rounded-md border border-[#1f1d1a]  px-3",children:[e.jsx(W,{className:"h-6 w-6"}),e.jsx("input",{type:"text",autoComplete:"off",...o("x"),className:`no-box-shadow w-full appearance-none border-none bg-transparent py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(u=a.x)!=null&&u.message?"border-red-500":""}`,placeholder:"X URL"})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(h=a.x)==null?void 0:h.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"Medium"}),e.jsxs("div",{className:"flex items-center gap-3 rounded-md border border-[#1f1d1a]  px-3",children:[e.jsx("img",{src:"/medium.png",alt:"medium",className:"h-6 w-6 object-cover"}),e.jsx("input",{type:"text",autoComplete:"off",...o("medium"),className:`no-box-shadow w-full appearance-none border-none bg-transparent py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(g=a.medium)!=null&&g.message?"border-red-500":""}`,placeholder:"Medium URL"})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(j=a.medium)==null?void 0:j.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"Facebook"}),e.jsxs("div",{className:"flex items-center gap-3 rounded-md border border-[#1f1d1a]  px-3",children:[e.jsx("img",{src:"/facebook.png",alt:"facebook",className:"h-6 w-6 object-cover"}),e.jsx("input",{type:"text",autoComplete:"off",...o("facebook"),className:`no-box-shadow w-full appearance-none border-none bg-transparent py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(w=a.facebook)!=null&&w.message?"border-red-500":""}`,placeholder:"Facebook URL"})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(N=a.facebook)==null?void 0:N.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"Youtube"}),e.jsxs("div",{className:"flex items-center gap-3 rounded-md border border-[#1f1d1a]  px-3",children:[e.jsx("img",{src:"/youtube.png",alt:"youtube",className:"h-6 w-6 object-cover"}),e.jsx("input",{type:"text",autoComplete:"off",...o("youtube"),className:`no-box-shadow w-full appearance-none border-none bg-transparent py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(y=a.youtube)!=null&&y.message?"border-red-500":""}`,placeholder:"Youtube URL"})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(v=a.youtube)==null?void 0:v.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"Reddit"}),e.jsxs("div",{className:"flex items-center gap-3 rounded-md border border-[#1f1d1a]  px-3",children:[e.jsx("img",{src:"/reddit.png",alt:"reddit",className:"h-6 w-6 object-cover"}),e.jsx("input",{type:"text",autoComplete:"off",...o("reddit"),className:`no-box-shadow w-full appearance-none border-none bg-transparent py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(k=a.reddit)!=null&&k.message?"border-red-500":""}`,placeholder:"Reddit URL"})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(C=a.reddit)==null?void 0:C.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"AngelList"}),e.jsxs("div",{className:"flex items-center gap-3 rounded-md border border-[#1f1d1a]  px-3",children:[e.jsx("img",{src:"/angel_list.png",alt:"angel_list",className:"h-6 w-6 object-cover"}),e.jsx("input",{type:"text",autoComplete:"off",...o("angel_list"),className:`no-box-shadow w-full appearance-none border-none bg-transparent py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(z=a.angel_list)!=null&&z.message?"border-red-500":""}`,placeholder:"AngelList URL"})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(L=a.angel_list)==null?void 0:L.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"Crunchbase"}),e.jsxs("div",{className:"flex items-center gap-3 rounded-md border border-[#1f1d1a]  px-3",children:[e.jsx("img",{src:"/crunch_base.png",alt:"crunch_base",className:"h-6 w-6 object-cover"}),e.jsx("input",{type:"text",autoComplete:"off",...o("crunch_base"),className:`no-box-shadow w-full appearance-none border-none bg-transparent py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(_=a.crunch_base)!=null&&_.message?"border-red-500":""}`,placeholder:"Crunchbase URL"})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(R=a.crunch_base)==null?void 0:R.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"Calendar"}),e.jsxs("div",{className:"flex items-center gap-3 rounded-md border border-[#1f1d1a]  px-3",children:[e.jsx(K,{className:"h-6 w-6"}),e.jsx("input",{type:"text",autoComplete:"off",...o("calendar"),className:`no-box-shadow w-full appearance-none border-none bg-transparent py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${($=a.calendar)!=null&&$.message?"border-red-500":""}`,placeholder:"Calendar URL"})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(A=a.calendar)==null?void 0:A.message})]})]}),e.jsxs("div",{className:"mt-6 flex items-center justify-end gap-4",children:[" ",M?e.jsx("button",{className:"h-[40px] rounded-md border border-[#1f1d1a] px-4 py-2 font-iowan text-[10px] font-medium sm:text-[12px]    xl:text-base",type:"button",onClick:()=>E(),children:"Discard Changes"}):null,e.jsx(B,{loading:m,disabled:m,type:"submit",className:`whitespace-nowr disabled:bg-disabledblack h-[40px]  w-[115px] min-w-fit rounded-md bg-primary-black px-6 py-2 text-center text-[10px] font-semibold font-semibold text-white transition-colors \r
duration-100 sm:!text-[12px] md:!w-[146px]  xl:!text-base`,children:"Save changes"})]})]})})}export{ve as default};
