import{j as e,l as B,a as R}from"./@nextui-org/listbox-0f38ca19.js";import{r as s,b as Z}from"./vendor-4cdf2bd1.js";import{A as H,G as A,M as V,s as L,t as T,L as E,a3 as Y,l as z}from"./index-f2ad9142.js";import{u as G,A as O}from"./AddCompanyModal-e0dc0776.js";import{b as W}from"./index.esm-be5e1c14.js";import{InteractiveButton2 as K}from"./InteractiveButton-060359e0.js";import{X}from"./XMarkIcon-cfb26fe7.js";import{t as _,S as D,H as $,L as b}from"./@headlessui/react-cdd9213e.js";import{C as J}from"./ChevronDownIcon-8b7ce98c.js";import{h as j}from"./moment-a9aaa855.js";import{A as Q}from"./AddTime-1e28f1bd.js";import"./react-scroll-9384d626.js";import{R as ee}from"./tableWrapper-ca490fb1.js";import{u as te}from"./useUpdates-fb92bb6b.js";import{A as U}from"./AcceptUpdateButton-12330f86.js";import{R as se}from"./tableWrapperDashboard-6c11f374.js";import{L as ae}from"./index-b8adfdf8.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./yup-0917e80c.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-hook-form-a383372b.js";import"./yup-342a5df4.js";import"./react-icons-36ae72b7.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./index.esm-7add6cfb.js";function I({afterRequest:l}){const[C,x]=s.useState(!1),{dispatch:r,state:f}=s.useContext(H),{dispatch:g}=s.useContext(A),[a,h]=s.useState(!1),{companies:i,refetch:v}=G(f.user),[d,c]=s.useState([]),[m,w]=s.useState(!1),M=s.useRef();async function S(){h(!0);try{const n=new V;for(const p of d){const{data:o}=await n.callRawAPI("/v4/api/records/updates",{user_id:p.user_id,company_id:p.id,status:-1},"POST");await n.callRawAPI("/v3/api/custom/goodbadugly/users/request-update",{investor_id:f.user,update_id:o,status:0,is_requested:1},"POST")}c([]),l(),x(!1),L(g,"Request sent successfully")}catch(n){T(r,n.message),L(g,n.message,5e3,"error")}h(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"flex h-[44px] items-center justify-center rounded-[2px] bg-[#1F1D1A] px-6 py-2.5 font-iowan text-lg font-medium tracking-wide text-white outline-none focus:outline-none",onClick:()=>x(!0),children:"Request Update"}),e.jsx(_,{appear:!0,show:C,as:s.Fragment,children:e.jsxs(D,{as:"div",className:"relative z-[10]",onClose:()=>x(!1),children:[e.jsx(_.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(_.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(D.Panel,{className:"w-full max-w-md transform rounded-md bg-brown-main-bg p-4 text-left align-middle text-base shadow-xl transition-all xl:p-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(D.Title,{as:"h3",className:"text-lg font-semibold leading-7 text-[#1f1d1a]  ",children:"Request an update"}),e.jsx("button",{onClick:()=>x(!1),type:"button",children:e.jsx(X,{className:"h-6 w-6"})})]}),e.jsx("p",{className:"mt-4 font-iowan text-base",children:"Select existing companies or add new ones"}),e.jsxs($,{value:d,onChange:c,multiple:!0,as:"div",className:"relative mt-5 w-full text-left",children:[e.jsxs($.Button,{ref:M,className:"relative line-clamp-1 inline-flex h-[40px] w-full truncate rounded-md border border-black/30 px-4 py-2 pr-5 text-left text-sm font-medium focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75",children:[e.jsx("span",{className:"w-[100%] truncate text-ellipsis pr-2",children:d.length>0?d.map(n=>n.name).join(", "):"Select Companies"}),e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center  p-2",children:e.jsx(J,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})})]}),e.jsx(_,{as:s.Fragment,enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:e.jsxs(B,{className:"custom-overflow absolute left-[1px] max-h-[260px] overflow-y-auto rounded bg-brown-main-bg shadow","aria-label":"Actions",onAction:n=>{const p=i.find(o=>(o==null?void 0:o.id)==n)||null;p&&c(o=>o.some(k=>k.id===p.id)?o.filter(k=>k.id!==p.id):[...o,p])},children:[i.map(n=>{const p=d.find(o=>(o==null?void 0:o.id)==(n==null?void 0:n.id))||null;return e.jsx(R,{className:"flex cursor-pointer items-center gap-2 transition-all ease-out hover:bg-black/80  hover:text-white ",children:e.jsxs("span",{className:"flex items-center gap-3",children:[n==null?void 0:n.name,p&&e.jsx(W,{})]})},n==null?void 0:n.id)}),e.jsx(R,{onClick:()=>w(!0),className:"cursor-pointer underline hover:bg-black/80 hover:text-white",children:"+ Add new Company"},null)]})})]}),e.jsx("div",{className:" mt-6 flex justify-end",children:e.jsx(K,{loading:a,disabled:a||d.length===0,onClick:S,className:"disabled:bg-disabledblack rounded-md bg-primary-black px-8 py-2 text-center font-semibold text-white transition-colors duration-100",children:"Request Update"})})]})})})})]})}),e.jsx(O,{isOpen:m,closeModal:()=>w(!1),afterCreate:()=>{v(),c([]),l()}})]})}function ne(l){const[C,x]=s.useState(!1),[r,f]=s.useState([]),{dispatch:g}=s.useContext(H),{dispatch:a}=s.useContext(A);async function h(){x(!0);try{const v=await new V().callRawAPI(`/v4/api/records/user?filter=email,in,${l.map(d=>`'${d}'`).join(",")}`);f(v.list)}catch(i){T(g,i.message),L(a,i.message,5e3,"error")}x(!1)}return s.useEffect(()=>{l.length>0?h():f([])},[l.length]),{loading:C,investors:r}}const re=[{header:"Title",accessor:"title"},{header:"Date/Time",accessor:"time/date"},{header:"Comments",accessor:"comment"}],le=({data:l})=>{var r,f,g;s.useState(5),s.useState(null);const C=Z();ne(""),(r=l==null?void 0:l.comments)==null||r.filter(a=>a.comment===""),console.log(l);const x=({row:a})=>{const[h,i]=s.useState(!1),[v,d]=s.useState(null);return e.jsx("div",{className:"relative",onMouseEnter:()=>i(!0),onMouseLeave:()=>i(!1),children:e.jsxs("div",{className:"flex -space-x-2",children:[a.comments.slice(0,3).map((c,m)=>e.jsx(b,{className:"relative",children:({open:w})=>e.jsxs(e.Fragment,{children:[e.jsx(b.Button,{as:"div",className:"object-cover relative w-8 h-8 rounded-full cursor-pointer",onMouseEnter:()=>d(c.comment_id),onMouseLeave:()=>d(null),children:e.jsx("img",{src:"/default.png",className:"object-cover w-8 h-8 rounded-full",alt:"default"})}),e.jsx(_,{as:s.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",show:v===c.comment_id,className:"absolute z-50 max-w-[300px]",children:e.jsx(b.Panel,{className:"px-4",children:e.jsx("div",{className:"px-4 py-1 text-white bg-black rounded-lg ring-1 shadow-lg ring-black/5",children:e.jsxs("div",{className:"flex flex-col gap-2 text-[10px] font-medium",children:[e.jsx("p",{className:"font-semibold",children:c.first_name?c.first_name:"  "+c.last_name?c.last_name:""}),e.jsx("p",{children:c.comment})]})})})})]})},m)),a.comments.length>3&&e.jsx(b,{className:"relative",children:({open:c})=>e.jsxs(e.Fragment,{children:[e.jsxs(b.Button,{as:"div",className:"flex relative justify-center items-center w-8 h-8 text-center text-white bg-black rounded-full cursor-pointer",onMouseEnter:()=>d("more"),onMouseLeave:()=>d(null),children:["+",a.comments.length-3]}),e.jsx(_,{as:s.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",show:v==="more",className:"absolute z-50 max-w-[300px]",children:e.jsx(b.Panel,{className:"px-4",children:e.jsx("div",{className:"px-4 py-1 text-white bg-black rounded-lg ring-1 shadow-lg ring-black/5",children:e.jsx("div",{className:"flex flex-col gap-2 text-[10px] font-medium",children:a.comments.slice(3).map((m,w)=>e.jsxs("div",{children:[e.jsx("p",{className:"font-semibold",children:m.first_name?m.first_name:"  "+m.last_name?m.last_name:""}),e.jsx("p",{children:m.comment})]},w))})})})})]})})]})})};return e.jsxs("div",{className:"custom-overflow mt-10 w-full overflow-x-auto md:mt-20 lg:px-3 lg:pl-3 xl:w-full 2xl:mt-0 2xl:w-[40%] 2xl:pl-6",children:[e.jsx("div",{className:"flex flex-row gap-4 justify-between items-center",children:e.jsx("h3",{className:"my-4  text-[16px] font-[600] sm:text-[20px] ",children:"Recent Engagement"})}),e.jsx("div",{className:"overflow-x-auto custom-overflow",children:e.jsx(ee,{children:e.jsxs("table",{className:"w-full divide-y divide-[#1f1d1a]/10 ",children:[e.jsx("thead",{className:"",children:e.jsx("tr",{className:" j border-b border-b-[#1f1d1a]/20",children:re.map((a,h)=>e.jsx("th",{scope:"col",className:`font whitespace-nowrap border-b-[#1f1d1a]/10  px-3 text-left md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3 ${a.header=="Title"?"xl:min-w-[30%]":""}`,children:a.header},h))})}),e.jsx("tbody",{className:"font-iowan-regular  divide-y divide-[#1f1d1a]/10 ",children:(f=l==null?void 0:l.comments)==null?void 0:f.map((a,h)=>{var i;return j(a.sent_at).add(a.recipient_access,"days").toDate()<new Date,a.sent_at&&j(a.sent_at).add(a.recipient_access??0,"days").diff(j(),"days"),e.jsxs("tr",{className:"border-b border-b-[#1f1d1a]/20 md:h-[97px]",children:[e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-nowrap md:px-6 md:py-6",children:e.jsx("button",{className:"cursor-pointer font-iowan font-medium text-[#292829fd] underline underline-offset-[5px] hover:underline disabled:cursor-not-allowed disabled:text-gray-400",onClick:()=>{C(`${a.update_link}`)},children:e.jsxs("span",{children:[" ",(i=a.name)!=null&&i.startsWith("Update ")&&a.name.slice(7).match(/^\d{4}-\d{2}-\d{2}$/)?`Update ${new Date(a.name.slice(7)).toLocaleString("en-US",{month:"short",day:"2-digit",year:"numeric"}).replace(/,/g,",")}`:a.name]})})}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-nowrap md:px-6 md:py-6",children:j(a.sent_at).format("MMM DD, YYYY / hh:mm A")}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:px-6 md:py-6",children:e.jsx(x,{row:a})})]},h)})})]})})}),((g=l.comments)==null?void 0:g.length)==0?e.jsxs("div",{className:"mb-[20px] mt-24 flex flex-col items-center",children:[e.jsx(E,{children:e.jsx(Y,{fill:"black",className:"!h-[5rem] !w-[5rem]"})}),e.jsx("p",{className:"mt-4 text-base font-medium text-center",children:"No Recent Engagement"})]}):null]})},ie=[{header:"Company Name",accessor:"company_name"},{header:"Update Name",accessor:"update_url"},{header:"Date received",accessor:"sent_at"},{header:"Status",accessor:"status"},{header:"Availability",accessor:"availability"},{header:"Action",accessor:""}],Ye=()=>{var o,k,q;const{dispatch:l,state:C}=s.useContext(H),{dispatch:x}=s.useContext(A),[r,f]=s.useState({}),[g,a]=s.useState(!0),h=Z(),{updates:i,refetch:v}=te("",""),d=window.location.href.split("/");s.useState(!1),s.useState(!1),s.useState(!1),s.useState(!1),s.useState(!1);const[c,m]=s.useState(!1),w=[{id:1,title:"Updates Received",data:`${r.updates_received}`,tooltip:"Number of Updates received from Companies",icon:e.jsxs("svg",{width:"25",height:"24",viewBox:"0 0 25 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("circle",{cx:"12.7998",cy:"12",r:"11.625",fill:"#990F3D",stroke:"#1F1D1A","stroke-width":"0.75"}),e.jsx("path",{d:"M9.9873 6.375H9.4248C7.7373 6.375 7.1748 7.38187 7.1748 8.625V9.1875V17.0625C7.1748 17.5294 7.70355 17.7937 8.0748 17.5125L9.03668 16.7925C9.26168 16.6238 9.57668 16.6463 9.77918 16.8488L10.7129 17.7881C10.9323 18.0075 11.2923 18.0075 11.5117 17.7881L12.4567 16.8431C12.6536 16.6463 12.9686 16.6238 13.1879 16.7925L14.1498 17.5125C14.5211 17.7881 15.0498 17.5237 15.0498 17.0625V7.5C15.0498 6.88125 15.5561 6.375 16.1748 6.375H9.9873ZM9.40793 13.1306C9.09855 13.1306 8.84543 12.8775 8.84543 12.5681C8.84543 12.2588 9.09855 12.0056 9.40793 12.0056C9.7173 12.0056 9.97043 12.2588 9.97043 12.5681C9.97043 12.8775 9.7173 13.1306 9.40793 13.1306ZM9.40793 10.8806C9.09855 10.8806 8.84543 10.6275 8.84543 10.3181C8.84543 10.0088 9.09855 9.75563 9.40793 9.75563C9.7173 9.75563 9.97043 10.0088 9.97043 10.3181C9.97043 10.6275 9.7173 10.8806 9.40793 10.8806ZM12.7998 12.99H11.1123C10.8817 12.99 10.6904 12.7988 10.6904 12.5681C10.6904 12.3375 10.8817 12.1463 11.1123 12.1463H12.7998C13.0304 12.1463 13.2217 12.3375 13.2217 12.5681C13.2217 12.7988 13.0304 12.99 12.7998 12.99ZM12.7998 10.74H11.1123C10.8817 10.74 10.6904 10.5488 10.6904 10.3181C10.6904 10.0875 10.8817 9.89625 11.1123 9.89625H12.7998C13.0304 9.89625 13.2217 10.0875 13.2217 10.3181C13.2217 10.5488 13.0304 10.74 12.7998 10.74Z",fill:"white"}),e.jsx("path",{d:"M16.1804 6.375V7.21875C16.5517 7.21875 16.9061 7.37062 17.1648 7.62375C17.4348 7.89937 17.5811 8.25375 17.5811 8.625V9.98625C17.5811 10.4025 17.3954 10.5938 16.9736 10.5938H15.8936V7.50563C15.8936 7.34813 16.0229 7.21875 16.1804 7.21875V6.375ZM16.1804 6.375C15.5561 6.375 15.0498 6.88125 15.0498 7.50563V11.4375H16.9736C17.8623 11.4375 18.4248 10.875 18.4248 9.98625V8.625C18.4248 8.00625 18.1717 7.44375 17.7667 7.03313C17.3561 6.62813 16.7992 6.38062 16.1804 6.375C16.1804 6.375 16.1861 6.375 16.1804 6.375Z",fill:"white"})]})},{id:2,title:"Recent activities",data:`${r.update_requests} `,tooltip:"Number of recent activities on Updates",icon:e.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("circle",{cx:"12",cy:"12",r:"11.625",fill:"#990F3D",stroke:"#1F1D1A","stroke-width":"0.75"}),e.jsx("path",{d:"M16.7137 15.22L16.9737 17.3267C17.0403 17.88 16.447 18.2667 15.9737 17.98L13.667 16.6067C13.507 16.5133 13.467 16.3133 13.5537 16.1533C13.887 15.54 14.067 14.8467 14.067 14.1533C14.067 11.7133 11.9737 9.72666 9.40032 9.72666C8.87365 9.72666 8.36032 9.80666 7.88032 9.96666C7.63365 10.0467 7.39365 9.82 7.45365 9.56666C8.06032 7.14 10.3937 5.33333 13.1803 5.33333C16.4337 5.33333 19.067 7.79333 19.067 10.8267C19.067 12.6267 18.1403 14.22 16.7137 15.22Z",fill:"white"}),e.jsx("path",{d:"M13.0667 14.1533C13.0667 14.9466 12.7734 15.68 12.2801 16.26C11.6201 17.06 10.5734 17.5733 9.40007 17.5733L7.66007 18.6066C7.36673 18.7866 6.9934 18.54 7.0334 18.2L7.20007 16.8866C6.30673 16.2666 5.7334 15.2733 5.7334 14.1533C5.7334 12.98 6.36007 11.9466 7.32007 11.3333C7.9134 10.9466 8.62673 10.7266 9.40007 10.7266C11.4267 10.7266 13.0667 12.26 13.0667 14.1533Z",fill:"white"})]})},{id:3,title:"Requests Accepted",data:`${`${r.accepted_requests} `}`,tooltip:"Number of request accepted by Companies",icon:e.jsxs("svg",{width:"25",height:"24",viewBox:"0 0 25 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("circle",{cx:"12.3994",cy:"12",r:"11.625",fill:"#990F3D",stroke:"#1F1D1A","stroke-width":"0.75"}),e.jsx("path",{d:"M10.333 15.6667L8.00593 15.0849C7.41477 14.9371 6.95153 15.5944 7.28954 16.1015L7.73927 16.7761C8.11021 17.3325 8.73467 17.6667 9.40337 17.6667L17.6663 17.6667L17.7227 17.4414C18.0653 16.0708 17.2795 14.6708 15.931 14.2494L12.9997 13.3333L12.4734 9.91279C12.3926 9.38765 11.9408 9 11.4095 9C10.815 9 10.333 9.48196 10.333 10.0765L10.333 15.6667Z",fill:"white",stroke:"white","stroke-width":"1.33333","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M14.5141 10.6667C14.6132 10.351 14.6667 10.0151 14.6667 9.66666C14.6667 7.82571 13.1743 6.33333 11.3333 6.33333C9.49238 6.33333 8 7.82571 8 9.66666C8 10.0151 8.05345 10.351 8.1526 10.6667",stroke:"white","stroke-width":"1.33333","stroke-linecap":"round","stroke-linejoin":"round"})]})},{id:4,title:"Accept Rate",data:r.update_requests?(r.accepted_requests*100/r.update_requests).toFixed(2)+" / 100%":"0 / 100%",tooltip:"Percentage of requests accepted by Companies",icon:e.jsxs("svg",{width:"25",height:"24",viewBox:"0 0 25 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("circle",{cx:"12.5996",cy:"12",r:"11.625",fill:"#990F3D",stroke:"#1F1D1A","stroke-width":"0.75"}),e.jsx("path",{d:"M15.6663 10.6667L12.9997 6.66667L10.333 10.6667",stroke:"white","stroke-width":"0.666667"}),e.jsx("path",{d:"M14.1121 5.33333H11.8926C11.4014 5.33333 11 5.80992 11 6.39999V6.93333C11 7.5234 11.3967 7.99999 11.8878 7.99999H14.1121C14.6033 7.99999 15 7.5234 15 6.93333V6.39999C15.0047 5.80992 14.6033 5.33333 14.1121 5.33333Z",fill:"white"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M9.66667 10C8.19391 10 7 11.1939 7 12.6667V16C7 17.4728 8.19391 18.6667 9.66667 18.6667H16.3333C17.8061 18.6667 19 17.4728 19 16V12.6667C19 11.1939 17.8061 10 16.3333 10H9.66667ZM8.51867 15.1813C8.73733 15.3013 8.976 15.3613 9.23467 15.3613C9.496 15.3613 9.73467 15.3013 9.95067 15.1813C10.1693 15.0587 10.3413 14.8893 10.4667 14.6733C10.592 14.4547 10.6547 14.2093 10.6547 13.9373C10.6547 13.6653 10.592 13.4213 10.4667 13.2053C10.3413 12.9893 10.1693 12.8213 9.95067 12.7013C9.73467 12.5787 9.496 12.5173 9.23467 12.5173C8.976 12.5173 8.73733 12.5787 8.51867 12.7013C8.30267 12.8213 8.13067 12.9893 8.00267 13.2053C7.87733 13.4213 7.81467 13.6653 7.81467 13.9373C7.81467 14.2093 7.87733 14.4547 8.00267 14.6733C8.13067 14.8893 8.30267 15.0587 8.51867 15.1813ZM9.72667 14.8413C9.58267 14.924 9.41867 14.9653 9.23467 14.9653C9.05067 14.9653 8.88667 14.924 8.74267 14.8413C8.59867 14.756 8.48533 14.636 8.40267 14.4813C8.32267 14.324 8.28267 14.1427 8.28267 13.9373C8.28267 13.732 8.32267 13.552 8.40267 13.3973C8.48533 13.2427 8.59867 13.124 8.74267 13.0413C8.88667 12.9587 9.05067 12.9173 9.23467 12.9173C9.41867 12.9173 9.58267 12.9587 9.72667 13.0413C9.87067 13.124 9.98267 13.2427 10.0627 13.3973C10.1453 13.552 10.1867 13.732 10.1867 13.9373C10.1867 14.1427 10.1453 14.324 10.0627 14.4813C9.98267 14.636 9.87067 14.756 9.72667 14.8413ZM12.9474 13.7813C13.0141 13.656 13.0474 13.5227 13.0474 13.3813C13.0474 13.2267 13.0101 13.0867 12.9354 12.9613C12.8634 12.836 12.7527 12.7373 12.6034 12.6653C12.4567 12.5907 12.2767 12.5533 12.0634 12.5533H11.1034V15.3333H11.5594V14.2053H12.0634C12.2927 14.2053 12.4807 14.1667 12.6274 14.0893C12.7741 14.0093 12.8807 13.9067 12.9474 13.7813ZM12.4514 13.7173C12.3661 13.7947 12.2367 13.8333 12.0634 13.8333H11.5594V12.9253H12.0634C12.4074 12.9253 12.5794 13.0773 12.5794 13.3813C12.5794 13.5253 12.5367 13.6373 12.4514 13.7173ZM13.9383 13.7373V12.9213H15.0183V12.5493H13.4823V15.3333H15.0183V14.9613H13.9383V14.1093H14.8983V13.7373H13.9383ZM17.4079 15.3333H17.8639V12.5493H17.4079V14.6213L16.0359 12.5493H15.5799V15.3333H16.0359V13.2573L17.4079 15.3333Z",fill:"white"})]})}];async function M(){a(!0);try{const u=await new V().callRawAPI("/v3/api/custom/goodbadugly/investor/dashboard",void 0,"GET");f(u.data)}catch(t){T(l,t.message),L(x,t.message,5e3,"error")}a(!1)}s.useEffect(()=>{x({type:"SETPATH",payload:{path:"dashboard"}}),M()},[]);const S=t=>{navigator.clipboard.writeText(t),L(x,"Link Copied")};(o=r==null?void 0:r.updates)==null||o.filter(t=>t.view_count==0);const n=new Date().getHours();let p;return n<12?p="Good morning":n<18?p="Good afternoon":p="Good evening",g?e.jsx("div",{className:"flex flex-row justify-center items-center h-screen",children:e.jsx(ae,{size:40})}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"p-5 pt-8 md:px-8",children:[e.jsxs("div",{className:"flex flex-col gap-3 justify-between items-start sm:flex-row",children:[e.jsxs("h2",{className:"text-xl font-iowan sm:text-xl md:text-lgheader",children:[e.jsxs("span",{className:"font-bold font-iowan",children:[p,", ",C.profile.first_name,"!"]})," ",e.jsx("br",{})," ",e.jsx("span",{className:"font-iowan-regular  text-[18px] font-normal",children:"Your portfolio company update overview"})]}),((k=r.updates)==null?void 0:k.length)>0?e.jsx(I,{afterRequest:()=>M()}):null]}),e.jsx("div",{className:"grid relative mt-8 w-full sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4",children:w.map((t,u)=>e.jsxs("div",{className:"relative",children:[" ",e.jsx("div",{className:` w-full ${u===w.length-1?"border-r-0 sm:border-r-[3px] lg:border-r-transparent":"border-r-0 sm:border-r-[3px]"}  h-[90px] border-[#1f1d1a] p-1 `,children:e.jsxs("div",{className:"flex flex-row gap-2 justify-between p-4 xl:p-1 2xl:p-3",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("p",{className:"whitespace-nowrap font-iowan  text-[12px] font-[700] uppercase sm:text-[14px] xl:text-[14px] 2xl:text-[14px]",children:t.title}),e.jsx("p",{className:"mt-5 text-center font-iowan text-[24px] font-[700] sm:text-[28px] xl:text-[20px] 2xl:text-[28px]",children:t==null?void 0:t.data})]}),e.jsxs("span",{className:`relative cursor-pointer ${u==1&&"2xl:-ml-3"}`,onMouseEnter:()=>m(t.id),onMouseLeave:()=>m(!1),children:[e.jsx(b,{className:"relative z-[6]",children:({open:y})=>e.jsx(e.Fragment,{children:e.jsx(_,{as:s.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",show:t.id===c,onMouseEnter:()=>m(t.id),onMouseLeave:()=>m(!1),children:e.jsx(b.Panel,{className:`absolute bottom-0  z-[6] mt-2 w-fit  -translate-x-[100%] transform px-2      ${u==0&&"xl:translate-x-[5px]  xl:translate-y-[-10px] 2xl:-translate-x-[100%] 2xl:translate-y-0"}`,children:e.jsx("div",{className:"custom-overflow relative w-[170px] overflow-auto rounded-lg bg-[#1f1d1a] p-2 text-white shadow-lg ring-1 ring-[#1f1d1a]/5 md:w-[300px]",children:e.jsx("div",{className:" text-[12px] font-medium",children:t.tooltip})})})})})}),t.icon]})]})},t.id)]},u))}),e.jsx("hr",{className:"mt-[32px] hidden border-[2px] border-[#1f1d1a] sm:block"}),e.jsx("div",{className:"p-5 px-0 mt-8 w-full rounded",children:e.jsxs("div",{className:"flex flex-col justify-between 2xl:flex-row",children:[e.jsxs("div",{className:"w-full max-w-full lg:pr-3 xl:max-w-full xl:border-r-[#1f1d1a] 2xl:w-[60%] 2xl:border-r-[3px] 2xl:pr-6",children:[e.jsx("div",{className:"flex flex-row justify-between items-center",children:e.jsx("h3",{className:"my-4  text-[16px] font-[600] sm:text-[20px]",children:"Most Recent Activity"})}),e.jsx("div",{className:"overflow-x-auto pb-2 w-full custom-overflow",children:e.jsx(se,{children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10  ",children:[e.jsx("thead",{className:"",children:e.jsx("tr",{className:"border-b-[#1f1d1a]/20",children:ie.map((t,u)=>e.jsx("th",{scope:"col",className:"font whitespace-normal  border-b-[#1f1d1a]/10 px-3 text-left capitalize tracking-wider  text-[#1f1d1a] md:whitespace-nowrap md:border-0 md:border-b-[3px]  md:border-dashed md:px-6 md:py-3",children:t.header},u))})}),e.jsx("tbody",{className:"font-iowan-regular  w-full divide-y divide-[#1f1d1a]/10",children:i==null?void 0:i.filter((t,u,y)=>u===y.findIndex(N=>N.id===t.id)).reverse().map(t=>{const u=r.updates.find(P=>P.update_id===t.update_id),y=u.public_link_id?t.update_link:`/reports/view/${t.update_id}`;console.log(y,t.update_id,t,u);const N=j(t.sent_at).add(t.recipient_access,"days").toDate()<new Date,F=t.sent_at?j(t.sent_at).add(t.recipient_access??0,"days").diff(j(),"days"):0;return e.jsxs("tr",{className:"  md:h-[60px]",children:[e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:t.company_name}),e.jsx("td",{className:"px-6 py-6 cursor-pointer md:max-w-[500px]",children:e.jsxs("button",{className:"flex cursor-pointer items-center justify-start whitespace-nowrap font-iowan  font-medium text-[#292829fd] underline underline-offset-[5px] hover:underline disabled:cursor-not-allowed disabled:text-gray-400 md:justify-center",onClick:()=>{h(y)},disabled:(N||t.request===0)&&!t.is_requested,children:[" ",t.update_name.startsWith("Update ")&&t.update_name.slice(7).match(/^\d{4}-\d{2}-\d{2}$/)?`Update ${new Date(t.update_name.slice(7)).toLocaleString("en-US",{month:"short",day:"2-digit",year:"numeric"}).replace(/,/g,",")}`:t.update_name]})}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:j(t.sent_at).format("MMM DD, YYYY")}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:t.view_count==0?e.jsx("span",{className:"text-green-500",children:"New"}):`Viewed(${t.view_count})`}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:N?e.jsx("span",{children:"0 hrs / 0 minutes (update expired)"}):e.jsxs("span",{children:[F<1?"":`${F} days,  `,t.sent_at?j(t.sent_at).add(t.recipient_access,"days").diff(j().add(F,"days"),"hours"):0," ","hrs"]})}),e.jsx("td",{className:"flex flex-col gap-4 justify-center items-start px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex flex-row gap-4 items-start md:justify-center",children:[e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("button",{className:"cursor-pointer text-[#292829fd] underline hover:underline disabled:cursor-not-allowed disabled:text-gray-400",onClick:()=>{h(y)},disabled:(N||t.request===0)&&!t.is_requested,children:e.jsx("span",{children:"View"})}),e.jsx("button",{className:"cursor-pointer px-0 text-[#292829fd]  underline hover:underline disabled:cursor-not-allowed disabled:text-gray-400",onClick:()=>{S(`${d[2]}/${y}`)},disabled:N||t.request===0||t.public_link_enabled==0,children:e.jsx("span",{children:"Share"})}),N?e.jsx("button",{className:"cursor-pointer px-0 font-medium text-[#292829fd] hover:underline disabled:text-[#1f1d1a]",onClick:()=>{},children:e.jsx("span",{className:"text-green-500",children:e.jsx(Q,{data:t})})}):null]}),t.request===0&&t.is_requested!==1&&e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx(U,{refetch:v,row:t}),e.jsx(U,{reject:!0,refetch:v,row:t})]})]})})]},t.id)})})]})})}),((q=r.updates)==null?void 0:q.length)==0?e.jsxs("div",{className:"mb-[20px] mt-24 flex flex-col items-center",children:[e.jsx(E,{children:e.jsx(z,{fill:"black",className:"!h-[5rem] !w-[5rem]"})}),e.jsx("p",{className:"mt-4 text-lg",children:"No Company Updates"}),e.jsx("p",{className:"mt-2 mb-2 text-sm text-center",children:"Keep your stakeholders up to date with company updates"}),e.jsx(I,{afterRequest:()=>M()})]}):null]}),e.jsx(le,{data:r})]})})]})})};export{Ye as default};
