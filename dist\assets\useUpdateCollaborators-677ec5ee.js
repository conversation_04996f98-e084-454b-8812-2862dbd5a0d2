import{A as d,G as f,M as h,t as b,s as C}from"./index-f2ad9142.js";import{r as t}from"./vendor-4cdf2bd1.js";function g(o,r){const[l,s]=t.useState(!1),[c,n]=t.useState([]),{dispatch:i}=t.useContext(d),{dispatch:u}=t.useContext(f);async function e(){s(!0);try{const p=await new h().callRawAPI(`/v4/api/records/update_collaborators?filter=update_id,eq,${o}&join=user|collaborator_id&filter=note_id,eq,${r}`);n(p.list)}catch(a){b(i,a.message),C(u,a.message,5e3,"error")}s(!1)}return t.useEffect(()=>{o&&e()},[o]),{loading:l,updateCollaborators:c,refetch:e}}export{g as u};
