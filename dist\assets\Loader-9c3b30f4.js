import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{L as e}from"./index-23a711b5.js";import{G as m}from"./index-f2ad9142.js";import{r as p}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const G=({style:o,className:r=""})=>{const{state:i,dispatch:s}=p.useContext(m);return t.jsx("div",{style:{display:"flex",width:"100%",justifyContent:"center",alignItems:"center",...o},className:`h-screen max-h-screen ${i.isOpen?"":"w-[calc(100vw-80px)]"} ${r}`,children:t.jsx(e,{})})};export{G as default};
