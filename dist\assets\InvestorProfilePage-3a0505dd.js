import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as F,r}from"./vendor-4cdf2bd1.js";import{u as oe}from"./react-hook-form-a383372b.js";import{o as le}from"./yup-0917e80c.js";import{c as ne,a as re}from"./yup-342a5df4.js";import{M as de,G as V,I,t as ee,s as P}from"./index-f2ad9142.js";import me from"./ModalPrompt-6520dab5.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";let v=new de;const Re=()=>{var K,J,Q,X,Y;const T=ne({email:re().email().required()}).required(),{dispatch:c}=F.useContext(V),[g,$]=r.useState(""),[d,M]=F.useState({}),[A,_]=r.useState(!1),[R,o]=r.useState(!1),[y,p]=r.useState(!1),[Z,f]=r.useState(!1),[N,w]=r.useState("");r.useState("");const[x,u]=r.useState(!1),[j,E]=r.useState("Profile"),[t,U]=r.useState({}),{dispatch:se}=F.useContext(V),{register:D,handleSubmit:C,setError:k,setValue:z,formState:{errors:S}}=oe({resolver:le(T)}),te=(s,i,b=!1)=>{let a=d;console.log(i),b?a[s]?a[s]=[...a[s],{file:i.files[0],tempFile:{url:URL.createObjectURL(i.files[0]),name:i.files[0].name,type:i.files[0].type}}]:a[s]=[{file:i.files[0],tempFile:{url:URL.createObjectURL(i.files[0]),name:i.files[0].name,type:i.files[0].type}}]:a[s]={file:i.files[0],tempURL:URL.createObjectURL(i.files[0])},M({...a})};async function H(){try{const s=await v.getProfile();U(s),z("email",s==null?void 0:s.email),z("first_name",s==null?void 0:s.first_name),z("last_name",s==null?void 0:s.last_name),$(s==null?void 0:s.email),w(s==null?void 0:s.photo)}catch(s){console.log("Error",s),ee(c,s.response.data.message?s.response.data.message:s.message)}}const L=async s=>{var i,b;U(s);try{if(u(!0),d&&d.photo&&((i=d.photo)!=null&&i.file)){let l=new FormData;l.append("file",(b=d.photo)==null?void 0:b.file);let n=await v.uploadImage(l);console.log("uploadResult"),console.log(n),s.photo=n.url,P(c,"Profile Photo Updated",1e3)}const a=await v.updateProfile({first_name:s.first_name||(t==null?void 0:t.first_name),last_name:s.last_name||(t==null?void 0:t.last_name),photo:s.photo||N});if(!a.error)P(c,"Profile Updated",4e3),h();else{if(a.validation){const l=Object.keys(a.validation);for(let n=0;n<l.length;n++){const m=l[n];k(m,{type:"manual",message:a.validation[m]})}}h()}if(g!==s.email){const l=await v.updateEmail(s.email);if(!l.error)P(c,"Email Updated",1e3);else if(l.validation){const n=Object.keys(l.validation);for(let m=0;m<n.length;m++){const O=n[m];k(O,{type:"manual",message:l.validation[O]})}}h()}if(s.password.length>0){const l=await v.updatePassword(s.password);if(!l.error)P(c,"Password Updated",2e3);else if(l.validation){const n=Object.keys(l.validation);for(let m=0;m<n.length;m++){const O=n[m];k(O,{type:"manual",message:l.validation[O]})}}}await H(),u(!1)}catch(a){u(!1),console.log("Error",a),k("email",{type:"manual",message:a.response.data.message?a.response.data.message:a.message}),ee(c,a.response.data.message?a.response.data.message:a.message)}};F.useEffect(()=>{se({type:"SETPATH",payload:{path:"profile"}}),H()},[]);const ae=()=>{_(!0)},q=()=>{o(!0)},G=()=>{p(!0)},W=()=>{f(!0)},h=()=>{_(!1),o(!1),p(!1),f(!1)},ie=async()=>{try{u(!0);const s=await v.updateProfile({first_name:t==null?void 0:t.first_name,last_name:t==null?void 0:t.last_name,photo:""});if(!s.error)P(c,"Profile Picture Deleted",1e3);else if(s.validation){const i=Object.keys(s.validation);for(let b=0;b<i.length;b++){const a=i[b];k(a,{type:"manual",message:s.validation[a]})}}await H(),u(!1),h()}catch(s){u(!1),console.log("Error",s)}};return e.jsxs("div",{className:"mt-6 w-10/12 rounded-md border",children:[e.jsx("div",{className:"flex items-center border-b border-b-[#E0E0E0] px-8 py-3 text-[#8D8D8D]",children:e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsx("div",{className:`cursor-pointer rounded-lg px-3 py-1 ${j==="Profile"?"bg-[#f4f4f4] text-[#1f1d1a]":""} `,onClick:()=>E("Profile"),children:"Profile"}),e.jsx("div",{className:`cursor-pointer rounded-lg px-3 py-1 ${j==="Security"?"bg-[#f4f4f4] text-[#1f1d1a]":""} `,onClick:()=>E("Security"),children:"Security"})]})}),e.jsxs("main",{children:[j==="Profile"&&e.jsx("div",{className:"rounded bg-brown-main-bg",children:e.jsxs("form",{onSubmit:C(L),children:[e.jsxs("div",{className:"mb-8 flex items-center border-b border-b-[#E0E0E0] px-10",children:[e.jsxs("div",{className:"relative mb-4 h-[100px] w-fit py-5",children:[d&&((K=d.photo)!=null&&K.tempURL)||N?e.jsx("div",{className:"flex h-[80px] w-[80px] items-center rounded-2xl",children:e.jsx("img",{className:"h-[80px] w-[80px] rounded-2xl object-cover",src:((J=d.photo)==null?void 0:J.tempURL)||N,alt:""})}):null,N||(Q=d.photo)!=null&&Q.file?null:e.jsx("div",{className:"flex h-[80px] w-full items-center justify-center bg-slate-300",children:"Select a picture"}),e.jsx("input",{className:"focus:shadow-outline absolute left-0 top-0 h-full w-[100px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] opacity-0   shadow focus:outline-none sm:w-[180px]",id:"photo",type:"file",placeholder:"Photo",name:"photo",onChange:s=>te("photo",s.target)}),e.jsx("p",{className:"text-xs italic text-red-500",children:(X=S.photo)==null?void 0:X.message})]}),e.jsx("div",{className:"ml-6	mr-4	text-sm font-semibold text-indigo-600",children:e.jsx(I,{type:"submit",loading:x,disabled:x,children:"Update"})}),e.jsx("div",{className:"ml-3	cursor-pointer	text-sm font-semibold text-gray-600",onClick:ae,children:"Remove"})]}),e.jsxs("div",{className:"mx-10 max-w-lg",children:[e.jsx("p",{className:"mb-3	text-base	font-medium text-gray-900",children:"Personal Details"}),e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-x-20",children:[e.jsx("p",{className:"text-base	font-medium	text-gray-600",children:"First Name"}),e.jsx("p",{className:"text-base	font-medium	text-gray-900",children:t==null?void 0:t.first_name})]}),e.jsx("p",{className:"cursor-pointer	text-base	font-semibold text-indigo-600",onClick:q,children:"Edit"})]}),e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-x-20",children:[e.jsx("p",{className:"text-base	font-medium	text-gray-600",children:"Last Name"}),e.jsx("p",{className:"text-base	font-medium	text-gray-900",children:t==null?void 0:t.last_name})]}),e.jsx("p",{className:"cursor-pointer	text-base	font-semibold text-indigo-600",onClick:G,children:"Edit"})]}),e.jsxs("div",{className:"mb-6 flex items-center justify-between text-left",children:[e.jsxs("div",{className:"flex items-center gap-x-2",children:[e.jsx("p",{className:"mr-28	text-base	font-medium text-gray-600",children:"Email"}),e.jsx("p",{className:"text-base	font-medium	text-gray-900",children:g})]}),e.jsx("p",{className:"cursor-pointer	text-base	font-semibold text-indigo-600",onClick:W,children:"Edit"})]})]})]})}),j==="Security"&&e.jsx("div",{className:"rounded bg-brown-main-bg px-10 py-6",children:e.jsx("form",{onSubmit:C(L),className:"max-w-lg",children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold capitalize capitalize text-gray-700",children:"Password"}),e.jsx("input",{...D("password"),name:"password",className:"focus:shadow-outline w-[100px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] shadow   focus:outline-none sm:w-[180px]",id:"password",type:"password",placeholder:"******************"}),e.jsx("p",{className:"text-xs italic text-red-500",children:(Y=S.password)==null?void 0:Y.message})]}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(I,{className:"focus:shadow-outline rounded bg-indigo-600 px-4 py-2 font-bold text-white hover:bg-indigo-600 focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:x,disabled:x,children:"Update"})})]})})}),A&&e.jsx(me,{actionHandler:ie,closeModalFunction:h,title:"Are you sure ? ",message:"Are you sure you want to delete profile picture ? ",acceptText:"DELETE",rejectText:"CANCEL"}),R&&e.jsx(B,{title:"Edit information",label:"First Name",buttonName:"Save and close",isOpen:q,onClose:h,handleSubmit:C,onSubmit:L,register:D,id:"first_name",submitLoading:x,errors:S}),y&&e.jsx(B,{title:"Edit information",label:"Last Name",buttonName:"Save and close",isOpen:G,onClose:h,handleSubmit:C,onSubmit:L,register:D,id:"last_name",submitLoading:x,errors:S}),Z&&e.jsx(B,{title:"Change Email",label:"Email",buttonName:"Submit",isOpen:W,onClose:h,handleSubmit:C,onSubmit:L,register:D,id:"email",submitLoading:x,errors:S,defaultValues:t})]})]})},B=T=>{var j,E;const{title:c,label:g,buttonName:$,isOpen:d,onClose:M,handleSubmit:A,onSubmit:_,register:R,id:o,submitLoading:y,errors:p,defaultValues:Z}=T,[f,N]=r.useState(!1),[w,x]=r.useState({email:""}),u=t=>U=>{t==="email"&&x({...w,[t]:U.target.value})};return e.jsx("div",{className:"fixed inset-0 z-10 overflow-y-auto",children:e.jsx("div",{className:`fixed inset-0 z-10 overflow-y-auto ${d?"block":"hidden"} `,children:e.jsxs("div",{className:"flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 transition-opacity",children:e.jsx("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),e.jsx("span",{className:"hidden sm:inline-block sm:h-screen sm:align-middle","aria-hidden":"true",children:"​"}),e.jsxs("div",{className:"inline-block transform overflow-hidden rounded-lg bg-brown-main-bg px-4 pb-4 pt-5 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-md sm:p-6 sm:align-middle",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-lg font-semibold leading-6 text-gray-900",children:c}),e.jsx("button",{className:"text-gray-500 hover:text-gray-700 focus:outline-none",onClick:M,children:e.jsx("svg",{className:"h-6 w-6",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("form",{onSubmit:A(_),className:"max-w-lg",children:[f===!0&&e.jsxs("div",{className:"mt-3 flex",children:[e.jsx("div",{className:"mr-2",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.0003 1.66663C5.39795 1.66663 1.66699 5.39759 1.66699 9.99996C1.66699 14.6023 5.39795 18.3333 10.0003 18.3333C14.6027 18.3333 18.3337 14.6023 18.3337 9.99996C18.3337 5.39759 14.6027 1.66663 10.0003 1.66663ZM8.33366 9.16663C8.33366 8.82145 8.61348 8.54163 8.95866 8.54163H10.0003C10.3455 8.54163 10.6253 8.82145 10.6253 9.16663L10.6253 13.5416C10.6253 13.8868 10.3455 14.1666 10.0003 14.1666C9.65515 14.1666 9.37533 13.8868 9.37533 13.5416L9.37532 9.79163H8.95866C8.61348 9.79163 8.33366 9.5118 8.33366 9.16663ZM10.0003 6.04163C9.65515 6.04163 9.37533 6.32145 9.37533 6.66663C9.37533 7.0118 9.65515 7.29163 10.0003 7.29163C10.3455 7.29163 10.6253 7.0118 10.6253 6.66663C10.6253 6.32145 10.3455 6.04163 10.0003 6.04163Z",fill:"#4F46E5"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-1	text-sm	font-medium text-gray-600",children:"We've send an email to:"}),e.jsx("p",{className:"mb-2	text-sm	font-semibold text-gray-900",children:w==null?void 0:w.email}),e.jsx("p",{className:"mb-2	text-sm	font-medium text-gray-600",children:"In order to complete the email update click the confirmation link."}),e.jsx("p",{className:"mb-2	text-sm	font-medium text-gray-600",children:"(the link expires in 24 hours)"})]})]}),f===!1&&(o==="first_name"||o==="last_name")&&e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{htmlFor:"firstName",className:"mb-1 block text-sm font-medium text-gray-700",children:g}),e.jsx("input",{className:"focus:shadow-outline w-[100px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] shadow   focus:outline-none sm:w-[180px]",id:o,type:"text",placeholder:`Enter ${g} `,name:o,...R(o)}),e.jsx("p",{className:"text-xs italic text-red-500",children:(j=p==null?void 0:p.id)==null?void 0:j.message})]}),f===!1&&o==="email"&&e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{htmlFor:"firstName",className:"mb-1 block text-sm font-medium text-gray-700",children:g}),e.jsx("input",{className:"focus:shadow-outline w-[100px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] shadow   focus:outline-none sm:w-[180px]",id:o,type:"text",placeholder:`Enter ${g}`,name:o,...R(o),onChange:u("email")}),e.jsx("p",{className:"text-xs italic text-red-500",children:(E=p==null?void 0:p.id)==null?void 0:E.message})]}),e.jsxs("div",{className:"mt-4 flex justify-between",children:[e.jsx("button",{className:"mr-2 w-full rounded-md border border-black/60 px-4 py-2 text-gray-700	",onClick:M,children:"Cancel"}),(o==="first_name"||o==="last_name"||f===!0)&&e.jsx(I,{className:"focus:shadow-outline w-full rounded-md bg-indigo-500 px-4 py-2 font-bold text-white hover:bg-indigo-600 focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:y,disabled:y,children:$}),o==="email"&&!f&&e.jsx(I,{className:"focus:shadow-outline w-full rounded-md bg-indigo-500 px-4 py-2 font-bold text-white hover:bg-indigo-600 focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:y,disabled:y,onClick:()=>N(!0),children:"Submit"})]})]})]})]})})})};export{B as EditInfoModal,Re as default};
