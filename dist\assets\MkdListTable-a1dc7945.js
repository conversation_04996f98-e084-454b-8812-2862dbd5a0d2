import{j as l}from"./@nextui-org/listbox-0f38ca19.js";import{r as o,R as n,u as be,b as Ee}from"./vendor-4cdf2bd1.js";import{A as Fe,a as Ce,G as Ge,L as le,f as Je,$ as Ke}from"./index-f2ad9142.js";import Be from"./MkdListTableRowCol-90dbfa63.js";import{_ as He}from"./qr-scanner-cf010ec4.js";import{c as Ue,d as Ve}from"./index-d07d87ac.js";import{D as We}from"./lucide-react-0b94883e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./MkdPopover-4a64f030.js";import"./react-tooltip-a338585f.js";import"./@mantine/core-38f49ae4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const Xe=o.lazy(()=>He(()=>import("./NoteModal-a92fe5a5.js"),["assets/NoteModal-a92fe5a5.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/AddButton-51d1b2cd.js"])),Ye=({table:qe,tableTitle:Qe,onSort:he,loading:O,setLoading:Ze,columns:F=[],actions:e,actionPostion:v=[],tableRole:fe,deleteItem:Pe,deleteLoading:Ie,actionId:k="id",showDeleteModal:Te,currentTableData:h=[],setShowDeleteModal:ie,handleTableCellChange:me,setSelectedItems:j,allowEditing:de,useImage:C=!0,columnData:i=null,setColumns:ce=null,setColumnData:G=null,selectedItems:x=[],allowSortColumns:t=!0,onPopoverStateChange:xe=null,popoverShown:De=!1,maxHeight:ae=null,noDataComponent:y={use:!1,component:l.jsx(l.Fragment,{})},showYScrollbar:es=!0,showXScrollbar:ss=!0,showScrollbar:we=!0})=>{var V,W,X,Y,q,Q,Z,P,I,T,c,D,a,ee,se,re;n.useContext(Fe);const{globalState:R}=Ce();n.useContext(Ge);const ge=be(),[rs,ne]=n.useState(null),[ls,ue]=n.useState(!1),[hs,A]=n.useState(!1),[pe,b]=n.useState(!1),[$,E]=n.useState(null),[M,z]=n.useState(null),[fs,oe]=n.useState(0),[je,J]=n.useState(null),[te,K]=n.useState(null),[ve,is]=n.useState({key:null,held:!1,startingKey:!1}),Ne=s=>{J(s),K(!0)},ke=o.useMemo(()=>JSON.stringify(h),[h]),ye=o.useMemo(()=>JSON.stringify(i==null?void 0:i.columns),[i]),_=o.useMemo(()=>i==null?void 0:i.columns.find(s=>(s==null?void 0:s.accessor)==="row"),[i]),B=o.useMemo(()=>{var s,r;return F.find(f=>f.accessor==="")&&((s=e==null?void 0:e.delete)==null?void 0:s.show)||((r=Object.keys(e).filter(f=>{var m,w,g,d,p,S,L,u;return((m=e[f])==null?void 0:m.show)&&((w=e[f])==null?void 0:w.locations)&&((d=(g=e[f])==null?void 0:g.locations)==null?void 0:d.length)&&(((S=(p=e[f])==null?void 0:p.locations)==null?void 0:S.includes("dropdown"))||((u=(L=e[f])==null?void 0:L.locations)==null?void 0:u.includes("buttons")))}))==null?void 0:r.length)},[F,e]),Se=o.useCallback(s=>s.length>1?h.findIndex(f=>(f==null?void 0:f.id)==(s==null?void 0:s[(s==null?void 0:s.length)-1])):s.length==1?h.findIndex(f=>(f==null?void 0:f.id)==(s==null?void 0:s[0])):null,[h]),Le=o.useCallback((s,r)=>{const f=s<r?s:r,m=s>r?s:r,w=f==r?f+1:f,g=m==r?m-1:m;return{start:w,end:g,lower:f,upper:m}},[]),Me=o.useCallback((s,r,f)=>{var w,g;const m=Se(f);if(m!==null){const{lower:d,upper:p,start:S,end:L}=Le(r,m),u=[...x];for(let N=d;N<=p;N++)u.push((w=h[N])==null?void 0:w.id);j(u)}else j([(g=h==null?void 0:h[r])==null?void 0:g.id])},[ve,h]);function H(s,r,f){var w,g,d,p,S,L;const m=x;if((w=e==null?void 0:e.select)!=null&&w.multiple)if((g=f==null?void 0:f.nativeEvent)!=null&&g.shiftKey){if((d=e==null?void 0:e.select)!=null&&d.max&&((p=e==null?void 0:e.select)==null?void 0:p.max)==(x==null?void 0:x.length))return;Me(s,r,m)}else if(m.includes(s)){const u=m.filter(N=>N!==s);j(u)}else{if((S=e==null?void 0:e.select)!=null&&S.max&&((L=e==null?void 0:e.select)==null?void 0:L.max)==(x==null?void 0:x.length))return;const u=[...m,s];j(u)}else if(m.includes(s)){const u=m.filter(N=>N!==s);j(u)}else j([s])}const $e=()=>{if(h.every(r=>x.includes(r==null?void 0:r.id)))j([]);else{const r=h.map(f=>f[k]);j(r)}};Ee();const U=async s=>{ie(!0),ne(s)},ze=(s,r)=>{t&&(E(r),b(!0))},_e=s=>{if(t){if(s.preventDefault(),$&&M&&$!=M){const r=[...i==null?void 0:i.columns],f=r[$];r[M],r.splice($,1),r.splice(M,0,f),G&&G(m=>({...m,columns:r}))}z(null),E(null),b(!1)}},Oe=(s,r)=>{t&&(s.preventDefault(),z(r))},Re=s=>{t&&(s.preventDefault(),z(null),E(null),b(!1))},Ae=s=>{t&&(s.preventDefault(),z(null))};return n.useEffect(()=>{x.length<=0&&(ue(!1),A(!1)),x.length===(h==null?void 0:h.length)&&A(!0),x.length<(h==null?void 0:h.length)&&x.length>0&&A(!1)},[x==null?void 0:x.length,ke]),n.useEffect(()=>{var r;const s=(r=i==null?void 0:i.columns)==null?void 0:r.reduce((f,m)=>!["row",""].includes(m==null?void 0:m.accessor)&&(m!=null&&m.selected_column)?f+1:f,0);oe(s)},[ye]),l.jsx(le,{count:7,counts:[2,2,2,2,2,2],children:l.jsxs("div",{className:`team-table relative !h-full !max-h-full !min-h-full  !w-full min-w-full max-w-full justify-center overflow-auto !rounded-[.625rem] bg-transparent ${ge.pathname.includes("/teams")?`  ${R!=null&&R.isOpen?"overflow-auto":" overflow-auto xl:overflow-auto 2xl:[overflow:unset_!important]"}`:""}  ${we?"":"scrollbar-hide"}`,children:[O&&C||O&&(!(h!=null&&h.length)||!((V=i==null?void 0:i.columns)!=null&&V.length))?l.jsx("div",{className:"justify-center items-center w-full max-h-fit min-h-fit",children:l.jsx(Je,{count:7,counts:[2,2,2,2,2,2]})}):(W=i==null?void 0:i.columns)!=null&&W.length&&(h!=null&&h.length)?l.jsxs("table",{className:" h-fit min-w-full divide-y divide-[#1F1D1A1A] rounded-md",children:[l.jsx("thead",{className:"bg-brown-main-bg",children:l.jsxs("tr",{className:"!h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem]",children:[[(X=e==null?void 0:e.select)==null?void 0:X.show].includes(!0)||_?l.jsxs(l.Fragment,{children:[[(Y=e==null?void 0:e.select)==null?void 0:Y.show].includes(!0)?l.jsx("th",{className:"$ sticky -left-[0.05rem] -top-[0.05rem] z-[19] !h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem] !w-[2.65rem] !min-w-[2.65rem] !max-w-[2.65rem] bg-brown-main-bg  px-[.75rem] py-[.5rem] text-xs font-medium capitalize tracking-wider text-black",children:(q=e==null?void 0:e.select)!=null&&q.multiple&&!((Q=e==null?void 0:e.select)!=null&&Q.max)||(Z=e==null?void 0:e.select)!=null&&Z.multiple&&((P=e==null?void 0:e.select)!=null&&P.max)&&((I=e==null?void 0:e.select)==null?void 0:I.max)>=(h==null?void 0:h.length)?l.jsx("input",{type:"checkbox",disabled:!((T=e==null?void 0:e.select)!=null&&T.multiple)||((c=e==null?void 0:e.select)==null?void 0:c.max)&&((D=e==null?void 0:e.select)==null?void 0:D.max)<(h==null?void 0:h.length),id:"select_all_rows",className:"focus:shadow-outline focus:shadow-outline  mr-1 !h-4 !w-4 cursor-pointer  appearance-none  rounded-[.125rem]  text-[.8125rem]  text-sm font-normal leading-tight text-black shadow focus:outline-none focus:ring-0 sm:!text-base",checked:(x==null?void 0:x.length)===(h==null?void 0:h.length),onChange:((a=e==null?void 0:e.select)==null?void 0:a.max)&&((ee=e==null?void 0:e.select)==null?void 0:ee.max)>=(h==null?void 0:h.length)&&$e}):null}):null,_?l.jsx("th",{className:`$ sticky -top-[0.05rem] ${[(se=e==null?void 0:e.select)==null?void 0:se.show].includes(!0)?"left-10":"left-0"} z-10 !h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem] !w-[2.65rem] !min-w-[2.65rem] max-w-[auto] bg-brown-main-bg  px-[.75rem] py-[.5rem] text-left text-[1.125rem] font-medium capitalize tracking-wider text-black`,children:"Row"}):null]}):null,(re=i==null?void 0:i.columns)==null?void 0:re.map((s,r)=>{var f,m,w;if(!["row",""].includes(s==null?void 0:s.accessor)&&(s!=null&&s.selected_column)){const g=[0,"0"].includes(r)&&[(f=e==null?void 0:e.select)==null?void 0:f.show].includes(!1);return l.jsx("th",{draggable:t,onDragStart:d=>ze(d,r),onDragEnd:Re,onDragOver:d=>Oe(d,r),onDragLeave:d=>Ae(d),onDrop:d=>_e(d),scope:"col",className:`$ sticky -top-[0.05rem] z-[5] !h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem] !w-[auto] !min-w-[6.25rem] !max-w-[auto] shrink-0 grow  bg-brown-main-bg py-[.5rem] pr-6 text-left font-iowan text-[1.125rem] font-[700] capitalize leading-[1.25rem] tracking-wider text-black  ${t&&pe?"cursor-grabbing":s!=null&&s.isSorted?"cursor-pointer":""} ${M==r?"bg-primary-light":"bg-weak-100"} ${(s==null?void 0:s.size)=="reduce"?"!w-[auto] !min-w-[3rem] !max-w-[3rem] ":""} ${g?"pl-0":"pl-6"} `,children:l.jsxs("div",{className:"flex gap-5 justify-between items-center w-full",children:[l.jsxs("div",{className:"flex gap-5 justify-between items-center grow",onClick:s!=null&&s.isSorted?()=>he(r):void 0,children:[l.jsx("div",{className:"w-auto capitalize whitespace-nowrap grow",children:(w=(m=s==null?void 0:s.header)==null?void 0:m.split("_"))==null?void 0:w.join(" ")}),l.jsx("span",{className:"invisible w-fit",children:s.isSorted?l.jsx(Ke,{className:`h-2 w-2 ${s.isSortedDesc?"rotate-180":""}`}):null})]}),t?l.jsx(We,{className:"w-2 h-2 min-w-2 max-w-2 cursor-grab"}):null]})},r)}}),B?l.jsx("th",{className:"$ relative -right-[0.05rem] -top-[0.05rem] z-10 !h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem] !w-fit !min-w-fit max-w-fit shrink-0 grow bg-brown-main-bg  px-[.75rem] py-[.5rem] text-left font-iowan text-[1.125rem]  font-[700]  capitalize tracking-wider text-black",children:"Action"}):null]})}),l.jsx("tbody",{className:"divide-y divide-brown-main-bg bg-brown-main-bg",children:h==null?void 0:h.map((s,r)=>{var f,m,w,g;return l.jsxs("tr",{className:`!h-[4rem] !max-h-[4rem] !min-h-[4rem] ${s.isChild,""}`,children:[[(f=e==null?void 0:e.select)==null?void 0:f.show].includes(!0)||_?l.jsxs(l.Fragment,{children:[[(m=e==null?void 0:e.select)==null?void 0:m.show].includes(!0)?l.jsx("td",{className:"text-sub-500 sticky -left-[0.05rem] z-10 !h-full !max-h-full !min-h-full  !w-[2.65rem] !min-w-[2.65rem] !max-w-[2.65rem] cursor-pointer whitespace-nowrap bg-brown-main-bg px-[.75rem] py-[.5rem] text-sm font-[400] capitalize leading-[1.5rem] tracking-wider",children:l.jsx("input",{type:"checkbox",className:"focus:shadow-outline focus:shadow-outline  mr-1 !h-4 !w-4 cursor-pointer  appearance-none  rounded-[.125rem]  text-[.8125rem]  text-sm font-normal leading-tight text-black shadow focus:outline-none focus:ring-0 sm:!text-base",name:"select_item",checked:(x==null?void 0:x.length)&&x.includes(s[k]),onChange:d=>H(s[k],r,d)})}):null,_?l.jsx("td",{className:`sticky ${[(w=e==null?void 0:e.select)==null?void 0:w.show].includes(!0)?"left-10":"left-0"} z-[5] flex h-full w-[auto] !min-w-[2.65rem] !max-w-[auto] items-center whitespace-nowrap  bg-brown-main-bg px-[.75rem] py-[.5rem] text-sm`,children:r+1}):null]}):null,(g=i==null?void 0:i.columns)==null?void 0:g.map((d,p)=>{if(!["row",""].includes(d==null?void 0:d.accessor)&&(d!=null&&d.selected_column))return l.jsx(Be,{columnIndex:p,row:s,columns:i==null?void 0:i.columns,column:d,currentTableData:h,actions:e,allowEditing:de,handleSelectRow:H,handleTableCellChange:me,actionPostion:v,onPopoverStateChange:xe,selectedIds:x,actionId:k,tableRole:fe,showNote:Ne},p)}),B?l.jsx("td",{className:"static -right-[0.05rem] z-[5] !w-fit !min-w-fit !max-w-fit whitespace-nowrap border-b bg-transparent px-[.75rem]  py-[.5rem] pr-6",children:l.jsxs("div",{className:"flex justify-center items-center w-full h-full",children:[v!=null&&v.includes("dropdown")?l.jsx(Ue,{row:s,actions:e,actionId:k,setDeleteId:U,columns:i==null?void 0:i.columns}):null,v!=null&&v.includes("buttons")?l.jsx(Ve,{row:s,actions:e,actionId:k,setDeleteId:U,columns:i==null?void 0:i.columns}):null]})}):null]},r)})})]}):!O&&!(h!=null&&h.length)?l.jsx("div",{className:"relative w-full max-w-full h-full min-h-full max-h-full",children:l.jsx("div",{className:"relative justify-center items-center w-full min-w-full max-w-full h-full min-h-full max-h-full",children:l.jsx("div",{className:"flex relative justify-center items-center w-full h-full",children:y!=null&&y.use?l.jsx(l.Fragment,{children:y==null?void 0:y.component}):C?l.jsx(l.Fragment,{}):l.jsx(l.Fragment,{children:"No Data"})})})}):null,l.jsx(le,{children:l.jsx(Xe,{isOpen:te,note:je,onClose:()=>{K(!1),J(null)}})})]})})},Js=o.memo(Ye);export{Js as default};
