import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{A as E,G as M,a as z,u as U,L as o,I as B,bv as v,o as P,s as N}from"./index-f2ad9142.js";import{r as t,b as D}from"./vendor-4cdf2bd1.js";import{U as H}from"./index-4e4ee51a.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const ne=({onSuccess:l=null})=>{var p,u,x,b,d,f;const[c,i]=t.useState(!1),[S,y]=t.useState(!1);t.useContext(E);const{dispatch:m}=t.useContext(M),{globalState:r}=z(),e=r==null?void 0:r.subscriptionData;U();const a=D();t.useEffect(()=>{const n=()=>{y(window.innerWidth<=768)};return n(),window.addEventListener("resize",n),()=>window.removeEventListener("resize",n)},[]);const A=()=>{var n,h,j,g,w,C,k,I,L;if(console.log(e==null?void 0:e.subscription,"billing"),!(e!=null&&e.subscription)){N(m,"You need to upgrade your plan to Enterprise or Business to use AI",5e3,"warning"),a("/member/billing?openManagePlan=true");return}if((j=(h=(n=e==null?void 0:e.object)==null?void 0:n.plan)==null?void 0:h.nickname)!=null&&j.toLowerCase().includes("pro")){N(m,"You need to upgrade your plan to Enterprise or Business to use AI",5e3,"warning"),a("/member/billing?openManagePlan=true");return}if((C=(w=(g=e==null?void 0:e.object)==null?void 0:g.plan)==null?void 0:w.nickname)!=null&&C.toLowerCase().includes("business")||(L=(I=(k=e==null?void 0:e.object)==null?void 0:k.plan)==null?void 0:I.nickname)!=null&&L.toLowerCase().includes("enterprise")){i(!0);return}a("/member/billing?openManagePlan=true")};return s.jsxs(t.Fragment,{children:[s.jsx(o,{children:s.jsx(B,{type:"button",color:"black",className:"!w-fit !self-end !bg-transparent !px-0 !font-normal md:!self-auto",onClick:A,children:s.jsxs(o,{children:[s.jsx(v,{})," ",s.jsxs("span",{className:"flex items-center justify-start",children:[s.jsx("b",{children:"Update"}),"AI"]})]})})}),(e==null?void 0:e.subscription)&&(((x=(u=(p=e==null?void 0:e.object)==null?void 0:p.plan)==null?void 0:u.nickname)==null?void 0:x.toLowerCase().includes("business"))||((f=(d=(b=e==null?void 0:e.object)==null?void 0:b.plan)==null?void 0:d.nickname)==null?void 0:f.toLowerCase().includes("enterprise")))&&s.jsx(o,{children:s.jsx(P,{modalHeader:!0,panelHeader:!0,panelClassName:"!bg-[#565452]",modalHeaderClassName:"!bg-black text-brown-main-bg",title:s.jsx(o,{children:s.jsxs("div",{className:"flex items-center justify-start gap-2",children:[s.jsx(v,{})," ",s.jsxs("span",{className:"flex items-center justify-start",children:[s.jsx("b",{children:"Update"}),s.jsx("span",{className:"!font-thin",children:"AI"})]})]})}),isOpen:c,modalCloseClick:()=>i(!1),classes:{modalDialog:"!bg-black h-fit min-h-fit max-h-fit w-full !max-h-[500px] md:!w-fit !max-w-full min-w-full md:!min-w-[595px] md:!max-w-[595px] !gap-0 ",modalContent:"!bg-black !text-brown-main-bg !z-10 !mt-0 overflow-y-auto max-h-[500px] !pt-0",panelClassName:"!bg-[#565452]"},children:c?s.jsx("div",{children:s.jsx(H,{onClose:()=>i(!1),onSuccess:()=>{i(!1),l&&l()}})}):null})})]})};export{ne as default};
