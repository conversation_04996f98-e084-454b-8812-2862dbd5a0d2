import{R as m}from"./vendor-4cdf2bd1.js";import{A as b,u as q,p as i,q as l,s as o,r as _,v as w,w as u,x as k}from"./index-f2ad9142.js";const A=(x={filter:[]})=>{const{state:P,dispatch:s}=m.useContext(b),{state:S,dispatch:a}=m.useContext(b),[d,f]=m.useState({list:[],single:null,myMembers:[]}),[p,n]=m.useState({list:!1,single:!1,update:!1,delete:!1,create:!1,myMembers:!1}),{profile:y}=q({isPublic:!1}),M=m.useCallback((r={filter:[]})=>(async()=>{n(e=>({...e,list:!0}));try{const e=await i(a,s,"company_member",{filter:[...r==null?void 0:r.filter],join:["user|member_id"]});if(!(e!=null&&e.error))return f(t=>({...t,list:e==null?void 0:e.data})),e==null?void 0:e.data}catch{return null}finally{n(e=>({...e,list:!1}))}})(),[y,s,a,i]),g=m.useCallback((r={filter:[]})=>(async()=>{try{n(t=>({...t,myMembers:!0}));const e=await i(a,s,"company_member",{filter:[...r==null?void 0:r.filter],join:["user|member_id"]});return e!=null&&e.error||f(t=>({...t,myMembers:e==null?void 0:e.data})),e}catch(e){return console.error("Error fetching company members:",e),{error:!0,message:(e==null?void 0:e.message)||"Failed to fetch members"}}finally{n(e=>({...e,myMembers:!1}))}})(),[y,s,a,i]),c=m.useCallback(r=>{if(l(r))return o(a,"Company id is Required!");(async()=>{n(e=>({...e,single:!0}));try{const e=await _(a,s,"company_member",r,{method:"GET",isPublic:!1,join:["user|member_id"],state:"companyMemberModel"});e!=null&&e.error||f(t=>({...t,single:e==null?void 0:e.data}))}catch{}finally{n(e=>({...e,single:!1}))}})()},[y,s,a,i]),C=m.useCallback(r=>{if(l(r))return o(a,"Payload is Required!");(async()=>{n(e=>({...e,create:!0}));try{const e=await w(a,s,"company_member",{...r},!1);e!=null&&e.error||c(e==null?void 0:e.data)}catch{}finally{n(e=>({...e,create:!1}))}})()},[c,s,a,u]),h=m.useCallback((r,e)=>{if(l(r))return o(a,"Member id is Required!");if(l(e))return o(a,"Payload is Required!");(async()=>{n(t=>({...t,update:!0}));try{const t=await u(a,s,"company_member",r,e,!1);t!=null&&t.error||c(r)}catch{}finally{n(t=>({...t,update:!1}))}})()},[c,s,a,u]),R=m.useCallback(r=>{if(l(r))return o(a,"Member id is Required!");(async()=>{n(e=>({...e,delete:!0}));try{const e=await k(a,s,"company_member",r,!0);e!=null&&e.error||c(r)}catch{}finally{n(e=>({...e,delete:!1}))}})()},[c,s,a,u]);return{loading:p,companyMember:d,getCompanyMember:c,getCompanyMembers:M,getMyCompanyMembers:g,updateCompanyMember:h,createCompanyMember:C,deleteCompanyMember:R}};export{A as u};
