import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{r as h}from"./vendor-4cdf2bd1.js";import{u as j}from"./useUpdateCollaborator-1187c43b.js";import"./index-64a9a9df.js";import{u,ab as w,L as C}from"./index-f2ad9142.js";import{M as g}from"./index-713720be.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const v=({members:o=[],title:d=""})=>{const{profile:c}=u(),l=o==null?void 0:o.slice(0,3),i=(o==null?void 0:o.length)>3?(o==null?void 0:o.length)-3:0,f=t=>(t==null?void 0:t.id)===(c==null?void 0:c.id);return s.jsx("div",{style:{display:"flex",alignItems:"center",position:"relative"},children:o!=null&&o.length?s.jsx(g,{tooltipClasses:"!left-[50px] !md:left-[50px] !absolute ",display:s.jsxs("div",{className:"relative h-[24px] w-[calc(100%+5rem)]",children:[l.map((t,a)=>s.jsx("div",{style:{left:`${a*16}px`,zIndex:l.length-a},className:"absolute h-[24px] w-[24px] overflow-hidden rounded-full border border-white bg-white",children:t!=null&&t.photo?s.jsx("img",{src:t==null?void 0:t.photo,alt:`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`,className:"h-full w-full object-cover"}):s.jsx(w,{className:"h-full w-full"})},a)),i>0&&s.jsx("div",{style:{left:`${l.length*16}px`,zIndex:0},className:"absolute flex h-[24px] w-[24px] items-center justify-center overflow-hidden rounded-full border border-white bg-primary-black text-white",children:s.jsxs("span",{className:"text-xs font-medium",children:["+",i]})})]}),place:"top",openOnClick:!1,backgroundColor:"#1f1d1a",show:!!(o!=null&&o.length),children:s.jsxs("div",{className:"flex w-[220px] flex-col gap-2 font-Inter md:w-[250px]",children:[s.jsx("div",{className:`text-white ${d?"":"hidden"}`,children:d}),o==null?void 0:o.map((t,a)=>s.jsxs("div",{className:"flex w-full items-center justify-between gap-3 text-white",children:[s.jsxs("div",{className:"flex items-center justify-between gap-2",children:[s.jsx("div",{className:"h-[24px] w-[24px] overflow-hidden rounded-full border border-white/20",children:t!=null&&t.photo?s.jsx("img",{src:t==null?void 0:t.photo,alt:`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`,className:"h-full w-full object-cover"}):s.jsx(w,{className:"h-full w-full"})}),s.jsx("span",{className:"line-clamp-1 max-w-[90px] overflow-ellipsis whitespace-nowrap text-sm md:text-base",children:f(t)?"Me":`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`})]}),(t==null?void 0:t.role)&&s.jsx("span",{className:"whitespace-nowrap rounded px-2 py-0.5 text-sm font-semibold md:text-base",style:{color:t.roleColor},children:t.role})]},a))]})}):"-"})},D=({update:o=null})=>{const[d,c]=h.useState([]),{profile:l}=u(),{data:i,fetchUpdateContributors:f,getUpdateCollaborator:t,collaborators:a}=j();return h.useEffect(()=>{o!=null&&o.id&&(f(o==null?void 0:o.id),t({filter:[`update_id,eq,${o==null?void 0:o.id}`]}))},[o==null?void 0:o.id]),h.useEffect(()=>{if(i!=null&&i.updateContributors&&a){const r=i.updateContributors.find(n=>n.id===(o==null?void 0:o.user_id)),p=i.updateContributors.filter(n=>a.some(x=>x.collaborator_id===n.id)).map(n=>{const x=n.id===(l==null?void 0:l.id);return{...n,first_name:x?"Me":n.first_name,last_name:x?"":n.last_name,role:"Collaborator",roleColor:"#CAB8FF"}});if(r){const n=r.id===(l==null?void 0:l.id);p.unshift({...r,first_name:n?"Me":r.first_name,last_name:n?"":r.last_name,role:"Update Owner",roleColor:"#F6A03C"})}c(p)}},[i==null?void 0:i.updateContributors,a,o==null?void 0:o.user_id,l==null?void 0:l.id]),s.jsx(s.Fragment,{children:s.jsxs("div",{className:"flex  flex-col items-start gap-[1.3125rem] font-iowan text-base font-semibold",children:[s.jsx("h3",{className:"font-iowan text-base font-semibold",children:"Contributors"}),s.jsx(C,{children:s.jsx(v,{members:d,title:""})})]})})};export{D as default};
