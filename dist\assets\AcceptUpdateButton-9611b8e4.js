import{A as R,G as $,M as A,t as q,s as N,I as _}from"./index-f2ad9142.js";import{r as a,h as B,i as M}from"./vendor-4cdf2bd1.js";import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{X as L}from"./XMarkIcon-cfb26fe7.js";import{t as S,S as k}from"./@headlessui/react-cdd9213e.js";function J(m,r){const[o,x]=a.useState(!1),[c,C]=a.useState([]),[u,l]=a.useState(0),[t,d]=B(),{dispatch:f,state:T}=a.useContext(R),{dispatch:P}=a.useContext($),i=parseInt(t.get("limit")||"30"),s=parseInt(t.get("page")||"1"),g=t.get("company_name")||"",h=t.get("status")||"";async function y(){var w,I;x(!0);try{const j=new A,U=D=>Object.entries(D).reduce((F,[E,v])=>(v!=null&&v!==""&&(F[E]=v),F),{}),z=new URLSearchParams(U({page:s,limit:i,request:m,update_id:r,company_name:g,status:h})),n=await j.callRawAPI(`/v3/api/goodbadugly/customer/fund-update-requests?${z.toString()}`);C(n==null?void 0:n.list),console.log(n==null?void 0:n.total),l((I=(w=n==null?void 0:n.total)==null?void 0:w[0])==null?void 0:I.total)}catch(j){q(f,j.message),N(P,j.message,5e3,"error")}x(!1)}a.useEffect(()=>{y()},[i,s,g,h,m,r]);const p=Math.ceil(u/i);return console.log(u,i),console.log(p),{loading:o,updates:c,refetch:y,currentPage:s,pageCount:p,pageSize:i,updatePageSize:w=>{t.set("limit",w.toString()),t.set("page","1"),d(t)},previousPage:()=>{s>1&&(t.set("page",(s-1).toString()),d(t))},nextPage:()=>{s<p&&(t.set("page",(s+1).toString()),d(t))},canPreviousPage:s>1,canNextPage:s<p}}new A;function V({refetch:m,reject:r,row:o}){const[x,c]=a.useState(!1),{dispatch:C}=a.useContext(R),{dispatch:u}=a.useContext($),[l,t]=a.useState(!1);M();const d=()=>{c(!0)};async function f(y,p){t(!0);try{await new A().callRawAPI(`/v3/api/goodbadugly/customer/fund-update-requests?page=1&limit=10&request=${y}&update_id=${p}`),c(!1),N(u,"Accepted"),m()}catch(b){q(C,b.message),N(u,b.message,5e3,"error")}setUpdating(!1)}const T=`Are you sure you want to accept this update from ${o.company_name}?`,P=`Are you sure you want to reject this update from ${o.company_name}?`,i=r?P:T,s=`If accepted, ${o.company_name} will be able to send and you will be able to request updates moving forward.`,g=`If rejected, ${o.company_name} will be prohibited from sending you updates and you will be prohibited from requesting updates moving forward.`,h=r?g:s;return e.jsxs(e.Fragment,{children:[r?e.jsx("div",{className:" flex h-[24px] w-[60px] cursor-pointer flex-row items-center justify-center rounded-[4px] bg-red-500 font-Inter text-[12px]  text-white ",onClick:()=>d(),children:"Reject"}):e.jsx("div",{className:" flex h-[24px] w-[60px] cursor-pointer flex-row items-center justify-center rounded-[4px] bg-black font-Inter text-[12px]  text-white ",onClick:()=>d(),children:"Accept"}),e.jsx(S,{appear:!0,show:x,as:a.Fragment,children:e.jsxs(k,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>c(!1),children:[e.jsx(S.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(S.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(k.Panel,{className:"flex h-auto w-full max-w-md transform flex-col justify-between overflow-hidden rounded-md bg-[#f2dfce] p-6 text-left align-middle  shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between gap-5",children:[e.jsx(k.Title,{as:"h3",className:"text-[16px] font-medium  leading-7 text-gray-900 sm:text-[20px]",children:i}),e.jsx("button",{onClick:()=>c(!1),type:"button",children:e.jsx(L,{className:"h-6 w-6"})})]}),e.jsx("div",{className:"mt-4 flex w-full flex-row items-center justify-between  gap-3 text-sm",children:e.jsx("p",{children:h})}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"font-iowan rounded-md border border-[#1f1d1a] py-2 text-center",type:"button",onClick:()=>c(!1),children:"Cancel"}),r?e.jsx(_,{loading:l,disabled:l,onClick:()=>f(2,o.update_id),className:"h-[40px] w-full max-w-[200px] rounded-md bg-red-500 py-2 text-center text-[20px] font-bold text-white transition-colors duration-100 disabled:bg-disabledblack",children:"Yes, reject"}):e.jsx(_,{loading:l,disabled:l,onClick:()=>f(1,o.update_id),className:"h-[40px] w-full max-w-[200px] rounded-md bg-black py-2 text-center text-[20px] font-bold text-white transition-colors duration-100 disabled:bg-disabledblack ",children:"Yes, accept"})]})]})})})})]})})]})}export{V as A,J as u};
