import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{T as h}from"./index-cb9e08c3.js";import{G as b,A as j,M as p,t as d,s as l}from"./index-f2ad9142.js";import{r as s}from"./vendor-4cdf2bd1.js";import{M as g}from"./MkdInput-d37679e9.js";import"./@nextui-org/theme-345a09ed.js";import"./@headlessui/react-cdd9213e.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";function Q(){s.useState(!1),s.useState(!1),s.useState(!1);const[t,u]=s.useState(),{dispatch:o}=s.useContext(b),{dispatch:m}=s.useContext(j);async function c(){try{const r=await new p().callRawAPI("/v2/api/lambda/preference",void 0,"GET");u(r)}catch(a){d(m,a.message),l(o,a.message,5e3,"error")}}async function i(a,r){console.log(a);try{await new p().callRawAPI("/v2/api/lambda/preference",{payload:a},"POST"),c(),l(o,r)}catch(n){d(m,n.message),l(o,n.message,5e3,"error")}}s.useEffect(()=>{c()},[]);const x=(t==null?void 0:t.enable_email_notification)===1?{enable_email_notification:0}:{enable_email_notification:1},f=(t==null?void 0:t.enable_sms_notification)===1?{enable_sms_notification:0}:{enable_sms_notification:1};return e.jsxs("div",{className:"px-4 md:px-8",children:[e.jsx("div",{className:"mb-2 font-iowan text-[24px] font-[700] leading-[1.865rem]",children:"Notification Schedule"}),e.jsx("p",{className:"mb-3 mt-3 font-inter text-[16px] font-[400] leading-[1.21rem]",children:"Create a schedule for future notifications"}),e.jsxs("div",{className:"max-w-[21.25rem] flex-col items-start justify-between gap-6 text-xl sm:flex-row sm:items-center",children:[e.jsx("p",{className:"mt-7 font-iowan text-[16px]",children:"Email Notifications"}),e.jsx("div",{className:"flex gap-4 items-center font-regular",children:e.jsx(g,{type:"toggle",onChange:a=>{i(x,"Email updated")},label:"",value:(t==null?void 0:t.enable_email_notification)==1})}),e.jsxs("div",{className:"hidden relative gap-3 items-center",children:[e.jsx("div",{className:"absolute z-10  h-full w-full cursor-not-allowed opacity-[50]"}),e.jsx(h,{enabled:(t==null?void 0:t.enable_sms_notification)==1,setEnabled:()=>i(f,"SMS updated"),className:"w-[38px] opacity-[0.3]"}),e.jsx("span",{className:"font-iowan-regular  opacity-[0.5]",children:"SMS Notifications"})]})]}),e.jsxs("div",{className:"gap-6 justify-between mt-2 max-w-full sm:flex-row sm:items-center",children:[e.jsx("div",{className:"mb-4 font-iowan text-[24px] font-[700] leading-[1.865rem]",children:"Notification Frequency"}),e.jsxs("select",{name:"",id:"",className:"font h-[41.6px] w-full rounded-md border border-[#1f1d1a] bg-transparent text-sm sm:text-sm md:w-[45%]",onChange:a=>i({notification_frequency:Number(a.target.value)},"Frequency updated"),children:[e.jsxs("option",{value:"",hidden:!0,children:[(t==null?void 0:t.notification_frequency)||"Set"," hours"]}),e.jsx("option",{value:"6",children:"6 hours"}),e.jsx("option",{value:"12",children:"12 hours"}),e.jsx("option",{value:"24",children:"24 hours"}),e.jsx("option",{value:"36",children:"36 hours"}),e.jsx("option",{value:"48",children:"48 hours"}),e.jsx("option",{value:"72",children:"72 hours"})]})]})]})}export{Q as default};
