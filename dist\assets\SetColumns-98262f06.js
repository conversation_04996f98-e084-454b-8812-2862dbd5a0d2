import{j as l}from"./@nextui-org/listbox-0f38ca19.js";import{R as P,r as m}from"./vendor-4cdf2bd1.js";import{M as D}from"./MkdInput-d37679e9.js";import{A as z}from"./AddButton-51d1b2cd.js";import{A as ee,G as se,u as re,w as k,v as ie,r as H,o as ne,n as te,I as de,L as le}from"./index-f2ad9142.js";import{A as oe}from"./index-afef2e72.js";import{C as fe,a as ae,D as ue}from"./lucide-react-0b94883e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const Pe=({isOpen:W=!1,onClose:j=null,onUpdate:O=null,onSuccess:h=null,columnModel:b="",columnData:n=null})=>{var B,q,F;const{dispatch:y}=P.useContext(ee),{dispatch:_,state:{updateModel:c,createModel:w}}=P.useContext(se),{profile:p}=re(),[r,g]=m.useState({modal:"",viewName:"",showModal:!1,isDirty:!1}),[N,S]=m.useState(null),[C,A]=m.useState(null),[G,E]=m.useState(!1),[t,J]=m.useState({views:[],data:null,columns:[],columnId:0,...n}),[v,T]=m.useState((B=n==null?void 0:n.data)==null?void 0:B.id),[a,I]=m.useState([...n==null?void 0:n.columns]),K=(e,s)=>{const i=[...a];i.splice(s,1,{...e,selected_column:!(e!=null&&e.selected_column)}),I(()=>[...i]),r.isDirty||g(o=>({...o,isDirty:!0}))},Q=e=>{const s=(e==null?void 0:e.columns)&&JSON.parse(e==null?void 0:e.columns);T(e==null?void 0:e.id),J(i=>({...i,columnId:e==null?void 0:e.column_id,columns:s,data:e})),I(()=>[...s]),r.isDirty||g(i=>({...i,isDirty:!0}))},R=async()=>{var e;t&&((e=t==null?void 0:t.data)!=null&&e.user_id)?X():$()},X=async()=>{var o;const e=[...a],s=t,i=r!=null&&r.isDirty?await k(_,y,"column_views",v,{columns:JSON.stringify(e),current_view:!0}):{error:!0};if(i!=null&&i.error)h&&h({...s,columns:a});else{const u=await H(_,y,"column_views",v,"GET"),d=(o=s==null?void 0:s.views)==null?void 0:o.find(f=>(f==null?void 0:f.current_view)&&(f==null?void 0:f.id)!==v);d&&await k(_,y,"column_views",d==null?void 0:d.id,{current_view:!1}),r.isDirty&&g(f=>({...f,isDirty:!1})),h&&h({...s,data:u==null?void 0:u.data,columns:a,views:[...s==null?void 0:s.views.filter(f=>(f==null?void 0:f.id)!==v),u==null?void 0:u.data]})}},Y=m.useCallback(async(e={name:"",id:null})=>{var u;const s=t,i={...e,model:b,current_view:!0,column_id:t==null?void 0:t.columnId,user_id:p==null?void 0:p.id,columns:JSON.stringify(a)},o=(u=s==null?void 0:s.views)==null?void 0:u.find(d=>d==null?void 0:d.current_view);o&&await k(_,y,"column_views",o==null?void 0:o.id,{current_view:!1}),J(d=>({...d,data:i,columnId:t==null?void 0:t.columnId,views:[...d==null?void 0:d.views,i]})),T(e==null?void 0:e.id),O&&O({...s,data:i,columns:a,views:[...s==null?void 0:s.views,i]}),r.isDirty&&g(d=>({...d,isDirty:!1}))},[r,h,a,t,b,O]),$=m.useCallback(async e=>{var u;const s=[...a],i=t;if(!(r!=null&&r.viewName)||!e)return g(d=>({...d,showModal:!0,modal:"enter_name"}));const o=await ie(_,y,"column_views",{model:b,current_view:!0,column_id:t==null?void 0:t.columnId,user_id:p==null?void 0:p.id,name:(r==null?void 0:r.viewName)||e,columns:JSON.stringify(s)});if(!(o!=null&&o.error)){const d=await H(_,y,"column_views",o==null?void 0:o.data,"GET"),f=(u=i==null?void 0:i.views)==null?void 0:u.find(x=>(x==null?void 0:x.current_view)&&(x==null?void 0:x.id)!==v);f&&await k(_,y,"column_views",f==null?void 0:f.id,{current_view:!1}),r.isDirty&&g(x=>({...x,isDirty:!1})),h&&h({...i,data:d==null?void 0:d.data,columns:a,views:[...i==null?void 0:i.views,d==null?void 0:d.data]})}},[r,h,a,t,b]),Z=(e,s)=>{A(s),E(!0)},M=e=>{if(e.preventDefault(),C&&N&&C!=N){const s=[...a],i=s[C];s.splice(C,1),s.splice(N,0,i),I(()=>[...s]),r.isDirty||g(o=>({...o,isDirty:!0}))}S(null),A(null),E(!1)},L=(e,s)=>{e.preventDefault(),S(s)},U=e=>{e.preventDefault(),S(null),A(null),E(!1)},V=e=>{e.preventDefault(),S(null)};return m.useEffect(()=>{var e,s;(e=n==null?void 0:n.data)!=null&&e.name&&g(i=>{var o;return{...i,viewName:(o=n==null?void 0:n.data)==null?void 0:o.name}}),n!=null&&n.columns&&I(()=>[...n==null?void 0:n.columns]),J(()=>n),T((s=n==null?void 0:n.data)==null?void 0:s.id)},[(q=n==null?void 0:n.data)==null?void 0:q.name,n==null?void 0:n.columns]),l.jsxs(l.Fragment,{children:[l.jsx(ne,{isOpen:W,modalCloseClick:()=>j&&j(),customMinWidthInTw:"!w-[23rem]",modalHeader:!0,title:l.jsxs("div",{className:"flex items-center gap-2 font-inter text-[1.125rem] font-bold leading-[1.5rem] text-[#18181B]",children:[l.jsx(fe,{})," Columns"]}),classes:{modalDialog:"!grid grid-rows-[auto_90%] !gap-0 !w-full !px-0 md:!w-[30.375rem] md:min-h-[70%] md:h-[70%] md:max-h-[70%] max-h-[70%] min-h-[70%]",modalContent:"!z-10 !mt-0 !px-0 overflow-hidden !pt-0",modal:"h-full"},children:l.jsxs("div",{className:"relative mx-auto grid h-full max-h-full min-h-full w-full grow grid-cols-1 grid-rows-[auto_1fr_auto] gap-5 rounded px-5 text-start !font-inter leading-snug tracking-wide",children:[l.jsxs("div",{className:"grid w-full min-w-full max-w-full grid-cols-[1fr_auto] grid-rows-1 items-center justify-start gap-2 py-1",children:[l.jsx("div",{className:"scrollbar-hide flex w-full min-w-full max-w-full items-center justify-start gap-2 overflow-auto",children:(F=t==null?void 0:t.views)!=null&&F.length?t==null?void 0:t.views.map((e,s)=>l.jsx(z,{type:"button",anination:!1,onClick:()=>Q(e),className:`flex h-full w-fit min-w-fit max-w-fit items-center justify-between rounded-md !py-0 ${(e==null?void 0:e.id)===v?"!bg-primary !text-white":"!border-soft-200 !text-sub-500 !bg-transparent"}`,children:e==null?void 0:e.name},s)):null}),l.jsx(z,{type:"button",onClick:()=>{g(e=>({...e,showModal:!0,modal:"new_view"}))},className:"flex h-full w-fit items-center justify-between rounded-md !border-0 !bg-white !py-0",children:l.jsx(ae,{className:"h-5 w-5"})})]}),l.jsx("div",{className:"space-y-5 overflow-auto",children:l.jsx("div",{className:"h-full max-h-full min-h-full w-full overflow-y-auto",children:a.map((e,s)=>{if(!["Row","Action"].includes(e==null?void 0:e.header))return l.jsxs("div",{draggable:!0,onDragStart:i=>Z(i,s),onDragEnd:U,onDragOver:i=>L(i,s),onDragLeave:i=>V(i),onDrop:i=>M(i),className:`${G?"cursor-grabbing":"cursor-grab"} flex h-[2.5rem] w-full items-center justify-between rounded-md p-2 ${N==s?"bg-primary-light text-white":""}`,children:[l.jsx(ue,{className:`${G?"cursor-grabbing":"cursor-grab"}`}),l.jsx("div",{className:"grow px-5 text-left !capitalize",children:te(e==null?void 0:e.header,{casetype:"capitalize",separator:" "})}),l.jsx("div",{children:l.jsx(D,{type:"toggle",onChange:i=>K(e,s),value:e==null?void 0:e.selected_column})})]},s)})})}),l.jsxs("div",{className:"relative flex gap-5",children:[l.jsx("div",{className:"w-1/2",children:l.jsx(z,{onClick:()=>j&&j(),disabled:(c==null?void 0:c.loading)||(w==null?void 0:w.loading),className:"!bg-soft-200 !text-sub-500 !w-full !grow !border-none",children:"Cancel"})}),l.jsx(de,{type:"button",disabled:(c==null?void 0:c.loading)||(w==null?void 0:w.loading),loading:(c==null?void 0:c.loading)||(w==null?void 0:w.loading),onClick:()=>{R()},className:"w-1/2",children:"Save and Close"})]})]})}),l.jsx(le,{children:l.jsx(oe,{isOpen:(r==null?void 0:r.showModal)&&["enter_name","new_view"].includes(r==null?void 0:r.modal),title:"Save View",modalClasses:{modalDialog:"max-h-[90%] min-h-[12rem] overflow-y-auto !w-full md:!w-[29.0625rem]",modal:"h-full"},onClose:()=>{g(e=>({...e,modal:"",viewName:"",showModal:!1}))},onSuccess:e=>{["new_view"].includes(r==null?void 0:r.modal)&&Y(e),["enter_name"].includes(r==null?void 0:r.modal)&&$(e==null?void 0:e.name),g(s=>({...s,modal:"",viewName:e==null?void 0:e.name,showModal:!1}))},data:{model:b,current_view:!0,column_id:t==null?void 0:t.columnId,user_id:p==null?void 0:p.id,name:r==null?void 0:r.viewName,columns:JSON.stringify(a)},action:["new_view"].includes(r==null?void 0:r.modal)?"create":"save",mode:["new_view"].includes(r==null?void 0:r.modal)?"input_create":"input",input:"name",customMessage:"Enter View Name",multiple:!1,table:"column_views"})})]})};export{Pe as default};
