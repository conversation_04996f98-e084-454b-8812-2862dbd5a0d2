import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as o}from"./vendor-4cdf2bd1.js";import{h as T}from"./moment-a9aaa855.js";import{b as J}from"./index.esm-bb52b9ca.js";import{b as M,a as Q,L as W,s as m}from"./index-f2ad9142.js";import{C as U}from"./index-c523e7e9.js";import{D as X,k as Y}from"./index-d20ea84b.js";import{u as Z}from"./useNote-ea33f376.js";import{P as ee}from"./PlusIcon-26cedb5d.js";import{L as E,t as se}from"./@headlessui/react-cdd9213e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-icons-36ae72b7.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-quill-a78e6fc7.js";import"./@craftjs/core-a2cdaeb4.js";const Re=({note:r,html:ae,userCommentOnNote:h,setComment:B,data:S,comment:x,refetchAll:f,refetchAllComment:C,update_id:L,update:d})=>{const{sdk:p}=M(),{globalDispatch:n,authState:v,authDispatch:b}=Q(),[te,$]=o.useState(""),[D,k]=o.useState(""),[R,N]=o.useState(!1),[F,_]=o.useState(!1),[j,g]=o.useState(!1),[re,y]=o.useState(!1),{refetch:z}=Z(r.id,r),[w,A]=o.useState({}),I=o.useCallback(async s=>{if(w[s])return w[s];try{const t=await p.callRawAPI(`/v4/api/records/user/${s}`,{},"GET"),i={...w,[s]:t};return A(i),t}catch(t){return console.error("Error fetching user details:",t),null}},[w]);o.useEffect(()=>{(async()=>{var c,l,u;const t=[...(x==null?void 0:x.update_comment_replies)||[],...((u=(l=(c=d==null?void 0:d.find(a=>a.id===r.id))==null?void 0:c.comments)==null?void 0:l.find(a=>a.id===x.id))==null?void 0:u.update_comment_replies)||[]],i=[...new Set(t.map(a=>a.user_id))];for(const a of i)await I(a)})()},[x,d,r.id,I]);const q=async(s,t,i,c)=>{try{const l=await p.callRawAPI(`/v3/api/custom/goodbadugly/updates/comments/replies/${s}`,{reply:D,note_id:t,update_id:i},"POST");m(n,"Reply added successfully"),k(""),c()}catch(l){tokenExpireError(b,l.message),m(n,l.message,5e3,"error")}};async function G(s,t){try{g(!0),await p.callRawAPI(`/v4/api/records/notes/${t}`,{type:s},"PUT"),f(),g(!1),m(n,"Saved")}catch(i){tokenExpireError(b,i.message),m(n,i.message,5e3,"error"),g(!1)}}const P=()=>{y(!0)},K=async s=>{console.log(s),_(!0);try{const t=await p.callRawAPI(`/v3/api/custom/goodbadugly/updates/${s==null?void 0:s.update_id}/comments`,{comment:x,note_id:s.id},"POST");_(!1),z(),f(),C(),N(!1)}catch(t){_(!1),$(t.message),tokenExpireError(b,t.message),m(n,t.message,5e3,"error")}};async function O(s){try{await p.callRawAPI(`/v4/api/records/notes/${s}`,{},"DELETE"),m(n,"Deleted",3e3,"success"),f()}catch(t){tokenExpireError(b,t.message),m(n,t.message,5e3,"error")}}const V=s=>{N(R===s?!1:s)};return e.jsxs("section",{className:"border border-b-[2px] border-red-600 border-b-[#1f1d1a] pb-4",children:[e.jsxs("div",{className:"flex flex-row justify-between",children:[e.jsxs("div",{id:r.type,className:"flex w-full flex-col",children:[e.jsx("input",{className:`no-box-shadow border-non w-full pb-4 ${r.type==="Section title"?"bg-brown-main-bg":"bg-transparent"}  p-0 text-xl font-semibold ring-transparent`,defaultValue:r.type,readOnly:!j,onBlur:s=>{let t;t&&clearTimeout(t),t=setTimeout(()=>G(s.target.value,r.id),500)}}),j?e.jsx(U,{data:S,report:!0,note_id:r.id,editorID:`editorjs-container-${r.id}`,afterEdit:f,setUpdated:y,updateSaved:P}):e.jsx(U,{data:S,report:!0,note_id:r.id,editorID:`editorjs-container-${r.id}`,afterEdit:f,setUpdated:y,updateSaved:P,editing:!1}),e.jsxs("div",{className:"mt-5",children:[h.length!==0&&e.jsx("div",{className:"flex flex-row items-center gap-5 text-[14px] font-semibold"}),h==null?void 0:h.map(s=>{var t,i,c,l,u;return e.jsxs(e.Fragment,{children:[e.jsx("div",{children:e.jsx("div",{className:"flex flex-row items-end gap-2",children:e.jsxs("div",{className:"my-3 flex flex-col",children:[e.jsxs("div",{className:"flex flex-row items-center gap-2",children:[e.jsx("img",{className:"h-7 w-7 rounded-[50%] object-cover",src:"/default.png"}),e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("span",{className:"flex flex-row items-center gap-1 text-[14px] font-semibold capitalize text-[#1f1d1a]",children:[s.first_name+" "+s.last_name,((t=v==null?void 0:v.profile)==null?void 0:t.user_id)===s.user_id&&e.jsx(X,{comment:s,refetch:C})]}),e.jsx("span",{className:"text-[12px] text-[#1f1d1a]",children:T(s.update_at).fromNow()})]})]}),e.jsx("span",{className:"mt-2",children:s.comment})]})})}),e.jsx(W,{children:e.jsx(Y,{commentId:s.id,noteId:r.id,updateId:L,replyComment:D,setReplyComment:k,handleReplyComment:q,refetchAll:f})}),d&&d.some(a=>a.id===r.id&&a.comments&&a.comments.some(H=>H.id===s.id))&&e.jsxs("div",{className:"my-5 ml-8 flex flex-col gap-2 border-l-2 border-black/40",children:[console.log([...(s==null?void 0:s.update_comment_replies)||[],...((c=(i=d.find(a=>a.id===r.id))==null?void 0:i.comments.find(a=>a.id===s.id))==null?void 0:c.update_comment_replies)||[]]),[...(s==null?void 0:s.update_comment_replies)||[],...((u=(l=d.find(a=>a.id===r.id))==null?void 0:l.comments.find(a=>a.id===s.id))==null?void 0:u.update_comment_replies)||[]].map(a=>e.jsxs("div",{className:"mt-2 pl-4",children:[e.jsxs("div",{className:"flex flex-row items-center gap-2",children:[e.jsx("img",{className:"h-7 w-7 rounded-[50%] object-cover",src:"/default.png"}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-[12px] font-semibold capitalize text-[#1f1d1a]",children:a!=null&&a.first_name?(a==null?void 0:a.first_name)+" "+(a==null?void 0:a.last_name):"user"}),e.jsx("span",{className:"text-[10px] text-[#1f1d1a]",children:T(a==null?void 0:a.created_at).fromNow()})]})]}),e.jsx("span",{className:"mt-1 text-sm",children:a==null?void 0:a.reply})]},a.id))]})]})})]}),e.jsx("div",{}),e.jsxs("div",{className:"invisible flex h-[41px] w-full max-w-[174px] cursor-pointer flex-row items-center justify-center gap-3 rounded border-[2px] border-[#1f1d1a] font-iowan",onClick:()=>V(r.id),children:[e.jsx(ee,{className:"h-5 w-5"}),e.jsx("span",{children:"Add Comment"})]})]}),e.jsx(E,{className:"relative",children:({open:s,close:t})=>e.jsxs(e.Fragment,{children:[e.jsx(E.Button,{as:"div",className:"cursor-pointer",children:e.jsx(J,{className:"invisible",size:20})}),e.jsx(se,{as:o.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(E.Panel,{className:"absolute right-0 z-10 mt-1 w-screen max-w-[100px] transform rounded-md border-2 border-[#1F1D1A] bg-brown-main-bg  ",children:e.jsxs("div",{className:"space-y-3 p-2",children:[e.jsx("div",{onClick:()=>{g(!j),t()},className:"flex cursor-pointer flex-row items-center gap-2 border-b-[1px] border-b-gray-300 p-1",children:e.jsx("div",{className:"flex flex-col",children:e.jsx("span",{className:"font-iowan font-semibold",children:j?"Close Edit":"Edit"})})}),e.jsx("div",{onClick:async()=>{await O(r==null?void 0:r.id),t()},className:"flex cursor-pointer flex-row items-center gap-2 border-b-[1px] border-b-gray-300 p-1",children:e.jsx("div",{className:"flex flex-col",children:e.jsx("span",{className:"font-iowan font-semibold text-red-500",children:"Delete"})})})]})})})]})})]}),R===r.id&&e.jsxs("form",{className:"mt-7 space-y-3",children:[e.jsx("textarea",{name:"comment",id:"comment",cols:"10",rows:"5",className:"w-full rounded border border-[#1f1d1a] bg-transparent ",onChange:s=>B(s.target.value)}),e.jsx("div",{className:"flex flex-row justify-end",children:e.jsx("button",{className:"ml-auto h-[36px] w-full max-w-[120px] rounded bg-black font-iowan text-sm text-white",onClick:s=>{s.preventDefault(),s.stopPropagation(),K(r)},children:F?"Submitting...":"Send"})})]})]},r.id)};export{Re as default};
