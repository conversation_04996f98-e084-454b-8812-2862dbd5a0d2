import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as s,b as q,h as J}from"./vendor-4cdf2bd1.js";import{A as M,G as R,M as $,t as I,s as L,a5 as Q}from"./index-f2ad9142.js";import{u as Z}from"./react-hook-form-a383372b.js";import{o as ee}from"./yup-0917e80c.js";import{c as te,a as b}from"./yup-342a5df4.js";import{A as ae}from"./AddButton-51d1b2cd.js";import{E as se}from"./ExportButton-eb4cf1f9.js";import{u as re}from"./useCompanyMembers-afd93b3b.js";import{InteractiveButton2 as le}from"./InteractiveButton-060359e0.js";import{X as ne}from"./XMarkIcon-cfb26fe7.js";import{t as v,S as y}from"./@headlessui/react-cdd9213e.js";import{L as ie}from"./index-b8adfdf8.js";import{R as oe}from"./tableWrapper-ca490fb1.js";import{P as me}from"./index-9dceff66.js";import{C as de}from"./ClipboardDocumentIcon-f03b0627.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-b50d6e2a.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./index.esm-7add6cfb.js";import"./react-icons-36ae72b7.js";function ce({user:c,afterDelete:g}){const[p,n]=s.useState(!1),{dispatch:a}=s.useContext(M),{dispatch:d}=s.useContext(R),[r,i]=s.useState(!1);async function j(){i(!0);try{const l=new $;await l.callRawAPI(`/v4/api/records/profile/${c.id}`,{},"DELETE"),await l.callRawAPI(`/v4/api/records/user/${c.user_id}`,{},"DELETE"),n(!1),g()}catch(l){I(a,l.message),L(d,l.message,5e3,"error")}i(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"cursor-pointer px-1 text-sm font-medium text-red-500 underline hover:underline",onClick:()=>n(!0),children:e.jsx("span",{children:"Delete"})}),e.jsx(v,{appear:!0,show:p,as:s.Fragment,children:e.jsxs(y,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(v.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(v.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(y.Panel,{className:"w-full max-w-xl transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(y.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Delete user"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(ne,{className:"h-6 w-6"})})]}),e.jsxs("p",{className:"mt-4",children:["You are about to delete user ",c.user_id,", note that this action is irreversible"]}),e.jsxs("span",{className:"mt-4 font-semibold",children:[" ","This action cannot be undone."]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border py-2 text-center font-medium",type:"button",onClick:()=>n(!1),children:"Cancel"}),e.jsx(le,{loading:r,disabled:r,onClick:j,className:"rounded-lg bg-[#1f1d1a] py-2 text-center font-iowan font-semibold text-white transition-colors duration-100 disabled:bg-opacity-60",children:"Yes, delete"})]})]})})})})]})})]})}const pe=[{header:"First name",accessor:"first_name"},{header:"Last name",accessor:"last_name"},{header:"Role",accessor:"role"},{header:"Email",accessor:"email"},{header:"Status",accessor:"member_status"},{header:"Action",accessor:""}],xe=[{label:"Manager/Founder",value:"member"},{label:"Collaborator",value:"collaborator"},{label:"Fund manager",value:"investor"},{label:"Stakeholder",value:"stakeholder"}],Ke=()=>{var _,S,C,k,E,P,T,z,F;const{dispatch:c,state:g}=s.useContext(M),{dispatch:p}=s.useContext(R),n=q(),[a,d]=J(),{members:r,loading:i,refetch:j,totalCount:l}=re(g.company.id),[o,w]=s.useState(parseInt(a.get("page")||"1")),[N,B]=s.useState(parseInt(a.get("limit")||"30")),U=te({first_name:b(),last_name:b(),email:b(),role:b()}),{register:x,handleSubmit:G,setError:ue,reset:O,formState:{errors:m}}=Z({resolver:ee(U),defaultValues:async()=>{const t=a.get("first_name")??"",u=a.get("last_name")??"",f=a.get("email")??"",h=a.get("role")??"";return{first_name:t,last_name:u,email:f,role:h}}});async function Y(){try{const t=new $;t.setTable("user"),await t.exportCSV()}catch(t){I(c,t.message),L(p,t.message,5e3,"error")}}s.useEffect(()=>{p({type:"SETPATH",payload:{path:"users"}})},[]);const V=()=>{n("/member/add-user")};function W(t){a.set("first_name",t.first_name),a.set("last_name",t.last_name),a.set("email",t.email),a.set("role",t.role),a.set("page",1),d(a)}const X=t=>{B(t),a.set("limit",t.toString()),a.set("page","1"),d(a)},H=()=>{o>1&&(w(o-1),a.set("page",(o-1).toString()),d(a))},K=()=>{w(o+1),a.set("page",(o+1).toString()),d(a)};return e.jsx("div",{className:"px-5 pt-8 md:px-8",children:i?e.jsx(ie,{}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"rounded bg-brown-main-bg",children:[e.jsxs("div",{className:"flex justify-between mb-3 w-full item-center",children:[e.jsx("h4",{className:"text-[16px] font-semibold md:text-xl",children:"Search"}),e.jsx("div",{className:"flex"})]}),e.jsxs("form",{onSubmit:G(W),className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"flex flex-row flex-wrap gap-4",children:[e.jsxs("div",{className:"w-full sm:w-auto",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"First name"}),e.jsx("input",{type:"text",...x("first_name"),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm text-sm text-sm font-normal font-normal font-normal capitalize leading-tight text-[#1d1f1a] shadow focus:outline-none    sm:w-[180px] ${(_=m.first_name)!=null&&_.message?"border-red-500":""}`}),e.jsx("p",{className:"italic text-red-500 text-field-error",children:(S=m.first_name)==null?void 0:S.message})]}),e.jsxs("div",{className:"w-full sm:w-auto",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Last name"}),e.jsx("input",{type:"text",...x("last_name"),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm text-sm text-sm font-normal font-normal font-normal capitalize leading-tight text-[#1d1f1a] shadow focus:outline-none   sm:w-[150px] sm:w-[180px] ${(C=m.last_name)!=null&&C.message?"border-red-500":""}`}),e.jsx("p",{className:"italic text-red-500 text-field-error",children:(k=m.last_name)==null?void 0:k.message})]}),e.jsxs("div",{className:"w-full sm:w-auto",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Email"}),e.jsx("input",{type:"text",...x("email"),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm text-sm text-sm font-normal font-normal font-normal capitalize leading-tight text-[#1d1f1a] shadow focus:outline-none   sm:w-[150px] sm:w-[180px] ${(E=m.email)!=null&&E.message?"border-red-500":""}`}),e.jsx("p",{className:"italic text-red-500 text-field-error",children:(P=m.email)==null?void 0:P.message})]}),e.jsxs("div",{className:"w-full sm:w-auto",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Role"}),e.jsxs("select",{className:`focus:shadow-outline w-full appearance-none rounded border border border-[#1f1d1a] bg-transparent py-2 pl-6 pr-8 font-Inter text-sm font-normal leading-tight text-[#1d1f1a] shadow shadow-none focus:outline-none sm:w-[180px] ${(T=m.role)!=null&&T.message?"border-red-500":""}`,...x("role"),children:[e.jsx("option",{value:"",children:"-Select Role-"}),xe.map(t=>e.jsx("option",{value:t.value,children:t.label},t.value))]}),e.jsx("p",{className:"italic text-red-500 text-field-error",children:(z=m.role)==null?void 0:z.message})]})]}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsx("button",{type:"submit",disabled:i,className:"px-4 py-1 font-semibold text-white font-iowan-regularrounded-md bg-primary-black/80 hover:bg-primary-black",children:"Search"}),e.jsx("button",{type:"button",onClick:()=>{O({first_name:"",last_name:"",role:"",email:""}),a.set("first_name",""),a.set("last_name",""),a.set("email",""),a.set("role",""),a.set("page",1),d(a)},disabled:i,className:"rounded-md px-4 py-1 font-semibold text-[#1f1d1a]",children:"Clear"})]})]}),e.jsxs("div",{className:"overflow-x-auto p-5 px-0 mt-10 rounded bg-brown-main-bg md:mt-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-3 w-full text-center",children:[e.jsx("h4",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"User management"}),e.jsxs("div",{className:"flex gap-3 items-center",children:[e.jsx(se,{onClick:Y,className:"py-2 font-medium sm:px-2"}),e.jsx(ae,{onClick:V,className:"py-2 font-medium sm:px-2"})]})]}),e.jsx("div",{className:`${i?"":"custom-overflow overflow-x-auto"}`,children:e.jsx(e.Fragment,{children:e.jsx(oe,{children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10",children:[e.jsx("thead",{children:e.jsx("tr",{children:pe.map((t,u)=>e.jsx("th",{scope:"col",className:"font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3",children:t.header},u))})}),e.jsx("tbody",{className:"font-iowan-regulardivide-y divide-[#1f1d1a]/10",children:(F=r==null?void 0:r.toReversed())==null?void 0:F.map((t,u)=>{var f,h,D,A;return e.jsxs("tr",{className:"md:h-[60px]",children:[e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:(f=t==null?void 0:t.user)==null?void 0:f.first_name}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:(h=t==null?void 0:t.user)==null?void 0:h.last_name}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:(D=t==null?void 0:t.user)==null?void 0:D.role}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:(A=t==null?void 0:t.user)==null?void 0:A.email}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:Q[t.member_status]}),e.jsx("td",{className:"flex gap-2 justify-start items-center px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex h-auto items-center gap-2 sm:h-[60px] sm:justify-center",children:[e.jsx("button",{className:"text-sm underline font-iowan-regularcursor-pointer hover:underline",onClick:()=>{n(`/member/edit-user/${t==null?void 0:t.id}`)},children:"Edit"}),e.jsx(ce,{afterDelete:j,user:t})]})})]},t==null?void 0:t.id)})})]})})})}),(r==null?void 0:r.length)==0?e.jsxs("div",{className:"mb-[20px] mt-24 flex flex-col items-center",children:[e.jsx(de,{className:"w-8 h-8 text-gray-700",strokeWidth:2}),e.jsx("p",{className:"mt-4 text-base font-medium text-center",children:"No users added yet"})]}):null,e.jsx(me,{currentPage:o,pageCount:Math.ceil(l/N),pageSize:N,canPreviousPage:o>1,canNextPage:o<Math.ceil(l/N),updatePageSize:X,previousPage:H,nextPage:K,dataLoading:i,totalCount:l})]})]})})})};export{Ke as default};
