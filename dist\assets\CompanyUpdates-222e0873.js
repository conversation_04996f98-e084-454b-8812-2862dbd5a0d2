import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{r as m,h as W,b as K,j as Ce}from"./vendor-4cdf2bd1.js";import{a as te,b as Ae,u as le,L as E,F as Se,U as Ue,R as Ee,i as qe,A as me,G as ce,T as ie,M as H,t as ne,s as re,D as Pe,I as ke,d as Me,E as Re,j as $e,k as S,O as ve,B as Ie,l as Fe}from"./index-f2ad9142.js";import{c as Oe,a as ee}from"./yup-342a5df4.js";import{u as Be}from"./react-hook-form-a383372b.js";import{o as Le}from"./yup-0917e80c.js";import{M as Ye}from"./MkdInput-d37679e9.js";import{u as ze}from"./useCompanyMember-0033d2de.js";import{L as oe,t as B,S as z}from"./@headlessui/react-cdd9213e.js";import{L as Ve}from"./index-23a711b5.js";import{h as O}from"./moment-a9aaa855.js";import{D as Ge}from"./DocumentTextIcon-54b5e200.js";import{D as Qe}from"./DocumentIcon-22c47322.js";import{X as je}from"./XMarkIcon-cfb26fe7.js";import{InteractiveButton2 as He}from"./InteractiveButton-060359e0.js";import{U as _e,C as We,a as Ke}from"./index-2a0f7dff.js";import{A as Xe}from"./index-afef2e72.js";import{M as he}from"./index-713720be.js";import{u as Ze}from"./useDate-c1da5729.js";import{E as Je,P as De}from"./lucide-react-0b94883e.js";import{M as pe}from"./index-dc002f62.js";import{R as de}from"./index-4e4ee51a.js";import{_ as we}from"./qr-scanner-cf010ec4.js";function es(e){const[x,l]=m.useState(!1),[f,_]=m.useState([]),{authDispatch:a,globalDispatch:r}=te(),{sdk:c}=Ae(),[n]=W();n.get("limit"),n.get("page");const d=["start_date","end_date","status","recipients","availability"],[b,o]=m.useState({page:1,total:0,num_pages:0});async function y(g,w){l(!0);try{const i={limit:g,page:w};d.forEach(A=>{const R=n.get(A);R&&(i[A]=R)});let P=`/v3/api/goodbadugly/customer/company-updates?${Object.entries(i).map(([A,R])=>R?`${A}=${R}`:"").join("&")}`;const I=await c.callRawAPI(P);_(I.model.filter(A=>A.status!="-1")),o({total:I.total,page:I.page,num_pages:I.num_pages})}catch(i){tokenExpireError(a,i.message),showToast(r,i.message,5e3,"error")}l(!1)}return{loading:x,updates:f,refetch:y,paginationData:b}}function ss(){const[e,x]=W(),{profile:l}=le(),{companyMember:f,getMyCompanyMembers:_}=ze(),a=Oe({start_date:ee(),end_date:ee(),availability:ee(),status:ee(),recipients:ee()}),{register:r,handleSubmit:c,formState:{errors:n},reset:d,setValue:b,control:o,watch:y}=Be({resolver:Le(a),defaultValues:{status:e.get("status")||"",end_date:e.get("end_date")||"",start_date:e.get("start_date")||"",recipients:e.get("recipients")||"",availability:e.get("availability")||"available"}}),g={availability:"available"},{availability:w,end_date:i,recipients:q,start_date:P,status:I}=y();async function A(C){Object.entries(C).forEach(([T,v])=>{qe(v)?["availability"].includes(T)?e.set(T,g[T]):e.delete(T):e.set(T,v)}),x(e)}const R=C=>{const T=new Date(C.target.value).toISOString().split("T")[0];b("start_date",T)},k=C=>{const T=new Date(C.target.value).toISOString().split("T")[0];b("end_date",T)};return m.useEffect(()=>{var C,T;l!=null&&l.id&&_({filter:[`company_id,eq,${(T=(C=l==null?void 0:l.companies)==null?void 0:C[0])==null?void 0:T.id}`]})},[l==null?void 0:l.id]),s.jsx(oe,{className:"relative z-[8] mt-5 flex grow items-center justify-between gap-5 md:mt-0 md:justify-end",children:({open:C})=>{var T,v,V,F,L,Y,G,X;return s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"flex w-fit flex-col items-start gap-2 font-inter text-[1rem] font-[500] leading-[1.21rem] sm:flex sm:flex-row sm:items-center",children:[s.jsx("span",{className:"font-iowan",children:"Show: "}),s.jsxs("div",{className:"flex w-fit items-center gap-2",children:[s.jsx("span",{className:"",children:"All Updates"}),s.jsx("span",{className:"ml-2 md:ml-0",children:s.jsx(E,{children:s.jsx(Ye,{type:"toggle",value:["available"].includes(w),onChange:u=>{b("availability",u.target.checked?"available":"all"),e.set("availability",u.target.checked?"available":"all"),x(e)}})})}),s.jsx("span",{children:"Available"})]})]}),s.jsxs(oe.Button,{className:`
              group inline-flex items-center gap-2 rounded-sm border border-[#1f1d1a] px-3 py-2 text-base font-medium focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75`,children:[s.jsx(E,{children:s.jsx(Se,{})}),s.jsx("span",{className:"font-iowan",children:"Filters"})]}),s.jsx(B,{as:m.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 translate-y-1",children:s.jsx(oe.Panel,{className:"absolute -right-4 top-[100%] z-10 mt-3 w-screen max-w-[250px] transform rounded-sm border border-[#1f1d1a] bg-brown-main-bg p-5 sm:right-0  sm:max-w-md",children:s.jsxs("form",{onSubmit:c(A),children:[s.jsx("div",{className:"gap-6",children:s.jsxs("div",{className:"",children:[s.jsx("div",{className:"mb-2 block cursor-pointer text-left font-iowan text-[16px] font-bold text-[#1f1d1a]",children:"Date"}),s.jsxs("div",{className:"flex w-full w-full flex-row items-center gap-4",children:[s.jsxs("div",{className:"w-[47%] md:w-auto md:grow",children:[s.jsx("div",{className:"mb-2 block cursor-pointer text-left font-iowan text-[14px] font-bold text-[#1f1d1a]",children:"From"}),s.jsx("input",{type:"date",className:`w-full min-w-full appearance-none rounded-sm border-[#1f1d1a] bg-transparent px-3 py-2 pr-5 text-sm font-normal leading-tight text-[#1d1f1a] shadow sm:pr-3     ${(T=n.date)!=null&&T.message?"border-red-500":""}`,value:P,onChange:R})]}),s.jsxs("div",{className:"w-[47%] md:w-auto md:grow",children:[s.jsx("div",{className:"mb-2 block cursor-pointer text-left font-iowan  text-[14px] font-bold text-[#1f1d1a]",children:"To"}),s.jsx("input",{type:"date",className:`w-full min-w-full appearance-none rounded-sm border-[#1f1d1a] bg-transparent px-3 py-2 pr-5 text-sm font-normal leading-tight text-[#1d1f1a] shadow sm:pr-3     ${(v=n.date)!=null&&v.message?"border-red-500":""}`,value:i,min:P,onChange:k})]})]}),s.jsxs("div",{className:"mt-6 flex w-full items-center gap-4 sm:mt-6",children:[" ",s.jsxs("div",{className:"w-full",children:[s.jsx("label",{className:"mb-2 block cursor-pointer text-left font-iowan  text-[14px] font-bold capitalize text-[#1f1d1a]",children:"Status"}),s.jsxs("select",{...r("status"),className:`focus:shadow-outline w-full  appearance-none rounded-sm border-[#1f1d1a] bg-transparent py-2 pl-6 pr-8 font-Inter text-sm font-normal leading-tight text-[#1d1f1a] shadow  focus:outline-none ${(V=n.status)!=null&&V.message?"border-red-500":""}`,onChange:u=>b("status",u.target.value),children:[s.jsx("option",{value:"",children:"-select-"}),Object.entries(Ue).map(([u,Q])=>s.jsx("option",{value:String(u),children:Q},u))]}),s.jsx("p",{className:"text-field-error italic text-red-500",children:(F=n.status)==null?void 0:F.message})]}),s.jsxs("div",{className:"w-full",children:[s.jsx("label",{className:"mb-2 block cursor-pointer text-left font-iowan  text-[14px] font-bold capitalize  text-[#1f1d1a]",children:"Recipients"}),s.jsxs("select",{className:`focus:shadow-outline w-full  appearance-none rounded-sm border border-[#1f1d1a] bg-transparent py-2 pl-6 pr-8 font-Inter text-sm font-normal leading-tight text-[#1d1f1a] shadow  focus:outline-none ${(L=n.status)!=null&&L.message?"border-red-500":""}`,...r("recipients"),children:[s.jsx("option",{value:"",children:"-select-"}),(Y=f==null?void 0:f.myMembers)==null?void 0:Y.map((u,Q)=>{var Z,J,D;return s.jsxs("option",{value:u==null?void 0:u.member_id,children:[(Z=u==null?void 0:u.user)==null?void 0:Z.first_name," ",(J=u==null?void 0:u.user)==null?void 0:J.last_name," |"," ",(D=u==null?void 0:u.user)==null?void 0:D.email]},Q)})]}),s.jsx("p",{className:"text-field-error italic text-red-500",children:(G=n.status)==null?void 0:G.message})]})]}),s.jsx("p",{className:"text-field-error italic text-red-500",children:(X=n.date)==null?void 0:X.message})]})}),s.jsxs("div",{className:"mt-6 flex w-full items-center justify-center gap-6",children:[s.jsxs("button",{type:"button",onClick:()=>{d({status:"",end_date:"",start_date:"",recipients:"",availability:"available"}),e.delete("status"),e.delete("end_date"),e.delete("start_date"),e.delete("recipients"),e.set("availability","available"),x(e)},className:"flex grow items-center justify-center gap-3 rounded-[.25rem] border border-[#1f1d1a] p-4 py-2 text-center font-iowan  text-[16px]  font-[700] leading-5 md:h-[40px]",children:[s.jsx(E,{children:s.jsx(Ee,{})}),"Reset"]}),s.jsx("button",{type:"submit",className:"grow whitespace-nowrap rounded-[.25rem] bg-black p-3 py-2 text-center font-iowan text-[1rem]  font-[700] leading-5 text-white transition-colors duration-100 disabled:bg-disabled-black md:h-[40px]",children:"Apply Filters"})]})]})})})]})}})}function ts({isOpen:e,closeModal:x,showModifyRecentOption:l}){const{dispatch:f,state:_}=m.useContext(me),{dispatch:a}=m.useContext(ce),[r,c]=m.useState(!1),n=K();async function d(){c(!0);try{const g=await new ie().create("updates",{name:`Update ${O().format("MMM D, YYYY")}`,user_id:_.user,mrr:0,arr:0,cash:0,burnrate:0,date:O().format("MMM D, YYYY"),public_link_enabled:0,private_link_open:1,company_id:_.company.id}),w=new H;await w.callRawAPI("/v3/api/custom/goodbadugly/activities/draft",{update_id:g.data},"POST");const q=(await w.callRawAPI(`/v4/api/records/updates?filter=id,lt,${g.data}&page=1,1&order=id,desc`,void 0,"GET")).list[0];q&&await o(q.id,g.data),x(),n(`/member/edit-updates/${g.data}?autofocus=true`)}catch(y){ne(f,y.message),re(a,y.message,5e3,"error")}c(!1)}async function b(){c(!0);try{const g=await new ie().create("updates",{name:`Update ${O().format("MMM D, YYYY")}`,user_id:_.user,mrr:0,arr:0,cash:0,burnrate:0,date:O().format("MMM D, YYYY"),public_link_enabled:0,private_link_open:1,company_id:_.company.id});await new H().callRawAPI("/v3/api/custom/goodbadugly/activities/draft",{update_id:g.data},"POST"),x(),n(`/member/edit-updates/${g.data}?autofocus=true`)}catch(y){ne(f,y.message),re(a,y.message,5e3,"error")}c(!1)}async function o(y,g){const w=new H,{list:i}=await w.callRawAPI(`/v4/api/records/notes?filter=update_id,eq,${y}&order=id,asc`,void 0,"GET");for(let q=0;q<i.length;q++){const P=i[q];await w.callRawAPI("/v4/api/records/notes",{update_id:g,content:P.content,type:P.type,status:0},"POST")}}return s.jsx(B,{appear:!0,show:e,as:m.Fragment,children:s.jsxs(z,{as:"div",className:"relative z-[50]",onClose:x,children:[s.jsx(B.Child,{as:m.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:s.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),s.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:s.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:s.jsx(B.Child,{as:m.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:s.jsxs(z.Panel,{className:"relative w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg text-left align-middle text-base shadow-xl transition-all",children:[r?s.jsx("div",{className:"absolute inset-0 z-10 flex items-center justify-center",children:s.jsx(Ve,{})}):null,s.jsxs("div",{className:"divide-y divide-[#1f1d1a]/10",children:[l?s.jsxs("button",{className:"flex w-full items-start gap-4 px-6 py-4 hover:bg-brown-main-bg",onClick:d,disabled:r,children:[s.jsx("img",{src:"/assets/edit-2.svg",alt:"",className:"h-8 w-8"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-left text-xl font-semibold",children:"Modify Most Recent"}),s.jsx("p",{className:"mt-1 font-medium",children:"Start with most recent sent update"})]})]}):null,s.jsxs(Ce,{className:"flex w-full items-start gap-4 px-6 py-4 hover:bg-brown-main-bg",to:"/member/select-template",disabled:r,children:[s.jsx(Ge,{className:"h-8 w-8 text-primary-black",strokeWidth:2}),s.jsxs("div",{children:[s.jsx("p",{className:"text-left text-xl font-semibold",children:"New Template Update"}),s.jsx("p",{className:"mt-1 font-medium",children:"Start an update using the default template"})]})]}),s.jsxs("button",{className:"flex w-full items-start gap-4 px-6 py-4 hover:bg-brown-main-bg",onClick:b,disabled:r,children:[s.jsx(Qe,{className:"h-8 w-8 text-primary-black",strokeWidth:2}),s.jsxs("div",{children:[s.jsx("p",{className:"text-left text-xl font-semibold",children:"New Blank Update"}),s.jsx("p",{className:"mt-1 font-medium",children:"Start an update from scratch"})]})]})]})]})})})})]})})}function is({update_id:e}){const[x,l]=m.useState(!1),{dispatch:f,state:_}=m.useContext(me),{dispatch:a}=m.useContext(ce),[r,c]=m.useState(!1),n=K();async function d(o,y){const g=new H,{list:w}=await g.callRawAPI(`/v4/api/records/notes?filter=update_id,eq,${o}`,void 0,"GET");await Promise.all(w.map(i=>g.callRawAPI("/v4/api/records/notes",{update_id:y,content:i.content,type:i.type,status:0},"POST")))}async function b(){c(!0);try{const o=new ie,y=(await o.getList("companies",{filter:[`user_id,eq,${_.user}`,"default_company,eq,1"]})).list[0],g=await o.create("updates",{name:`Update ${O().format("MMM D, YYYY")}`,user_id:_.user,mrr:0,arr:0,cash:0,burnrate:0,date:O().format("MMM D, YYYY"),company_id:y.id,public_link_enabled:0,private_link_open:1});await new H().callRawAPI("/v3/api/custom/goodbadugly/activities/draft",{update_id:g.data},"POST"),await d(e,g.data),l(!1),n(`/member/edit-updates/${g.data}?autofocus=true`)}catch(o){ne(f,o.message),re(a,o.message,5e3,"error")}c(!1)}return s.jsxs(s.Fragment,{children:[s.jsx("button",{onClick:()=>l(!0),className:"rounded-lg p-2 transition-colors hover:bg-gray-100",title:"Copy",children:s.jsx(Pe,{})}),s.jsx(B,{appear:!0,show:x,as:m.Fragment,children:s.jsxs(z,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>l(!1),children:[s.jsx(B.Child,{as:m.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:s.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),s.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:s.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:s.jsx(B.Child,{as:m.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:s.jsxs(z.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(z.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Are you sure"}),s.jsx("button",{onClick:()=>l(!1),type:"button",children:s.jsx(je,{className:"h-6 w-6"})})]}),s.jsx("p",{className:"mt-2",children:"Are you certain you wish to generate a report from this version?"}),s.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[s.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>l(!1),children:"Cancel"}),s.jsx(ke,{loading:r,disabled:r,onClick:b,className:"disabled:bg-disabledblack w-full rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Yes, create"})]})]})})})})]})})]})}function ns({update:e,afterDelete:x}){const[l,f]=m.useState(!1),{dispatch:_}=m.useContext(me),{dispatch:a}=m.useContext(ce),[r,c]=m.useState(!1);async function n(){c(!0);try{await new H().callRawAPI(`/v4/api/records/updates/${e.id}`,{},"DELETE"),f(!1),x()}catch(d){ne(_,d.message),re(a,d.message,5e3,"error")}c(!1)}return s.jsxs(s.Fragment,{children:[s.jsxs("button",{className:"rounded-lg p-2 px-1 text-xs text-red-500 transition-colors hover:bg-gray-100",onClick:()=>f(!0),title:"Delete Update",children:[" ",s.jsx(Me,{})]}),s.jsx(B,{appear:!0,show:l,as:m.Fragment,children:s.jsxs(z,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>f(!1),children:[s.jsx(B.Child,{as:m.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:s.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),s.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:s.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:s.jsx(B.Child,{as:m.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:s.jsxs(z.Panel,{className:"w-full max-w-xl transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(z.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Delete Update"}),s.jsx("button",{onClick:()=>f(!1),type:"button",children:s.jsx(je,{className:"h-6 w-6"})})]}),s.jsxs("p",{className:"mt-2",children:["You are about to delete Updates ",e.id,", note that this action is irreversible ",s.jsx("br",{}),s.jsx("br",{}),s.jsxs("span",{className:"font-semibold",children:[" ",s.jsxs("span",{className:"font-semibold",children:[" ","This action cannot be undone."]})]})]}),s.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[s.jsx("button",{className:"rounded-lg border py-2 text-center font-medium",type:"button",onClick:()=>f(!1),children:"Cancel"}),s.jsx(He,{loading:r,disabled:r,onClick:n,className:"rounded-lg bg-[#1f1d1a] py-2 text-center font-iowan font-semibold text-white transition-colors duration-100 disabled:bg-opacity-60",children:"Yes, delete"})]})]})})})})]})})]})}const h={title:{decline_collaboration:"Decline Collaboration",accept_collaboration:"Accept Collaboration",accept_update_request:"Accept Update Request",decline_update_request:"Decline Update Request"},table:{decline_collaboration:"update_collaborators",accept_collaboration:"update_collaborators",accept_update_request:"update_requests",decline_update_request:"update_requests"},endpoint:{decline_collaboration:"/v3/api/custom/goodbadugly/collaborator/response",accept_collaboration:"/v3/api/custom/goodbadugly/collaborator/response",accept_update_request:"/v3/api/custom/goodbadugly/update-requests/acceptance",decline_update_request:"/v3/api/custom/goodbadugly/update-requests/acceptance"},method:{decline_collaboration:"POST",accept_collaboration:"POST",accept_update_request:"POST",decline_update_request:"POST"},status:{decline_collaboration:2,accept_collaboration:1,accept_update_request:1,decline_update_request:2},mode:{decline_collaboration:"custom",accept_collaboration:"custom",accept_update_request:"custom",decline_update_request:"custom"},action:{decline_collaboration:"decline",accept_collaboration:"accept",accept_update_request:"accept",decline_update_request:"decline"},customMessage:{decline_collaboration:"Are you sure you want to decline this update collaboration?",accept_collaboration:"Are you sure you want to accept this update collaboration?",accept_update_request:"Are you sure you want to accept this update request?",decline_update_request:"Are you sure you want to decline this update request?"}},se=e=>{var l,f,_,a;const x=(l=e==null?void 0:e.name)!=null&&l.startsWith("Update ")&&((_=(f=e==null?void 0:e.name)==null?void 0:f.slice(7))!=null&&_.match(/^\d{4}-\d{2}-\d{2}$/))?`Update ${new Date((a=e==null?void 0:e.name)==null?void 0:a.slice(7)).toLocaleString("en-US",{month:"short",day:"2-digit",year:"numeric"}).replace(/,/g,",")}`:e==null?void 0:e.name;return x==null?void 0:x.substring(0,20)},ge={0:"Draft",1:"Sent",2:"Deleted",3:"Collaboration",4:"Received",5:"New",6:"Invited",7:"Rejected",8:"Received",9:"Pending",10:"Accepted"},rs={0:"Pending",1:"Accepted",2:"Rejected",3:"Draft",4:"Sent",5:"Deleted",6:"New",8:"Scheduled",7:"Received",9:"Expired"},ye={0:"#BCBBBA",1:"#9DD321",2:"#BCBBBA",3:"#BCBBBA",4:"#CAB8FF",5:"#F6A13C",6:"#4DC6EC",7:"#BCBBBA",8:"#CAB8FF",9:"#F6A13C",10:"#CAB8FF"},ls={0:"#F6A13C",1:"#9DD321",2:"#BCBBBA",3:"#BCBBBA",4:"#9DD321",5:"#BCBBBA",6:"#F6A13C",7:"#CAB8FF",8:"#9DD321",9:"#ff4e4e"},M={0:7,1:3,2:8},cs={0:5,1:8},ae={[S.MY_UPDATES]:{0:9},[S.TEAM_UPDATES]:{0:5,3:4,7:7},draft:{0:9,3:10}},os={1:(e,x)=>{var l,f,_,a,r,c;if((l=e==null?void 0:e.activity_type)!=null&&l.includes("COLLABORATOR_"))return s.jsx(s.Fragment,{children:se(e)});if((f=e==null?void 0:e.activity_type)!=null&&f.includes("REQUEST_")){if((e==null?void 0:e.activity_status)===0)return s.jsxs(s.Fragment,{children:[(_=e==null?void 0:e.creator)==null?void 0:_.first_name," ",(a=e==null?void 0:e.creator)==null?void 0:a.last_name]});if([6,7,1].includes(e==null?void 0:e.activity_status))return s.jsx(s.Fragment,{children:se(e)})}return e!=null&&e.sent_at||[S.MY_UPDATES].includes(x)?s.jsx(s.Fragment,{children:se(e)}):s.jsxs(s.Fragment,{children:[(r=e==null?void 0:e.user)==null?void 0:r.first_name," ",(c=e==null?void 0:e.user)==null?void 0:c.last_name]})},2:(e,x)=>{var l,f,_,a,r,c;if(!(e!=null&&e.sent_at)&&[(l=S)==null?void 0:l.MY_UPDATES].includes(x))return s.jsxs(s.Fragment,{children:[(f=e==null?void 0:e.user)==null?void 0:f.first_name," ",(_=e==null?void 0:e.user)==null?void 0:_.last_name]});if(!(e!=null&&e.sent_at)&&[(a=S)==null?void 0:a.TEAM_UPDATES].includes(x)){if((r=e==null?void 0:e.activity_type)!=null&&r.includes("COLLABORATOR_")&&(e==null?void 0:e.activity_status)===2)return s.jsx(s.Fragment,{children:se(e)});if((c=e==null?void 0:e.activity_type)!=null&&c.includes("REQUEST_")&&(e==null?void 0:e.activity_status)===0)return s.jsx(s.Fragment,{children:se(e)})}return null},3:(e,x)=>{var l,f,_,a,r,c,n,d;if([(l=S)==null?void 0:l.TEAM_UPDATES].includes(x)){if((f=e==null?void 0:e.activity_type)!=null&&f.includes("UPDATE_")&&(e==null?void 0:e.activity_status)===1)return s.jsxs("p",{className:"truncate text-center font-iowan text-[1.125rem] font-[700] capitalize leading-5 text-gray-800",children:[(_=e==null?void 0:e.user)==null?void 0:_.first_name," ",(a=e==null?void 0:e.user)==null?void 0:a.last_name]});if((r=e==null?void 0:e.activity_type)!=null&&r.includes("COLLABORATOR_")&&(e==null?void 0:e.activity_status)===1||(c=e==null?void 0:e.activity_type)!=null&&c.includes("REQUEST_")&&(e==null?void 0:e.activity_status)===3)return s.jsxs("p",{className:"truncate text-center font-iowan text-[1.125rem] font-[700] capitalize leading-5 text-gray-800",children:[(n=e==null?void 0:e.user)==null?void 0:n.first_name," ",(d=e==null?void 0:e.user)==null?void 0:d.last_name]})}return null}},be=(e,x,l)=>{const f=os[l];return f?f(e,x):null},as=()=>{const e=navigator.userAgent.toLowerCase();return e.indexOf("safari")!==-1&&e.indexOf("chrome")===-1};function ue({update:e,refresh:x,view:l=S.MY_UPDATES}){var L,Y,G,X,u,Q,Z,J,D,fe,xe;const{update:f,setGlobalState:_,globalState:a}=te(),{convertDate:r}=Ze();m.useContext(ce);const c=K(),{profile:n}=le({isPublic:!1}),[d]=W(),b=d.get("update_id"),o=d.get("action"),y=m.useRef(null),g=m.useRef(null),w=as(),[i,q]=m.useState({modal:null,showModal:!1,selectedItems:[],status:e==null?void 0:e.status,updateSeenLoading:!1,updateRequestLoading:!1,collaborationLoading:!1,isMenuOpen:!1,collaborator_id:null}),P=()=>[0,1].includes(i==null?void 0:i.status)||[3].includes(M==null?void 0:M[e==null?void 0:e.collaborator_status])||[8].includes(i==null?void 0:i.status)&&(e==null?void 0:e.update_recipient_id),I=t=>{var N,j,U;return(t==null?void 0:t.activity_status)!==null&&(t==null?void 0:t.activity_status)!==void 0?(N=t==null?void 0:t.activity_type)!=null&&N.includes("IN")&&(t==null?void 0:t.activity_status)==1?7:t.activity_status:t!=null&&t.collaborator_id?M[t==null?void 0:t.collaborator_status]:t!=null&&t.recipient_id?cs[t==null?void 0:t.recipient_seen]:t!=null&&t.update_request_id?(U=ae==null?void 0:ae[(j=S)==null?void 0:j.TEAM_UPDATES])==null?void 0:U[t==null?void 0:t.request_status]:t==null?void 0:t.status},A=()=>{var t;if([(t=S)==null?void 0:t.MY_UPDATES].includes(l)&&[0,1,8].includes(e==null?void 0:e.status)||[3].includes(M==null?void 0:M[e==null?void 0:e.collaborator_status]))return c(`/${n==null?void 0:n.role}/edit-updates/${e==null?void 0:e.id}`);if(e!=null&&e.activity_type.includes("REQUEST_IN")){if((e==null?void 0:e.activity_status)===1)return c(`/${n==null?void 0:n.role}/edit-updates/${e==null?void 0:e.id}`);if((e==null?void 0:e.activity_status)===6)return c(`/${n==null?void 0:n.role}/edit-updates/${e==null?void 0:e.id}?autofocus=true`);if((e==null?void 0:e.activity_status)===7)return c(`/${n==null?void 0:n.role}/edit-updates/${e==null?void 0:e.id}`)}if(e!=null&&e.activity_type.includes("UPDATE_IN"))return(e==null?void 0:e.activity_status)===6?c(`/${n==null?void 0:n.role}/update/private/view/${e==null?void 0:e.id}?new=${e==null?void 0:e.activity_id}`):c(`/${n==null?void 0:n.role}/update/private/view/${e==null?void 0:e.id}`);if(e!=null&&e.activity_type.includes("COLLABORATOR_IN")){if((e==null?void 0:e.activity_status)===1)return c(`/${n==null?void 0:n.role}/edit-updates/${e==null?void 0:e.id}`);if((e==null?void 0:e.activity_status)===6)return c(`/${n==null?void 0:n.role}/edit-updates/${e==null?void 0:e.id}?autofocus=true&new=${e==null?void 0:e.id}`);if((e==null?void 0:e.activity_status)===7)return c(`/${n==null?void 0:n.role}/edit-updates/${e==null?void 0:e.id}`)}if(e!=null&&e.activity_type.includes("REQUEST_OUT"))return c(`/${n==null?void 0:n.role}/update/private/view/${e==null?void 0:e.id}`);if(e!=null&&e.activity_type.includes("COLLABORATOR_OUT"))return c(`/${n==null?void 0:n.role}/update/private/view/${e==null?void 0:e.id}`)},R=t=>{var N,j,U,$,p;if((N=t==null?void 0:t.activity_type)!=null&&N.includes("REQUEST_IN"))return"Update Request";if((j=t==null?void 0:t.activity_type)!=null&&j.includes("COLLABORATOR_IN"))return"Collaboration Request";if((U=t==null?void 0:t.activity_type)!=null&&U.includes("COLLABORATOR")&&(t==null?void 0:t.activity_status)==1)return"Collaboration";if(($=t==null?void 0:t.activity_type)!=null&&$.includes("REQUEST_OUT"))return"Update Request";if((p=t==null?void 0:t.activity_type)!=null&&p.includes("UPDATE_IN"))return"Update (In)"},k=t=>{var N,j,U,$,p;if((N=t==null?void 0:t.activity_type)!=null&&N.includes("UPDATE_IN")){if((t==null?void 0:t.activity_status)==0)return"Pending";if((t==null?void 0:t.activity_status)==2)return"Rejected";if((t==null?void 0:t.activity_status)==1)return"Received";if((t==null?void 0:t.activity_status)==6)return"New"}if((j=t==null?void 0:t.activity_type)!=null&&j.includes("REQUEST_IN")){if((t==null?void 0:t.activity_status)==0)return"Pending";if((t==null?void 0:t.activity_status)==2)return"Rejected";if((t==null?void 0:t.activity_status)==1)return"Received";if((t==null?void 0:t.activity_status)==6)return"New"}if((U=t==null?void 0:t.activity_type)!=null&&U.includes("COLLABORATOR_IN")){if((t==null?void 0:t.activity_status)==0)return"Pending";if((t==null?void 0:t.activity_status)==2)return"Rejected";if((t==null?void 0:t.activity_status)==1)return"Received";if((t==null?void 0:t.activity_status)==6)return"New";if((t==null?void 0:t.activity_status)==7)return"Received";if((t==null?void 0:t.activity_status)==9)return"Expired"}if(($=t==null?void 0:t.activity_type)!=null&&$.includes("COLLABORATOR_OUT")){if((t==null?void 0:t.activity_status)==0)return"Pending";if((t==null?void 0:t.activity_status)==2)return"Rejected";if((t==null?void 0:t.activity_status)==1)return"Accepted";if((t==null?void 0:t.activity_status)==9)return"Expired"}if((p=t==null?void 0:t.activity_type)!=null&&p.includes("REQUEST_OUT")){if((t==null?void 0:t.activity_status)==0)return"Pending";if((t==null?void 0:t.activity_status)==2)return"Rejected";if((t==null?void 0:t.activity_status)==1)return"Accepted";if((t==null?void 0:t.activity_status)==9)return"Expired"}return null},C=()=>{c(`/${n==null?void 0:n.role}/view-updates/${e==null?void 0:e.id}`)},T=[{icon:null,component:s.jsx(is,{update_id:e==null?void 0:e.id}),label:null,onClick:null,statuses:[0,1]},{icon:null,component:s.jsx("button",{onClick:A,className:"p-2 rounded-lg transition-colors hover:bg-gray-100",title:"Edit updates details",children:s.jsx(Re,{})}),label:null,onClick:null,statuses:[0,1,3]},{icon:null,component:s.jsx("button",{onClick:()=>c(`/${n==null?void 0:n.role}/update/preview/${e==null?void 0:e.id}`),className:"p-2 rounded-lg transition-colors hover:bg-gray-100",title:"Preview Update",children:s.jsx($e,{})}),label:null,onClick:null,statuses:[0,1,3]},{icon:null,component:s.jsx(ns,{update:e}),label:null,onClick:null,statuses:[0,1]}],v=(t,N,j)=>{q(U=>({...U,modal:t,showModal:N,selectedItems:Array.isArray(j)?j:[j]}))},V=async t=>{var U,$;const j=await new ie().getList("update_collaborators",{filter:[`collaborator_id,eq,${n==null?void 0:n.id}`,`goodbadugly_update_collaborators.update_id,eq,${t}`]});return($=(U=j==null?void 0:j.list)==null?void 0:U[0])==null?void 0:$.id};m.useEffect(()=>{if(!w)return;const t=N=>{g.current&&y.current&&!g.current.contains(N.target)&&!y.current.contains(N.target)&&q(j=>({...j,isMenuOpen:!1}))};return document.addEventListener("mousedown",t),()=>{document.removeEventListener("mousedown",t)}},[w]),m.useEffect(()=>{const t=async N=>{const j=await V(N);j&&q(U=>({...U,collaborator_id:j}))};n!=null&&n.id&&t(e==null?void 0:e.id),I(e)},[e==null?void 0:e.id,n==null?void 0:n.id]),m.useEffect(()=>{var t,N,j;b&&b===((t=e==null?void 0:e.id)==null?void 0:t.toString())&&([S.TEAM_UPDATES].includes(l)&&((N=e==null?void 0:e.activity_type)!=null&&N.includes("COLLABORATOR_IN"))&&((e==null?void 0:e.activity_status)===0||(e==null?void 0:e.activity_status)===6)?i.collaborator_id&&(o==="decline"?v("decline_collaboration",!0,[i.collaborator_id,e==null?void 0:e.id]):v("accept_collaboration",!0,[i.collaborator_id,e==null?void 0:e.id])):[S.MY_UPDATES].includes(l)&&(e!=null&&e.update_request_id)&&((j=e==null?void 0:e.update_request)==null?void 0:j.is_requested)===1&&(e==null?void 0:e.user_id)===(n==null?void 0:n.id)&&(e==null?void 0:e.request_status)===0&&(o==="decline"?v("decline_update_request",!0,[e==null?void 0:e.update_request_id,e==null?void 0:e.id]):v("accept_update_request",!0,[e==null?void 0:e.update_request_id,e==null?void 0:e.id])))},[b,o,e==null?void 0:e.id,l,i.collaborator_id,n==null?void 0:n.id]);const F=e!=null&&e.sent_at?O(e.sent_at).add(e.recipient_access??0,"days").diff(O(),"days"):0;return s.jsxs(s.Fragment,{children:[i!=null&&i.updateSeenLoading?s.jsx(ve,{color:"black",loading:!0}):null,s.jsxs("div",{className:"relative h-[15.125rem] min-h-[16rem] rounded-[.25rem]  border border-black bg-brown-main-bg pb-3 text-center shadow-lg transition-all duration-300 ",children:[[(L=S)==null?void 0:L.MY_UPDATES].includes(l)&&[0,1].includes(e==null?void 0:e.status)||[3].includes(M==null?void 0:M[e==null?void 0:e.collaborator_status])?s.jsxs(s.Fragment,{children:[s.jsx("button",{ref:y,className:`absolute right-3 top-2 rounded-full p-1 transition-colors ${w?"":"peer"} hover:bg-black/5`,onClick:()=>{w&&q(t=>({...t,isMenuOpen:!t.isMenuOpen}))},children:s.jsx(Je,{className:"w-5 h-5 text-gray-600"})}),s.jsx("div",{ref:g,className:`absolute right-3 ${w?i.isMenuOpen?"top-12 opacity-100":"top-[-5000%] opacity-0":"top-[-5000%] opacity-0 hover:top-12 hover:opacity-100 focus:top-12 focus:opacity-100 peer-focus:top-12 peer-focus:opacity-100 peer-focus-visible:top-12 peer-focus-visible:opacity-100"} flex gap-2 rounded-[.25rem] border border-black bg-brown-main-bg p-2 shadow-lg transition-all`,children:T.map((t,N)=>s.jsx(m.Fragment,{children:t.statuses.includes(e==null?void 0:e.status)||t.statuses.includes(M==null?void 0:M[e==null?void 0:e.collaborator_status])?t.component:null},N))})]}):null,s.jsxs("div",{className:`flex h-full flex-col items-center justify-center p-2 ${e!=null&&e.sent_at,""}`,children:[s.jsxs("div",{className:"flex flex-col gap-5 justify-center items-center grow",children:[s.jsxs("div",{disabled:![0,1].includes(i==null?void 0:i.status),onClick:A,className:`flex w-full min-w-full max-w-full flex-col items-center justify-center truncate ${[0,1].includes(i==null?void 0:i.status)?"cursor-pointer":""}`,children:[s.jsx(E,{children:s.jsx(he,{display:s.jsx("p",{className:"w-full min-w-full max-w-full truncate  text-center font-iowan text-[1.125rem] font-[700] capitalize leading-5 text-[#1f1d1a]",children:be(e,l,1)}),className:`${P()?"cursor-pointer":"cursor-default"}`,openOnClick:!1,show:!!(e!=null&&e.company_name),backgroundColor:"#1f1d1a",place:"top",children:e!=null&&e.company_name?s.jsxs("p",{className:"mt-1 text-sm text-white",children:["(",e==null?void 0:e.company_name,")"]}):null})}),s.jsx("p",{className:"font-iowan-regular mt-3 text-[14px] font-normal text-[#1f1d1a]",children:r((e==null?void 0:e.activity_time)??(e==null?void 0:e.sent_at)??(e==null?void 0:e.date),{formatMatcher:"best fit",year:"numeric",month:"numeric",day:"numeric",timeZoneName:"short",timeZone:"America/Los_Angeles"}).replace(", "," - ")})]}),s.jsxs("div",{className:`flex flex-col items-center justify-center gap-1 ${e!=null&&e.sent_at?"-mb-[18px]":""}`,children:[s.jsx("span",{style:{backgroundColor:(e==null?void 0:e.activity_status)!=null?ls[(Y=e==null?void 0:e.activity_type)!=null&&Y.includes("IN")&&(e==null?void 0:e.activity_status)==1?7:e==null?void 0:e.activity_status]:e!=null&&e.update_request_id&&((G=e==null?void 0:e.update_request)==null?void 0:G.is_requested)===1&&(e==null?void 0:e.user_id)===(n==null?void 0:n.id)&&(e==null?void 0:e.request_status)===0?ye[4]:ye[i==null?void 0:i.status]},className:"flex h-[1.5rem] w-full max-w-fit items-center justify-center whitespace-nowrap rounded-[100px] border-[.0625rem] border-[#1f1d1a] px-[10px] font-iowan text-xs",children:(e==null?void 0:e.activity_status)!=null?rs[(X=e==null?void 0:e.activity_type)!=null&&X.includes("_IN")&&(e==null?void 0:e.activity_status)==1?7:e==null?void 0:e.activity_status]:e!=null&&e.update_request_id&&((u=e==null?void 0:e.update_request)==null?void 0:u.is_requested)===1&&(e==null?void 0:e.user_id)===(n==null?void 0:n.id)&&(e==null?void 0:e.request_status)===0?ge[4]:ge[i==null?void 0:i.status]}),(Q=e==null?void 0:e.update)!=null&&Q.sent_at?s.jsx("span",{className:"font-iowan text-[.875rem] font-normal leading-[1.25rem] text-[#1f1d1a]",children:F>=0?s.jsxs(s.Fragment,{children:["(",F<1?"":`${F}d  `,e!=null&&e.sent_at?O(e==null?void 0:e.sent_at).add(e==null?void 0:e.recipient_access,"days").diff(O().add(F,"days"),"hours"):0,"hr remaining)"]}):"Expired"}):null,(Z=e==null?void 0:e.activity_type)!=null&&Z.includes("REQUEST")||(J=e==null?void 0:e.activity_type)!=null&&J.includes("COLLABORATOR_")||(D=e==null?void 0:e.activity_type)!=null&&D.includes("UPDATE_IN")?s.jsx(E,{children:s.jsx(he,{display:s.jsx("p",{className:"text-black font-iowan-regular",children:R(e)}),className:`${P()?"cursor-pointer":"cursor-default"}`,openOnClick:!1,backgroundColor:"#1f1d1a",place:"top",children:s.jsx("p",{className:"mt-1 text-sm text-white",children:k(e)})})}):null]})]}),s.jsx("div",{className:"mt-[.75rem]",children:be(e,l,3)}),s.jsxs("div",{className:"w-full",children:[[S.TEAM_UPDATES].includes(l)&&((fe=e==null?void 0:e.activity_type)!=null&&fe.includes("COLLABORATOR_IN"))&&((e==null?void 0:e.activity_status)===0||(e==null?void 0:e.activity_status)===6)?s.jsx(E,{children:s.jsx(_e,{onDecline:()=>{v("decline_collaboration",!0,[i==null?void 0:i.collaborator_id,e==null?void 0:e.id])},onAccept:()=>{v("accept_collaboration",!0,[i==null?void 0:i.collaborator_id,e==null?void 0:e.id])},update:e,loading:i==null?void 0:i.collaborationLoading})}):null,[S.MY_UPDATES].includes(l)&&(e!=null&&e.update_request_id)&&((xe=e==null?void 0:e.update_request)==null?void 0:xe.is_requested)===1&&(e==null?void 0:e.user_id)===(n==null?void 0:n.id)&&(e==null?void 0:e.request_status)===0?s.jsx(E,{children:s.jsx(_e,{onDecline:t=>{v("decline_update_request",!0,[t==null?void 0:t.update_request_id,t==null?void 0:t.id])},onAccept:t=>{v("accept_update_request",!0,[t==null?void 0:t.update_request_id,t==null?void 0:t.id])},update:e,loading:i==null?void 0:i.updateRequestLoading})}):null]}),s.jsx("div",{className:"",children:(e==null?void 0:e.activity_status)==4&&[S.MY_UPDATES].includes(l)?s.jsxs("button",{onClick:C,className:` flex h-[2rem] w-full items-center justify-center gap-2 self-end rounded-[.125rem]   border-black px-2 text-sm\r
            font-medium text-black transition-all duration-200\r
            active:translate-y-[2px] active:shadow-none`,children:[s.jsx(Ie,{className:"w-4 h-4"}),"View Insights"]}):![S.TEAM_UPDATES].includes(l)&&!(e!=null&&e.update_request_id)?s.jsxs("button",{onClick:()=>{c(`/${n==null?void 0:n.role}/edit-updates/${e==null?void 0:e.id}`)},className:` flex h-[2rem] w-full items-center justify-center gap-2 self-end rounded-[.125rem]   border-black px-2 text-sm\r
            font-medium text-black transition-all duration-200\r
            active:translate-y-[2px] active:shadow-none`,children:[s.jsx(De,{className:"w-4 h-4"}),"Continue Editing"]}):null})]})]}),s.jsx(E,{children:s.jsx(Xe,{isOpen:(i==null?void 0:i.showModal)&&["decline_collaboration","accept_collaboration","accept_update_request","decline_update_request"].includes(i==null?void 0:i.modal),customMessage:h==null?void 0:h.customMessage[i==null?void 0:i.modal],title:h==null?void 0:h.title[i==null?void 0:i.modal],data:{update_id:i==null?void 0:i.selectedItems[1],...["accept_update_request","decline_update_request"].includes(i==null?void 0:i.modal)?{request:h==null?void 0:h.status[i==null?void 0:i.modal]}:{},...["decline_collaboration","accept_collaboration"].includes(i==null?void 0:i.modal)?{status:h==null?void 0:h.status[i==null?void 0:i.modal],collaborator_id:i==null?void 0:i.selectedItems[0]}:{}},table:h==null?void 0:h.table[i==null?void 0:i.modal],action:h==null?void 0:h.action[i==null?void 0:i.modal],mode:h==null?void 0:h.mode[i==null?void 0:i.modal],options:{endpoint:h==null?void 0:h.endpoint[i==null?void 0:i.modal],method:h==null?void 0:h.method[i==null?void 0:i.modal],payload:["decline_collaboration","accept_collaboration"].includes(i==null?void 0:i.modal)?{status:h==null?void 0:h.status[i==null?void 0:i.modal],update_id:i==null?void 0:i.selectedItems[1],collaborator_id:i==null?void 0:i.selectedItems[0]}:{request:h==null?void 0:h.status[i==null?void 0:i.modal],update_id:i==null?void 0:i.selectedItems[1]}},onSuccess:()=>{const t=i==null?void 0:i.modal;t==="accept_update_request"||t==="decline_update_request"?(console.log("Setting updateRequestChange global state",!(a!=null&&a.updateRequestChange)),_("updateRequestChange",!(a!=null&&a.updateRequestChange)),x(),t==="accept_update_request"&&setTimeout(()=>{c(`/${n==null?void 0:n.role}/edit-updates/${i==null?void 0:i.selectedItems[1]}?autofocus=true`)},100)):(t==="accept_collaboration"||t==="decline_collaboration")&&(console.log("Setting collaborationChange global state",!(a!=null&&a.collaborationChange)),_("collaborationChange",!(a!=null&&a.collaborationChange)),x(),t==="accept_collaboration"&&setTimeout(()=>{c(`/${n==null?void 0:n.role}/edit-updates/${i==null?void 0:i.selectedItems[1]}`)},100)),v(t,!1,[])},onClose:()=>{v(i==null?void 0:i.modal,!1,[])},inputConfirmation:!1})})]})}const Is=Object.freeze(Object.defineProperty({__proto__:null,default:ue},Symbol.toStringTag,{value:"Module"}));function Ne({updates:e,refresh:x,view:l}){return s.jsx(s.Fragment,{children:[...e].map((f,_)=>s.jsx(ue,{update:f,refresh:x,view:l},`${f==null?void 0:f.id}-${_}`))})}const Fs=Object.freeze(Object.defineProperty({__proto__:null,default:Ne},Symbol.toStringTag,{value:"Module"})),ms=({myUpdateRefreshRef:e})=>{const{authState:x,authDispatch:l,globalDispatch:f,getMany:_,getSingle:a}=te(),{profile:r}=le(),[c]=W(),n=m.useMemo(()=>c==null?void 0:c.toString(),[c]),[d,b]=m.useState({loading:!1,page:1,pages:null,refresh:!1,requestedUpdates:[]}),[o,y]=m.useState(!1),{updates:g,refetch:w,loading:i,paginationData:q}=es(x.user);K();const P=async()=>{var I;try{const A=await _("update_requests",{filter:[`goodbadugly_update_requests.user_id,eq,${r==null?void 0:r.id}`,"goodbadugly_update_requests.status,eq,0","goodbadugly_update_requests.is_requested,eq,1","goodbadugly_update_requests.request,eq,0"],join:["updates|update_id"]}),R=await Promise.all((I=A==null?void 0:A.data)==null?void 0:I.map(async k=>{var V,F,L;const C=await a("user",k==null?void 0:k.requesting_user_id,{join:["companies"],method:"GET"}),T=k==null?void 0:k.updates,v={};return Object.entries(k).forEach(([Y,G])=>{Y!="updates"&&(v[Y]=G)}),{...T,user:C==null?void 0:C.data,company_name:(L=(F=(V=C==null?void 0:C.data)==null?void 0:V.companies)==null?void 0:F[0])==null?void 0:L.name,update_request:v,update_request_id:v==null?void 0:v.id,request_status:v==null?void 0:v.status}}));b(k=>({...k,requestedUpdates:R||[]}))}catch(A){console.error("Error fetching update requests:",A)}};return m.useEffect(()=>{f({type:"SETPATH",payload:{path:"updates"}}),r!=null&&r.id&&(w(),P())},[r==null?void 0:r.id,n]),s.jsxs(s.Fragment,{children:[s.jsx("button",{hidden:!0,ref:e,onClick:()=>{w(),P()}}),s.jsxs("div",{className:"relative mt-4 grid h-full max-h-full min-h-full w-full grid-rows-[auto_1fr] space-y-[2rem] rounded",children:[s.jsxs("div",{className:"flex flex-col justify-between w-full md:flex-row md:text-center",children:[s.jsx("h2",{className:"font-iowan text-xl font-semibold capitalize sm:text-[30px]",children:"My Updates"}),s.jsx(ss,{})]}),s.jsxs("div",{className:`grid h-full max-h-full min-h-full w-full grid-rows-[1fr_auto] ${i?"":"mt-6"}`,children:[s.jsx("div",{className:"overflow-auto w-full min-w-full max-w-full h-full min-h-full max-h-full",children:s.jsxs("div",{className:"grid w-full min-w-full max-w-full grid-cols-[repeat(auto-fill,minmax(16.6875rem,1fr))] flex-wrap items-start justify-between gap-[1rem] pb-4",children:[i&&g.length==0||s.jsx(We,{setComposeUpdate:y}),i?s.jsx("div",{className:"",children:s.jsx(ve,{color:"black",loading:!0})}):s.jsx(Ne,{updates:[...d.requestedUpdates,...g],refresh:()=>{w(),P()}})]})}),(d==null?void 0:d.page)>=(d==null?void 0:d.pages)?null:(d==null?void 0:d.total)>0&&s.jsx("div",{className:"flex justify-center my-4",children:s.jsx("button",{className:"self-cneter m-auto w-[7.1875rem] min-w-[7.1875rem] max-w-[7.1875rem] animate-pulse border-2 border-[#1f1d1a] p-[.625rem] text-primary-black",children:s.jsx("span",{className:"font-semibold text-[#1f1d1a]",children:"Load More"})})})]})]}),s.jsx(ts,{isOpen:o,closeModal:()=>y(!1),showModifyRecentOption:(g==null?void 0:g.length)>0})]})},Te=({refreshMyUpdates:e})=>{const{authState:x,authDispatch:l,globalDispatch:f,custom:_}=te(),{profile:a}=le(),[r,c]=m.useState({myTeamUpdates:[],loading:!1,page:1,limit:10,total:null,pages:null,refresh:!0});K();const[n,d]=W();n.get("my_team_updates_page"),n.get("my_team_updates_limit");const b=async()=>{c(o=>({...o,loading:!0}));try{const o=await _({endpoint:`/v3/api/custom/goodbadugly/team-updates?page=${r==null?void 0:r.page}&limit=${r==null?void 0:r.limit}`,method:"GET"},null,!1);o!=null&&o.error||c(y=>({...y,myTeamUpdates:[...y.myTeamUpdates,...o==null?void 0:o.data],total:o==null?void 0:o.total,pages:o==null?void 0:o.pages,refresh:!1}))}catch(o){console.error("Error fetching data:",o)}finally{c(o=>({...o,loading:!1}))}};return m.useEffect(()=>{f({type:"SETPATH",payload:{path:"updates"}}),a!=null&&a.id&&(r!=null&&r.refresh)&&b()},[a==null?void 0:a.id,r==null?void 0:r.page,r==null?void 0:r.limit,r==null?void 0:r.refresh]),r!=null&&r.loading?s.jsx("div",{className:" z-[9999] h-full w-full bg-black/20",children:s.jsx("div",{className:"flex justify-center items-center h-full",children:s.jsx(pe,{loading:r==null?void 0:r.loading,color:"#1f1d1a"})})}):s.jsx(s.Fragment,{children:s.jsxs("div",{className:"relative grid h-full max-h-full min-h-full  w-full grid-cols-1 grid-rows-[auto_1fr] flex-col  rounded bg-brown-main-bg ",children:[s.jsxs("div",{className:"flex justify-between items-center w-full",children:[s.jsx("h2",{className:"font-iowan text-xl font-semibold capitalize sm:text-[30px]",children:"Team Updates"}),s.jsx(E,{children:s.jsx(de,{onSuccess:()=>{c(o=>({...o,myTeamUpdates:[],page:1,pages:null,refresh:!0}))}})})]}),s.jsxs("div",{className:"grid mt-6 w-full h-full min-h-full max-h-full grid-rows-[1fr_auto]",children:[s.jsx("div",{className:"overflow-auto w-full min-w-full max-w-full h-full min-h-full max-h-full",children:s.jsx("div",{className:"grid w-full min-w-full max-w-full grid-cols-[repeat(auto-fill,minmax(16.6875rem,1fr))] flex-wrap items-start justify-between gap-[1rem] pb-4 ",children:s.jsx(E,{children:s.jsx(Ke,{updates:r==null?void 0:r.myTeamUpdates,refresh:()=>{c(o=>({...o,myTeamUpdates:[],page:1,pages:null,refresh:!0}))},view:S.TEAM_UPDATES})})})}),(r==null?void 0:r.page)>(r==null?void 0:r.pages)?s.jsxs("div",{className:"flex justify-center items-center w-full h-full",children:[" ",s.jsxs("div",{className:"flex flex-col items-center justify-center gap-[1.5rem] text-center",children:[s.jsxs("div",{className:"flex flex-col justify-center items-center w-full text-center",children:[s.jsx("div",{className:"no-updates-icon",children:s.jsx(E,{children:s.jsx(Fe,{fill:"black",className:"!h-[3.75rem] !w-[3.75rem]"})})}),s.jsx("br",{}),s.jsx("div",{className:"font-iowan text-[20px] font-[700] md:text-[1.5rem] md:leading-[1.865rem]  ",children:"You Have No Team Updates"}),s.jsx("br",{}),s.jsx("p",{className:"font-inter text-[1rem] font-[400] leading-[1.5rem]",children:"You currently don’t have any team updates."}),s.jsx("p",{className:"font-inter text-[1rem] font-[400] leading-[1.5rem]",children:"Request one from your team now."})]}),s.jsx(E,{children:s.jsx(de,{onSuccess:()=>{c(o=>({...o,myTeamUpdates:[],page:1,pages:null,refresh:!0}))}})})]})]}):(r==null?void 0:r.total)>0&&s.jsx("button",{disabled:(r==null?void 0:r.page)>=(r==null?void 0:r.pages),onClick:()=>{c(o=>({...o,page:(o==null?void 0:o.page)+1,refresh:!0}))},className:"self-cneter m-auto w-[7.1875rem] min-w-[7.1875rem] max-w-[7.1875rem] animate-pulse border-2 border-[#1f1d1a] p-[.625rem] text-primary-black",children:s.jsx("span",{className:"font-semibold text-[#1f1d1a]",children:"Load More"})})]})]})})},Os=Object.freeze(Object.defineProperty({__proto__:null,default:Te},Symbol.toStringTag,{value:"Module"})),Bs=m.lazy(()=>we(()=>import("./Tabs-d291eb5e.js"),["assets/Tabs-d291eb5e.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/Tabs-27c985e5.css"])),fs=m.lazy(()=>we(()=>import("./ViewWrapper-e0c240de.js"),["assets/ViewWrapper-e0c240de.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/yup-342a5df4.js","assets/react-hook-form-a383372b.js","assets/yup-0917e80c.js","assets/@hookform/resolvers-b50d6e2a.js","assets/MkdInput-d37679e9.js","assets/react-toggle-6478c5c4.js","assets/@uppy/dashboard-51133bb7.js","assets/@fullcalendar/core-085b11ae.js","assets/core-b9802b0d.css","assets/@uppy/core-a4ba4b97.js","assets/@uppy/aws-s3-a6b02742.js","assets/@craftjs/core-a2cdaeb4.js","assets/@uppy/compressor-4bcbc734.js","assets/MkdInput-5e6afe8d.css","assets/useCompanyMember-0033d2de.js","assets/index-23a711b5.js","assets/DocumentTextIcon-54b5e200.js","assets/DocumentIcon-22c47322.js","assets/XMarkIcon-cfb26fe7.js","assets/InteractiveButton-060359e0.js","assets/index-dc002f62.js","assets/react-spinners-b860a5a3.js","assets/index-2a0f7dff.js","assets/index-afef2e72.js","assets/index-713720be.js","assets/useDate-c1da5729.js","assets/lucide-react-0b94883e.js","assets/index-4e4ee51a.js"])),xs=()=>{const e=m.useRef(null),{globalDispatch:x}=te(),[l,f]=m.useState({MY_UPDATES:{value:"my_updates",hasCount:!1,count:0},TEAM_UPDATES:{value:"team_updates",hasCount:!1,count:0}}),[_,a]=m.useState(l.MY_UPDATES.value);K();const[r,c]=W(),n=b=>{r.set("view",b),r.set("availability","available"),c(r.toString())},d=()=>{var b;e.current&&((b=e==null?void 0:e.current)==null||b.click())};return m.useEffect(()=>{r.get("view")&&a(r.get("view"))},[r]),m.useEffect(()=>{x({type:"SETPATH",payload:{path:"updates"}})},[]),s.jsx("div",{className:"relative mx-auto  h-full max-h-full min-h-full  space-y-5 p-[2rem]",children:s.jsx(E,{children:s.jsxs(fs,{view:_,views:[...Object.keys(l)],viewsMap:l,setView:b=>{n(l[b].value)},children:[s.jsx(E,{count:7,counts:[2,2,3,2,1],view:l.MY_UPDATES.value,children:s.jsx(ms,{myUpdateRefreshRef:e,view:l.MY_UPDATES.value})}),s.jsx(E,{count:7,counts:[2,2,3,2,1],view:l.TEAM_UPDATES.value,children:s.jsx(Te,{refreshMyUpdates:d,view:l.TEAM_UPDATES.value})})]})})})},Ls=Object.freeze(Object.defineProperty({__proto__:null,default:xs},Symbol.toStringTag,{value:"Module"}));export{Ls as C,Os as M,Bs as T,Is as U,Fs as a};
