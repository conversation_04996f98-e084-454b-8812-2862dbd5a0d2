import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as a,b as R,r as i,i as A}from"./vendor-4cdf2bd1.js";import{u as U}from"./react-hook-form-a383372b.js";import{o as D}from"./yup-0917e80c.js";import{c as L,a as c}from"./yup-342a5df4.js";import{M,A as _,G as F,t as G,f as O,I as $,s as q}from"./index-f2ad9142.js";import"./react-quill-a78e6fc7.js";/* empty css                   */import{M as u}from"./MkdInput-d37679e9.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@craftjs/core-a2cdaeb4.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@uppy/compressor-4bcbc734.js";let n=new M;const It=()=>{const{dispatch:j}=a.useContext(_),E=L({content:c(),type:c(),status:c(),update_id:c()}).required(),{dispatch:g}=a.useContext(F),[h,B]=a.useState({}),[b,f]=a.useState(!1),[N,x]=a.useState(!1),w=R(),[H,I]=i.useState(""),[K,T]=i.useState(""),[V,k]=i.useState(""),[z,v]=i.useState(""),{register:l,handleSubmit:C,setError:S,setValue:m,formState:{errors:p}}=U({resolver:D(E)}),y=A();i.useEffect(function(){(async function(){try{x(!0),n.setTable("notes");const t=await n.callRestAPI({id:Number(y==null?void 0:y.id)},"GET");t.error||(m("content",t.model.content),m("type",t.model.type),m("status",t.model.status),m("update_id",t.model.update_id),I(t.model.content),T(t.model.type),k(t.model.status),v(t.model.update_id),setId(t.model.id),x(!1))}catch(t){x(!1),console.log("error",t),G(j,t.message)}})()},[]);const P=async t=>{f(!0);try{n.setTable("notes");for(let r in h){let o=new FormData;o.append("file",h[r].file);let d=await n.uploadImage(o);t[r]=d.url}const s=await n.callRestAPI({id,content:t.content,type:t.type,status:t.status,update_id:t.update_id},"PUT");if(!s.error)q(g,"Updated"),w("/admin/notes");else if(s.validation){const r=Object.keys(s.validation);for(let o=0;o<r.length;o++){const d=r[o];S(d,{type:"manual",message:s.validation[d]})}}f(!1)}catch(s){f(!1),console.log("Error",s),S("content",{type:"manual",message:s.message})}};return a.useEffect(()=>{g({type:"SETPATH",payload:{path:"notes"}})},[]),e.jsxs("div",{className:" mx-auto rounded   p-5 shadow-md",children:[e.jsx("h4",{className:"text-[16px] font-medium md:text-xl",children:"Edit Notes"}),N?e.jsx(O,{}):e.jsxs("form",{className:" w-full max-w-lg",onSubmit:C(P),children:[e.jsx(u,{type:"text",page:"edit",name:"content",errors:p,label:"Content",placeholder:"Content",register:l,className:""}),e.jsx(u,{type:"text",page:"edit",name:"type",errors:p,label:"Type",placeholder:"Type",register:l,className:""}),e.jsx(u,{type:"text",page:"edit",name:"status",errors:p,label:"Status",placeholder:"Status",register:l,className:""}),e.jsx(u,{type:"text",page:"edit",name:"update_id",errors:p,label:"Update Id",placeholder:"Update Id",register:l,className:""}),e.jsx($,{type:"submit",className:"focus:shadow-outline rounded bg-primary-black px-4 py-2 font-bold text-white focus:outline-none",loading:b,disable:b,children:"Submit"})]})]})};export{It as default};
