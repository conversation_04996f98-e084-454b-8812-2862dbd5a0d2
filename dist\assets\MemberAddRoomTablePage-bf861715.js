import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as m,b as S}from"./vendor-4cdf2bd1.js";import{u as I}from"./react-hook-form-a383372b.js";import{o as N}from"./yup-0917e80c.js";import{c as w,a as i}from"./yup-342a5df4.js";import{G as f,I as A,M as v,s as O,t as k}from"./index-f2ad9142.js";import"./react-quill-a78e6fc7.js";/* empty css                   */import{M as l}from"./MkdInput-d37679e9.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@craftjs/core-a2cdaeb4.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@uppy/compressor-4bcbc734.js";const de=()=>{const{dispatch:c}=m.useContext(f),x=w({user_id:i(),other_user_id:i(),chat_id:i(),unread:i(),user_update_at:i(),other_user_update_at:i()}).required(),{dispatch:g}=m.useContext(f),[h,E]=m.useState({}),[_,u]=m.useState(!1),y=S(),{register:a,handleSubmit:j,setError:b,setValue:R,formState:{errors:o}}=I({resolver:N(x)});m.useState([]);const U=async r=>{let n=new v;u(!0);try{for(let p in h){let s=new FormData;s.append("file",h[p].file);let d=await n.uploadImage(s);r[p]=d.url}n.setTable("room");const t=await n.callRestAPI({user_id:r.user_id,other_user_id:r.other_user_id,chat_id:r.chat_id,unread:r.unread,user_update_at:r.user_update_at,other_user_update_at:r.other_user_update_at},"POST");if(!t.error)O(c,"Added"),y("/member/room");else if(t.validation){const p=Object.keys(t.validation);for(let s=0;s<p.length;s++){const d=p[s];b(d,{type:"manual",message:t.validation[d]})}}u(!1)}catch(t){u(!1),console.log("Error",t),b("user_id",{type:"manual",message:t.message}),k(g,t.message)}};return m.useEffect(()=>{c({type:"SETPATH",payload:{path:"room"}})},[]),e.jsxs("div",{className:" mx-auto rounded  p-5 shadow-md",children:[e.jsx("h4",{className:"text-[16px] font-medium md:text-xl",children:"Add Room"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:j(U),children:[e.jsx(l,{type:"number",page:"room",name:"user_id",errors:o,label:"User Id",placeholder:"User Id",register:a,className:""}),e.jsx(l,{type:"number",page:"room",name:"other_user_id",errors:o,label:"Other User Id",placeholder:"Other User Id",register:a,className:""}),e.jsx(l,{type:"number",page:"room",name:"chat_id",errors:o,label:"Chat Id",placeholder:"Chat Id",register:a,className:""}),e.jsx(l,{type:"number",page:"room",name:"unread",errors:o,label:"Unread",placeholder:"Unread",register:a,className:""}),e.jsx(l,{type:"datetime-local",page:"room",name:"user_update_at",errors:o,label:"User Update At",placeholder:"User Update At",register:a,className:""}),e.jsx(l,{type:"datetime-local",page:"room",name:"other_user_update_at",errors:o,label:"Other User Update At",placeholder:"Other User Update At",register:a,className:""}),e.jsx(A,{type:"submit",loading:_,disabled:_,className:"focus:shadow-outline rounded bg-primary-black px-4 py-2 font-bold text-white focus:outline-none",children:"Submit"})]})]})};export{de as default};
