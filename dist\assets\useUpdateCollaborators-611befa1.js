import{r as t}from"./vendor-4cdf2bd1.js";import{A as d,G as f,M as p,t as h,s as g}from"./index-f2ad9142.js";function m({title:e,titleId:n,...a},r){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":n},a),e?t.createElement("title",{id:n},e):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244"}))}const w=t.forwardRef(m),C=w;function x(e){const[n,a]=t.useState(!0),[r,c]=t.useState({}),{dispatch:i}=t.useContext(d),{dispatch:l}=t.useContext(f);async function o(){try{const u=await new p().callRawAPI(`/v4/api/records/updates/${e}?join=companies|company_id`,void 0,"GET");c(u.model)}catch(s){h(i,s.message),g(l,s.message,5e3,"error")}a(!1)}return t.useEffect(()=>{o()},[e]),{loading:n,update:r,refetch:o}}function E(e){const[n,a]=t.useState(!1),[r,c]=t.useState([]),{dispatch:i}=t.useContext(d),{dispatch:l}=t.useContext(f),o=t.useCallback(async()=>{a(!0);try{const u=await new p().callRawAPI(`/v4/api/records/notes?filter=update_id,eq,${e}&order=id,asc`);c(u.list)}catch(s){h(i,s.message),g(l,s.message,5e3,"error")}a(!1)},[]);return t.useEffect(()=>{o()},[]),{loading:n,notes:r,refetch:o}}function y(e){const[n,a]=t.useState(!1),[r,c]=t.useState([]),{dispatch:i}=t.useContext(d),{dispatch:l}=t.useContext(f);async function o(){a(!0);try{const u=await new p().callRawAPI(`/v4/api/records/update_questions?filter=update_id,eq,${e}&join=user|investor_id`);c(u.list)}catch(s){h(i,s.message),g(l,s.message,5e3,"error")}a(!1)}return t.useEffect(()=>{e&&o()},[e]),{loading:n,questions:r,refetch:o}}function D(e){const[n,a]=t.useState(!1),[r,c]=t.useState([]),{dispatch:i}=t.useContext(d),{dispatch:l}=t.useContext(f);async function o(){a(!0);try{const u=await new p().callRawAPI(`/v4/api/records/update_collaborators?filter=update_id,eq,${e}&join=user|collaborator_id`);c(u.list)}catch(s){h(i,s.message),g(l,s.message,5e3,"error")}a(!1)}return t.useEffect(()=>{e&&o()},[e]),{loading:n,updateCollaborators:r,refetch:o}}export{C as L,E as a,y as b,D as c,x as u};
