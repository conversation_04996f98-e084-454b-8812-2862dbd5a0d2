import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{O as l}from"./index-f2ad9142.js";import"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const C=({icon:r,onClick:o=()=>{},name:e,style:i={},className:m="",loading:p=!1,disabled:s=!1})=>t.jsxs("button",{type:"button",disabled:s,style:{...i},className:`relative flex h-[2.25rem] max-h-[2.25rem] min-h-[2.25rem] w-full cursor-pointer items-center gap-3 px-2 text-left font-inter text-[.875rem] font-[600] capitalize leading-[1.25rem] text-black hover:bg-brown-main-bg hover:text-[#262626] ${m}`,onClick:a=>{o(a)},children:[r&&t.jsxs("span",{className:"",children:[" ",r]}),p?t.jsx(l,{color:"black",loading:!0}):t.jsx(t.Fragment,{children:e&&t.jsxs("span",{className:"grow",children:[" ",e]})})]});export{C as default};
