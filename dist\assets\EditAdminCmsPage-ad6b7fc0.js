import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as f,b as $,i as B,r as h}from"./vendor-4cdf2bd1.js";import{u as F}from"./react-hook-form-a383372b.js";import{o as G}from"./yup-0917e80c.js";import{c as q,a as b}from"./yup-342a5df4.js";import{M as z,A as D,G as H,t as S,s as g}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";let r=new z;const pe=({activeId:i,setSidebar:u})=>{var N,E,A,C;const P=q({email:b().email().required(),password:b(),role:b()}).required(),{dispatch:w}=f.useContext(D),{dispatch:d}=f.useContext(H),R=$();B();const[T,U]=h.useState(""),[I,L]=h.useState(0),[y,j]=h.useState(!1),{register:m,handleSubmit:v,setError:c,setValue:x,formState:{errors:p}}=F({resolver:G(P)}),M=["admin","member"],O=[{key:"0",value:"Inactive"},{key:"2",value:"Suspend"},{key:"1",value:"Active"}],k=async s=>{j(!0);try{if(T!==s.email){const a=await r.updateEmailByAdmin(s.email,i);if(!a.error)g(d,"Email Updated",1e3);else if(a.validation){const o=Object.keys(a.validation);for(let t=0;t<o.length;t++){const n=o[t];c(n,{type:"manual",message:a.validation[n]})}}}if(s.password.length>0){const a=await r.updatePasswordByAdmin(s.password,i);if(!a.error)g(d,"Password Updated",2e3);else if(a.validation){const o=Object.keys(a.validation);for(let t=0;t<o.length;t++){const n=o[t];c(n,{type:"manual",message:a.validation[n]})}}}r.setTable("user");const l=await r.callRestAPI({activeId:i,email:s.email,role:s.role,status:s.status},"PUT");if(!l.error)g(d,"Added",4e3),R("/admin/users");else if(l.validation){const a=Object.keys(l.validation);for(let o=0;o<a.length;o++){const t=a[o];c(t,{type:"manual",message:l.validation[t]})}}}catch(l){console.log("Error",l),c("email",{type:"manual",message:l.message}),S(w,l.message)}j(!1)};return f.useEffect(()=>{d({type:"SETPATH",payload:{path:"users"}}),async function(){try{r.setTable("user");const s=await r.callRestAPI({id:i},"GET");s.error||(x("email",s.model.email),x("role",s.model.role),x("status",s.model.status),U(s.model.email),L(s.model.id))}catch(s){console.log("Error",s),S(w,s.message)}}()},[i]),e.jsxs("div",{className:"mx-auto rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsxs("div",{className:"flex gap-3 items-center",children:[e.jsx("svg",{onClick:()=>u(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"text-lg font-semibold",children:"Edit User"})]}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#f4f4f4]",onClick:()=>u(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[#1f1d1a] px-3 py-2 text-white shadow-sm",onClick:async()=>{await v(k)(),u(!1)},disabled:y,children:y?"Saving...":"Save"})]})]}),e.jsxs("form",{className:"p-4 w-full max-w-lg text-left",onSubmit:v(k),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",...m("email"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(N=p.email)!=null&&N.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(E=p.email)==null?void 0:E.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700 capitalize",children:"Role"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none",...m("role"),children:M.map(s=>e.jsx("option",{name:"role",value:s,children:s},s))})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700 capitalize",children:"Status"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none",...m("status"),children:O.map(s=>e.jsx("option",{name:"status",value:s.key,children:s.value},s.key))})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"password",children:"Password"}),e.jsx("input",{type:"password",placeholder:"******************",...m("password"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none ${(A=p.password)!=null&&A.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(C=p.password)==null?void 0:C.message})]})]})]})};export{pe as default};
