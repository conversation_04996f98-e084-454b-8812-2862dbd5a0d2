import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{a as p,u as m}from"./index-f2ad9142.js";import{u as l}from"./useSubscription-dc563085.js";import{r as o}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const c={pro:5,"pro yearly":5,business:10,"business yearly":10,enterprise:1/0},I=({name:e})=>{var i;if(console.log(e,"limitt"),["enterprise","enterprise yearly"].includes(e))return null;p();const{profile:t}=m(),{getSentUpdates:n,data:s}=l();return console.log(s,"limitt"),o.useEffect(()=>{t!=null&&t.id&&n(t)},[t==null?void 0:t.id]),r.jsx(o.Fragment,{children:r.jsxs("div",{className:"flex gap-2 items-center",children:[(s==null?void 0:s.sentUpdates)>0?r.jsxs("span",{children:[r.jsx("b",{children:s==null?void 0:s.sentUpdates}),"/",c[(i=e==null?void 0:e.split(" ")[0])==null?void 0:i.trim()]]}):null,(s==null?void 0:s.sentUpdates)>0?r.jsx("span",{children:"update sent this month"}):r.jsx("span",{children:"No update sent this month"})]})})};export{I as default};
