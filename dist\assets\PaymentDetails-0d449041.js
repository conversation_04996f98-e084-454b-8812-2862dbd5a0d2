import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{InteractiveButton2 as k}from"./InteractiveButton-060359e0.js";import{b as D,a as I,O as A,L as C,b5 as L,b6 as R}from"./index-f2ad9142.js";import{M as m}from"./MkdInput-d37679e9.js";import{u as B}from"./react-hook-form-a383372b.js";import{o as O}from"./yup-0917e80c.js";import{c as M,a as n}from"./yup-342a5df4.js";import{r as p}from"./vendor-4cdf2bd1.js";import{c as y}from"./countries-912e22d5.js";import"./@nextui-org/theme-345a09ed.js";import"./index-dc002f62.js";import"./qr-scanner-cf010ec4.js";import"./react-spinners-b860a5a3.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./@hookform/resolvers-b50d6e2a.js";const je=()=>{var j,v,w;const{sdk:g}=D(),{showToast:u,tokenExpireError:b}=I(),[x,h]=p.useState(!1),[c,N]=p.useState({firstName:"",lastName:"",address:"",country:"",city:"",state:"",zipCode:"",receipt_to:"",cc_receipt_to:""}),S=M({firstName:n().required("First name is required"),lastName:n().required("Last name is required"),address:n().required("Address is required"),country:n().required("Country is required"),city:n().required("City is required"),state:n().required("State is required"),zipCode:n().required("ZIP code is required")}),{register:a,handleSubmit:q,watch:E,setValue:r,formState:{errors:o}}=B({resolver:O(S),defaultValues:{firstName:"",lastName:"",address:"",country:"",city:"",state:"",zipCode:""}}),z=E(),{country:d,state:f}=z,P=async e=>{N(e),r("firstName",e.firstName),r("lastName",e.lastName),r("address",e.address),r("zipCode",e.zipCode),e.country&&(r("country",e.country),await new Promise(l=>setTimeout(l,100)),e.state&&(r("state",e.state),await new Promise(l=>setTimeout(l,100)),e.city&&r("city",e.city)))},T=async()=>{var e,l;console.log("Fetching billing details...");try{const s=await g.callRawAPI("/v3/api/custom/goodbadugly/user/billing-details",{},"GET");if(console.log("Billing details API response:",s),!s.error&&s.data){const i=s.data;console.log("Setting form values with:",i),await P(i)}}catch(s){console.error("Error fetching billing details:",s);const i=((l=(e=s==null?void 0:s.response)==null?void 0:e.data)==null?void 0:l.message)??s.message;u(i,5e3),b(i)}};p.useEffect(()=>{T()},[]),p.useEffect(()=>{d&&(c.state||r("state",""),c.city||r("city",""))},[d]),p.useEffect(()=>{f&&(c.city||r("city",""))},[f]);const F=async e=>{var l,s;h(!0);try{(await g.callRawAPI("/v3/api/custom/goodbadugly/user/billing-details",{firstName:e.firstName,lastName:e.lastName,address:e.address,country:e.country,city:e.city,state:e.state,zipCode:e.zipCode,receipt_to:e.receipt_to||c.receipt_to,cc_receipt_to:e.cc_receipt_to||c.cc_receipt_to},"POST")).error?u("Failed to update billing details",5e3,"error"):(N({...c,firstName:e.firstName,lastName:e.lastName,address:e.address,country:e.country,city:e.city,state:e.state,zipCode:e.zipCode,receipt_to:e.receipt_to||c.receipt_to,cc_receipt_to:e.cc_receipt_to||c.cc_receipt_to}),u("Billing details updated successfully",3e3,"success"))}catch(i){console.error("Error saving billing details:",i);const _=((s=(l=i==null?void 0:i.response)==null?void 0:l.data)==null?void 0:s.message)??i.message;u(_,5e3,"error"),b(_)}finally{h(!1)}};return t.jsxs("div",{className:"mx-auto grid h-full max-h-full min-h-full w-full grid-cols-1 grid-rows-[auto_1fr_auto] gap-5 px-6 pb-[60px]",children:[x&&t.jsx(A,{loading:x}),t.jsx("div",{className:"mb-4 font-iowan text-[20px] font-[700] md:text-[1.5rem] md:leading-[1.865rem] ",children:"Payment Details"}),t.jsx("form",{onSubmit:q(F),children:t.jsx("div",{className:"mb-6 w-full items-center",children:t.jsx("div",{className:"w-full md:w-[45rem] md:min-w-[45rem] md:max-w-[45rem]",children:t.jsxs("div",{className:"relative",children:[t.jsxs("div",{className:"mb-[1.5rem] grid grid-cols-[repeat(auto-fill,minmax(16.75rem,1fr))] gap-4",children:[t.jsx(C,{children:t.jsx(L,{})}),t.jsx(C,{children:t.jsx(R,{})})]}),t.jsxs("div",{className:"mb-[4rem] grid grid-cols-[repeat(auto-fill,minmax(16.75rem,1fr))] gap-4",children:[t.jsx("div",{className:"mb-4",children:t.jsx(m,{type:"email",name:"receipt_to",errors:o,register:a,label:"Send Receipts To",className:"mt-1 block w-full p-2",placeholder:"<EMAIL>"})}),t.jsx("div",{className:"mb-4",children:t.jsx(m,{type:"email",name:"cc_receipt_to",label:"CC Receipts to",errors:o,register:a,className:"mt-1 block w-full p-2",placeholder:"Enter Email to"})})]}),t.jsx("div",{className:"mb-[1.5rem] font-iowan text-[20px] font-[700] md:text-[1.5rem] md:leading-[1.865rem] ",children:"Company"}),t.jsxs("div",{className:"mb-[20px] !space-y-[1rem] md:mb-[60px]",children:[t.jsxs("div",{className:"grid grid-cols-[repeat(auto-fill,minmax(16.75rem,1fr))] gap-4",children:[t.jsx("div",{className:"mb-4",children:t.jsx(m,{type:"text",label:"First Name",errors:o,register:a,name:"firstName"})}),t.jsx("div",{className:"mb-4",children:t.jsx(m,{type:"text",label:"Last Name",errors:o,register:a,name:"lastName"})})]}),t.jsx("div",{className:"grid w-[auto] grid-cols-[repeat(auto-fill,minmax(auto,1fr))]",children:t.jsx("div",{className:"mb-4",children:t.jsx(m,{type:"text",label:"Company Address",errors:o,register:a,name:"address"})})}),t.jsxs("div",{className:"grid grid-cols-[repeat(auto-fill,minmax(16.75rem,1fr))] gap-4",children:[t.jsx("div",{className:"",children:t.jsx(m,{type:"select",label:"Country",errors:o,register:a,name:"country",noneText:"Select Country",options:y.map(e=>e.name)})}),t.jsx("div",{className:"",children:t.jsx(m,{type:"select",label:"State",errors:o,register:a,name:"state",noneText:"Select State",options:d?((j=y.find(e=>e.name===d))==null?void 0:j.states.map(e=>e.name))||[]:[]})})]}),t.jsxs("div",{className:"grid grid-cols-[repeat(auto-fill,minmax(16.75rem,1fr))] gap-4",children:[t.jsx("div",{className:"mb-4",children:t.jsx(m,{type:"select",label:"City",errors:o,register:a,name:"city",noneText:"Select City",options:d&&f?((w=(v=y.find(e=>e.name===d))==null?void 0:v.states.find(e=>e.name===f))==null?void 0:w.cities.map(e=>e.name))||[]:[]})}),t.jsx("div",{className:"mb-4",children:t.jsx(m,{type:"text",label:"ZIP Code",errors:o,register:a,name:"zipCode"})})]})]}),t.jsx("div",{className:"mb-8 flex justify-end",children:t.jsx(k,{loading:x,disabled:x,type:"submit",className:"whitespace-nowr disabled:bg-disabledblack h-[40px] w-[115px] min-w-fit rounded bg-primary-black px-2 py-2 text-center text-[10px] font-semibold text-white transition-colors duration-100 sm:px-4 sm:!text-[12px] md:!w-[146px] md:px-6 xl:!text-base",children:"Save changes"})})]})})})})]})};export{je as default};
