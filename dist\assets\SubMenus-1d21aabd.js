import{j as s}from"./@nextui-org/listbox-0f38ca19.js";import{r as h,u as de,L as oe,j as z}from"./vendor-4cdf2bd1.js";import{a as le}from"./index-f2ad9142.js";import{C as G}from"./react-spinners-b860a5a3.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const Ae=({menus:L=[],engagements:F={myUpdates:[],teamUpdates:[]},totals:u={totalMyUpdates:0,totalTeamUpdates:0},refetch:M,loading:_,activeParams:p,resetView:Q})=>{var W;const{globalState:n,setGlobalState:$,authState:H}=le(),[U,J]=h.useState(!0),[k,K]=h.useState(!0);h.useState(!0);const[C,S]=h.useState(!0),[A,X]=h.useState(!0),[Y,P]=h.useState({}),[ee,se]=h.useState(new Set),o=de();console.log("Current menus in SubMenus:",L),console.log("Current updateSideNotes:",n==null?void 0:n.updateSideNotes),console.log("Current pathname:",o.pathname),console.log("Current hash:",o.hash),h.useEffect(()=>{var e,t,i;!((e=o==null?void 0:o.pathname)!=null&&e.includes("/member/update/private/view"))&&!((t=o==null?void 0:o.pathname)!=null&&t.includes("/member/update/preview"))&&(i=n==null?void 0:n.updateSideNotes)!=null&&i.length&&($("updateSideNotes",[]),$("currentUpdate",null))},[o==null?void 0:o.pathname]);const m=L.filter(e=>e.belong==="current-update"),v=[],O=e=>e.reduce((t,i)=>(t[i.user.id]={user:i.user,engagements:i.engagements,unviewed_count:i.unviewed_count},t),{}),I=()=>{const e=o.hash.substring(1),[t,i]=e.split("?"),r=new URLSearchParams(i||"");return{from:r.get("from"),engagement_id:r.get("engagement_id"),engagement_type:r.get("engagement_type"),section:t}},ne=e=>{const t=I(),i=t.from==="engagement"&&t.engagement_id==e.id.toString();return i&&!ee.has(e.id)&&(se(r=>{const c=new Set(r);return c.add(e.id),c}),e.is_viewed=1),i},T=e=>{const t={...p,[e==="myUpdates"?"showAllCompany":"showAllTeam"]:!0};M(t)},E=e=>{const t={...p,[e==="myUpdates"?"showAllCompany":"showAllTeam"]:!1};!t.showAllCompany&&!t.showAllTeam?Q():M(t)},b=(e,t)=>{var g;const i=Y[t];let{user:r,engagements:c,unviewed_count:f}=e;const a=I(),j=c.reduce((d,x)=>{const w=a.from==="engagement"&&a.engagement_id==x.id.toString();return x.is_viewed==1||w?d:d+1},0);return s.jsxs("div",{className:"mb-2",children:[s.jsxs("div",{onClick:()=>P(d=>({...d,[t]:!d[t]})),className:"flex h-[35px] cursor-pointer items-center justify-between rounded-[.25rem] px-2 pl-4 hover:bg-[#F7F5D714]",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[r.photo?s.jsx("img",{src:r.photo,alt:`${r.first_name}`,className:"h-6 w-6 rounded-full object-cover"}):s.jsx("div",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-[#1f1d1a]/5",children:s.jsx("span",{className:"text-sm font-medium",children:(g=r==null?void 0:r.first_name)==null?void 0:g[0]})}),s.jsxs("span",{className:"font-Inter text-[.875rem] font-[500] text-white",children:[r==null?void 0:r.first_name," ",r==null?void 0:r.last_name]})]}),s.jsx("div",{className:"flex items-center gap-2",children:j>0&&s.jsx("div",{className:"flex h-5 min-h-[20px] w-5 min-w-[20px] items-center justify-center rounded-full bg-[#F6A03C] text-xs text-[#1f1d1a]",children:s.jsx("span",{children:j})})})]}),i&&s.jsx("div",{className:"mt-2 space-y-2",children:c.map((d,x)=>{const w=ne(d),N=a.from==="engagement"&&a.engagement_id==d.id.toString();return s.jsxs(oe,{to:`/member/update/private/view/${d.update_id}#${d.section}?from=engagement&engagement_id=${d.id}&engagement_type=${d.type||"comment"}`,className:`flex h-[35px] w-full items-center justify-between gap-2 rounded-[.25rem] p-2 pl-[42px] text-[.875rem] ${w?"bg-[#F7F5D714] text-white":"text-white hover:bg-[#F7F5D714]"}`,children:[s.jsx("span",{className:"truncate",children:d.update_name}),d.is_viewed===0&&!N&&s.jsx("span",{className:"mr-[6px] h-[10px] min-h-[10px] w-[10px] min-w-[10px] rounded-full bg-[#F6A03C]"})]},x)})})]},t)},te=()=>{const e=O(F.myUpdates||[]),t=O(F.teamUpdates||[]);return s.jsxs("div",{className:"flex flex-col gap-4",children:[s.jsxs("div",{className:"flex flex-col gap-2",children:[s.jsxs("div",{onClick:()=>S(!C),className:"my-1 flex h-[2.5rem] cursor-pointer items-center gap-4 rounded-[.25rem] pl-4 transition-all hover:bg-[#F7F5D714]",children:[C?s.jsx("svg",{width:"12",height:"8",viewBox:"0 0 12 8",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M11 1.91675L6 6.91675L1 1.91675L11 1.91675Z",fill:"white",stroke:"white",strokeWidth:"2",strokeLinejoin:"round"})}):s.jsx("svg",{width:"8",height:"12",viewBox:"0 0 8 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M1.91663 1L6.91663 6L1.91663 11L1.91663 1Z",fill:"white",stroke:"white",strokeWidth:"2",strokeLinejoin:"round"})}),s.jsx("span",{className:"font-inter text-[.875rem] font-[500] leading-[1.0588rem] text-white",children:"My Updates"})]}),C&&s.jsxs("div",{className:"",children:[Object.entries(e).slice(0,u.totalMyUpdates<=3||p.showAllCompany?void 0:2).map(([i,r])=>b(r,i)),u.totalMyUpdates>3&&s.jsxs("div",{className:"flex items-center gap-2 py-2 pl-4",children:[_&&s.jsx(G,{size:15,color:"#ffffff"}),s.jsx("button",{onClick:()=>p.showAllCompany?E("myUpdates"):T("myUpdates"),className:"flex items-end text-sm text-[#A5A5A3] hover:underline",children:p.showAllCompany?"Show Less":`Show +${u.totalMyUpdates-2}`})]})]})]}),s.jsxs("div",{className:"flex flex-col gap-2",children:[s.jsxs("div",{onClick:()=>X(!A),className:"my-1 flex h-[2.5rem] cursor-pointer items-center gap-4 rounded-[.25rem] pl-4 transition-all hover:bg-[#F7F5D714]",children:[A?s.jsx("svg",{width:"12",height:"8",viewBox:"0 0 12 8",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M11 1.91675L6 6.91675L1 1.91675L11 1.91675Z",fill:"white",stroke:"white",strokeWidth:"2",strokeLinejoin:"round"})}):s.jsx("svg",{width:"8",height:"12",viewBox:"0 0 8 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M1.91663 1L6.91663 6L1.91663 11L1.91663 1Z",fill:"white",stroke:"white",strokeWidth:"2",strokeLinejoin:"round"})}),s.jsx("span",{className:"font-inter text-[.875rem] font-[500] leading-[1.0588rem] text-white",children:"Team Updates"})]}),A&&s.jsxs("div",{className:"",children:[Object.entries(t).slice(0,u.totalTeamUpdates<=3||p.showAllTeam?void 0:2).map(([i,r])=>b(r,i)),u.totalTeamUpdates>3&&s.jsxs("div",{className:"flex items-center gap-2 py-2 pl-4",children:[_&&s.jsx(G,{size:15,color:"#ffffff"}),s.jsx("button",{onClick:()=>p.showAllTeam?E("teamUpdates"):T("teamUpdates"),className:"text-sm text-[#A5A5A3] hover:underline",children:p.showAllTeam?"Show Less":`Show +${u.totalTeamUpdates-2}`})]})]})]})]})},re=(e,t)=>{var j,g,d,x,w,N,Z,D,q,R,V;const i=o.search.includes("mode=preview")||o.search.includes("preview")||o.pathname.includes("/preview/");(j=e.to)!=null&&j.startsWith("#")?e.to.substring(1):e.value;const c=!!((g=n==null?void 0:n.updateSideNotes)==null?void 0:g.find(l=>l.id===e.id)),f=c?((d=e.id)==null?void 0:d.toString())===((x=n==null?void 0:n.noteId)==null?void 0:x.toString()):e.value===(n==null?void 0:n.path);if(console.log("🎯 Menu Item Check:",{text:e==null?void 0:e.text,id:e==null?void 0:e.id,type:e==null?void 0:e.type,noteId:n==null?void 0:n.noteId,path:n==null?void 0:n.path,isActive:f,why:c?`Note item (${e.id}) ${((w=e.id)==null?void 0:w.toString())===((N=n==null?void 0:n.noteId)==null?void 0:N.toString())?"✅ matches":"❌ does not match"} current noteId (${(Z=n==null?void 0:n.currentUpdate)==null?void 0:Z.noteId})`:`Regular item (${e.value}) ${e.value===(n==null?void 0:n.path)?"✅ matches":"❌ does not match"} current path (${n==null?void 0:n.path})`}),e.text==="Asks"&&!i&&(!((D=n==null?void 0:n.updateQuestions)!=null&&D.length)||!((q=n==null?void 0:n.updateQuestions)!=null&&q.some(l=>l.investor_id==H.user))))return null;let a=0;if(e!=null&&e.id){const l=(R=n==null?void 0:n.updateSideNotes)==null?void 0:R.find(y=>y.id===e.id&&!y.content);l!=null&&l.counts&&(a=Object.values(l.counts).reduce((y,ie)=>y+ie,0))}return e.type==="href"&&e.text!=="Asks"&&((V=n==null?void 0:n.updateSideNotes)!=null&&V.find(l=>l.id==e.id&&l.content==null))?null:s.jsxs("li",{className:`flex h-[2.5rem] w-full cursor-pointer list-none items-center rounded-[.25rem] font-inter text-[.875rem] font-[500] leading-[1.0588rem] ${f?"bg-[#F7F5D714] text-white":""} hover:bg-[#F7F5D714]`,"data-note-id":e==null?void 0:e.id,children:[["link"].includes(e==null?void 0:e.type)?s.jsxs(z,{title:e==null?void 0:e.text,to:e==null?void 0:e.to,className:`flex h-full w-full items-center gap-4 truncate whitespace-nowrap px-[.8456rem] ${n!=null&&n.isOpen?"w-full justify-start":"w-[2.5rem] justify-center"}`,children:[e!=null&&e.icon?s.jsx("span",{children:e==null?void 0:e.icon}):null,(n==null?void 0:n.isOpen)&&s.jsxs("span",{className:"flex items-center justify-between gap-2",children:[s.jsx("span",{className:`truncate text-ellipsis ${f?"text-white":""}`,children:e==null?void 0:e.text}),a>0&&s.jsx("span",{className:"flex h-5 min-h-5 w-5 min-w-[20px] items-center justify-center rounded-full bg-[#F6A03C] text-xs text-[#1f1d1a]",children:s.jsx("span",{children:a})})]})]}):null,["href"].includes(e==null?void 0:e.type)?s.jsxs(z,{title:e==null?void 0:e.text,to:e==null?void 0:e.to,className:`flex w-full items-center gap-4 truncate whitespace-nowrap px-[.8456rem] ${n!=null&&n.isOpen?"w-full justify-start":"w-[2.5rem] justify-center"}`,children:[e!=null&&e.icon?s.jsx("span",{children:e==null?void 0:e.icon}):null,(n==null?void 0:n.isOpen)&&s.jsxs("div",{className:"flex w-full items-center justify-between gap-2",children:[s.jsx("span",{className:`truncate text-ellipsis ${f?"text-white":""}`,children:e==null?void 0:e.text}),a>0&&s.jsx("div",{className:"flex h-[20px] min-h-[20px] w-[20px] min-w-[20px] items-center justify-center rounded-full bg-[#F6A03C] text-[#1f1d1a]",children:s.jsx("span",{className:"text-sm font-semibold text-[#1f1d1a]",children:a})})]})]}):null]},t)},B=e=>e==null?void 0:e.filter(t=>(t==null?void 0:t.type)!=="title").map((t,i)=>re(t,i));return s.jsxs("section",{className:"submenus-section",children:[m.length>0&&s.jsxs(s.Fragment,{children:[s.jsxs("div",{onClick:()=>J(!U),className:"my-1 flex h-[2.5rem] cursor-pointer items-center gap-4 rounded-[.25rem] pl-4 transition-all hover:bg-[#F7F5D714]",children:[U?s.jsx("svg",{className:"min-h-[8px] min-w-[12px]",width:"12",height:"8",viewBox:"0 0 12 8",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M11 1.91675L6 6.91675L1 1.91675L11 1.91675Z",fill:"white",stroke:"white",strokeWidth:"2",strokeLinejoin:"round"})}):s.jsx("svg",{className:"min-h-[8px] min-w-[12px]",width:"8",height:"12",viewBox:"0 0 8 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M1.91663 1L6.91663 6L1.91663 11L1.91663 1Z",fill:"white",stroke:"white",strokeWidth:"2",strokeLinejoin:"round"})}),s.jsx("h2",{className:"line-clamp-1 text-ellipsis font-inter text-[.875rem] font-[500] leading-[1.0588rem] text-white hover:line-clamp-2 ",children:(W=m==null?void 0:m.find(e=>(e==null?void 0:e.type)==="title"))==null?void 0:W.text})]}),U&&s.jsx("div",{className:"ml-5 border-l border-l-brown-main-bg pl-[6px]",children:B(m)})]}),v.length>0&&s.jsxs(s.Fragment,{children:[s.jsxs("div",{onClick:()=>K(!k),className:"my-1 flex h-[2.5rem] cursor-pointer items-center gap-4 rounded-[.25rem] pl-4 transition-all hover:bg-[#F7F5D714]",children:[k?s.jsx("svg",{className:"min-h-[8px] min-w-[12px]",width:"8",height:"12",viewBox:"0 0 8 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M1.91663 1L6.91663 6L1.91663 11L1.91663 1Z",fill:"white",stroke:"white",strokeWidth:"2",strokeLinejoin:"round"})}):s.jsx("svg",{className:"min-h-[8px] min-w-[12px]",width:"12",height:"8",viewBox:"0 0 12 8",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M11 1.91675L6 6.91675L1 1.91675L11 1.91675Z",fill:"white",stroke:"white",strokeWidth:"2",strokeLinejoin:"round"})}),s.jsxs("div",{className:"flex w-full items-center justify-between gap-2 pr-[.8456rem]",children:[s.jsx("span",{className:`font-inter text-[.875rem] font-[500] leading-[1.0588rem] text-white ${v.length>0?"text-white":""}`,children:"Requested Updates"}),s.jsx("div",{className:"flex h-[20px] min-h-[20px] w-[20px] min-w-[20px] items-center justify-center rounded-full bg-[#F6A03C] text-xs text-[#1f1d1a] ",children:s.jsx("span",{className:"text-sm font-semibold text-[#1f1d1a]",children:v.length})})]})]}),k&&s.jsx("div",{className:"ml-5 border-l border-l-brown-main-bg pl-[6px]",children:B(v)})]}),L.find(e=>e.belong==="engagement")&&te()]})};export{Ae as default};
