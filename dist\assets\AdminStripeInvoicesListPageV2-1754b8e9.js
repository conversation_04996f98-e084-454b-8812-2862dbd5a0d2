import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as r,b as X}from"./vendor-4cdf2bd1.js";import{M as Y,G as Z,A as ee,f as te,t as se,g as ae}from"./index-f2ad9142.js";import{o as re}from"./yup-0917e80c.js";import{u as ne}from"./react-hook-form-a383372b.js";import{c as ie,a as oe}from"./yup-342a5df4.js";import{P as ce}from"./index-9dceff66.js";import{B as le,a as de}from"./index.esm-54e24cf9.js";import{A as ue,a as pe}from"./index.esm-25e0e799.js";import{R as me}from"./index.esm-3e7472af.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-icons-36ae72b7.js";const g=[{header:"Status",accessor:"status"},{header:"Currency",accessor:"currency"},{header:"Amount due",accessor:"amount_due",type:"currency"},{header:"Amount paid",accessor:"amount_paid",type:"currency"},{header:"Amount remaining",accessor:"amount_remaining",type:"currency"},{header:"Created at",accessor:"created_at",type:"timestamp"}],Ge=()=>{const A=new Y,{dispatch:f}=r.useContext(Z),{dispatch:F}=r.useContext(ee);r.useState("");const[y,_]=r.useState([]),[o,j]=r.useState(10),[b,$]=r.useState(0),[xe,D]=r.useState(0),[c,R]=r.useState(0),[T,E]=r.useState(!1),[O,q]=r.useState(!1),[w,L]=r.useState(!1),[v,N]=r.useState(!1),[i,u]=r.useState([]),[S,p]=r.useState([]),[z,B]=r.useState(""),[C,I]=r.useState("eq"),[V,P]=r.useState(!1);X(),console.log("data in invoice",y);const G=ie({customer_email:oe()}),{register:he,handleSubmit:M,formState:{errors:ge}}=ne({resolver:re(G)}),k=(t,a,s)=>{const n=a==="eq"&&isNaN(s)?`"${s}"`:s,m=`${t},${a},${n}`;p(x=>[...x.filter(d=>!d.includes(t)),m]),B(s)};function H(t){(async function(){j(t),await l(1,t)})()}function K(){(async function(){await l(c-1>1?c-1:1,o)})()}function U(){(async function(){await l(c+1<=b?c+1:1,o)})()}async function l(t,a,s){P(!0);try{const{list:n,total:m,limit:x,num_pages:h,page:d,error:Q,message:W}=await A.getStripeInvoicesV2({page:t,limit:a},`filter=${s.toString()}`);if(Q){showToast(f,W,5e3);return}_(n),j(+x),$(+h),R(+d),D(+m),E(+d>1),q(+d+1<=+h)}catch(n){console.log("ERROR",n),se(F,n.message)}P(!1)}const J=t=>{const a=ae(t.customer_email);l(1,o,{customer_email:a,product_name})};return r.useEffect(()=>{f({type:"SETPATH",payload:{path:"invoices"}});const a=setTimeout(async()=>{await l(1,o,S)},700);return()=>{clearTimeout(a)}},[z,S,C]),e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex items-center justify-between py-3",children:e.jsxs("form",{className:"relative rounded bg-brown-main-bg",onSubmit:M(J),children:[e.jsxs("div",{className:"flex items-center gap-4 text-gray-700",children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-[#0003] px-3 py-1",onClick:()=>L(!w),children:[e.jsx(le,{}),e.jsx("span",{children:"Filters"}),i.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start  text-white",children:i.length>0?i.length:null})]}),e.jsxs("div",{className:" flex cursor-pointer items-center justify-between gap-3 rounded-md border border-[#0003] px-2 py-1 focus-within:border-gray-400",children:[e.jsx(de,{className:"text-xl text-gray-200"}),e.jsx("input",{type:"text",placeholder:"search",className:"border-none p-0 placeholder:text-left  focus:outline-none",style:{boxShadow:"0 0 transparent"},onInput:t=>{var a;return k("name","cs",(a=t.target)==null?void 0:a.value)}}),e.jsx(ue,{className:"text-lg text-gray-200"})]})]}),w&&e.jsxs("div",{className:"top-fill filter-form-holder absolute left-0  z-20 mt-4 min-w-[70%] rounded-md border border-[#0003] bg-brown-main-bg p-5 shadow-xl",children:[i==null?void 0:i.map((t,a)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:" mb-3  w-1/3 rounded-md border border-black/60 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"w-[30%] appearance-none border-none outline-0",onChange:s=>{I(s.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>k(t,C,s.target.value)}),e.jsx(me,{className:" cursor-pointer text-xl",onClick:()=>{u(s=>s.filter(n=>n!==t)),p(s=>s.filter(n=>!n.includes(t)))}})]},a)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-brown-main-bg px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{N(!v)},children:[e.jsx(pe,{}),"Add filter"]}),v&&e.jsx("div",{className:"absolute top-11 z-10 bg-brown-main-bg px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:g.slice(0,-1).map(t=>e.jsx("li",{className:`${i.includes(t.header)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{i.includes(t.header)||u(a=>[...a,t.header]),N(!1)},children:t.header},t.header))})}),i.length>0&&e.jsx("div",{onClick:()=>{u([]),p([])},className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]})]})]})}),V?e.jsx(te,{}):e.jsx("div",{className:"overflow-x-auto rounded-md border border-[#0003] shadow ",children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10",children:[e.jsx("thead",{children:e.jsx("tr",{children:g.map((t,a)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},a))})}),e.jsx("tbody",{className:"font-iowan-regular divide-y divide-[#1f1d1a]/10",children:y.map((t,a)=>e.jsx("tr",{className:"  md:h-[60px]",children:g.map((s,n)=>s.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4"},n):s.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.mapping[t[s.accessor]]},n):s.type==="timestamp"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:new Date(t[s.accessor]*1e3).toLocaleString("en-US")},n):s.type==="currency"?e.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:["$",Number(t[s.accessor]/100).toFixed(2)]},n):s.type==="metadata"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[s.pre_accessor][s.accessor]??"n/a"},n):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[s.accessor]},n))},a))})]})}),e.jsx(ce,{currentPage:c,pageCount:b,pageSize:o,canPreviousPage:T,canNextPage:O,updatePageSize:H,previousPage:K,nextPage:U})]})};export{Ge as default};
