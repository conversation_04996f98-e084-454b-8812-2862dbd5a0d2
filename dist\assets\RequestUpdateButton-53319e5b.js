import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{L as m,I as c}from"./index-f2ad9142.js";import{r as t}from"./vendor-4cdf2bd1.js";import{a as d}from"./index-4e4ee51a.js";import{X as p}from"./XMarkIcon-6ed09631.js";import{t as o,S as i}from"./@headlessui/react-cdd9213e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const z=({onSuccess:a=null})=>{const[r,s]=t.useState(!1),n=t.useRef(null),l=()=>{s(!1),console.log(a,"success"),a!==null&&(console.log("hii","success"),a())};return e.jsxs(t.Fragment,{children:[e.jsx(m,{children:e.jsx(c,{type:"button",className:"my-4 flex !h-[40px] !w-[140px] items-center justify-center whitespace-nowrap rounded-[4px] bg-[#1f1d1a] px-2 py-2 font-iowan text-base tracking-wide text-white outline-none focus:outline-none md:h-[2.75rem] md:w-fit md:rounded-[.0625rem] md:px-5 md:!text-[1rem]",color:"black",onClick:()=>s(!0),children:"Request Update"})}),e.jsx(o,{appear:!0,show:r,as:t.Fragment,children:e.jsxs(i,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>s(!1),children:[e.jsx(o.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(o.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(i.Panel,{className:"h-auto w-full max-w-md transform rounded-md bg-brown-main-bg p-5 text-left text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsx(i.Title,{as:"h3",className:"mb-4 font-iowan text-lg font-medium leading-6 text-[#1f1d1a]",children:"Request Update"}),e.jsx("button",{onClick:()=>s(!1),type:"button",children:e.jsx(p,{className:"h-6 w-6"})})]}),e.jsx("div",{className:"mt-2",children:e.jsx(d,{onClose:()=>s(!1),containerRef:n,onSuccess:l})})]})})})})]})})]})};export{z as default};
