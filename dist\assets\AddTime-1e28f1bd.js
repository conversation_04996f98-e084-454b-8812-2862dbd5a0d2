import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{A as v,G as w,I as N,M as C,s as x,t as k}from"./index-f2ad9142.js";import{r as t,i as T}from"./vendor-4cdf2bd1.js";import"./yup-342a5df4.js";import{X as A}from"./XMarkIcon-cfb26fe7.js";import{t as r,S as i}from"./@headlessui/react-cdd9213e.js";function G({afterAdd:S,handleClick:F,data:u}){const[p,s]=t.useState(!1),{dispatch:h}=t.useContext(v),{dispatch:l}=t.useContext(w),[a,f]=t.useState(""),[d,n]=t.useState(!1),[g,c]=t.useState(!1);t.useState(""),T();const j=()=>{s(!0)},y=async o=>{if(a==="")c("Please enter time");else{n(!0);try{const b=await new C().callRawAPI(`/v3/api/custom/goodbadugly/updates/${o}/add-time`,{time:a},"POST");n(!1),x(l,"Time request sent"),s(!1),console.log(b)}catch(m){k(h,err.message),x(l,err.message,5e3,"error"),n(!1),c(!0),console.log(m)}}};return e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"cursor-pointer font-iowan  text-sm font-medium text-[#292829fd] hover:underline disabled:text-gray-500",onClick:()=>j(),children:e.jsxs("span",{className:"text-green-500",children:["Add",e.jsx("br",{}),"time"]})}),e.jsx(r,{appear:!0,show:p,as:t.Fragment,children:e.jsxs(i,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>s(!1),children:[e.jsx(r.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(r.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(i.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-sm shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(i.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Add Time"}),e.jsx("button",{onClick:()=>s(!1),type:"button",children:e.jsx(A,{className:"h-6 w-6"})})]}),e.jsxs("form",{className:"mt-6",children:[e.jsxs("select",{onChange:o=>f(o.target.value),className:"focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ",children:[e.jsx("option",{value:"",children:"Select"}),e.jsx("option",{value:"1",children:"1 day"}),e.jsx("option",{value:"2",children:"2 days"}),e.jsx("option",{value:"3",children:"3 days"}),e.jsx("option",{value:"4",children:"4 days"}),e.jsx("option",{value:"5",children:"5 days"}),e.jsx("option",{value:"6",children:"6 days"}),e.jsx("option",{value:"7",children:"7 days"})]}),e.jsx("p",{className:"text-xs italic text-red-500",children:g})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a]/30 py-2 text-center font-iowan font-medium",type:"button",onClick:()=>s(!1),children:"Cancel"}),e.jsx(N,{loading:d,disabled:d||a==="",onClick:()=>y(u.update_id),className:"disabled:bg-disabledblack rounded-lg bg-[#1f1d1a] py-2 text-center font-iowan font-semibold text-white transition-colors duration-100",children:"Add"})]})]})})})})]})})]})}export{G as A};
