import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{u as A,r as m,i as E}from"./vendor-4cdf2bd1.js";import"./index-d20ea84b.js";import{a as k,u as S,L as g,bT as P}from"./index-f2ad9142.js";import{U as q}from"./index-31067895.js";import{u as z}from"./useReactions-581cd573.js";import{u as D}from"./useNote-ea33f376.js";import{u as F}from"./useUpdateCollaborator-1187c43b.js";import{A as H}from"./index-afef2e72.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const gi=({update:i=null,note:o=null,toggleComments:j,commentVisibility:n,onSuccess:x})=>{var _;const{setLoading:d,RequestItems:l,showToast:w}=k(),C=A(),[e,N]=m.useState({html:null,data:null,modal:null,showModal:!1}),{reaction:t,loading:O,getReactions:y,react:b}=z(),{loading:R,notes:Y,customDeleteNote:L}=D(),{data:{isCollaborator:B,collaborator:G},getUpdateCollaborator:M}=F({filter:[`update_id,eq,${i==null?void 0:i.id}`]}),{profile:c}=S(),{public_link_id:v}=E();(c==null?void 0:c.id)==(i==null?void 0:i.user_id);function f(s,a){N($=>({...$,showModal:a,modal:a?s:null}))}async function T(){try{const s=await L({updateId:i==null?void 0:i.id,noteId:o==null?void 0:o.id});s!=null&&s.error||(f("delete_note",!1),d(l==null?void 0:l.deleteModel,!1),x&&x())}catch(s){console.error("error >> ",s)}finally{f("delete_note",!1),d(l==null?void 0:l.deleteModel,!1)}}const h=async()=>{y({filter:[`update_id,eq,${i==null?void 0:i.id}`,`note_id,eq,${o==null?void 0:o.id}`,"goodbadugly_update_reaction.type,eq,1"],target:1,note_id:o==null?void 0:o.id,update_id:i==null?void 0:i.id,exposure:v?"public":"private"})},U=async s=>{try{if(C.search.includes("mode=preview"))w("You are in preview mode");else{const a=await b({reaction:s,target:1,note_id:o==null?void 0:o.id,update_id:i==null?void 0:i.id});a!=null&&a.error||h()}}catch(a){console.log("error >> ",a)}};return m.useEffect(()=>{c!=null&&c.id&&(i!=null&&i.id)&&(o!=null&&o.id)&&M({filter:[`update_id,eq,${i==null?void 0:i.id}`,`note_id,eq,${o==null?void 0:o.id}`],join:[]})},[c==null?void 0:c.id,i==null?void 0:i.id,o==null?void 0:o.id]),m.useEffect(()=>{i!=null&&i.id&&(o!=null&&o.id)&&h()},[i==null?void 0:i.id,o==null?void 0:o.id]),r.jsxs(m.Fragment,{children:[r.jsx("div",{className:"flex w-full gap-[1rem]",children:r.jsxs("div",{className:"flex w-full gap-[1rem]",children:[r.jsx(g,{children:r.jsx(q,{reactions:t==null?void 0:t.list,onClick:U})}),r.jsx(g,{children:r.jsxs("button",{type:"button",onClick:()=>j("hideComment",!n),className:"flex w-full cursor-pointer gap-[1rem]",children:[r.jsx(P,{})," ",n?r.jsx("span",{className:"font-iowan",children:((_=o==null?void 0:o.update_comments)==null?void 0:_.length)||""}):"",r.jsxs("span",{children:[n?"Show":"Hide"," Comments"]})]})})]})}),r.jsx(g,{children:r.jsx(H,{isOpen:(e==null?void 0:e.showModal)&&(e==null?void 0:e.modal)==="delete_note",onClose:()=>f("delete_note",!1),customMessage:"Are you sure you want to delete this note?",table:"notes",title:"Delete Note",action:"delete",mode:"manual",data:{id:o==null?void 0:o.id},inputConfirmation:!1,onSuccess:s=>{d(l==null?void 0:l.deleteModel,!0),T()}})})]})};export{gi as default};
