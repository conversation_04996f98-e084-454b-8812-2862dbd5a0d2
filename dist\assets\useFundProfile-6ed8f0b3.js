import{A as i,G as l,m as d,t as p,s as f}from"./index-f2ad9142.js";import{r as t}from"./vendor-4cdf2bd1.js";function x(h){const[n,s]=t.useState(!1),[u,c]=t.useState([]),{dispatch:a}=t.useContext(i),{dispatch:o}=t.useContext(l),r=t.useCallback(async()=>{s(!0);try{const e=await d(o,a,{endpoint:"/v3/api/goodbadugly/customer/fund-profile/",method:"GET"});a({type:"SET_COMPANY_PROFILE",payload:e.data}),c(e.data)}catch(e){p(a,e.message),f(o,e.message,5e3,"error")}s(!1)},[]);return t.useEffect(()=>{r()},[]),{loading:n,profileData:u,refetch:r}}export{x as u};
