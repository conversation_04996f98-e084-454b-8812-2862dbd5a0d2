import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{r as a,R as W,b as X,h as Y}from"./vendor-4cdf2bd1.js";import{u as Z}from"./react-hook-form-a383372b.js";import{o as D}from"./yup-0917e80c.js";import{c as ee,a as R}from"./yup-342a5df4.js";import{A as v}from"./AddButton-51d1b2cd.js";import{b as re,a as se,u as te,L as g,I,a6 as oe,E as ie,d as ne,M as ae}from"./index-f2ad9142.js";import{M as ce}from"./index-d07d87ac.js";import{_ as le}from"./qr-scanner-cf010ec4.js";import{getCorrectValueTypeFormat as de}from"./MkdListTableV2-db78e8c5.js";import{G as me,M as ue,R as pe}from"./index-64a9a9df.js";import{A as fe}from"./index-afef2e72.js";import"./moment-a9aaa855.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index-9dceff66.js";import"./ExportButton-eb4cf1f9.js";import"./index.esm-54e24cf9.js";import"./react-icons-36ae72b7.js";import"./MkdInput-d37679e9.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./lucide-react-0b94883e.js";const he=a.lazy(()=>le(()=>import("./NoRecipients-c1014793.js"),["assets/NoRecipients-c1014793.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"])),xe=[{header:"ID",accessor:"id",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Group Name",accessor:"group_name",join:"group",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Members",accessor:"members",isSorted:!0,thumbnail:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Action",accessor:""}],ar=()=>{var M;const m=a.useRef(null),b=a.useRef(null),C=a.useRef(null),u=a.useRef(null),P=a.useRef(null),p=a.useRef(null),w=btoa("group:group_id"),j=btoa("group:members");re();const{globalDispatch:A,authDispatch:ge,authState:be,getManyById:$,showToast:F,tokenExpireError:we}=se(),[h,G]=W.useState([{accessor:"group_id",operator:"cs",value:"",uid:w},{accessor:"members",operator:"cs",value:"",uid:j}]),[s,_]=a.useState({filter:[],refresh:!1,modal:null,showModal:!1,selectedItems:[],loading:!1}),L=a.useMemo(()=>JSON.stringify(h),[h]),N=X();Y();const{profile:t}=te(),T=ee({group_id:R(),members:R()}),{register:je,reset:V,setValue:y,watch:O,formState:{errors:_e}}=Z({resolver:D(T)}),{group_id:q,members:z}=O(),x=a.useCallback((e,o,c)=>{G(i=>i.map(n=>(n==null?void 0:n.uid)===c?{...n,[e]:o}:n))},[h,L]),B=a.useCallback(e=>{let o=[];return new Set(e.map(i=>i==null?void 0:i.accessor)).forEach(i=>{const n=e.filter(l=>l.accessor===i);if((n==null?void 0:n.length)>0){const l=n.filter(d=>d==null?void 0:d.value);if(l.length>1)l.forEach(d=>{const{accessor:f,operator:k,value:U}=d,Q=`goodbadugly_recipient_group.${f},${k},${U}`;o.push(Q)});else if(l.length===1){const{accessor:d,operator:f,value:k}=l[0];o.push(`goodbadugly_recipient_group.${d},${f},${de(k,f)}`)}}}),o},[h]);async function H(){const e=new ae;try{await e.exportCSVGroup()}catch(o){console.error("Export error:",o),F("Failed to export data",5e3,"error")}}const K=()=>{N("/member/add-recipient_group")},E=a.useCallback((e={clear:!1})=>{var c,i;e!=null&&e.clear&&(x("value","",w),x("value","",j),u!=null&&u.current&&((c=u==null?void 0:u.current)==null||c.click()),p!=null&&p.current&&((i=p==null?void 0:p.current)==null||i.click()));const o=e!=null&&e.clear?[]:B(h);_(n=>({...n,filter:[`goodbadugly_recipient_group.user_id,eq,${t==null?void 0:t.id}`,...o],refresh:!0}))},[h]),J=async(e,o,c)=>{const i=e==null?void 0:e.map(async l=>{var f;const d=await $("user",(f=l==null?void 0:l.members)==null?void 0:f.split(","));return{...l,members:r.jsx(pe,{members:d==null?void 0:d.data})}}),n=await Promise.all(i);return console.log("recipientGroups",n),n},S=(e,o,c=[])=>{_(i=>({...i,seletedItems:c,showModal:o,modal:o?e:null}))};return a.useEffect(()=>{A({type:"SETPATH",payload:{path:"recipient_group"}}),t!=null&&t.id&&_(e=>({...e,refresh:!1,filter:[`goodbadugly_recipient_group.user_id,eq,${t==null?void 0:t.id}`]}))},[t==null?void 0:t.id]),a.useEffect(()=>{var e;s!=null&&s.refresh&&(m!=null&&m.current)&&((e=m==null?void 0:m.current)==null||e.click())},[s==null?void 0:s.refresh]),r.jsxs(a.Fragment,{children:[r.jsxs("div",{className:" space-y-[1rem] rounded  bg-brown-main-bg p-5 px-5 md:px-8",children:[r.jsxs("div",{className:"my-[16px] flex w-full items-center justify-between gap-5",children:[r.jsx("h4",{className:"font-iowan text-[20px] font-bold md:text-[2rem] md:leading-[2.486rem]",children:"Recipient Groups"}),r.jsx("div",{className:"flex gap-5 justify-start items-center w-fit",children:r.jsx(g,{children:r.jsx(v,{showPlus:!1,className:"!rounded-[.125rem] !p-[10px] !px-[10px]",onClick:()=>K(),children:"New Group"})})})]}),r.jsxs("div",{className:"flex gap-5 justify-between items-end w-full",children:[r.jsxs("div",{className:"flex w-full flex-col gap-5 sm:flex-row sm:items-end md:w-[55%]",children:[r.jsxs("div",{className:"sm:grow=[0.5] flex grid-cols-[repeat(auto-fill,minmax(14rem,1fr))] flex-wrap items-end gap-2 sm:grid",children:[r.jsx("div",{className:"!grow",children:r.jsx(g,{children:r.jsx(me,{onSelect:(e,o=!1)=>{var c,i;o?(x("value","",w),y("group_id","")):(x("value",(c=e==null?void 0:e.group)==null?void 0:c.id,w),y("group_id",(i=e==null?void 0:e.group)==null?void 0:i.id))},value:q,refreshRef:C,clearRef:u})})}),r.jsx("div",{className:"!grow",children:r.jsx(g,{children:r.jsx(ue,{onSelect:(e,o=!1)=>{o?(x("value","",j),y("member_id","")):(x("value",e==null?void 0:e.member_id,j),y("member_id",e==null?void 0:e.member_id))},value:z,refreshRef:P,clearRef:p})})})]}),r.jsxs("div",{className:"flex gap-3 items-end",children:[r.jsx(I,{type:"submit",className:"mb-[-5px] flex !h-[2.25rem] !w-fit !min-w-fit !max-w-fit items-center justify-center whitespace-nowrap !rounded-[0.125rem] !border  bg-[#1f1d1a] !py-[0.5rem] px-2 !text-[1rem] tracking-wide text-white outline-none focus:outline-none md:px-5",color:"black",disabled:!(t!=null&&t.id),onClick:()=>{E()},children:"Search"}),r.jsx(v,{onClick:()=>{V(),E({clear:!0})},showPlus:!1,className:"!w-fit !min-w-fit !max-w-fit !border-0 !bg-transparent !p-0 !font-inter !text-[1rem] !font-[600] !leading-[1.21rem] !tracking-wide !text-black !underline !shadow-none md:mb-[-10px]",children:"Clear"})]})]}),r.jsxs(I,{type:"button",className:"flex !h-[2.25rem]  !w-fit !min-w-fit items-center justify-center whitespace-nowrap !rounded-[0.125rem] !border border-black bg-transparent !py-[0.5rem] px-2 !text-[1rem] tracking-wide text-black outline-none focus:outline-none md:px-5",color:"black",onClick:()=>{H()},children:[r.jsx(oe,{})," Export"]})]}),r.jsx("div",{className:"h-[.125rem] w-full border-[.125rem] border-black bg-black "}),t!=null&&t.id&&((M=s==null?void 0:s.filter)==null?void 0:M.length)>0?r.jsx(ce,{showSearch:!1,useDefaultColumns:!0,defaultColumns:[...xe],noDataComponent:{use:!0,component:r.jsx(g,{children:r.jsx(he,{})})},onReady:()=>{_(e=>({...e,refresh:!1}))},processes:[J],hasFilter:!1,tableRole:"admin",actionId:"id",table:"recipient_group",join:["user","group"],defaultFilter:s==null?void 0:s.filter,actions:{view:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},update:{show:!0,action:e=>{N("/member/edit-recipient_group/"+(e==null?void 0:e[0]),{state:e==null?void 0:e[0]})},multiple:!0,children:"Edit",showChildren:!0,icon:r.jsx(ie,{}),locations:["dropdown"]},remove:{show:!0,action:e=>{S("delete",!0,e)},multiple:!0,children:"Delete",icon:r.jsx(ne,{fill:"#292D32"}),locations:["dropdown"]},view_all:{show:!1,type:"static",action:()=>{},children:r.jsx(r.Fragment,{children:"View All"}),className:"!gap-2 !bg-transparent !text-black !underline !shadow-none !border-0"},add:{show:!1,multiple:!0,children:"+ Add"},export:{show:!1,action:null,multiple:!0}},defaultPageSize:20,showPagination:!0,maxHeight:"md:grid-rows-[inherit] grid-rows-[inherit]",actionPostion:["dropdown"],refreshRef:m,updateRef:b}):null]}),r.jsx(g,{children:r.jsx(fe,{mode:"delete",action:"delete",table:"recipient_group",inputConfirmation:!1,title:"Delete Recipient Group",data:{id:s==null?void 0:s.selectedItems[0]},onSuccess:()=>{var e;S("delete",!1,[]),(e=b==null?void 0:b.current)==null||e.click()},onClose:()=>S("delete",!1,[]),isOpen:(s==null?void 0:s.showModal)&&["delete"].includes(s==null?void 0:s.modal)})})]})};export{ar as default};
