/**
 * Complete Stripe Products & Prices Bulk Creation Script
 *
 * This script creates both products and prices in sequence, automatically
 * mapping the created product IDs to the price creation.
 *
 * Usage:
 * 1. Copy this entire script
 * 2. Paste it in the browser console while logged into admin panel
 * 3. Call: await bulkCreateStripeComplete()
 * 4. The script will create products first, then prices automatically
 */

const bulkCreateStripeComplete = async () => {
  console.log("🚀 Starting complete Stripe setup...");
  console.log("This will create products first, then prices automatically.\n");

  // Get auth credentials
  const token =
    localStorage.getItem("token") || localStorage.getItem("admin_token");
  const projectId = "goodbadugly";
  const secret = "i3k2c2k8kjp9ook0m14mhrtcwpgdd8g";
  const base64Encode = btoa(projectId + ":" + secret);

  if (!token) {
    console.error("❌ No authentication token found. Please log in first.");
    return { success: false, error: "No authentication token found" };
  }

  // STEP 1: Create Products
  console.log("📦 STEP 1: Creating Products");
  console.log("============================");

  const products = [
    {
      name: "free",
      description: "30 days free plan for all new users",
    },
    {
      name: "pro",
      description: "Professional plan with advanced features",
    },
    {
      name: "business",
      description: "Business plan for growing teams",
    },
    {
      name: "enterprise",
      description: "Enterprise plan for large organizations",
    },
  ];

  const createdProducts = [];
  const productErrors = [];

  for (let i = 0; i < products.length; i++) {
    const product = products[i];

    try {
      console.log(`Creating product: "${product.name}"`);

      const response = await fetch(
        "https://api.updatestack.com/v2/api/lambda/stripe/product",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-project": base64Encode,
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(product),
        }
      );

      const result = await response.json();

      if (result.error) {
        console.error(`❌ Error creating "${product.name}":`, result.message);
        productErrors.push({ product: product.name, error: result.message });
      } else {
        console.log(`✅ Created "${product.name}" with ID: ${result.model}`);
        createdProducts.push({
          name: product.name,
          id: result.model,
          description: product.description,
        });
      }

      await new Promise((resolve) => setTimeout(resolve, 500));
    } catch (error) {
      console.error(
        `❌ Network error creating "${product.name}":`,
        error.message
      );
      productErrors.push({ product: product.name, error: error.message });
    }
  }

  if (productErrors.length > 0) {
    console.error(
      "\n❌ Some products failed to create. Cannot proceed with prices."
    );
    return {
      success: false,
      products: { created: createdProducts, errors: productErrors },
      error: "Product creation failed",
    };
  }

  // Create product mapping
  const productMapping = {};
  createdProducts.forEach((product) => {
    productMapping[product.name] = product.id;
  });

  console.log("\n✅ All products created successfully!");
  console.log("Product mapping:", productMapping);

  // STEP 2: Create Prices
  console.log("\n💰 STEP 2: Creating Prices");
  console.log("==========================");

  const prices = [
    // Monthly plans
    {
      product_id: productMapping.pro,
      name: "pro monthly",
      amount: "29.99", // Send as decimal string
      type: "recurring",
      interval: "month",
      interval_count: "1",
      usage_type: "licenced",
      trial_days: "",
      usage_limit: "",
    },
    {
      product_id: productMapping.business,
      name: "business monthly",
      amount: "49.99", // Send as decimal string
      type: "recurring",
      interval: "month",
      interval_count: "1",
      usage_type: "licenced",
      trial_days: "",
      usage_limit: "",
    },
    {
      product_id: productMapping.enterprise,
      name: "enterprise monthly",
      amount: "89.99", // Send as decimal string
      type: "recurring",
      interval: "month",
      interval_count: "1",
      usage_type: "licenced",
      trial_days: "",
      usage_limit: "",
    },
    // Yearly plans
    {
      product_id: productMapping.pro,
      name: "pro yearly",
      amount: "287.99", // Send as decimal string
      type: "recurring",
      interval: "year",
      interval_count: "1",
      usage_type: "licenced",
      trial_days: "",
      usage_limit: "",
    },
    {
      product_id: productMapping.business,
      name: "business yearly",
      amount: "479.99", // Send as decimal string
      type: "recurring",
      interval: "year",
      interval_count: "1",
      usage_type: "licenced",
      trial_days: "",
      usage_limit: "",
    },
    {
      product_id: productMapping.enterprise,
      name: "enterprise yearly",
      amount: "863.99", // Send as decimal string
      type: "recurring",
      interval: "year",
      interval_count: "1",
      usage_type: "licenced",
      trial_days: "",
      usage_limit: "",
    },
  ];

  const createdPrices = [];
  const priceErrors = [];

  for (let i = 0; i < prices.length; i++) {
    const price = prices[i];

    try {
      console.log(`Creating price: "${price.name}" ($${price.amount})`);

      const response = await fetch(
        "https://api.updatestack.com/v2/api/lambda/stripe/price",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-project": base64Encode,
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(price),
        }
      );

      const result = await response.json();

      if (result.error) {
        console.error(`❌ Error creating "${price.name}":`, result.message);
        priceErrors.push({ price: price.name, error: result.message });
      } else {
        console.log(`✅ Created "${price.name}" with ID: ${result.model}`);
        createdPrices.push({
          name: price.name,
          id: result.model,
          amount: price.amount,
          interval: price.interval,
        });
      }

      await new Promise((resolve) => setTimeout(resolve, 500));
    } catch (error) {
      console.error(
        `❌ Network error creating "${price.name}":`,
        error.message
      );
      priceErrors.push({ price: price.name, error: error.message });
    }
  }

  // Final Summary
  console.log("\n🎉 COMPLETE SETUP SUMMARY");
  console.log("=========================");
  console.log(`📦 Products created: ${createdProducts.length}`);
  console.log(`💰 Prices created: ${createdPrices.length}`);
  console.log(`❌ Total errors: ${productErrors.length + priceErrors.length}`);

  if (createdProducts.length > 0) {
    console.log("\n📦 Products:");
    createdProducts.forEach((product) => {
      console.log(`  • ${product.name} (ID: ${product.id})`);
    });
  }

  if (createdPrices.length > 0) {
    console.log("\n💰 Prices:");
    createdPrices.forEach((price) => {
      console.log(
        `  • ${price.name} - $${price.amount}/${price.interval} (ID: ${price.id})`
      );
    });
  }

  console.log(
    "\n🎯 Setup complete! Your Stripe products and prices are ready to use."
  );

  return {
    success: productErrors.length === 0 && priceErrors.length === 0,
    products: { created: createdProducts, errors: productErrors },
    prices: { created: createdPrices, errors: priceErrors },
    summary: {
      totalProducts: products.length,
      successfulProducts: createdProducts.length,
      totalPrices: prices.length,
      successfulPrices: createdPrices.length,
      totalErrors: productErrors.length + priceErrors.length,
    },
  };
};

console.log("📋 Complete Stripe Setup Script Loaded");
console.log("💻 Run: await bulkCreateStripeComplete()");
console.log("🔄 This will create products first, then prices automatically!");
