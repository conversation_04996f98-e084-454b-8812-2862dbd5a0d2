import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{r as o,b as m}from"./vendor-4cdf2bd1.js";import{c as a,a as r}from"./yup-342a5df4.js";import{u as p}from"./react-hook-form-a383372b.js";import{o as n}from"./yup-0917e80c.js";import{G as u,A as l,u as c,L as f,ax as s}from"./index-f2ad9142.js";import{u as x}from"./useLocalStorage-46cb237c.js";import{C as d,I as h}from"./index-5c103618.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const B=({role:e=null,updateStep:y=null})=>{o.useContext(u),o.useContext(l),m(),x(["step"]),c({isPublic:!0});const i=a({company_name:r().required("This field is required"),company_website:r()});return p({resolver:n(i),defaultValues:{company_name:"",company_website:""}}),t.jsx("div",{className:"flex h-svh max-h-svh min-h-svh w-full items-start justify-center",children:t.jsxs(f,{children:[[s.MEMBER].includes(e)?t.jsx(d,{}):null,[s.INVESTOR].includes(e)?t.jsx(h,{}):null]})})};export{B as default};
