import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as o,b as M,j as U}from"./vendor-4cdf2bd1.js";import"./index-79ee4c46.js";import{A as I,G as T,bD as B,L as v,I as w,v as P,M as Y,t as A,s as y}from"./index-f2ad9142.js";import{h as E}from"./moment-a9aaa855.js";import{M as k}from"./index-dc002f62.js";import{D as G}from"./DocumentTextIcon-54b5e200.js";import{D as W}from"./DocumentIcon-22c47322.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const $=({onClose:l,title:z="Create an update",classes:q={modal:"h-full",modalDialog:"h-[90%]",modalContent:""},page:C="",isOpen:H,disableCancel:K=!1,showModifyRecentOption:m=!1,onModifyRecent:u,limitReached:r=!1,limitChecking:s=!1,subscriptionData:a=null})=>{var N;const{dispatch:h,state:g}=o.useContext(I),{dispatch:c,state:f}=o.useContext(T),j=M(),[p,i]=o.useState(!1),[b,d]=o.useState(null),x=((N=f==null?void 0:f[B.createModel])==null?void 0:N.loading)||!1,n=x||p;async function S(){if(!(n||s||r))try{if(i(!0),d("blank"),r){l&&l(),i(!1),d(null);return}const t=await P(c,h,"updates",{name:"Update Title",user_id:g.user,mrr:0,arr:0,cash:0,burnrate:0,date:E().format("MMM D, YYYY"),public_link_enabled:0,private_link_open:1,company_id:g.company.id},!1);await new Y().callRawAPI("/v3/api/custom/goodbadugly/activities/draft",{update_id:t.data},"POST"),t!=null&&t.error||(l&&l(),j(`/member/edit-updates/${t.data}?autofocus=true`))}catch(t){A(h,t.message),y(c,t.message,5e3,"error")}finally{i(!1),d(null)}}const L=async()=>{if(!(n||s||r))try{i(!0),d("recent"),u&&await u()}catch(t){console.error(t),y(c,t.message,5e3,"error")}finally{i(!1),d(null)}},_=()=>n||s||r?!1:(l&&l(),!0);return e.jsx(v,{children:s?e.jsx("div",{className:"flex justify-center items-center w-full h-full",children:e.jsx(v,{children:e.jsx("p",{children:"Checking update limits..."})})}):r?e.jsxs("div",{className:"row-span-12 grid h-full max-h-full min-h-full w-full grid-cols-1 grid-rows-12 items-center justify-center p-5 md:w-[250px]",children:[e.jsx("div",{className:"text-center row-span-8",children:e.jsx("p",{children:a!=null&&a.trial_expired&&!(a!=null&&a.subscription)?"Please Upgrade your account to create an update!":"You have reached your monthly update limit for your current plan."})}),e.jsx("div",{className:"row-span-4 w-full",children:e.jsx("button",{className:"flex w-full flex-col items-center justify-center gap-5 rounded-[.125rem] bg-primary-black px-4 py-2 font-iowan text-[1rem] font-[700] leading-5 text-white",onClick:()=>{j("/member/billing?openManagePlan=true")},children:a!=null&&a.trial_expired&&!(a!=null&&a.subscription)?"Subscribe":"Upgrade Plan"})})]}):e.jsxs("div",{className:"h-fit max-h-fit min-h-fit",children:[e.jsx("div",{className:`flex w-full items-start gap-4 px-6 py-4 hover:bg-brown-main-bg  ${m?"hidden":""}`,children:e.jsxs("div",{children:[e.jsx("div",{className:"flex gap-3 items-center",children:e.jsx("span",{className:"text-left font-iowan text-[22px] font-semibold ",children:m?"":"Start Here"})}),e.jsx("p",{className:"mt-1 text-[14px] font-medium",children:"Create an update by selecting:"})]})}),m&&e.jsxs(w,{color:"#000000",disabled:n||s||r,onClick:L,className:"!flex !h-fit !max-h-fit !min-h-fit !w-full !items-start !justify-start !gap-4 border-b border-b-[#1f1d1a]/10 !bg-brown-main-bg !px-6 !py-4 !opacity-100 hover:!bg-brown-main-bg",children:[e.jsx("div",{className:"flex items-center justify-center gap-2 rounded-[.625rem]  p-0",children:e.jsx("div",{className:"flex items-center justify-center gap-2 rounded-[.625rem]  border border-[#1f1d1a] p-2",children:e.jsx("img",{src:"/assets/edit-2.svg",alt:"",className:"w-8 h-8"})})}),e.jsxs("div",{className:"flex-grow",children:[e.jsx("p",{className:"text-left text-[14px] font-semibold",children:"Modify with Recent"}),e.jsx("p",{className:"mt-1 font-medium text-left",children:"Start with most recent sent update"})]}),p&&b==="recent"&&!x&&e.jsx("div",{className:"flex items-center",children:e.jsx(k,{size:10,color:"#000000",loading:!0,type:"beat",className:"ml-2"})})]}),e.jsxs(U,{className:`flex w-full items-start gap-4 border-b border-b-[#1f1d1a]/10 px-6 py-4 hover:bg-brown-main-bg ${n||s||r?"pointer-events-none opacity-50":""}`,to:"/member/select-template",onClick:t=>{_()||t.preventDefault()},children:[e.jsx("div",{className:"flex items-center justify-center gap-2 rounded-[.625rem] border border-[#1f1d1a] p-2",children:e.jsx(G,{className:"w-8 h-8 text-primary-black",strokeWidth:2})}),e.jsxs("div",{className:"flex-grow",children:[e.jsx("p",{className:"text-left text-[14px] font-semibold",children:"New Template Update"}),e.jsx("p",{className:"mt-1 font-medium text-left",children:"Start an update using an existing update templates"})]})]}),e.jsxs(w,{color:"#000000",disabled:n||s||r,onClick:S,className:"!flex !h-fit !max-h-fit !min-h-fit !w-full !items-start !justify-start !gap-4 border-b border-b-[#1f1d1a]/10 !bg-brown-main-bg !px-6 !py-4 !opacity-100 hover:!bg-brown-main-bg",children:[e.jsx("div",{className:"flex items-center justify-center gap-2 rounded-[.625rem] border border-[#1f1d1a] p-2",children:e.jsx(W,{className:"w-8 h-8 text-primary-black",strokeWidth:2})}),e.jsxs("div",{className:"flex-grow",children:[e.jsx("p",{className:"text-left text-[14px] font-semibold",children:"New Blank Update"}),e.jsx("p",{className:"mt-1 font-medium text-left",children:"Start an update from scratch"})]}),p&&b==="blank"&&!x&&e.jsx("div",{className:"flex items-center",children:e.jsx(k,{size:10,color:"#000000",loading:!0,type:"beat",className:"ml-2"})})]})]})})},pe=o.memo($);export{pe as default};
