import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{R as r,b as ce,r as de}from"./vendor-4cdf2bd1.js";import{M as le,G as me,A as pe,T as ue,t as fe,f as he,az as xe,aA as ge}from"./index-f2ad9142.js";import{s as Se}from"./AuthAction-52ee0934.js";import"./yup-342a5df4.js";import{P as be}from"./index-9dceff66.js";import{A as R}from"./AddButton-51d1b2cd.js";import{M as T}from"./index-d526f96e.js";import we from"./InteractiveButton-060359e0.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";let w=new le;const j=[{header:"Email",accessor:"email",isSorted:!1,isSortedDesc:!1},{header:"Name",accessor:"name",render:i=>`${i.first_name} ${i.last_name}`,isSorted:!1,isSortedDesc:!1},{header:"Role",accessor:"role",isSorted:!1,isSortedDesc:!1},{header:"Signup Date",accessor:"create_at",isSorted:!1,isSortedDesc:!1,render:i=>new Date(i.create_at).toLocaleDateString()},{header:"Current Plan",accessor:"stripe_subscription",isSorted:!1,isSortedDesc:!1,render:i=>{var d,o;const c=(d=i.stripe_subscription)==null?void 0:d.find(s=>s.status=="active");if(!c)return"No active plan";try{const s=JSON.parse(c.object);return((o=s==null?void 0:s.plan)==null?void 0:o.nickname)||"N/A"}catch{return"Error parsing plan"}}},{header:"Plan End Date",accessor:"stripe_subscription",isSorted:!1,isSortedDesc:!1,render:i=>{var d;const c=(d=i.stripe_subscription)==null?void 0:d.find(o=>o.status==="active");if(!c)return"N/A";if(c.plan_end_date)return new Date(c.plan_end_date).toLocaleDateString();try{const o=JSON.parse(c.object);return o!=null&&o.current_period_end?new Date(o.current_period_end*1e3).toLocaleDateString():"N/A"}catch{return"Error parsing date"}}},{header:"Status",accessor:"status",mapping:["Inactive","Active","Suspend"],isSorted:!1,isSortedDesc:!1},{header:"Action",accessor:"",render:(i,c,d)=>{const o=async()=>{try{w.stopImpersonation();const s=await w.callRawAPI(`/v3/api/custom/goodbadugly/users/impersonate/${i.id}`,[],"POST");if(!s.error){const{token:u,user:l,original_admin:v}=s.data;console.log("token >>",u),w.startImpersonation(u),Se(c,{user_id:l.id,token:u,role:l.role,first_name:l.first_name,last_name:l.last_name,photo:l.photo,original_admin:v}),d("/member/dashboard")}}catch(s){w.stopImpersonation(),console.error("Impersonation failed:",s)}};return t.jsxs("div",{className:"flex items-center gap-3",children:[t.jsx("button",{className:"text-[#4F46E5] hover:underline",onClick:()=>{setActiveEditId(i.id),setShowEditSidebar(!0)},children:"Edit"}),t.jsx("button",{className:"text-[#4F46E5] hover:underline",onClick:o,children:"Login as"})]})}}],We=()=>{const{dispatch:i}=r.useContext(me),{dispatch:c}=r.useContext(pe),[d,o]=r.useState([]),[s,u]=r.useState(10),[l,v]=r.useState(0),[M,$]=r.useState(0),[f,z]=r.useState(1),[U,B]=r.useState(!1),[H,G]=r.useState(!1),[N,D]=r.useState(!0),[J,A]=r.useState(!1),[K,_]=r.useState(!1),[O,je]=r.useState(),[h,E]=r.useState(""),[x,P]=r.useState(""),[g,C]=r.useState(""),[S,k]=r.useState(""),[I,W]=r.useState("id"),[q,Q]=r.useState("desc"),F=ce(),V=["admin","member"];function X(e){u(e),p(1,e)}const Y=de.useCallback(e=>{p(e,s)},[s]);function Z(){const e=f-1>0?f-1:1;p(e,s)}function ee(){const e=f+1<=l?f+1:l;p(e,s)}const te=e=>{const n=j[e];!n.accessor||n.accessor===""||(n.isSorted?n.isSortedDesc=!n.isSortedDesc:(j.forEach(a=>{a.isSorted=!1,a.isSortedDesc=!1}),n.isSorted=!0),W(n.accessor),Q(n.isSortedDesc?"desc":"asc"),p(f,s))};async function p(e,n){D(!0);try{const a=new ue;w.setTable("user");const m={};h&&(m.email=h),x&&(m.first_name=`%${x}%`),g&&(m.last_name=`%${g}%`),S&&(m.role=S);let b=[];h&&b.push(`email,cs,${h}`),x&&b.push(`first_name,cs,${x}`),g&&b.push(`last_name,cs,${g}`),S&&b.push(`role,eq,${S}`);const re=await a.getPaginate("user",{join:["stripe_subscription|user_id","stripe_product|stripe_id"],size:n,page:e,filter:b,...I?{order:I,direction:q}:{}}),{list:ne,total:oe,limit:ie,num_pages:L,page:y}=re;o(ne),u(ie),v(L),z(y),$(oe),B(y>1),G(y<L)}catch(a){console.log("ERROR",a),fe(c,a.message)}D(!1)}const se=e=>{e.preventDefault(),p(1,s)},ae=()=>{E(""),P(""),C(""),k(""),p(1,s)};return r.useEffect(()=>{i({type:"SETPATH",payload:{path:"users"}}),p(1,s)},[]),t.jsxs("div",{className:"px-8",children:[t.jsxs("div",{className:"items-center py-3 pt-10",children:[t.jsxs("div",{className:"mb-6 flex w-full items-center justify-between gap-5",children:[t.jsx("h4",{className:" text-[1rem] font-semibold md:text-xl",children:"Users"}),t.jsx(R,{onClick:()=>_(!0)})]}),t.jsx("div",{className:"mb-6 flex items-center gap-4",children:t.jsxs("form",{onSubmit:se,className:"flex w-full items-end gap-2 md:w-[75%]",children:[t.jsx("input",{type:"text",placeholder:"Search by email",value:h,onChange:e=>E(e.target.value),className:"focus:shadow-outline h-[3rem] w-full appearance-none rounded-sm border-[.125rem] border-[#1f1d1a] bg-brown-main-bg p-[.75rem_1rem_.75rem_1rem] text-sm font-normal leading-tight text-[#1f1d1a] shadow focus:outline-none focus:ring-0"}),t.jsx("input",{type:"text",placeholder:"First name",value:x,onChange:e=>P(e.target.value),className:"focus:shadow-outline h-[3rem] w-full appearance-none rounded-sm border-[.125rem] border-[#1f1d1a] bg-brown-main-bg p-[.75rem_1rem_.75rem_1rem] text-sm font-normal leading-tight text-[#1f1d1a] shadow focus:outline-none focus:ring-0"}),t.jsx("input",{type:"text",placeholder:"Last name",value:g,onChange:e=>C(e.target.value),className:"focus:shadow-outline h-[3rem] w-full appearance-none rounded-sm border-[.125rem] border-[#1f1d1a] bg-brown-main-bg p-[.75rem_1rem_.75rem_1rem] text-sm font-normal leading-tight text-[#1f1d1a] shadow focus:outline-none focus:ring-0"}),t.jsxs("select",{className:"focus:shadow-outline h-[3rem] w-full appearance-none rounded-sm border-[.125rem] border-[#1f1d1a] bg-brown-main-bg p-[.75rem_1rem_.75rem_1rem] text-sm font-normal leading-tight text-[#1f1d1a] shadow focus:outline-none focus:ring-0",value:S,onChange:e=>{k(e.target.value)},children:[t.jsx("option",{value:"",children:"All Roles"}),V.map(e=>t.jsx("option",{value:e,children:e.charAt(0).toUpperCase()+e.slice(1)},e))]}),t.jsx(we,{type:"submit",className:"flex !h-[2.25rem] w-fit items-center justify-center whitespace-nowrap !rounded-[0.125rem] !border bg-[#1f1d1a] !py-[0.5rem] px-2 !text-[1rem] tracking-wide text-white outline-none focus:outline-none md:px-5",color:"black",disabled:N,children:"Search"}),(h||x||g||S)&&t.jsx(R,{onClick:ae,showPlus:!1,className:"relative mt-[5px] !w-fit !min-w-fit !max-w-fit !border-0 !bg-transparent !p-0 !font-inter !text-[1rem] !font-[600] !leading-[1.21rem] !tracking-wide !text-black !underline !shadow-none",children:"Clear"})]})})]}),N?t.jsx(he,{}):t.jsxs("div",{className:"overflow-x-auto shadow",children:[t.jsxs("table",{className:"min-w-full",children:[t.jsx("thead",{children:t.jsx("tr",{children:j.map((e,n)=>t.jsx("th",{scope:"col",className:`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 ${e.accessor&&e.accessor!==""?"cursor-pointer":""}`,onClick:()=>e.accessor&&e.accessor!==""&&te(n),children:t.jsxs("div",{className:"flex items-center gap-2",children:[e.header,e.isSorted&&t.jsx("span",{children:e.isSortedDesc?"▼":"▲"})]})},n))})}),t.jsx("tbody",{className:"font-iowan-regular divide-y divide-[#1f1d1a]/10",children:d.map((e,n)=>t.jsx("tr",{className:"md:h-[60px]",children:j.map((a,m)=>a.accessor===""?t.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:a.render(e,c,F)},m):a.mapping&&a.accessor==="status"?t.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm",children:e[a.accessor]===1?t.jsx("span",{className:"rounded-md bg-[#D1FAE5] px-3 py-1 text-[#065F46]",children:a.mapping[e[a.accessor]]}):t.jsx("span",{className:"rounded-md bg-[#F4F4F4] px-3 py-1 text-[#393939]",children:a.mapping[e[a.accessor]]})},m):a.render?t.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:a.render(e,c,F)},m):t.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e[a.accessor]},m))},n))})]}),!N&&d.length===0&&t.jsx("p",{className:"px-10 py-3 text-xl capitalize",children:"No users found"})]}),t.jsx("div",{className:"mb-8 mt-4 w-full",children:t.jsx(be,{updateCurrentPage:Y,currentPage:f,pageCount:l,pageSize:s,canPreviousPage:U,canNextPage:H,updatePageSize:X,previousPage:Z,nextPage:ee,dataTotal:M,multiplier:10,startSize:10,canChangeLimit:!0})}),t.jsx(T,{isModalActive:K,showHeader:!0,title:"Add User",closeModalFn:()=>_(!1),customMinWidthInTw:"md:w-[25%] w-full !bg-brown-main-bg",children:t.jsx(xe,{setSidebar:_})}),t.jsx(T,{isModalActive:J,showHeader:!0,title:"Edit User",closeModalFn:()=>A(!1),customMinWidthInTw:"md:w-[25%] w-full !bg-brown-main-bg",children:t.jsx(ge,{activeId:O,setSidebar:A})})]})};export{We as default};
