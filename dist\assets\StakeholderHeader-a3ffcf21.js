import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as a,L as x,j as n}from"./vendor-4cdf2bd1.js";import{a as m}from"./index.esm-c839cefc.js";import{M as d,G as c,A as p}from"./index-f2ad9142.js";import{d as l}from"./index.esm-6fcccbfe.js";import"./@nextui-org/theme-345a09ed.js";import"./react-icons-36ae72b7.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";new d;const h=[{to:"/stakeholder/dashboard",text:"Dashboard",icon:e.jsx(l,{className:"text-xl text-[#A8A8A8]"}),value:"dashboard"},{to:"/stakeholder/update_requests",text:" Updates (0)",icon:e.jsx(l,{className:"text-xl text-[#A8A8A8]"}),value:"update_requests"},{to:"/stakeholder/profile",text:"Profile",icon:e.jsx(m,{className:"text-xl text-[#A8A8A8]"}),value:"profile"}],$=()=>{const{state:{isOpen:s,path:r},dispatch:o}=a.useContext(c);a.useContext(p),a.useState(!1),a.useState(!1);let i=t=>{o({type:"OPEN_SIDEBAR",payload:{isOpen:t}})};return a.useEffect(()=>{},[]),e.jsx(e.Fragment,{children:e.jsxs("div",{className:`z-10 flex max-h-screen flex-1 flex-col border border-[#E0E0E0] bg-brown-main-bg py-4 text-[#A8A8A8] transition-all ${s?"absolute left-[0px] top-[0px] h-screen w-[220px] sm:fixed sm:w-[15rem] sm:min-w-[15rem] sm:max-w-[15rem] md:relative":"relative min-h-screen w-[3.1rem] bg-[#1f1d1a] text-white sm:w-[4.2rem] sm:min-w-[4.2rem] sm:max-w-[4.2rem]"} `,children:[e.jsxs("div",{className:`text-[#393939] ${s?"flex w-full":"flex items-center justify-center"} `,children:[e.jsx("div",{}),s&&e.jsx("div",{className:"w-full text-xl font-bold",children:e.jsxs(x,{to:"",className:"mb-4 block text-center text-3xl font-extrabold text-[#666666]",children:["update",e.jsx("span",{className:"font-normal",children:"stack"})]})})]}),e.jsx("div",{className:"h-fit w-auto flex-1",children:e.jsx("div",{className:"sidebar-list2 w-auto",children:e.jsx("ul",{className:"flex flex-wrap px-2 text-sm",children:h.map(t=>e.jsx("li",{className:"block w-full list-none",children:e.jsx(n,{to:t.to,className:`${r==t.value?"active-nav":""} `,children:e.jsxs("div",{className:"flex items-center gap-3",children:[t.icon,s&&e.jsx("span",{children:t.text})]})})},t.value))})})}),e.jsx("div",{className:"flex justify-end",children:e.jsx("div",{className:"mr-3 cursor-pointer rounded-lg border border-[#E0E0E0] bg-brown-main-bg p-2 text-xl text-gray-400",children:e.jsx("span",{onClick:()=>i(!s),children:e.jsx("svg",{className:`transition-transform ${s?"":"rotate-180"}`,xmlns:"http:www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12ZM10.4142 11L11.7071 9.70711C12.0976 9.31658 12.0976 8.68342 11.7071 8.29289C11.3166 7.90237 10.6834 7.90237 10.2929 8.29289L7.82322 10.7626C7.13981 11.446 7.13981 12.554 7.82322 13.2374L10.2929 15.7071C10.6834 16.0976 11.3166 16.0976 11.7071 15.7071C12.0976 15.3166 12.0976 14.6834 11.7071 14.2929L10.4142 13H16C16.5523 13 17 12.5523 17 12C17 11.4477 16.5523 11 16 11H10.4142Z",fill:"#A8A8A8"})})})})})]})})};export{$ as StakeholderHeader,$ as default};
