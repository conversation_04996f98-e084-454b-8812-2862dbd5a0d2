import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{o as h}from"./yup-0917e80c.js";import{A as b,G as g,M as w,s as l,t as E}from"./index-f2ad9142.js";import{InteractiveButton2 as j}from"./InteractiveButton-060359e0.js";import{M as y}from"./MkdInput-d37679e9.js";import{r as n}from"./vendor-4cdf2bd1.js";import{u as N}from"./react-hook-form-a383372b.js";import{c as S,a as k}from"./yup-342a5df4.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";function ie(){var i,s;const{dispatch:a,state:p}=n.useContext(b),{dispatch:o}=n.useContext(g),d=S({email:k().required("This field is required")}),{register:c,handleSubmit:u,setError:v,reset:C,formState:{errors:t,isSubmitting:m}}=N({resolver:h(d),defaultValues:{email:p.profile.email}});async function x(f){try{await new w().updateEmail(f.email),l(o,"Email changed successfully"),a({type:"REFETCH_PROFILE"})}catch(r){E(a,r.message),l(o,r.message,5e3,"error")}}return e.jsx("div",{className:"px-4 md:px-8",children:e.jsxs("div",{className:"w-full max-w-7xl pb-32",children:[e.jsx("div",{className:"mb-4 font-iowan text-[20px] font-[700] md:text-[1.5rem] md:leading-[1.865rem] ",children:"Account Email"}),e.jsx("p",{className:"mb-7 font-inter text-[1rem] font-[400] leading-[1.21rem]",children:"Update your account's email"}),e.jsxs("form",{onSubmit:u(x),children:[e.jsxs("div",{className:"flex flex-col gap-[1rem]",children:[e.jsx(y,{type:"email",name:"email",label:"Email",errors:t,register:c,className:`no-box-shadow Update your account's email h-[2.6rem] w-full max-w-[31.25rem] appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none md:max-w-[25rem] ${(i=t.email)!=null&&i.message?"border-red-500":""}`}),e.jsx(j,{loading:m,disabled:m,type:"submit",className:"disabled:bg-disabledblack h-[2.6rem] w-[10rem] whitespace-nowrap rounded-[.125rem] bg-black  px-5 py-2  text-center text-sm font-semibold text-white transition-colors duration-100",children:"Update"})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(s=t.email)==null?void 0:s.message})]})]})})}export{ie as default};
