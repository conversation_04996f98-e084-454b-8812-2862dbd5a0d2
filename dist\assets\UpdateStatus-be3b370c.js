import{j as o}from"./@nextui-org/listbox-0f38ca19.js";import{r as i,u as m}from"./vendor-4cdf2bd1.js";import{G as n}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const y=({nextScheduledUpdate:r=!1})=>{const{state:e}=i.useContext(n),{update:t}=e,s=m();return console.log(t,"sns"),!t||t!=null&&t.sent_at||t!=null&&t.status||!s.pathname.includes("edit-updates")?null:o.jsx("div",{className:"flex h-[2rem] max-h-[2rem] min-h-[2rem] w-fit rounded-full",children:o.jsx("span",{className:`flex h-[24px]  items-center justify-center rounded-3xl border border-black  px-2 py-1 text-xs font-semibold text-black  ${r?"w-[60px] bg-[#9DD321] px-4":"w-[54px] bg-[#BCBBBA]"}`,children:r?"Scheduled":"Draft"})})};export{y as default};
