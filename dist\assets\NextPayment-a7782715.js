import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{A as j}from"./AddButton-51d1b2cd.js";import{a as g,u as w}from"./index-f2ad9142.js";import{u as N}from"./useDate-c1da5729.js";import{u as v}from"./useSubscription-dc563085.js";import{r as y}from"./vendor-4cdf2bd1.js";import{IntervalMap as P}from"./CurrentPlan-129777d5.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index-ff987cdd.js";import"./index-49e40c51.js";const V=()=>{var o,i,n,m,p,l,c,a,x,d,u;const{globalState:t,setGlobalState:f}=g(),{data:e,loading:q,getSubscription:h}=v(),{convertDate:b}=N(),{profile:s}=w();return y.useEffect(()=>{s!=null&&s.id&&(h({filter:[`user_id,eq,${s==null?void 0:s.id}`,"cancelled,eq,0","status,eq,'active'"],join:[]}),t!=null&&t.refreshSubscription&&f("refreshSubscription",!1))},[s==null?void 0:s.id,t==null?void 0:t.refreshSubscription]),r.jsxs("div",{className:"grid h-[19.9375rem] max-h-[19.9375rem] min-h-[19.9375rem] w-full grid-cols-1 grid-rows-12 flex-col justify-between gap-[.75rem] rounded-[.625rem] md:h-full md:max-h-full md:min-h-full md:w-1/2",children:[r.jsx("div",{className:"row-span-2 font-iowan text-[20px] font-[700] leading-[24.86px]",children:"Next Payment"}),r.jsxs("div",{className:"row-span-6 space-y-[.75rem]  ",children:[r.jsxs("span",{className:"",children:[r.jsx("span",{className:"font-iowan text-[3.25rem] font-bold leading-[4.04rem] ",children:e!=null&&e.subscription?(n=(((i=(o=e==null?void 0:e.object)==null?void 0:o.plan)==null?void 0:i.amount)/100).toFixed(2))==null?void 0:n.split(".")[0]:"$0"}),r.jsx("span",{className:"font-iowan text-[3.25rem] font-bold leading-[4.04rem] ",children:e!=null&&e.subscription?r.jsxs(r.Fragment,{children:[".",(l=(((p=(m=e==null?void 0:e.object)==null?void 0:m.plan)==null?void 0:p.amount)/100).toFixed(2))==null?void 0:l.split(".")[1],(a=(c=e==null?void 0:e.object)==null?void 0:c.plan)!=null&&a.interval?r.jsxs("span",{className:"font-iowan text-[16px] font-bold leading-[64.64px] ",children:[" ","/",P.get((d=(x=e==null?void 0:e.object)==null?void 0:x.plan)==null?void 0:d.interval)]}):r.jsx("span",{className:"font-iowan text-[16px] font-bold leading-[64.64px] ",children:"/mo"})]}):".00"})]}),r.jsx("div",{className:"font-Inter text-[1rem] font-normal leading-[1.5rem] text-gray-600",children:e!=null&&e.subscription?b(((u=e==null?void 0:e.object)==null?void 0:u.current_period_end)*1e3):null})]}),r.jsx("div",{className:"flex row-span-4 items-end",children:r.jsx(j,{showPlus:!1,className:"!rounded-[.125rem] !border !border-primary-black !bg-transparent px-4 py-2 !text-primary-black",children:"Apply Coupon"})})]})};export{V as default};
