import{_ as o}from"./qr-scanner-cf010ec4.js";import{r as e}from"./vendor-4cdf2bd1.js";import"./@nextui-org/listbox-0f38ca19.js";import{l as r}from"./react-quill-a78e6fc7.js";const t=r.Quill.import("formats/size");t.whitelist=["extra-small","small","medium","large"];r.Quill.register(t,!0);const i=r.Quill.import("formats/font");i.whitelist=["arial","comic-sans","courier-new","georgia","helvetica","lucida"];r.Quill.register(i,!0);const u=e.lazy(()=>o(()=>import("./CustomEditor-4eddf399.js"),["assets/CustomEditor-4eddf399.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/@editorjs/editorjs-3bc58744.js","assets/@editorjs/paragraph-9d333c59.js","assets/@editorjs/header-da8c369a.js","assets/@editorjs/list-86b325f6.js","assets/@editorjs/link-7a38da73.js","assets/@editorjs/delimiter-89018da8.js","assets/@editorjs/checklist-1b2b7ac3.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"]));export{u as C};
