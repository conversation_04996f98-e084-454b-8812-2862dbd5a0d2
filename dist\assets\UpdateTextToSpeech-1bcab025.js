import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{r as c,R as $}from"./vendor-4cdf2bd1.js";import{b as fe,a as de,u as ue,M as le,y as me,z as he,L as S,aH as oe,aI as re,aJ as se,aK as ie,o as xe}from"./index-f2ad9142.js";import{h as te}from"./moment-a9aaa855.js";import{M as ae}from"./MkdInput-d37679e9.js";import{M as ge}from"./index-713720be.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const b={STOP:"stop",PLAY:"play",PAUSE:"pause",PAUSED:"paused",STOPPED:"stopped",PLAYING:"playing",SELECTION:"selection"},Y={SELECT:"select",ENTIRE_UPDATE:"entire_update",UPDATE_NO_METRICS:"update_no_metrics",summary:"summary"},ce=new Map([["entire_update","Entire update"],["update_no_metrics","Update w/o metrics"],["summary","Summary only"]]),pe=({update:s,summary:E})=>{const d=c.useRef(null);fe();const{authDispatch:O,globalDispatch:K,custom:z,globalState:F}=de(),[h,U]=$.useState({currentTime:0,duration:0,progress:0}),[H,v]=$.useState({synthesizer:!1,targets:{}}),[t,x]=$.useState({synthesizer:{},target:"",autoPlay:!1}),[j,A]=$.useState({synthesizer:{},src:null,ready:!1}),[L,N]=$.useState({state:"stop",played:null});ue({isPublic:!1});const _=$.useCallback((l={texts:[]})=>(async()=>{var g;const a=(g=l==null?void 0:l.texts)==null?void 0:g.map(o=>new Promise(async(P,f)=>{var i;try{v(y=>({...y,synthesizer:!0}));const p=new le;if(F!=null&&F.isPublicView){console.log("Skipping Polly synthesis - public view");return}const m=await p.callRawAPI("/v3/api/custom/goodbadugly/integrations/polly/synthesize",{text:o==null?void 0:o.text},"POST");if(m&&!m.error&&((i=m.data)!=null&&i.audioUrl)){const y=m.data.audioUrl;try{if(!(await fetch(y,{method:"HEAD"})).ok)throw new Error("Audio URL not accessible")}catch(M){throw console.error("Audio URL validation failed:",M),new Error("Failed to validate audio URL")}P({error:!1,target:o==null?void 0:o.target,url:y})}else throw console.error("Synthesis failed:",m),new Error((m==null?void 0:m.error)||"Failed to generate audio")}catch(p){console.error("Synthesis error:",p),f({error:!0,target:o==null?void 0:o.target,message:(p==null?void 0:p.message)||"Failed to generate audio"})}finally{v(p=>({...p,synthesizer:!1}))}}));try{const o=await Promise.all(a),P={};if(o==null||o.forEach(f=>{!(f!=null&&f.error)&&(f!=null&&f.url)?P[f==null?void 0:f.target]=f==null?void 0:f.url:console.error("Invalid result item:",f)}),Object.keys(P).length===0)throw new Error("No valid audio URLs generated");return console.log("Audio data:",P),A(f=>({...f,synthesizer:P,ready:!0})),P}catch(o){throw console.error("Failed to process audio results:",o),A(P=>({...P,synthesizer:{},ready:!1})),o}})(),[A,v]),e=c.useCallback(l=>{var M,B,W,Q,X,V;const g=((s==null?void 0:s.notes)||[]).map(w=>{const{blocks:Z}=me(w.content,{blocks:[]});if(Z.length===0)return null;const D=he(Z);return{title:w==null?void 0:w.type,para:D}}).filter(Boolean),o=E==null?void 0:E.content,f=((s==null?void 0:s.questions)||[]).map(w=>({title:w==null?void 0:w.question})),p=[...[{title:s==null?void 0:s.name,para:te(s==null?void 0:s.date).format("MM/DD/YYYY")},{title:"Financial Overview"},{title:"MMR",para:s==null?void 0:s.mrr},{title:"ARR",para:s==null?void 0:s.arr},{title:"Cash",para:s==null?void 0:s.cash},{title:"Burnrate",para:s==null?void 0:s.burnrate},{title:"Runway (months)",para:s==null?void 0:s.runway},{title:"Investment Overview"},{title:"Investment stage",para:s==null?void 0:s.investment_stage},{title:"Invested to date",para:s==null?void 0:s.invested_to_date},{title:"Investors on Cap Table",para:s==null?void 0:s.investors_on_cap_table},{title:"Valuation of last round",para:s==null?void 0:s.valuation_at_last_round},{title:"Date of last round",para:(s==null?void 0:s.date_of_last_round)&&te(s==null?void 0:s.date_of_last_round).format("MM/DD/YYYY")}],...g,{title:"Asks"},...f,...o||[]],m=[{title:s==null?void 0:s.name,para:te(s==null?void 0:s.date).format("MM/DD/YYYY")},...g,{title:"Asks"},...f,...o||[]],y=[{target:"entire_update",text:(W=(B=(M=p.map(w=>`${w.title}: ${w.para||""}`).join(". "))==null?void 0:M.replace(/. undefined:/g,""))==null?void 0:B.replace(/<p>/g,""))==null?void 0:W.replace(/<\/p>/g,"")},{target:"update_no_metrics",text:(V=(X=(Q=m.map(w=>`${w.title}: ${w.para||""}`).join(". "))==null?void 0:Q.replace(/. undefined:/g,""))==null?void 0:X.replace(/<p>/g,""))==null?void 0:V.replace(/<\/p>/g,"")},{target:"summary",text:o||"No summary available."},{target:"select",text:"Please select an option to speak."}];x(w=>({...w,synthesizer:y})),_({texts:y})},[t,E,s]),T=c.useCallback(async(l,a=b.SELECTION)=>{var o,P,f;if([l].includes(t==null?void 0:t.target))return;x(i=>({...i,target:l}));let g=(o=j==null?void 0:j.synthesizer)==null?void 0:o[l];if(g)A(i=>({...i,src:g}));else try{v(y=>({...y,targets:{...y.targets,[l]:!0}}));const i=(P=t==null?void 0:t.synthesizer)==null?void 0:P.find(y=>y.target===l);if(!i){console.error("No text content found for:",l);return}const p=new le;if(F!=null&&F.isPublicView){console.log("Skipping Polly synthesis - public view");return}const m=await p.callRawAPI("/v3/api/custom/goodbadugly/integrations/polly/synthesize",{text:i.text},"POST");if(m&&!m.error&&((f=m.data)!=null&&f.audioUrl))g=m.data.audioUrl,A(y=>({...y,synthesizer:{...y.synthesizer,[l]:g},src:g}));else throw new Error((m==null?void 0:m.error)||"Failed to generate audio")}catch(i){console.error("Failed to generate audio:",i);return}finally{v(i=>({...i,targets:{...i.targets,[l]:!1}}))}if(d.current&&([b.PLAY].includes(L==null?void 0:L.state)&&(d.current.pause(),d.current.currentTime=0),d.current.src=g,d.current.load(),d.current.onplay=()=>{N(i=>({...i,played:l,state:b.PLAY}))},d.current.onpause=()=>{N(i=>({...i,state:b.PAUSE}))},t!=null&&t.autoPlay)){const i=d.current.play();i!==void 0&&i.catch(p=>{console.error("Playback failed:",p),N(m=>({...m,state:b.STOP}))})}N(i=>({...i,played:l,state:t!=null&&t.autoPlay?b.PLAY:b.STOP}))},[t,j,L,b,d==null?void 0:d.current]),q=c.useCallback(l=>{if(d!=null&&d.current)if(N(a=>({...a,state:l})),l===b.PLAY){const a=d.current.play();a!==void 0&&a.catch(g=>{console.error("Playback failed:",g),N(o=>({...o,state:b.STOP}))})}else l===b.PAUSE?d.current.pause():l===b.STOP&&(d.current.pause(),d.current.currentTime=0)},[b]),J=c.useCallback(l=>{const a=l.target;U({currentTime:a.currentTime,duration:a.duration,progress:a.currentTime/a.duration*100})},[]),ne=l=>{if(!l)return"0:00";const a=Math.floor(l/60),g=Math.floor(l%60);return`${a}:${g.toString().padStart(2,"0")}`},R=c.useCallback(()=>{N(l=>({...l,state:b.STOP,played:null})),d.current&&(d.current.currentTime=0)},[N,b]),k=Array.from(ce.keys()).filter(l=>l!==(Y==null?void 0:Y.SELECT)),I=c.useCallback(()=>{const l=k.indexOf(t==null?void 0:t.target),a=l===k.length-1?0:l+1;T(k[a])},[k,t==null?void 0:t.target,T]),G=c.useCallback(()=>{const l=k.indexOf(t==null?void 0:t.target),a=l<=0?k.length-1:l-1;T(k[a])},[k,t==null?void 0:t.target,T]);return c.useEffect(()=>{s!=null&&s.id&&(E!=null&&E.content)&&e()},[s==null?void 0:s.id,E==null?void 0:E.content]),c.useEffect(()=>{j!=null&&j.ready&&(T(Y==null?void 0:Y.ENTIRE_UPDATE),A(l=>({...l,ready:!1})))},[j==null?void 0:j.ready]),{data:t,audio:j,player:L,loading:H,setData:x,audioRef:d,setAudio:A,PlayerMap:b,TargetMap:Y,optionMap:ce,setPlayer:N,formatTime:ne,handleNext:I,synthesizer:_,handleSpeech:T,handlePlayer:q,audioProgress:h,handlePrevious:G,handleAudioEnd:R,handleTimeUpdate:J}},Ve=({data:s,summary:E,nextUpdate:d,activeAudioRef:O,showControls:K,setShowControls:z,localData:F})=>{var Z,D;console.log(F,"localData");const{audio:h,player:U,setData:H,audioRef:v,PlayerMap:t,optionMap:x,formatTime:j,handleNext:A,handleSpeech:L,handlePlayer:N,audioProgress:_,data:e,handleTimeUpdate:T,setPlayer:q,handlePrevious:J}=pe({update:s,summary:E}),[ne,R]=c.useState(!1),{showToast:k,globalState:I}=de(),[G,l]=c.useState(!0),a=c.useRef(null),g=c.useRef(null),[o,P]=c.useState(!1),[f,i]=c.useState(0),p=c.useRef(null);c.useRef(null),c.useRef(null);const m=c.useRef(null),y=c.useRef(!1),M=()=>{k("Please login to interact with the update",3e3)};c.useEffect(()=>{const n=new URLSearchParams(location.search),u=n.get("listen_option"),C=n.get("autoplay");u&&x.has(u)&&(console.log("Initial mount: Setting target from URL param:",u),H(ee=>({...ee,target:u,autoPlay:C==="true"})),L(u,t.SELECTION)),C==="true"&&(h!=null&&h.src)&&(O!=null&&O.current&&(O.current.pause(),O.current.currentTime=0),v.current.play(),k("Audio Buffering...",3e3))},[h==null?void 0:h.src]),c.useEffect(()=>{if(G)return;const n=new URLSearchParams(location.search),u=n.get("listen_option");if((e==null?void 0:e.target)==="entire_update"&&u){console.log("Preventing update to entire_update"),H(C=>({...C,target:u}));return}if(e!=null&&e.target&&u!==e.target){console.log("Updating URL:",{from:u,to:e.target}),n.set("listen_option",e.target);const C=n.get("autoplay");C&&n.set("autoplay",C);const ee=`${window.location.pathname}?${n.toString()}`;window.history.pushState({path:ee},"",ee)}},[e==null?void 0:e.target,G]),c.useEffect(()=>{const n=v.current;if(!n)return;const u=()=>{a.current=setTimeout(()=>{k("Audio Buffering...",3e3)},500)},C=()=>{a.current&&(clearTimeout(a.current),a.current=null)};return n.addEventListener("waiting",u),n.addEventListener("playing",C),()=>{n.removeEventListener("waiting",u),n.removeEventListener("playing",C),a.current&&clearTimeout(a.current)}},[v.current]),c.useEffect(()=>{if(e!=null&&e.target&&(h!=null&&h.src)){const n=new Audio;return n.preload="auto",g.current=n,n.src=h.src,n.load(),()=>{g.current&&(g.current.src="",g.current=null)}}},[e==null?void 0:e.target,h==null?void 0:h.src]);const B=c.useCallback(()=>{if(q(n=>({...n,state:t.STOP,played:null})),e!=null&&e.autoPlay){const n=new URLSearchParams(window.location.search);n.set("autoplay","true"),e!=null&&e.target&&n.set("listen_option",e.target);const u=`${window.location.pathname}?${n.toString()}`;window.history.pushState({path:u},"",u),d(!0)}},[q,t,e==null?void 0:e.autoPlay,e==null?void 0:e.target,d]),W=n=>{p.current=n.touches[0].clientY,y.current=!0,P(!0)},Q=n=>{if(!y.current||!p.current)return;const C=n.touches[0].clientY-p.current;i(C),C>100?z(!1):z(!0)},X=n=>{if(!y.current||!p.current)return;const C=n.changedTouches[0].clientY-p.current;y.current=!1,p.current=null,P(!1),i(0),C<=100&&z(!0)},V=()=>{if(I!=null&&I.isPublicView){M();return}Array.from(x==null?void 0:x.keys()).includes(e==null?void 0:e.target)&&N(t.PLAY)},w=()=>{if(I!=null&&I.isPublicView){M();return}H(n=>({...n,autoPlay:!(n!=null&&n.autoPlay)})),e!=null&&e.autoPlay||v.current.play()};return console.log(e==null?void 0:e.target,x,"hello"),r.jsxs(r.Fragment,{children:[r.jsx("div",{className:"hidden w-full md:block md:grow",children:r.jsxs("div",{className:"flex w-full flex-col flex-wrap items-start gap-[1rem] font-iowan font-semibold md:flex-row md:items-center",children:[r.jsx("div",{className:"flex flex-col",children:r.jsx(S,{children:r.jsx(ae,{type:"mapping",labelStyle:"!text-[14px]",className:"!h-[34px] !border !border-primary-black !p-[4px] !px-[7px] !pl-[12px] font-iowan",value:e==null?void 0:e.target,label:"Listen to :",noneText:"Select",onChange:n=>L(n.target.value,t.SELECTION),options:Array.from(x.keys()),mapping:Array.from(x.entries()).reduce((n,[u,C])=>(n[u]=C,n),{})})})}),r.jsxs("div",{className:"flex w-full items-end justify-center gap-[.5rem] md:w-auto md:grow",children:[r.jsxs("div",{className:"flex grow flex-col items-center justify-center gap-[.5rem]",children:[r.jsxs("div",{className:"flex flex-row items-center justify-center gap-[1.5rem]",children:[r.jsx(S,{children:r.jsx(oe,{className:`cursor-pointer ${e!=null&&e.target?"":"opacity-50"}`,onClick:e!=null&&e.target?J:void 0})}),[t.STOP,t.PAUSE].includes(U==null?void 0:U.state)?r.jsx(S,{children:r.jsx(re,{className:`cursor-pointer ${Array.from(x==null?void 0:x.keys()).includes(e==null?void 0:e.target)?"":"opacity-50"}`,onClick:V})}):r.jsx(S,{children:r.jsx(se,{className:"cursor-pointer",onClick:()=>{N(t.PAUSE)}})}),r.jsx(S,{children:r.jsx(ie,{className:`cursor-pointer ${e!=null&&e.target?"":"opacity-50"}`,onClick:e!=null&&e.target?A:void 0})})]}),r.jsxs("div",{className:"grid w-full min-w-full max-w-full grid-cols-[auto_18.75rem_auto] grid-rows-1 items-center justify-center gap-[.5rem]",children:[r.jsx("code",{className:"text-xs text-gray-600 w-fit min-w-fit max-w-fit",children:j(_.currentTime)}),r.jsx("audio",{hidden:!0,ref:v,onTimeUpdate:T,onLoadedMetadata:T,onEnded:B,autoPlay:e==null?void 0:e.autoPlay,preload:"auto",children:r.jsx("source",{src:h==null?void 0:h.src,type:"audio/mpeg"})}),r.jsx("div",{className:"h-[.5rem] w-full overflow-hidden rounded-full bg-[#1F1D1A33]",children:r.jsx("div",{className:"h-full transition-all duration-100 bg-primary-black",style:{width:`${_.progress}%`}})}),r.jsx("code",{className:"text-xs text-gray-600 w-fit min-w-fit max-w-fit",children:j(_.duration)})]})]}),r.jsx("div",{className:"relative",children:r.jsx(ge,{place:"top",openOnClick:!1,display:r.jsxs("div",{className:"flex flex-col items-center justify-center gap-[1.3125rem] ",children:[r.jsx("span",{className:"text-base font-semibold whitespace-nowrap font-iowan",children:"Auto Play"}),r.jsx("div",{className:"relative h-[.75rem] w-[2rem] rounded-full bg-[#D2C6BC] ",children:r.jsx("button",{onClick:w,className:`absolute inset-y-0 m-auto flex h-[1rem] w-[1rem] items-center justify-center rounded-full bg-primary-black ${e!=null&&e.autoPlay?"right-0":""}`,children:e!=null&&e.autoPlay?r.jsx(se,{className:"!h-[50%] !w-[50%]",pathFill:"#FFF0E5"}):r.jsx(re,{className:"!h-[50%] !w-[50%]",pathFill:"#FFF0E5"})})})]}),backgroundColor:"#1f1d1a",tooltipClasses:"!left-[0px] !md:left-[20px] !absolute ",children:r.jsxs("span",{className:"text-xs text-white",children:["Autoplay ",e!=null&&e.autoPlay?"On":"Off"]})})})]})]})}),r.jsxs("div",{className:"fixed bottom-0 left-0 right-0 z-50 flex w-full flex-col border-t-2 border-t-[#F6A03C] bg-[#1f1d1a] p-4 pt-0 text-[#F2DFCE] shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1)] md:hidden",ref:m,style:{transform:`translateY(${f}px)`,transition:o?"none":"transform 0.3s ease-out"},children:[r.jsx("div",{className:"flex flex-col items-center w-full",children:r.jsx("svg",{onTouchStart:W,onTouchMove:Q,className:`mb-4 mt-2 cursor-pointer ${K?"rotate-[0deg]":"rotate-[180deg]"}`,onTouchEnd:X,onClick:()=>z(!K),width:"29",height:"9",viewBox:"0 0 29 9",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{d:"M2 2L14.5 6L27 2",stroke:"#F2DFCE","stroke-width":"4","stroke-linecap":"round"})})}),r.jsxs("div",{className:"flex justify-between items-center",children:[r.jsxs("div",{className:"flex flex-col gap-2",children:[r.jsx("h4",{className:"font-iowan-regular text-[12px] font-[400]",children:"Currently Playing:"}),r.jsx("h2",{className:"font-iowan text-[12px]",children:((Z=Array.from(x.entries()).find(([n])=>n===(e==null?void 0:e.target)))==null?void 0:Z[1])||""})]}),r.jsxs("div",{className:"flex flex-col gap-2",children:[r.jsx("h4",{className:"font-iowan-regular text-[12px] font-[400]",children:"Playing Next:"}),r.jsx("h2",{onClick:d,className:"cursor-pointer font-iowan text-[12px]",children:((D=F==null?void 0:F.nextUpdate)==null?void 0:D.name)??"No New Update"})]})]}),r.jsx(r.Fragment,{children:r.jsxs("div",{className:"mb-[4px] mt-4 flex items-center justify-center gap-8",children:[r.jsx(S,{children:r.jsx(oe,{pathFill:"#F2DFCE",className:`cursor-pointer ${e!=null&&e.target?"":"opacity-50"}`,onClick:e!=null&&e.target?J:void 0})}),[t.STOP,t.PAUSE].includes(U==null?void 0:U.state)?r.jsx(S,{children:r.jsx(re,{fill:"#F2DFCE",pathFill:"#F2DFCE",className:`cursor-pointer text-[#F2DFCE] ${Array.from(x==null?void 0:x.keys()).includes(e==null?void 0:e.target)?"":"opacity-50"}`,onClick:V})}):r.jsx(S,{children:r.jsx(se,{fill:"#F2DFCE",pathFill:"#F2DFCE",className:"text-white cursor-pointer",onClick:()=>{N(t.PAUSE)}})}),r.jsx(S,{children:r.jsx(ie,{fill:"#F2DFCE",pathFill:"#F2DFCE",className:`cursor-pointer text-white ${e!=null&&e.target?"":"opacity-50"}`,onClick:e!=null&&e.target?A:void 0})})]})}),r.jsxs("div",{className:"mb-[2px] flex w-full items-center justify-center gap-2",children:[r.jsx("audio",{hidden:!0,ref:v,onTimeUpdate:T,onLoadedMetadata:T,onEnded:B,autoPlay:e==null?void 0:e.autoPlay,preload:"auto",children:r.jsx("source",{src:h==null?void 0:h.src,type:"audio/mpeg"})}),r.jsx("div",{className:"h-[.5rem] w-full overflow-hidden rounded-full bg-[#F2DFCE]",children:r.jsx("div",{className:"h-full bg-[#F6A03C] transition-all duration-100",style:{width:`${_.progress}%`}})})]}),r.jsxs("div",{className:"flex justify-between items-center mb-2",children:[r.jsx("code",{className:"w-fit min-w-fit max-w-fit font-iowan text-xs text-[#F2DFCE]",children:j(_.currentTime)}),r.jsx("code",{className:"w-fit min-w-fit max-w-fit font-iowan text-xs text-[#F2DFCE]",children:j(_.duration)})]}),K&&r.jsxs("div",{className:"mb-3 mt-4 flex w-full flex-row-reverse items-center justify-center gap-[140px] px-10 md:mt-0 md:w-auto md:flex-row",children:[r.jsxs("div",{className:"flex-[unset] md:flex-1",children:[r.jsx("div",{className:"relative top-[30px] ml-[-160%] h-[1px] w-[40px] rotate-[90deg] bg-[#F2DFCE]"}),r.jsxs(S,{children:[r.jsx("div",{className:"hidden md:block",children:r.jsx(ae,{type:"mapping",labelStyle:"!text-[14px]",className:"!h-[34px] !w-[151px] !border !border-primary-black !p-[4px] !px-[7px] !pl-[12px] font-iowan md:w-auto",value:e==null?void 0:e.target,label:"Listen to:",noneText:"Select",onChange:n=>L(n.target.value,t.SELECTION),options:Array.from(x.keys()),mapping:Array.from(x.entries()).reduce((n,[u,C])=>(n[u]=C,n),{})})}),r.jsxs("div",{className:"flex flex-col gap-2 items-end md:hidden",children:[r.jsx("h4",{className:"text-base font-semibold whitespace-nowrap font-iowan",children:"Listen to"}),r.jsx("button",{onClick:()=>R(!0),className:"flex gap-2 items-center",children:r.jsx("svg",{width:"22",height:"18",className:"ml-[-30px] h-6 w-6",viewBox:"0 0 22 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{d:"M0.875 2C0.875 1.70163 0.993526 1.41548 1.2045 1.2045C1.41548 0.993526 1.70163 0.875 2 0.875H18.5C18.7984 0.875 19.0845 0.993526 19.2955 1.2045C19.5065 1.41548 19.625 1.70163 19.625 2C19.625 2.29837 19.5065 2.58452 19.2955 2.7955C19.0845 3.00647 18.7984 3.125 18.5 3.125H2C1.70163 3.125 1.41548 3.00647 1.2045 2.7955C0.993526 2.58452 0.875 2.29837 0.875 2ZM2 9.125H12.875C13.1734 9.125 13.4595 9.00647 13.6705 8.7955C13.8815 8.58452 14 8.29837 14 8C14 7.70163 13.8815 7.41548 13.6705 7.20451C13.4595 6.99353 13.1734 6.875 12.875 6.875H2C1.70163 6.875 1.41548 6.99353 1.2045 7.20451C0.993526 7.41548 0.875 7.70163 0.875 8C0.875 8.29837 0.993526 8.58452 1.2045 8.7955C1.41548 9.00647 1.70163 9.125 2 9.125ZM8.375 12.875H2C1.70163 12.875 1.41548 12.9935 1.2045 13.2045C0.993526 13.4155 0.875 13.7016 0.875 14C0.875 14.2984 0.993526 14.5845 1.2045 14.7955C1.41548 15.0065 1.70163 15.125 2 15.125H8.375C8.67337 15.125 8.95952 15.0065 9.1705 14.7955C9.38147 14.5845 9.5 14.2984 9.5 14C9.5 13.7016 9.38147 13.4155 9.1705 13.2045C8.95952 12.9935 8.67337 12.875 8.375 12.875ZM21.8272 7.94844C21.7406 8.23339 21.5445 8.47236 21.2819 8.61295C21.0193 8.75354 20.7118 8.78429 20.4266 8.69844L18.125 8.01219V14C18.125 14.7164 17.8971 15.4143 17.4741 15.9926C17.0512 16.5708 16.4552 16.9995 15.7724 17.2165C15.0896 17.4336 14.3555 17.4277 13.6763 17.1998C12.9971 16.9718 12.408 16.5337 11.9944 15.9487C11.5807 15.3637 11.3639 14.6623 11.3754 13.946C11.3869 13.2296 11.6261 12.5355 12.0583 11.9641C12.4904 11.3927 13.0933 10.9737 13.7794 10.7677C14.4656 10.5616 15.1995 10.5793 15.875 10.8181V6.5C15.8751 6.32463 15.9161 6.15171 15.9949 5.99502C16.0736 5.83834 16.1879 5.70224 16.3287 5.59759C16.4694 5.49294 16.6326 5.42263 16.8053 5.39229C16.9781 5.36194 17.1555 5.3724 17.3234 5.42281L21.0734 6.54781C21.3591 6.63366 21.5989 6.82943 21.7403 7.09208C21.8816 7.35472 21.9129 7.66275 21.8272 7.94844ZM15.875 14C15.875 13.7775 15.809 13.56 15.6854 13.375C15.5618 13.19 15.3861 13.0458 15.1805 12.9606C14.975 12.8755 14.7488 12.8532 14.5305 12.8966C14.3123 12.94 14.1118 13.0472 13.9545 13.2045C13.7972 13.3618 13.69 13.5623 13.6466 13.7805C13.6032 13.9988 13.6255 14.225 13.7106 14.4305C13.7958 14.6361 13.94 14.8118 14.125 14.9354C14.31 15.059 14.5275 15.125 14.75 15.125C15.0484 15.125 15.3345 15.0065 15.5455 14.7955C15.7565 14.5845 15.875 14.2984 15.875 14Z",fill:"#FCF1E6"})})})]})]})]}),r.jsxs("div",{className:"flex flex-col gap-5 items-center ml-4",children:[r.jsx("span",{className:"text-base font-semibold whitespace-nowrap font-iowan",children:"Auto Play"}),r.jsx("div",{className:"relative h-[.75rem] w-[2rem] rounded-full bg-[#F2DFCE]",children:r.jsx("button",{onClick:w,className:`absolute inset-y-0 m-auto flex h-[1rem] w-[1rem] items-center justify-center rounded-full bg-[#F6A03C] ${e!=null&&e.autoPlay?"right-0":""}`,children:e!=null&&e.autoPlay?r.jsx(se,{className:"!h-[50%] !w-[50%]",pathFill:"#FCF1E6"}):r.jsx(re,{className:"!h-[50%] !w-[50%]",pathFill:"#FCF1E6"})})})]})]})]}),r.jsx(S,{children:r.jsx(xe,{clickOutToClose:!0,modalHeader:!1,modalHeaderClassName:"!bg-black text-[#1f1d1a]",isOpen:ne,modalCloseClick:()=>R(!1),classes:{modalDialog:"!bg-black h-fit min-h-fit max-h-fit w-full !max-h-[500px] md:!w-fit !max-w-full min-w-full md:!min-w-[595px] md:!max-w-[595px] !gap-0 !m-0 !mt-auto !rounded-t-2xl",modalContent:"!bg-[#FCF1E6] !text-black !z-10 !mt-0 overflow-y-auto max-h-[500px] !pt-0",modal:"h-full items-end",modal:"!p-0"},children:r.jsxs("div",{className:"py-2 pb-6 md:py-6",children:[r.jsx("div",{onClick:()=>R(!1),className:"flex justify-center mt-1 mb-7",children:r.jsx("div",{className:"h-[6px] w-[60px] rounded-full bg-[#F2DFCE]"})}),r.jsx("div",{className:"flex gap-2 justify-start items-center mb-6",children:r.jsx("span",{className:"flex justify-start items-center text-xl font-iowan",children:"Listen to"})}),r.jsx("div",{className:"space-y-4",children:Array.from(x.entries()).map(([n,u])=>r.jsx("button",{onClick:()=>{L(n,t.SELECTION),R(!1)},className:`w-full rounded-sm border-b border-b-[#1f1d1a]/10 p-4 text-left font-iowan text-[#1f1d1a] transition-colors ${(e==null?void 0:e.target)===n?" border-b-2 font-bold text-[#1f1d1a]":"hover:text-[#1f1d1a]"}`,children:u},n))})]})})})]})};export{Ve as default};
