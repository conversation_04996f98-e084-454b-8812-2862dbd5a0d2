import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as n,b as h,L as m,j as C}from"./vendor-4cdf2bd1.js";import{d as f}from"./index.esm-6fcccbfe.js";import{G as j,A as w}from"./index-f2ad9142.js";import{d as u}from"./index.esm-54e24cf9.js";import{U as x,A as b}from"./UserIcon-63b89263.js";import{o as l}from"./@headlessui/react-cdd9213e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-icons-36ae72b7.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const G=()=>{var o;const{state:t,dispatch:d}=n.useContext(j),{state:r,dispatch:c}=n.useContext(w),i=h();let a=s=>{d({type:"OPEN_SIDEBAR",payload:{isOpen:s}})};const p=[{to:"/collaborator/dashboard",text:"Dashboard",icon:e.jsx(f,{className:"text-xl text-[#A8A8A8]"}),value:"dashboard"},{to:"/collaborator/update_requests",text:"Updates",icon:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M13.492 1.66666H6.50866C3.47533 1.66666 1.66699 3.475 1.66699 6.50833V13.4833C1.66699 16.525 3.47533 18.3333 6.50866 18.3333H13.4837C16.517 18.3333 18.3253 16.525 18.3253 13.4917V6.50833C18.3337 3.475 16.5253 1.66666 13.492 1.66666ZM4.37533 9.90833C4.40033 8.44166 4.98366 7.05833 6.01699 6.025C7.08366 4.95833 8.50033 4.375 10.0003 4.375C11.5003 4.375 12.917 4.95833 13.9753 6.025C14.0003 6.05 14.0253 6.08333 14.0503 6.11666V5.4C14.0503 5.05833 14.3337 4.775 14.6753 4.775C15.017 4.775 15.3003 5.05833 15.3003 5.4V7.60833C15.3003 7.95 15.017 8.23333 14.6753 8.23333H12.467C12.1253 8.23333 11.842 7.95 11.842 7.60833C11.842 7.26666 12.1253 6.98333 12.467 6.98333H13.1587C13.1337 6.95833 13.117 6.93333 13.092 6.90833C12.267 6.08333 11.167 5.625 10.0003 5.625C8.83366 5.625 7.73366 6.08333 6.90866 6.90833C6.10033 7.71666 5.65033 8.79166 5.63366 9.93333C5.62533 10.2667 5.34199 10.5417 5.00033 10.5417H4.99199C4.65033 10.5417 4.37533 10.25 4.37533 9.90833ZM13.9753 13.975C12.917 15.0333 11.5003 15.625 10.0003 15.625C8.50033 15.625 7.08366 15.0417 6.02533 13.975C6.00033 13.95 5.97533 13.9167 5.95033 13.8833V14.5917C5.95033 14.9333 5.66699 15.2167 5.32533 15.2167C4.98366 15.2167 4.70033 14.9333 4.70033 14.5917V12.3833C4.70033 12.0417 4.98366 11.7583 5.32533 11.7583H7.53366C7.87533 11.7583 8.15866 12.0417 8.15866 12.3833C8.15866 12.725 7.87533 13.0083 7.53366 13.0083H6.84199C6.86699 13.0333 6.88366 13.0583 6.90866 13.0833C7.73366 13.9083 8.83366 14.3667 10.0003 14.3667C11.167 14.3667 12.267 13.9083 13.092 13.0833C13.9087 12.2667 14.367 11.175 14.367 10.0083C14.367 9.66666 14.6503 9.38333 14.992 9.38333C15.3337 9.38333 15.617 9.66666 15.617 10.0083C15.617 11.5167 15.0337 12.925 13.9753 13.975Z",fill:"#A8A8A8","fill-opacity":`${t.path==="updates","1"}`})}),value:"update_requests"},{to:"/collaborator/companies",text:"Companies",icon:e.jsx(u,{className:"text-xl text-[#A8A8A8]"}),value:"companies"}];return console.log(t),e.jsx("div",{className:`z-[10] flex flex-col border border-[#E0E0E0] bg-[#1F1D1A] text-[#A8A8A8] transition-all ${t.isOpen?"absolute left-0 top-0 h-full w-[220px] sm:min-w-[16rem] sm:max-w-[16rem] lg:relative":"relative h-full w-[4.1rem] bg-[#1f1d1a] text-white sm:min-w-[5rem] sm:max-w-[4.2rem]"} `,children:e.jsxs("div",{className:"relative flex h-screen max-h-full min-h-full min-w-full max-w-full flex-col overflow-y-clip",children:[e.jsxs("div",{className:`text-[#393939] ${t.isOpen?"flex w-full":"flex items-center justify-center"} `,children:[!t.isOpen&&e.jsx("div",{className:" absolute -right-3 top-7 z-[5] cursor-pointer",children:e.jsx("div",{onClick:()=>a(!t.isOpen),children:e.jsx("span",{children:e.jsxs("svg",{width:"25",height:"25",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"19",height:"19",rx:"9.5",fill:"#1F1D1A"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"19",height:"19",rx:"9.5",stroke:"#FFF0E5"}),e.jsx("path",{d:"M8.66602 6.66667L11.9993 10L8.66602 13.3333",stroke:"#FFF0E5","stroke-width":"1.33333","stroke-linecap":"round","stroke-linejoin":"round"})]})})})}),e.jsx("div",{}),e.jsxs("div",{className:"my-3 flex w-full flex-row items-center justify-between px-5 text-xl font-bold",children:[e.jsx(m,{to:"",className:"block text-center text-xl font-extrabold text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[!t.isOpen&&e.jsxs("svg",{className:"ml-[-10px] mt-[13px]",width:"39",height:"24",viewBox:"0 0 39 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M33.8839 2.36687V17.8149H5.04853V5.44459H10.8325H19.4661H31.2166V2.36537L2.40017 2.35202V2.36863H1.93164V20.9075H37.0008V2.36863L33.8839 2.36687Z",fill:"#FEF1E5"}),e.jsx("path",{d:"M7.71582 12.6606V15.7531H31.2168V7.50627H19.4663H10.8327H7.71582V10.5989H28.1V12.6606H7.71582Z",fill:"#FEF1E5"})]}),t.isOpen&&e.jsxs("svg",{onClick:()=>window.location.href="https://updatestack.com",width:"176",height:"24",viewBox:"0 0 176 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M33.4072 2.36685V17.8149H4.77729V5.44457H10.5201H19.0922H30.7589V2.36535L2.14781 2.35201V2.36862H1.68262V20.9074H36.5018V2.36862L33.4072 2.36685Z",fill:"#FEF1E5"}),e.jsx("path",{d:"M7.4248 12.6606V15.7531H30.7583V7.50628H19.0916H10.5195H7.4248V10.5989H27.6637V12.6606H7.4248Z",fill:"#FEF1E5"}),e.jsx("path",{d:"M43.1436 12.1232V3.4893H46.1213V11.9849C46.1213 13.7143 47.2403 14.8097 48.824 14.8097C50.4038 14.8097 51.5232 13.7143 51.5232 11.9849V3.4893H54.501V12.1232C54.501 15.3686 52.0712 17.6359 48.824 17.6359C45.5734 17.6359 43.1436 15.3686 43.1436 12.1232Z",fill:"#FEF1E5"}),e.jsx("path",{d:"M62.8727 6.35143C66.0662 6.35143 68.428 8.7572 68.428 11.9936C68.428 15.2264 66.0662 17.6521 62.8727 17.6521C61.7746 17.6521 60.7776 17.3259 59.9559 16.7529V21.1834H57.0889V6.65372H58.9847L59.4742 7.62467C60.3748 6.82121 61.5505 6.35187 62.8731 6.35187L62.8727 6.35143ZM65.5252 11.9932C65.5252 10.316 64.3042 9.06629 62.6386 9.06629C60.9734 9.06629 59.7484 10.3196 59.7484 11.9932C59.7484 13.6673 60.9734 14.9203 62.6386 14.9203C64.3042 14.9203 65.5252 13.6708 65.5252 11.9932Z",fill:"#FEF1E5"}),e.jsx("path",{d:"M81.1364 2.80436V17.3338H79.0191L78.6804 16.4447C77.7903 17.2074 76.6447 17.6526 75.3596 17.6526C72.1425 17.6526 69.7842 15.2268 69.7842 11.9941C69.7842 8.75764 72.1425 6.35187 75.3596 6.35187C76.4546 6.35187 77.4485 6.67216 78.2694 7.23656V2.80436H81.1364ZM78.4834 11.9941C78.4834 10.32 77.2589 9.06695 75.5933 9.06695C73.9276 9.06695 72.7066 10.3165 72.7066 11.9941C72.7066 13.6717 73.9276 14.9212 75.5933 14.9212C77.2589 14.9212 78.4834 13.6642 78.4834 11.9941Z",fill:"#FEF1E5"}),e.jsx("path",{d:"M94.6879 6.63771V17.318H92.4403L92.194 16.3622C91.2834 17.1652 90.0955 17.6359 88.7577 17.6359C85.568 17.6359 83.1953 15.2106 83.1953 11.9779C83.1953 8.75766 85.568 6.35189 88.7577 6.35189C90.119 6.35189 91.3248 6.83506 92.2415 7.65806L92.548 6.63771H94.6879ZM91.878 11.9779C91.878 10.3003 90.657 9.05072 88.9913 9.05072C87.3261 9.05072 86.1012 10.3038 86.1012 11.9779C86.1012 13.652 87.3261 14.905 88.9913 14.905C90.657 14.905 91.878 13.6555 91.878 11.9779Z",fill:"#FEF1E5"}),e.jsx("path",{d:"M103.927 14.7628V17.3338H101.89C99.557 17.3338 98.1237 15.8902 98.1237 13.5202V8.97521H96.2061V8.3511L100.394 3.86688H100.942V6.65374H103.868V8.97521H100.991V13.1374C100.991 14.1692 101.58 14.7628 102.624 14.7628L103.927 14.7628Z",fill:"#FEF1E5"}),e.jsx("path",{d:"M116.024 12.8028H107.998C108.248 14.2605 109.199 15.0631 110.59 15.0631C111.586 15.0631 112.404 14.5885 112.839 13.8241H115.852C115.081 16.1524 113.051 17.6359 110.59 17.6359C107.459 17.6359 105.09 15.1943 105.09 11.9937C105.09 8.77721 107.443 6.35168 110.59 6.35168C113.846 6.35168 116.079 8.87973 116.079 11.9559C116.079 12.2382 116.06 12.5205 116.024 12.8028ZM108.074 10.8306H113.213C112.807 9.52491 111.876 8.81673 110.59 8.81673C109.313 8.81673 108.407 9.56662 108.074 10.8306Z",fill:"#FEF1E5"}),e.jsx("path",{d:"M117.544 12.8476H120.541C120.541 14.1854 121.639 14.8312 122.855 14.8312C123.974 14.8312 125.068 14.2359 125.068 13.2037C125.068 12.1307 123.815 11.8356 122.325 11.4853C120.252 10.9711 117.735 10.3648 117.735 7.38213C117.735 4.72654 119.677 3.23048 122.721 3.23048C125.88 3.23048 127.676 4.9296 127.676 7.68771H124.738C124.738 6.49569 123.761 5.93656 122.649 5.93656C121.686 5.93656 120.709 6.34817 120.709 7.25458C120.709 8.22883 121.903 8.52387 123.358 8.87445C125.454 9.40856 128.085 10.0667 128.085 13.1681C128.085 16.1581 125.72 17.6091 122.874 17.6091C119.718 17.6091 117.544 15.8235 117.544 12.8476Z",fill:"#FEF1E5"}),e.jsx("path",{d:"M136.621 14.7628V17.3338H134.584C132.251 17.3338 130.818 15.8902 130.818 13.5202V8.97521H128.9V8.3511L133.089 3.86688H133.637V6.65374H136.563V8.97521H133.685V13.1374C133.685 14.1692 134.274 14.7628 135.318 14.7628L136.621 14.7628Z",fill:"#FEF1E5"}),e.jsx("path",{d:"M149.275 6.63771V17.318H147.027L146.781 16.3622C145.87 17.1652 144.682 17.6359 143.345 17.6359C140.155 17.6359 137.782 15.2106 137.782 11.9779C137.782 8.75766 140.155 6.35189 143.345 6.35189C144.706 6.35189 145.912 6.83506 146.828 7.65806L147.135 6.63771H149.275ZM146.465 11.9779C146.465 10.3003 145.244 9.05072 143.578 9.05072C141.913 9.05072 140.688 10.3038 140.688 11.9779C140.688 13.652 141.913 14.905 143.578 14.905C145.244 14.905 146.465 13.6555 146.465 11.9779Z",fill:"#FEF1E5"}),e.jsx("path",{d:"M156.961 17.6355C153.771 17.6355 151.331 15.1614 151.331 11.9612C151.331 8.76076 153.755 6.35148 156.978 6.35148C159.723 6.35148 161.855 8.1046 162.37 10.7332H159.523C159.059 9.68342 158.104 9.06656 156.961 9.06656C155.406 9.06656 154.237 10.3236 154.237 11.9775C154.237 13.6318 155.426 14.9208 156.961 14.9208C158.12 14.9208 159.036 14.2719 159.52 13.13H162.406C161.906 15.8147 159.736 17.6359 156.961 17.6359L156.961 17.6355Z",fill:"#FEF1E5"}),e.jsx("path",{d:"M170.821 17.3338L167.428 12.2246V17.3338H164.561V2.80436H167.428V11.3992L170.611 6.65372H173.913L170.326 11.7394L174.295 17.3338H170.821Z",fill:"#FEF1E5"})]})]})}),t.isOpen&&e.jsx("div",{className:"cursor-pointer",children:e.jsx("div",{onClick:()=>a(!t.isOpen),children:e.jsx("span",{children:e.jsxs("svg",{width:"30",height:"30",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"19",height:"19",rx:"9.5",fill:"#1F1D1A"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"19",height:"19",rx:"9.5",stroke:"#FFF0E5"}),e.jsx("path",{d:"M11.334 6.66667L8.00065 10L11.334 13.3333",stroke:"#FFF0E5","stroke-width":"1.33333","stroke-linecap":"round","stroke-linejoin":"round"})]})})})})]})]}),e.jsx("div",{className:"mt-6 h-fit w-auto flex-1",children:e.jsx("div",{className:"sidebar-list2 w-auto",children:e.jsx("ul",{className:"flex flex-wrap px-2 text-sm",children:p.map(s=>e.jsx("li",{className:"block w-full list-none",children:e.jsx(C,{to:s.to,className:`${t.path==s.value?"active-nav":""} `,children:e.jsxs("div",{className:"flex items-center gap-3",children:[s.icon,t.isOpen&&e.jsx("span",{children:s.text})]})})},s.value))})})}),e.jsx("div",{className:"flex justify-end pb-2",children:e.jsxs(l,{as:"div",className:"flex w-full items-center justify-center gap-[5px] border-t border-t-white/10 pb-1 pt-4 sm:gap-6",children:[e.jsxs(l.Button,{className:"flex w-full items-center justify-center gap-[5px]  sm:gap-6",children:[e.jsx("img",{className:`${t.isOpen?"h-10 w-10":"h-5 w-5 xl:h-6 xl:w-6"} rounded-[50%] object-cover`,src:((o=r.profile)==null?void 0:o.photo)??"/default.png",alt:""}),t.isOpen?e.jsx(e.Fragment,{children:e.jsxs("div",{className:"text-left text-white",children:[e.jsxs("p",{className:"w-32 truncate text-sm font-medium",children:[r.profile.first_name," ",r.profile.last_name]}),e.jsx("p",{className:"mt-1 w-[150px] truncate text-xs",children:r.profile.email})]})}):null]}),e.jsx(l.Items,{className:` absolute ${t.isOpen?"-bottom-1 -right-full md:-right-[170px]":"-bottom-1 -right-[170px]"}  mb-8 w-[160px] origin-top-right divide-y divide-gray-100 rounded-md border border-[#1f1d1a] bg-brown-main-bg font-bold text-[#1f1d1a] shadow-lg ring-1 ring-[#1f1d1a] ring-opacity-5 focus:outline-none`,children:e.jsxs("div",{className:"px-1 py-1",children:[e.jsx(l.Item,{children:({active:s})=>e.jsxs("button",{className:`group flex w-full items-center px-3 py-3 text-sm ${s?"border-b border-b-black/20":"border-b border-b-transparent"}`,onClick:()=>{i("/collaborator/account")},children:[e.jsx(x,{className:"mr-2 h-5 w-5"}),"Account"]})}),e.jsx(l.Item,{children:({active:s})=>e.jsxs("button",{className:`group flex w-full items-center px-3 py-3 text-sm ${s?"border-b border-b-black/20":"border-b border-b-transparent"}`,onClick:()=>{i("/collaborator/startup-profile")},children:[e.jsx(x,{className:"mr-2 h-5 w-5"}),"Company"]})}),e.jsx(l.Item,{children:({active:s})=>e.jsxs("button",{className:`group flex w-full items-center px-3 py-3 text-sm ${s?"border-b border-b-black/20":"border-b border-b-transparent"}`,onClick:()=>{c({type:"LOGOUT"}),i("/collaborator/login")},children:[e.jsx(b,{className:"mr-2 h-5 w-5 rotate-90"}),"Logout"]})})]})})]})})]})})};export{G as CollaboratorHeader,G as default};
