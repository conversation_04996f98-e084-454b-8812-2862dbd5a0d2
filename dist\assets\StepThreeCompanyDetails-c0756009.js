import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as l,b as w}from"./vendor-4cdf2bd1.js";import{c as j,a as c}from"./yup-342a5df4.js";import{u as N}from"./react-hook-form-a383372b.js";import{M as S}from"./index-bf8349b8.js";import{o as v}from"./yup-0917e80c.js";import{l as C}from"./logo5-2e16f0f2.js";import{G as k,A as E,u as P,I,m as T,g as q,s as M}from"./index-f2ad9142.js";import{M as u}from"./MkdInput-d37679e9.js";import{u as G}from"./useLocalStorage-46cb237c.js";import{STEPS as U}from"./SignUpStepOneEmail-8cbdc5ad.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const xe=({role:m="member",updateStep:a=null})=>{const{dispatch:i,state:{companyCreation:s}}=l.useContext(k),{dispatch:d}=l.useContext(E);w();const{localStorageData:r,setLocalStorage:f}=G(["step"]),{updateProfile:x}=P({isPublic:!0}),h=j({company_name:c().required("This field is required"),company_website:c()}),{register:n,handleSubmit:y,setError:b,watch:z,formState:{errors:p}}=N({resolver:v(h),defaultValues:{company_name:"",company_website:""}}),g=async o=>{try{const t=await T(i,d,{endpoint:"/v3/api/custom/goodbadugly/users/create-company",method:"POST",payload:{company_name:o==null?void 0:o.company_name,company_website:q(o==null?void 0:o.company_website),role:m}},"companyCreation");t!=null&&t.error||(x({step:4}),f("step",4),a&&a())}catch(t){console.log("Error",t),M(i,t.message,5e3,"error"),b("company_name",{type:"manual",message:t.message})}};return e.jsxs("div",{className:"w-full md:w-[60%] md:px-6",children:[e.jsx("div",{className:"sticky right-0 top-0 z-[9] flex h-[4.5rem] w-full flex-row items-center justify-between bg-[#1f1d1a] px-8 md:hidden",children:e.jsx("img",{src:C,alt:"logo",className:"h-10 w-[180px]"})}),e.jsxs("div",{className:"flex w-full flex-col px-4 py-8 md:px-0",children:[e.jsxs("div",{className:"w-full space-y-[6.25rem] ",children:[e.jsx(S,{steps:U[m],currentStep:r==null?void 0:r.step,onClick:()=>{},className:""}),e.jsxs("div",{children:[e.jsx("h3",{className:" font-iowan text-[1.5rem] font-[700] sm:text-[2.5rem]",children:"Company Info"}),e.jsx("p",{className:"mb-3 font-normal text-black",children:"Update your company Info"})]})]}),e.jsxs("form",{className:"space-y-8",onSubmit:y(g),children:[e.jsx("div",{className:"mt-4",children:e.jsx(u,{type:"text",name:"company_name",label:"Company Name",errors:p,register:n,placeholder:"Enter Company Name",required:!0})}),e.jsx("div",{className:"mt-4",children:e.jsx(u,{type:"text",name:"company_website",label:"Company Website",errors:p,register:n,placeholder:"Enter Company Website"})}),e.jsx(I,{type:"submit",className:"my-4 flex h-[2.75rem] w-full items-center justify-center rounded-sm bg-[#1f1d1a] py-2 tracking-wide text-white outline-none focus:outline-none",loading:s==null?void 0:s.loading,disabled:s==null?void 0:s.loading,children:e.jsx("span",{className:"font-iowan capitalize",children:"Go To UpdateStack"})})]})]})]})};export{xe as default};
