import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{b as T,a as O,O as P,L as u,E as _,d as z,b7 as K,b8 as R}from"./index-f2ad9142.js";import{C as y}from"./index-45ba42b5.js";import{r as m}from"./vendor-4cdf2bd1.js";import{A as q}from"./index-afef2e72.js";import{E as B}from"./lucide-react-0b94883e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const de=()=>{var w,N,E,M,k,F,L,S;const[e,b]=m.useState({modal:null,showModal:!1,card:null}),[f,x]=m.useState({card:!1,deleteCard:!1}),{sdk:C}=T(),{showToast:c,tokenExpireError:v}=O(),l=(o,n,a=[])=>{b(i=>({...i,modal:o,showModal:n,ids:a}))};async function h(){var o,n,a,i,p;try{x(d=>({...d,card:!0}));const{data:s,error:r,message:g}=await C.getCustomerStripeCards();if(console.log(s),r&&c(g,5e3),!s)return;const j=(o=s==null?void 0:s.data)==null?void 0:o.find(d=>{var A;return!!((A=d==null?void 0:d.customer)!=null&&A.default_source)});b(d=>({...d,card:j}))}catch(s){console.error(s);const r=(a=(n=s==null?void 0:s.response)==null?void 0:n.data)!=null&&a.message?(p=(i=s==null?void 0:s.response)==null?void 0:i.data)==null?void 0:p.message:s==null?void 0:s.message;c(r,5e3),v(r)}finally{x(s=>({...s,card:!1}))}}const I=async()=>{var o,n,a,i,p;try{x(j=>({...j,deleteCard:!0}));const s=(o=e==null?void 0:e.card)==null?void 0:o.id;if(!s){c("No card found to delete",5e3,"error");return}const{error:r,message:g}=await C.deleteCustomerStripeCard(s);if(c(g||"Card deleted successfully",5e3,r?"error":"success"),r){console.error(r);return}l(null,!1),h()}catch(s){console.error(s);const r=(a=(n=s==null?void 0:s.response)==null?void 0:n.data)!=null&&a.message?(p=(i=s==null?void 0:s.response)==null?void 0:i.data)==null?void 0:p.message:s==null?void 0:s.message;c(r,5e3,"error"),v(r)}finally{x(s=>({...s,deleteCard:!1}))}};return m.useEffect(()=>{h()},[]),t.jsxs(m.Fragment,{children:[t.jsxs("div",{className:"relative flex h-[9.1875rem] max-h-[9.1875rem] min-h-[9.1875rem] flex-col justify-between gap-[.75rem] rounded-[.25rem] border border-[#1F1D1A] bg-[#FFF0E5] p-4",children:[f!=null&&f.card?t.jsx(P,{color:"black",loading:!0}):null,t.jsx("button",{className:"absolute top-2 right-3 p-1 rounded-full transition-colors peer hover:bg-black/5",children:t.jsx(B,{className:"w-5 h-5 text-gray-600"})}),t.jsxs("div",{className:"absolute right-3 top-[-5000%] flex gap-2 rounded-[.25rem] border border-black bg-brown-main-bg p-2 opacity-0 shadow-lg transition-all hover:top-12 hover:opacity-100 focus:top-12 focus:opacity-100 peer-focus:top-12 peer-focus:opacity-100 peer-focus-visible:top-12 peer-focus-visible:opacity-100",children:[t.jsx("button",{onClick:()=>l("edit",!0),className:"p-2 rounded-lg transition-colors hover:bg-gray-100",title:"Edit",children:t.jsx(u,{children:t.jsx(_,{})})}),t.jsx("button",{onClick:()=>l("delete",!0),className:"p-2 rounded-lg transition-colors hover:bg-gray-100",title:"Delete",children:t.jsx(u,{children:t.jsx(z,{})})})]}),t.jsx("label",{className:"block font-iowan text-[1.25rem] font-[700] leading-[1.5537rem] text-[#1F1D1A]",children:"Payment Method"}),e!=null&&e.card?t.jsxs(m.Fragment,{children:[t.jsx("div",{className:"space-y-[.75rem]  ",children:t.jsxs("div",{className:"flex items-center gap-2 font-iowan  text-[32px] font-[700] leading-[39.78px]",children:[t.jsx(u,{children:t.jsx(y,{name:(N=(w=e==null?void 0:e.card)==null?void 0:w.brand)==null?void 0:N.toLowerCase()})}),(E=e==null?void 0:e.card)==null?void 0:E.last4]})}),t.jsx("span",{className:" font-Inter text-[1rem] font-normal leading-[1.5rem] text-gray-600",children:K((M=e==null?void 0:e.card)==null?void 0:M.exp_month,(k=e==null?void 0:e.card)==null?void 0:k.exp_year)})]}):t.jsxs(m.Fragment,{children:[t.jsx("div",{className:"space-y-[.75rem]  ",children:t.jsxs("div",{className:"flex items-center gap-2 font-iowan  text-[32px] font-[700] leading-[39.78px]",children:[t.jsx(u,{children:t.jsx(y,{name:"visa"})}),"****"]})}),t.jsx("span",{className:" font-Inter text-[1rem] font-normal leading-[1.5rem] text-gray-600"})]})]}),t.jsx(R,{isOpen:(e==null?void 0:e.showModal)&&["edit"].includes(e==null?void 0:e.modal),onClose:()=>l(null,!1),onSuccess:()=>{l(null,!1),h()}}),t.jsx(u,{children:t.jsx(q,{title:"Delete Payment Method",mode:"manual",action:"Delete",multiple:!1,onSuccess:I,inputConfirmation:!1,onClose:()=>l(null,!1),customMessage:t.jsxs("div",{className:"space-y-3",children:[t.jsx("p",{children:"Are you sure you want to delete this payment method?"}),(e==null?void 0:e.card)&&t.jsxs("div",{className:"flex gap-2 items-center font-medium",children:[t.jsx(y,{name:(L=(F=e==null?void 0:e.card)==null?void 0:F.brand)==null?void 0:L.toLowerCase()}),t.jsxs("span",{children:["Card ending in ",(S=e==null?void 0:e.card)==null?void 0:S.last4]})]}),t.jsx("p",{className:"text-sm text-red-500",children:"This action cannot be undone."})]}),isOpen:(e==null?void 0:e.showModal)&&["delete"].includes(e==null?void 0:e.modal)})})]})};export{de as default};
