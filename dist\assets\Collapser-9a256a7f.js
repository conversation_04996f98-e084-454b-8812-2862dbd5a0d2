import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as o}from"./vendor-4cdf2bd1.js";import{bV as b}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const A=({title:m="Title",children:a,expand:l=!1,iconStroke:u="black",titleClasses:c="",onExpandRef:d=null,onCollapseRef:p=null,className:x="border-y border-soft-200",iconPosition:n="left"})=>{const t=o.useRef(null),[i,s]=o.useState(!0),h=o.useCallback(()=>{var r;t.current&&(i?(t.current.style.maxHeight=`${(r=t==null?void 0:t.current)==null?void 0:r.scrollHeight}px`,s(!1)):(t.current.style.maxHeight=null,s(!0)))},[t,i]),f=o.useCallback(()=>{t!=null&&t.current&&(i||(t.current.style.maxHeight=null,s(!0)))},[t,i]),g=o.useCallback(()=>{var r;t!=null&&t.current&&(t.current.style.maxHeight=`${(r=t==null?void 0:t.current)==null?void 0:r.scrollHeight}px`,s(!1))},[t,i]);return o.useEffect(()=>{l&&setTimeout(()=>{var r;t.current.style.maxHeight=`${(r=t==null?void 0:t.current)==null?void 0:r.scrollHeight}px`,s(!1)},1e3)},[l]),e.jsxs("div",{className:`w-full bg-white ${x}`,children:[e.jsx("button",{type:"button",hidden:!0,ref:p,onClick:()=>f()}),e.jsx("button",{type:"button",hidden:!0,ref:d,onClick:()=>g()}),e.jsxs("div",{onClick:h,className:`flex h-[3rem] max-h-[3rem] min-h-[3rem] w-full cursor-pointer items-center justify-between gap-3 self-stretch rounded-md py-[.75rem] pl-[.75rem] font-bold text-black ${c}`,children:[e.jsx("div",{className:`flex ${n==="right"?"order-2":"order-1"}`,children:e.jsx(b,{className:`relative h-[1.125rem] w-[1.125rem] cursor-pointer ${i?"rotate-180":""}`,stroke:u})}),e.jsx("div",{className:`grow font-inter text-sm font-[600] capitalize leading-[1.25rem] ${n==="right"?"order-1":"order-2"}`,children:m})]}),e.jsx("div",{ref:t,className:"max-h-0 overflow-y-hidden font-inter transition-all",children:a})]})};export{A as default};
