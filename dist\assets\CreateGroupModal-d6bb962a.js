import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{A as D,G,I as ee,v as O,t as R,s as A,M as ae,p as se,_ as re,o as le}from"./index-f2ad9142.js";import{r as a,b as oe}from"./vendor-4cdf2bd1.js";import{M as ne}from"./MkdCustomInput-aaf80542.js";import{o as q}from"./yup-0917e80c.js";import{u as z,a as ie}from"./react-hook-form-a383372b.js";import{c as B,a as T,d as ce}from"./yup-342a5df4.js";import{InteractiveButton2 as de}from"./InteractiveButton-060359e0.js";import{X as te}from"./XMarkIcon-cfb26fe7.js";import{t as U,S as $,W as L}from"./@headlessui/react-cdd9213e.js";import"./index-b8adfdf8.js";function me({onSuccess:n,onClose:i}){const{dispatch:c,state:m}=a.useContext(D),{dispatch:b,state:{createModel:r}}=a.useContext(G),l=(r==null?void 0:r.loading)||!1,s=B({group_name:T().required("This field is required")}),{register:x,handleSubmit:d,setError:j,setValue:y,watch:u,formState:{errors:N}}=z({resolver:q(s)});async function C(p){try{const o=await O(b,c,"group",{user_id:m.user,group_name:p==null?void 0:p.group_name,role:"member"});n&&n(o.data)}catch(o){R(c,o.message),A(b,o.message,5e3,"error")}}return e.jsxs("div",{className:"space-y-[1.375rem]",children:[e.jsx("p",{className:"mt-2 font-inter text-lg",children:"Use groups to organize your update recipients"}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block  text-[1rem] font-semibold capitalize leading-[1.5rem] text-[#1f1d1a]",children:"Group name"}),e.jsx(ne,{type:"text",errors:N,name:"group_name",placeholder:"New group name",onChange:p=>{y("group_name",p.target.value)}})]}),e.jsxs("div",{className:"mt-6 flex justify-end gap-4",children:[e.jsx("button",{className:"border border-[#0003] px-3 py-2 text-center font-iowan font-medium ",type:"button",onClick:()=>i(),children:"Cancel"}),e.jsx(ee,{loading:l,disabled:l,onClick:d(C,p=>console.log("error >>",p)),className:"bg-primary-black px-3 py-2 text-center font-semibold text-white transition-colors duration-100 disabled:bg-disabled-black",children:"Next"})]})]})}function ue({title:n,titleId:i,...c},m){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:m,"aria-labelledby":i},c),n?a.createElement("title",{id:i},n):null,a.createElement("path",{fillRule:"evenodd",d:"M10.53 3.47a.75.75 0 0 0-1.06 0L6.22 6.72a.75.75 0 0 0 1.06 1.06L10 5.06l2.72 2.72a.75.75 0 1 0 1.06-1.06l-3.25-3.25Zm-4.31 9.81 3.25 3.25a.75.75 0 0 0 1.06 0l3.25-3.25a.75.75 0 1 0-1.06-1.06L10 14.94l-2.72-2.72a.75.75 0 0 0-1.06 1.06Z",clipRule:"evenodd"}))}const pe=a.forwardRef(ue),fe=pe,xe=[{label:"Manager/Founder",value:"manager"},{label:"Collaborator",value:"collaborator"},{label:"Fund Manager",value:"investor"},{label:"Stakeholder",value:"stakeholder"}];function he({isOpen:n,closeModal:i,collaborator:c=!1,onAdd:m}){const{dispatch:b,state:r}=a.useContext(D),{dispatch:l}=a.useContext(G),[s,x]=a.useState(!1),d=B({first_name:T().required("This field is required"),last_name:T().required("This field is required"),email:T().email().required("This field is required"),role:T().required("This field is required")}),{register:j,handleSubmit:y,formState:{errors:u},reset:N,setValue:C}=z({resolver:q(d)});a.useEffect(()=>{c&&C("role","collaborator")},[c,C]);async function p(o){x(!0);try{const h=await new ae().callRawAPI("/v3/api/custom/goodbadugly/member/create-user",{email:o.email,first_name:o.first_name,last_name:o.last_name,role:o.role,company_id:r.company.id},"POST");i(),N(),A(l,"User added successfully"),m&&m()}catch(k){R(b,k.message),A(l,k.message,5e3,"error")}x(!1)}return e.jsx(U,{appear:!0,show:n,as:a.Fragment,children:e.jsxs($,{as:"div",className:"relative z-[50]",onClose:i,children:[e.jsx(U.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(U.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs($.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx($.Title,{as:"h3",className:"text-xl font-semibold leading-6 text-gray-900",children:"Add New User"}),e.jsx("button",{onClick:i,type:"button",children:e.jsx(te,{className:"h-6 w-6"})})]}),e.jsxs("form",{onSubmit:y(p),className:"mt-4",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold",children:"First Name"}),e.jsx("input",{...j("first_name"),className:"w-full rounded border border-[#1f1d1a] bg-transparent p-2"}),u.first_name&&e.jsx("p",{className:"text-red-500",children:u.first_name.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold",children:"Last Name"}),e.jsx("input",{...j("last_name"),className:"w-full rounded border border-[#1f1d1a] bg-transparent p-2"}),u.last_name&&e.jsx("p",{className:"text-red-500",children:u.last_name.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold",children:"Email"}),e.jsx("input",{...j("email"),className:"w-full rounded border border-[#1f1d1a] bg-transparent p-2",type:"email"}),u.email&&e.jsx("p",{className:"text-red-500",children:u.email.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold",children:"Role"}),e.jsxs("select",{...j("role"),disabled:c,defaultValue:c?"collaborator":"",className:"w-full rounded border border-[#1f1d1a] bg-transparent p-2",children:[e.jsx("option",{value:"",children:"Select a role"}),xe.map(o=>e.jsx("option",{value:o.value,children:o.label},o.value))]}),u.role&&e.jsx("p",{className:"text-red-500",children:u.role.message})]}),e.jsxs("div",{className:"mt-6 flex justify-end gap-4",children:[e.jsx("button",{className:"rounded-md border border-[#0003] px-3 py-2 text-center font-iowan font-medium ",type:"button",onClick:i,children:"Cancel"}),e.jsx(de,{loading:s,disabled:s,type:"submit",className:"disabled:bg-disabledblack rounded-lg bg-primary-black px-3 py-2 text-center font-semibold text-white transition-colors duration-100",children:"Add User"})]})]})]})})})})]})})}function be({control:n,name:i,setValue:c,watch:m,defaultSelectedMembers:b=[]}){var V,W;const[r,l]=a.useState([]),[s,x]=a.useState(""),[d,j]=a.useState([]),{dispatch:y}=a.useContext(G),{dispatch:u,state:N}=a.useContext(D),[C,p]=a.useState(!1),o=b.length>0?b:d;async function k(){try{const t=await se(y,u,"company_member",{filter:[`company_id,eq,${N.company.id}`,`member_status,eq,${re.ACTIVE}`],join:["user|member_id"]});t!=null&&t.error||l(()=>t==null?void 0:t.data)}catch(t){R(u,t.message),A(y,t.message,5e3,"error")}}a.useEffect(()=>{N.company.id&&k()},[N.company.id]);const{field:h,formState:g,fieldState:f}=ie({control:n,name:i});a.useEffect(()=>(h.value&&j(h.value),()=>{j("")}),[g]),console.log("recipients >>",r),console.log("membersSelected >>",o);const S=s===""?r==null?void 0:r.filter(t=>!(d!=null&&d.includes(t.id.toString()))):r==null?void 0:r.filter(t=>!(d!=null&&d.includes(t.id))).filter(t=>{var _,v,E,w,F,I,M,P,X,Y,Z,H,K,Q,J;return((F=(E=(v=(_=t==null?void 0:t.user)==null?void 0:_.email)==null?void 0:v.toLowerCase())==null?void 0:E.replace(/\s+/g,""))==null?void 0:F.includes((w=s==null?void 0:s.toLowerCase())==null?void 0:w.replace(/\s+/g,"")))||((Y=(P=(M=(I=t==null?void 0:t.user)==null?void 0:I.first_name)==null?void 0:M.toLowerCase())==null?void 0:P.replace(/\s+/g,""))==null?void 0:Y.includes((X=s==null?void 0:s.toLowerCase())==null?void 0:X.replace(/\s+/g,"")))||((J=(K=(H=(Z=t==null?void 0:t.user)==null?void 0:Z.last_name)==null?void 0:H.toLowerCase())==null?void 0:K.replace(/\s+/g,""))==null?void 0:J.includes((Q=s==null?void 0:s.toLowerCase())==null?void 0:Q.replace(/\s+/g,"")))});return e.jsxs(e.Fragment,{children:[e.jsxs(L,{value:h.value,onChange:t=>c([...h.value?h.value:[],t]),children:[e.jsxs("div",{className:"relative z-30 mt-3 w-full",children:[e.jsx("label",{className:"mb-1 block  font-iowan text-[16px] font-semibold capitalize text-[#1f1d1a]",children:"Members"}),e.jsxs("div",{className:"w-full",children:[e.jsxs(L.Button,{className:"relative w-full text-left rounded-md cursor-default focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 sm:text-sm",children:[e.jsx(L.Input,{className:`focus:shadow-outline h-[2.6rem] w-full appearance-none rounded-[2px] border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal leading-tight text-[#1f1d1a]   placeholder:text-[14px] placeholder:text-[#1f1d1a] focus:outline-none md:h-[44px] ${(V=f.error)!=null&&V.message?"border-red-500":""}`,placeholder:"Type to search",onChange:t=>x(t.target.value),onBlur:h.onBlur,ref:h.ref,name:h.name,autoComplete:"off"}),e.jsx("div",{className:"flex absolute inset-y-0 right-0 items-center pr-2",children:e.jsx(fe,{className:"h-5 w-5 text-[#1f1d1a]","aria-hidden":"true"})})]}),e.jsx("p",{className:"italic text-red-500 text-field-error",children:(W=f.error)==null?void 0:W.message})]}),e.jsx(U,{as:a.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",afterLeave:()=>x(""),children:e.jsx(L.Options,{className:"custom-overflow absolute mt-1 max-h-96 w-full overflow-auto rounded-md border bg-brown-main-bg py-1 text-base shadow-lg ring-1 ring-[#1f1d1a]/5 focus:outline-none",children:(S==null?void 0:S.length)===0&&s!==""?e.jsx("div",{className:"relative px-4 py-2 text-gray-700 cursor-default select-none",children:"Nothing found."}):e.jsxs(e.Fragment,{children:[S==null?void 0:S.map(t=>{var _;return e.jsx(L.Option,{className:({active:v})=>`relative cursor-default select-none py-2 pl-10 pr-4 ${v?"bg-primary-black text-white":"text-gray-900"}`,value:(_=t==null?void 0:t.user)==null?void 0:_.id,children:({selected:v,active:E})=>{var w;return e.jsx(e.Fragment,{children:e.jsx("span",{className:"block truncate font-medium",children:(w=t==null?void 0:t.user)==null?void 0:w.email})})}},t.id)}),e.jsx("li",{value:"add_new",className:({active:t})=>`relative cursor-pointer select-none py-2 pl-10 pr-4 ${t?"bg-primary-black text-white":"text-gray-900"}`,onClick:()=>p(!0),children:({active:t})=>e.jsx("span",{className:`block truncate font-medium underline ${t?"text-white":"text-primary-black"}`,children:"+ Add New User"})})]})})})]}),e.jsx("div",{className:"flex overflow-y-auto flex-wrap gap-4 mt-2 w-full text-sm custom-overflow",children:r&&(r==null?void 0:r.map((t,_)=>{var v,E;if(o!=null&&o.includes((v=t==null?void 0:t.user)==null?void 0:v.id))return e.jsxs("div",{className:"flex items-center gap-3 rounded-sm border border-[#1f1d1a] bg-brown-main-bg px-3 py-2",children:[e.jsx("span",{children:(E=t==null?void 0:t.user)==null?void 0:E.email}),e.jsx("button",{type:"button",onClick:()=>{const w=[...h.value],F=w.findIndex(I=>{var M;return I===((M=t==null?void 0:t.user)==null?void 0:M.id)});F!==-1&&w.splice(F,1),c(w)},children:e.jsx(te,{className:"h-4",strokeWidth:2})})]},_)}))})]}),e.jsx(he,{isOpen:C,closeModal:()=>p(!1)})]})}function ge({groupId:n,onSuccess:i,onClose:c}){const{dispatch:m,state:b}=a.useContext(D),{dispatch:r,state:{update:l,createModel:s}}=a.useContext(G),x=(s==null?void 0:s.loading)||!1;a.useState([]),oe();const d=B({members:ce().min(1,"You must add at least one member").of(T())}),{register:j,handleSubmit:y,setError:u,watch:N,setValue:C,control:p,formState:{errors:o}}=z({resolver:q(d),defaultValues:{members:[]}}),k=async g=>{console.log(g);try{const f=await O(r,m,"recipient_group",{group_id:n,members:g.members.join(","),user_id:b.user},!1);await Promise.all(g.members.map(S=>O(r,m,"recipient_member",{recipient_group_id:f.data,user_id:S},!1))),l&&(l!=null&&l.id)&&h(n)}catch(f){R(m,f.message),u("group_id",{type:"manual",message:f.message})}},h=async g=>{try{const f=await O(r,m,"update_group",{update_id:l==null?void 0:l.id,group_id:g,type:1},!1);f!=null&&f.error||i&&i()}catch(f){R(m,f.message),u("group_id",{type:"manual",message:f.message})}};return e.jsxs("div",{className:"space-y-[1.375rem]",children:[e.jsx("p",{className:"mt-2 font-inter text-lg",children:"Use groups to organize your update recipients"}),e.jsxs("div",{className:"z-30",children:["Select One or Multiple",e.jsx(be,{control:p,name:"members",setValue:g=>C("members",g)})]}),e.jsxs("div",{className:"mt-6 flex justify-end gap-4",children:[e.jsx("button",{className:"border border-[#0003] px-3 py-2 text-center font-iowan font-medium ",type:"button",onClick:()=>c(),children:"Cancel"}),e.jsx(ee,{loading:x,disabled:x,onClick:y(k,g=>console.log("error >>",g)),className:"bg-primary-black px-3 py-2 text-center font-semibold text-white transition-colors duration-100 disabled:bg-disabled-black",children:"Create + Add Members"})]})]})}const je={1:"Create New Group",2:"Add Members To New Group"};function Me({afterCreate:n,buttonRef:i,type:c="update"}){a.useContext(D),a.useContext(G);const[m,b]=a.useState(0),[r,l]=a.useState(!1),[s,x]=a.useState(1);return e.jsx(le,{isOpen:r,modalCloseClick:()=>l(!1),modalHeader:!0,title:je[s],classes:{modal:"w-full",modalDialog:"!w-[100%] md:!w-[40%]",modalContent:""},children:e.jsxs("div",{className:"relative",children:[e.jsx("button",{type:"button",hidden:!0,ref:i,onClick:()=>l(!0)}),s==1?e.jsx(me,{onSuccess:d=>{b(d),c==="update"?x(2):(l(!1),n&&n())},onClose:()=>{l(!1)}}):null,s==2?e.jsx(ge,{groupId:m,onSuccess:d=>{l(!1),x(1),n()},onClose:()=>{l(!1),x(1)}}):null]})})}export{Me as C,be as S,fe as a};
