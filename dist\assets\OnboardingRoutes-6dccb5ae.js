import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{G as E,A,M as D,t as I,s as v,y as L,u as re,w as oe}from"./index-f2ad9142.js";import{r as t,R as $,u as ie,b as ne}from"./vendor-4cdf2bd1.js";import{c as X,a as J,b as q}from"./yup-342a5df4.js";import{u as W}from"./react-hook-form-a383372b.js";import{o as K}from"./yup-0917e80c.js";import"./MkdCustomInput-aaf80542.js";import{u as le}from"./useRecipientGroups-30bdbf7a.js";import{InteractiveButton2 as Z}from"./InteractiveButton-060359e0.js";import{L as ce}from"./index-b8adfdf8.js";import"./react-addons-update-ccb093e5.js";import"./index-713720be.js";import"./index-dc002f62.js";import{h as M}from"./moment-a9aaa855.js";import"./useDate-c1da5729.js";import{C as de}from"./index-c523e7e9.js";import{X as Q}from"./XMarkIcon-cfb26fe7.js";import{t as _,S as O}from"./@headlessui/react-cdd9213e.js";import{g as me}from"./react-audio-voice-recorder-a95781ec.js";import{S as pe,M as ue}from"./MicrophoneIcon-ed3ea0f8.js";import{u as fe}from"./useNote-ea33f376.js";import"./lodash-82bd9112.js";import{C as xe}from"./Collaborators-6025a01b.js";import{I as he}from"./index.esm-7add6cfb.js";import be from"./SubscriptionOnboarding-44dcfd0a.js";import{X as ge}from"./XMarkIcon-6ed09631.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./react-spinners-b860a5a3.js";import"./react-quill-a78e6fc7.js";import"./useUpdateCollaborators-677ec5ee.js";import"./useCompanyMember-0033d2de.js";import"./PlusIcon-26cedb5d.js";import"./ChevronUpDownIcon-e0f342e0.js";import"./react-icons-36ae72b7.js";function we({updateStep:i=null}){var z,Y,U;const{dispatch:x}=t.useContext(E),{dispatch:a,state:r}=t.useContext(A),[o,s]=t.useState(!1),n=X({email:J().required("This field is required").test("is-valid-emails","Some emails are invalid",f=>f?f.split(",").map(T=>T.trim()).every(T=>/^\S+@\S+\.\S+$/.test(T)):!1)}),l=new D,{register:h,handleSubmit:g,setError:N,setValue:b,formState:{errors:p,isSubmitting:H},control:S,clearErrors:m,watch:V,reset:c}=W({resolver:K(n),defaultValues:{first_name:"",last_name:"",group_id:"",email:"",role:"investor"}}),[d,u]=t.useState(""),{groups:k,refetch:F,loading:y}=le(r==null?void 0:r.user),[w,C]=t.useState(""),[ve,ee]=t.useState({members:[]});let R=k.find(f=>(f==null?void 0:f.group_name)==w)||null;const te=async f=>{var T;s(!0);const P=f.email.split(",").map(j=>j.trim());try{for(const j of P){if(!/^\S+@\S+\.\S+$/.test(j)){v(x,`${j} is not a valid email`,5e3,"error");continue}await l.callRawAPI(`/v3/api/custom/goodbadugly/member/${r.company.id}/invite-to-team`,{email:j,role:"collaborator"},"POST")}s(!1),c(),v(x,"Invite sent"),x({type:"SET_ONBOARDING",payload:2}),F()}catch(j){s(!1),I(a,j.message),v(x,(j==null?void 0:j.message)||((T=j==null?void 0:j.mailResult)==null?void 0:T.message)||"Something went wrong",5e3,"error")}},G=V(["role","group_id"]),[B,se]=t.useMemo(()=>G,[G]);t.useEffect(()=>{u(B)},[B]),t.useEffect(()=>{m(["group_id"])},[se]),t.useEffect(()=>{(async function(){if(R){const f=await ae();ee(f),b("members",f.members)}else b("members",[])})()},[R,w]);async function ae(){try{let f=new D;f.setTable("recipient_group");const P=await f.callRawAPI(`/v4/api/records/recipient_group/${R.id}`,void 0,"GET");f.setTable("recipient_member");const{list:T}=await f.callRawAPI(`/v4/api/records/recipient_member?filter=recipient_group_id,eq,${R.id}`);return{group_id:P.model.group_id,members:T.map(j=>String(j.user_id))}}catch(f){return I(a,f.message),{group_id:"",members:[]}}}return e.jsxs("div",{className:"min-h-[12.5rem] w-full space-y-5",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg md:text-xl",children:"Next, add team members to your account"}),e.jsx("h5",{className:"font-iowan text-sm font-bold",children:"Invite team members to collaborate on Updatestack"})]}),e.jsx("p",{className:"font-iowan-regular font-semibold italic",children:"Add team members by email, if adding multiple team members separate each email by a comma"}),e.jsxs("form",{onSubmit:g(te),className:"mt-2 grid grid-cols-[1fr_auto] flex-wrap items-start gap-4",children:[e.jsxs("div",{className:"grid grid-cols-1",children:[e.jsx("input",{type:"text",autoComplete:"off",...h("email"),className:`focus:shadow-outline h-[2.75rem] w-full appearance-none rounded border border-[#1f1d1a] bg-white px-3 py-2 text-sm leading-tight text-[#1d1f1a] shadow focus:outline-none ${(z=p.email)!=null&&z.message?"border-red-500":""}`,placeholder:"<EMAIL>,<EMAIL>"}),((Y=p==null?void 0:p.email)==null?void 0:Y.message)&&e.jsx("p",{className:"text-red-700",children:(U=p==null?void 0:p.email)==null?void 0:U.message})]}),e.jsx("div",{className:"",children:e.jsx(Z,{loading:o,disabled:o,type:"submit",className:"h-[2.75rem] w-[7.9375rem] min-w-[7.9375rem] whitespace-nowrap rounded-md bg-primary-black/90 px-5 py-2 text-center text-lg font-semibold text-white transition-colors duration-100 disabled:bg-disabled-black",children:"Send Invite"})})]})]})}function je({note_id:i,afterDelete:x}){const[a,r]=t.useState(!1),{dispatch:o}=t.useContext(A),{dispatch:s}=t.useContext(E),[n,l]=t.useState(!1);async function h(){l(!0);try{await new D().callRawAPI(`/v4/api/records/notes/${i}`,{},"DELETE"),r(!1),x()}catch(g){I(o,g.message),v(s,g.message,5e3,"error")}l(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"h-[41px] w-[70px] rounded border-[1px] border-[#1f1d1a] bg-brown-main-bg px-4 py-2 font-iowan text-[14px] font-medium text-[#1f1d1a] sm:w-[80px] md:text-[14px]",onClick:()=>r(!0),children:"Delete"}),e.jsx(_,{appear:!0,show:a,as:t.Fragment,children:e.jsxs(O,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>r(!1),children:[e.jsx(_.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"overflow-y-auto fixed inset-0",children:e.jsx("div",{className:"flex justify-center items-center p-4 min-h-full text-center",children:e.jsx(_.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(O.Panel,{className:"overflow-hidden p-6 w-full text-base text-left align-middle rounded-md shadow-xl transition-all transform bg-brown-main-bg sm:max-w-md",children:[e.jsxs("div",{className:"flex gap-2 justify-between items-center",children:[e.jsx(O.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900 font-iowan",children:"Are you sure you want to delete this entry?"}),e.jsx("button",{onClick:()=>r(!1),type:"button",children:e.jsx(Q,{className:"w-6 h-6"})})]}),e.jsx("p",{className:"mt-2",children:"This action cannot be undone."}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mt-6",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>r(!1),children:"Cancel"}),e.jsx(Z,{loading:n,disabled:n,onClick:h,className:"rounded-lg bg-[#1f1d1a] py-2 text-center font-iowan font-semibold text-white transition-colors duration-100 disabled:bg-opacity-60",children:"Yes, delete"})]})]})})})})]})})]})}function ye({note:i,afterAsking:x}){const[a,r]=t.useState(!1),{dispatch:o}=t.useContext(A),{dispatch:s}=t.useContext(E),[n,l]=t.useState(""),h=X({expandOrShorten:J().nullable().optional(),rephrase:q(),correctGrammar:q()}),{register:g,handleSubmit:N,formState:{isSubmitting:b},reset:p,setValue:H}=W({resolver:K(h),defaultValues:{expandOrShorten:"",rephrase:!1,correctGrammar:!1}}),S=c=>{n===c?(l(""),H("expandOrShorten","")):(l(c),H("expandOrShorten",c))};function m(c){return!c||!c.blocks?"":c.blocks.map(d=>d.type==="header"?`${d.data.text}`:d.type==="list"?d.data.items.map(u=>`• ${u}`).join(`
`):d.type==="paragraph"?d.data.text:"").join(`

`)}async function V(c){var d;try{let u=m(L(i.content,{blocks:[]}));const k=new D;if(c.expandOrShorten||c.correctGrammar||c.rephrase){const y=`
        ${c.expandOrShorten?`${c.expandOrShorten} text. 
`:""}
        ${c.rephrase?`Rewrite text. 
`:""}
        ${c.correctGrammar?`Fix grammar. 
`:""}
            ${u}
        `.trim(),w=[{role:"system",content:"You are an expert auditor auditing for a startup. create and update for the investor do not explain"},{role:"user",content:y}],C=await k.callRawAPI("/v3/api/custom/goodbadugly/ai/ask",{temperature:1,prompt:w},"POST");u=(d=C==null?void 0:C.data)==null?void 0:d.content,console.log("goomba",C==null?void 0:C.data),await k.callRawAPI(`/v4/api/records/notes/${i.id}`,{content:JSON.stringify({time:Date.now(),blocks:[{id:Math.floor(Math.random()*999)+1,type:"list",data:{style:"unordered",items:[u]}}]})},"PUT")}x(),r(!1),p({expandOrShorten:"",rephrase:!1,correctGrammar:!1})}catch(u){I(o,u.message),v(s,u.message,5e3,"error")}}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"text-bla font h-[41px] w-[70px] whitespace-nowrap rounded border-[1px] border-[#1f1d1a] bg-[#1f1d1a] px-2 py-2 text-[14px] font-medium text-white sm:w-[80px] sm:px-4 sm:text-[14px] md:text-sm",onClick:()=>{r(!0),x()},children:"Ask AI"}),e.jsx(_,{appear:!0,show:a,as:t.Fragment,children:e.jsxs(O,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>r(!1),children:[e.jsx(_.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(_.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(O.Panel,{className:"w-full max-w-[400px] transform overflow-hidden rounded-md border border-[#1f1d1a] bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",as:"form",onSubmit:N(V),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(O.Title,{as:"h3",className:"text-xl font-bold leading-6 text-gray-900",children:"Ask AI"}),e.jsx("button",{onClick:()=>r(!1),type:"button",children:e.jsx(Q,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-4 space-y-2",children:[e.jsxs("div",{className:"bold flex items-center gap-4 font-iowan text-lg",children:[e.jsx("input",{type:"checkbox",checked:n==="expand",onChange:()=>S("expand"),className:"h-4 w-4 border-[2px] border-[#1f1d1a] bg-brown-main-bg checked:bg-[#1f1d1a]"}),e.jsx("label",{children:"Expand"})]}),e.jsxs("div",{className:"bold flex items-center gap-4 font-iowan text-lg",children:[e.jsx("input",{type:"checkbox",checked:n==="shorten",onChange:()=>S("shorten"),disabled:n==="expand",className:"h-4 w-4 border-[2px] border-[#1f1d1a] bg-brown-main-bg checked:bg-[#1f1d1a] disabled:opacity-50"}),e.jsx("label",{children:"Shorten"})]})]}),e.jsxs("div",{className:"bold mt-4 flex items-center gap-4 font-iowan text-lg",children:[e.jsx("input",{type:"checkbox",...g("rephrase"),className:"border-[2px] border-[#1f1d1a] bg-brown-main-bg"}),e.jsx("label",{children:"Rephrase"})]}),e.jsxs("div",{className:"bold flex items-center gap-4 font-iowan text-lg",children:[e.jsx("input",{type:"checkbox",...g("correctGrammar"),className:"border-[2px] border-[#1f1d1a] bg-brown-main-bg"}),e.jsx("label",{children:"Correct Grammar"})]}),e.jsx(Z,{type:"submit",loading:b,disabled:b,className:"mt-6 w-full rounded bg-[#1f1d1a] px-4 py-2 font-bold text-white",children:"Send request"})]})})})})]})})]})}function Ce({note:i,afterEdit:x}){const[a,r]=t.useState(!1),{dispatch:o}=t.useContext(A),{dispatch:s}=t.useContext(E),[n,l]=t.useState(!1),[h,g]=t.useState(null),[N,b]=t.useState(!1),{startRecording:p,stopRecording:H,recordingBlob:S,isRecording:m}=me();t.useEffect(()=>{S&&(console.log("setting blob"),g(S))},[S]),t.useEffect(()=>{!h||!n||V()},[h,n]);async function V(){r(!0);try{let d=new FormData;const u=new File([h],"audio.wav",{type:"audio/wav"});d.append("file",u),console.log("f",h);const F=await new D().callTranscribe("/v3/api/custom/goodbadugly/ai/transcribe-audio",d,"POST");console.log(F);const y=L(i.content,{blocks:[]});c(JSON.stringify({time:Date.now(),blocks:[...y.blocks,{id:Math.floor(Math.random()*999)+1,type:"list",data:{style:"unordered",items:[F.data]}}]}))}catch(d){I(o,d.message),v(s,d.message,5e3,"error")}r(!1),l(!1),g(null)}async function c(d){b(!0);try{await new D().callRawAPI(`/v4/api/records/notes/${i.id}`,{content:d},"PUT"),x()}catch(u){I(o,u.message),v(s,u.message,5e3,"error")}b(!1)}return e.jsx("button",{onClick:async()=>{m?(H(),l(!0)):p()},disabled:a||N,className:`focus:shadow-outline ${m?"animate-pulse border-red-500":"border-[#1f1d1a]"} rounded border-[1px]  px-4 py-2`,title:"Transcribe more",children:m?e.jsx("div",{className:"h-6 w-6 rounded-[50%]  bg-red-500",children:e.jsx(pe,{className:"h-6 w-6 text-white"})}):e.jsx(ue,{className:"h-6 w-6 text-[#1f1d1a]",strokeWidth:2})})}function Se({note:i,afterEdit:x}){const{dispatch:a}=t.useContext(A),{dispatch:r}=t.useContext(E),[o,s]=t.useState(!1),[n,l]=t.useState(""),[h,g]=t.useState(null);t.useEffect(()=>{l(i.type)},[i]);async function N(b){s(!0);try{await new D().callRawAPI(`/v4/api/records/notes/${i.id}`,{type:b},"PUT"),x(),v(r,"Saved")}catch(p){I(a,p.message),v(r,p.message,5e3,"error")}s(!1)}return e.jsx(e.Fragment,{children:e.jsx("input",{className:`no-box-shadow w-full border-none ${n==="Section title"?"bg-brown-main-bg":"bg-transparent"}  p-0 text-xl font-semibold ring-transparent`,value:n,autoFocus:n==="Section title",readOnly:n==="Section title"?!1:o,onChange:b=>{l(b.target.value),h&&clearTimeout(h);const p=setTimeout(()=>N(b.target.value),2e3);g(p)}})})}t.memo(({note:i,refetch:x,setEdited:a})=>{const[r,o]=t.useState(!1),{note:s,arrayData:n,refetch:l}=fe(i.id,i);t.useContext(A);const h=L(s.content,{blocks:[{id:"zbGZFPM-iI",type:"paragraph",data:{text:""}}]});t.useEffect(()=>{i&&l()},[i]);const g=()=>{o(!0),setTimeout(()=>o(!1),2e3)};return e.jsx("div",{className:"flex flex-col justify-between gap-4",children:e.jsxs("div",{className:"flex flex-col justify-between",children:[e.jsxs("div",{className:"flex w-full flex-col items-start justify-between sm:flex-row sm:items-center",children:[e.jsxs("div",{className:"group mt-5 flex flex-col",children:[e.jsx(Se,{note:s,afterEdit:l}),e.jsxs("p",{className:"flex flex-row items-center gap-2 font-medium",children:[r&&e.jsx(he,{color:"green",size:20}),Math.abs(M(s.update_at).diff(M(),"hours"))<24?M(s.update_at).format("hh:mm a"):Math.abs(M(s.update_at).diff(M(),"years"))>0?M(s.update_at).format("MM/DD/YYYY"):M(s.update_at).format("DD MMM hh:mm a")]})]}),e.jsx(xe,{note_id:s.id})]}),e.jsx("div",{className:"mt-4 w-full space-y-4 self-end",children:e.jsxs("div",{className:"",children:[" ",console.log(s),e.jsx(de,{data:h,note_id:s.id,editorID:`editorjs-container-${s.id}`,afterEdit:l,setUpdated:o,updateSaved:g}),e.jsx("div",{className:"mt-4 flex items-center justify-end",children:e.jsxs("div",{className:"flex items-center gap-2 sm:gap-4",children:[e.jsx(Ce,{note:s,afterEdit:l}),e.jsx(je,{note_id:s.id,afterDelete:x}),e.jsx(ye,{note:s,afterAsking:l})]})})]})})]})})});function vt(){$.useState(1);const{dispatch:i,state:x}=t.useContext(E),{onboardingSteps:a}=x;t.useState(!0);const[r,o]=t.useState(!1),{dispatch:s,state:{subStatus:n}}=t.useContext(E),{dispatch:l,state:h}=t.useContext(A),[g,N]=t.useState(!1),{state:b}=ie(),[p,H]=$.useState(!!(b!=null&&b.first_login)),S=t.useRef(!1),{profile:m,getProfile:V}=re();t.useEffect(()=>(S.current=!0,()=>{S.current=!1}),[]);const c=ne(),d=new D,u=(y,w)=>{i({type:"SET_ONBOARDING",payload:y})},k=async y=>{o(!0);try{const w=await oe(i,l,"user",Number(localStorage.getItem("user")),{step:y},!1);w!=null&&w.error||(i({type:"SET_ONBOARDING",payload:y}),V(),o(!1))}catch{o(!1)}};async function F(){var y;o(!0);try{const C=await new D().getCustomerStripeSubscription();console.log("PROFILE?.STEP >>",m==null?void 0:m.step),N(!!((y=C==null?void 0:C.customer)!=null&&y.subId)),u(m==null?void 0:m.step,"fetchSubStatus"),o(!1)}catch(w){o(!1),console.error(w)}}return t.useEffect(()=>{m!=null&&m.id&&F()},[m==null?void 0:m.id]),t.useEffect(()=>{n&&N(!0)},[n]),$.useEffect(()=>{const y=async w=>{try{o(!0),d.setTable("user");const C=await d.callRestAPI({id:localStorage.getItem("user"),is_onboarded:w},"PUT");localStorage.removeItem("update_id"),c("/member/updates"),o(!1)}catch(C){o(!1),console.log(C)}};a>1&&y(1)},[a]),r||[-1].includes(a)?e.jsx(ce,{}):e.jsxs("div",{className:"pb-10 mx-auto w-full min-h-screen bg-brown-main-bg",children:[p?e.jsxs("div",{className:"flex items-center justify-between bg-[#aaeed4] px-4 py-5",children:[e.jsx("span",{}),e.jsxs("p",{className:"text-lg font-medium text-[#01633d]",children:[e.jsx("span",{className:"font-semibold",children:"Success! "})," Your email has been successfully verified"]}),e.jsx("button",{onClick:()=>H(!1),children:e.jsx(ge,{className:"h-4 w-4 text-[#01633d]",strokeWidth:5})})]}):null,e.jsxs("div",{className:"mx-auto w-full space-y-5 px-5 pt-10 sm:px-10 lg:px-[3.125rem] xl:max-w-[62.5rem] 2xl:max-w-[87.5rem]",children:[e.jsxs("svg",{onClick:()=>window.location.href="https://updatestack.com",className:"h-[32px] w-[180px] cursor-pointer sm:h-auto sm:w-auto",width:"294",height:"40",viewBox:"0 0 294 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M56.011 3.94476V29.6915H8.29451V9.07431H17.8659H32.1526H51.5972V3.94226L3.91203 3.92002V3.94771H3.13672V34.8457H61.1688V3.94771L56.011 3.94476Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M12.707 21.101V26.2552H51.5962V12.5105H32.1516H17.8648H12.707V17.6648H46.4386V21.101H12.707Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M72.2383 20.2053V5.81549H77.2012V19.9748C77.2012 22.8572 79.0662 24.6829 81.7057 24.6829C84.3387 24.6829 86.2044 22.8572 86.2044 19.9748V5.81549H91.1674V20.2053C91.1674 25.6144 87.1177 29.3931 81.7057 29.3931C76.288 29.3931 72.2383 25.6144 72.2383 20.2053Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M105.12 10.5858C110.443 10.5858 114.379 14.5954 114.379 19.9895C114.379 25.3773 110.443 29.4202 105.12 29.4202C103.29 29.4202 101.628 28.8765 100.259 27.9216V35.3057H95.4805V11.0896H98.6402L99.4561 12.7078C100.957 11.3687 102.917 10.5865 105.121 10.5865L105.12 10.5858ZM109.541 19.9887C109.541 17.1934 107.506 15.1105 104.73 15.1105C101.955 15.1105 99.913 17.1993 99.913 19.9887C99.913 22.7789 101.955 24.8673 104.73 24.8673C107.506 24.8673 109.541 22.7847 109.541 19.9887Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M135.561 4.67397V28.8897H132.032L131.468 27.4079C129.984 28.679 128.075 29.421 125.933 29.421C120.571 29.421 116.641 25.378 116.641 19.9902C116.641 14.5961 120.571 10.5865 125.933 10.5865C127.758 10.5865 129.415 11.1203 130.783 12.061V4.67397H135.561ZM131.139 19.9902C131.139 17.2 129.099 15.1116 126.322 15.1116C123.546 15.1116 121.511 17.1942 121.511 19.9902C121.511 22.7862 123.546 24.8687 126.322 24.8687C129.098 24.8687 131.139 22.7738 131.139 19.9902Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M158.147 11.0629V28.8634H154.401L153.99 27.2703C152.472 28.6087 150.492 29.3931 148.263 29.3931C142.947 29.3931 138.992 25.351 138.992 19.9631C138.992 14.5961 142.947 10.5865 148.263 10.5865C150.532 10.5865 152.541 11.3918 154.069 12.7634L154.58 11.0629H158.147ZM153.463 19.9631C153.463 17.1671 151.428 15.0845 148.652 15.0845C145.877 15.0845 143.835 17.173 143.835 19.9631C143.835 22.7533 145.877 24.8417 148.652 24.8417C151.428 24.8417 153.463 22.7591 153.463 19.9631Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M173.544 24.6046V28.8897H170.149C166.261 28.8897 163.872 26.4837 163.872 22.5337V14.9587H160.676V13.9185L167.656 6.44479H168.57V11.0896H173.446V14.9587H168.65V21.8957C168.65 23.6153 169.632 24.6046 171.373 24.6046L173.544 24.6046Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M193.704 21.3381H180.328C180.743 23.7675 182.329 25.1051 184.647 25.1051C186.307 25.1051 187.67 24.3141 188.396 23.0401H193.418C192.133 26.9206 188.75 29.3931 184.647 29.3931C179.428 29.3931 175.48 25.3239 175.48 19.9895C175.48 14.6287 179.402 10.5861 184.647 10.5861C190.074 10.5861 193.796 14.7995 193.796 19.9265C193.796 20.397 193.764 20.8675 193.704 21.3381ZM180.454 18.0511H189.019C188.343 15.8748 186.791 14.6945 184.647 14.6945C182.52 14.6945 181.009 15.9443 180.454 18.0511Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M196.238 21.4127H201.233C201.233 23.6423 203.063 24.7187 205.09 24.7187C206.955 24.7187 208.779 23.7265 208.779 22.0061C208.779 20.2178 206.69 19.726 204.206 19.1421C200.752 18.2852 196.557 17.2747 196.557 12.3035C196.557 7.87756 199.793 5.38412 204.867 5.38412C210.133 5.38412 213.126 8.21599 213.126 12.8128H208.228C208.228 10.8261 206.601 9.89425 204.747 9.89425C203.141 9.89425 201.514 10.5803 201.514 12.091C201.514 13.7147 203.504 14.2064 205.929 14.7907C209.421 15.6809 213.807 16.7778 213.807 21.9469C213.807 26.9301 209.865 29.3485 205.122 29.3485C199.862 29.3485 196.238 26.3725 196.238 21.4127Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M228.032 24.6046V28.8897H224.637C220.749 28.8897 218.36 26.4837 218.36 22.5337V14.9587H215.164V13.9185L222.145 6.44479H223.058V11.0896H227.934V14.9587H223.139V21.8957C223.139 23.6153 224.12 24.6046 225.861 24.6046L228.032 24.6046Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M249.123 11.0629V28.8634H245.377L244.967 27.2703C243.449 28.6087 241.469 29.3931 239.239 29.3931C233.923 29.3931 229.969 25.351 229.969 19.9631C229.969 14.5961 233.923 10.5865 239.239 10.5865C241.508 10.5865 243.518 11.3918 245.046 12.7634L245.557 11.0629H249.123ZM244.44 19.9631C244.44 17.1671 242.405 15.0845 239.629 15.0845C236.853 15.0845 234.812 17.173 234.812 19.9631C234.812 22.7533 236.853 24.8417 239.629 24.8417C242.405 24.8417 244.44 22.7591 244.44 19.9631Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M261.935 29.3924C256.618 29.3924 252.551 25.269 252.551 19.9353C252.551 14.6012 256.591 10.5858 261.962 10.5858C266.538 10.5858 270.09 13.5076 270.95 17.8886H266.205C265.431 16.139 263.839 15.1109 261.935 15.1109C259.343 15.1109 257.394 17.2059 257.394 19.9624C257.394 22.7196 259.376 24.868 261.935 24.868C263.866 24.868 265.392 23.7865 266.199 21.8832H271.009C270.177 26.3579 266.558 29.3931 261.935 29.3931L261.935 29.3924Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M285.032 28.8897L279.376 20.3743V28.8897H274.598V4.67397H279.376V18.9987L284.681 11.0896H290.185L284.207 19.5658L290.822 28.8897H285.032Z",fill:"#1F1D1A"})]}),e.jsxs("div",{className:"flex justify-between items-center mt-10 w-full",children:[e.jsx("h3",{className:"text-3xl font-semibold capitalize xl:text-3xl",children:h.company.name}),e.jsxs("div",{className:"flex gap-3 items-center text-base font-semibold",children:[e.jsx("div",{disabled:a!==0,className:` flex h-7 w-7 items-center  justify-center rounded-full  border  transition-all duration-200 ${a===0?" border-black/30 bg-black text-white ":a>0?"border-green-700 bg-green-700 text-white":" bg-transparent"}`,children:"1"}),e.jsx("div",{disabled:a!==1,className:` flex h-7 w-7 items-center  justify-center rounded-full  border border-black/30 transition-all duration-200 ${a===1?" border-black/30 bg-black text-white ":a>1?"border-green-700 bg-green-700 text-white":" bg-transparent"}`,children:"2"})]})]}),e.jsxs("div",{className:"mt-10 h-full min-h-fit",children:[a===0?e.jsx(be,{updateStep:()=>k(1),trial:"Try FREE For 30 days"}):null,a===1?e.jsx(we,{updateStep:()=>k(2)}):null]}),e.jsx("div",{className:"grid grid-cols-[auto] items-center justify-end gap-3",children:a===1?e.jsxs("span",{className:"cursor-pointer w-fit",onClick:()=>{S.current?k(a+1):v(s,"Retry in a moment.Preparing update...",5e3,"warning")},children:[e.jsx("span",{className:"text-sm font-medium whitespace-nowrap md:text-base",children:"Skip, "}),a==1?"add team members later →":""]}):null})]})]})}export{vt as default};
