import{A as d,G as m,M as g,t as f,s as h}from"./index-f2ad9142.js";import{r as t}from"./vendor-4cdf2bd1.js";function G(){const[r,o]=t.useState(!1),[n,c]=t.useState([]),{dispatch:u,state:s}=t.useContext(d),{dispatch:i}=t.useContext(m);async function p(){var a;console.log(s),o(!0);try{const e=new g,l=(a=s==null?void 0:s.company)!=null&&a.user_id?await e.callRawAPI(`/v3/api/custom/goodbadugly/member/get-recipient-groups?user_id=${s.company.user_id}&limit=1000`,void 0,"GET"):[];c(l.list)}catch(e){f(u,e.message),h(i,e.message,5e3,"error")}o(!1)}return t.useEffect(()=>{p()},[s.company.user_id]),{loading:r,groups:n}}export{G as u};
