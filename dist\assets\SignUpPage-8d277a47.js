import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{u as h,b as E,r as c}from"./vendor-4cdf2bd1.js";import{a as v,b as L,c as o,d as N,e as w}from"./index-5c103618.js";import{u as y,Z as x,bE as A,L as d,bF as R,ax as r}from"./index-f2ad9142.js";import{u as T}from"./useLocalStorage-46cb237c.js";import{L as O}from"./index-b8adfdf8.js";import{b as g}from"./index.esm-6fcccbfe.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-icons-36ae72b7.js";const S=({role:n="member"})=>{const m=h(),p=E(),{profile:i,getProfile:j}=y({isPublic:!0}),{localStorageData:s,setLocalStorage:u,removeFromLocalStorage:b}=T(["token","user","role","step","verified"]),f=t=>{u("step",t),[r.COLLABORATOR,r.STAKEHOLDER].includes(n)&&[3].includes(t)&&p(`/${x[n]}/update_requests`)};return c.useLayoutEffect(()=>{if(["/fundmanager/sign-up","/member/sign-up"].includes(m==null?void 0:m.pathname))return u("step",1);!(s!=null&&s.step)&&(s!=null&&s.token)&&(s!=null&&s.user)&&j()},[s==null?void 0:s.token]),c.useLayoutEffect(()=>{i!=null&&i.is_onboarded&&p(`/${x[n]}/dashboard`),i!=null&&i.step&&u("step",i==null?void 0:i.step)},[i==null?void 0:i.id,i==null?void 0:i.step,i==null?void 0:i.is_onboarded]),c.useLayoutEffect(()=>{!["/fundmanager/sign-up","/member/sign-up"].includes(m==null?void 0:m.pathname)&&(s==null?void 0:s.step)==1&&p(`/${x[n]}/sign-up`)},[s==null?void 0:s.step]),e.jsxs("div",{className:`grid min-h-screen grid-cols-1  bg-brown-main-bg  md:pb-0 ${(s==null?void 0:s.step)>1&&(s!=null&&s.verified)?"grid-rows-[auto_1fr]":"grid-rows-1"}`,children:[(s==null?void 0:s.step)>1&&(s!=null&&s.verified)?e.jsxs("div",{className:"flex h-[5rem] min-h-[5rem] w-full items-center justify-between gap-[1rem] bg-[#E4FFA7] px-5 text-[#6A930A]",children:[e.jsxs("div",{className:"flex items-center justify-center gap-[1rem] md:grow",children:[e.jsx("div",{className:" flex h-[1.2rem] w-[1.2rem] items-center justify-center  rounded-full border  border-[#6A930A]  bg-[#6A930A] text-white transition-all duration-200 ",children:e.jsx(A,{className:"h-[.625rem] w-[.75rem]"})}),e.jsx("span",{children:"Success! Your email has been successfully verified"})]}),e.jsx("button",{onClick:()=>{b("verified")},children:e.jsx(g,{className:"text-xl"})})]}):null,e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-[.5fr_1fr]",children:[e.jsx(d,{children:e.jsx(v,{accountType:R[n]})}),e.jsxs("div",{className:"flex justify-center bg-brown-main-bg",children:[s!=null&&s.step?null:e.jsx(O,{className:"m-auto"}),(s==null?void 0:s.step)==1?e.jsx(d,{count:9,counts:[1,1,1,2,3,1,1,1,1],children:e.jsx(L,{role:n,updateStep:()=>f(2)})}):null,(s==null?void 0:s.step)==2?e.jsx(d,{count:9,counts:[1,1,1,2,3,1,1,1,1],children:e.jsx(o,{role:n,updateStep:t=>f(t)})}):null,[r.MEMBER,r.INVESTOR].includes(n)&&(s==null?void 0:s.step)==3?e.jsx(d,{count:9,counts:[1,1,1,2,3,1,1,1,1],children:e.jsx(N,{role:n,updateStep:()=>{u("step",4),u("verified",!1)}})}):null,[r.MEMBER,r.INVESTOR].includes(n)&&(s==null?void 0:s.step)==4?e.jsx(d,{count:9,counts:[1,1,1,2,3,1,1,1,1],children:e.jsx(w,{role:n,updateStep:()=>u("step",5)})}):null]})]})]})};export{S as default};
