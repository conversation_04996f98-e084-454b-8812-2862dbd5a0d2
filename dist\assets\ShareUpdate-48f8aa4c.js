import{j as i}from"./@nextui-org/listbox-0f38ca19.js";import{i as r}from"./vendor-4cdf2bd1.js";import{S as t}from"./ShareButton-6a607afd.js";import{L as l}from"./LockOpenIcon-34dcf8cd.js";import"./@nextui-org/theme-345a09ed.js";import"./InteractiveButton-060359e0.js";import"./index-dc002f62.js";import"./qr-scanner-cf010ec4.js";import"./react-spinners-b860a5a3.js";import"./index-f2ad9142.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./yup-0917e80c.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-hook-form-a383372b.js";import"./index.esm-3e7472af.js";import"./react-icons-36ae72b7.js";import"./yup-342a5df4.js";const o=(s,e)=>e?!1:!!((s==null?void 0:s.private_link_access)==2&&(s!=null&&s.sent_at)),W=({update:s=null})=>{const{update_id:e,public_link_id:m}=r();return i.jsxs("div",{className:"flex h-[2.25rem] max-h-[2.25rem] min-h-[2.25rem] w-full flex-row items-start justify-end gap-3 sm:w-auto md:justify-normal ",children:[(s==null?void 0:s.private_link_access)==1&&i.jsxs(i.Fragment,{children:[i.jsxs("div",{className:"hidden h-full items-center gap-2 whitespace-nowrap rounded bg-[#1f1d1a] px-2 py-3 font-iowan text-sm font-medium text-white sm:px-4 sm:text-base xl:flex",children:[i.jsx("img",{src:"/assets/lock.svg",alt:"logo",className:"h-4 min-h-[16px] w-4 min-w-[16px]"}),"Private Update"]}),i.jsx("div",{className:"flex h-full items-center gap-2 whitespace-nowrap rounded bg-[#1f1d1a] p-2 xl:hidden",children:i.jsx("img",{src:"/assets/lock.svg",alt:"logo",className:"h-4 min-h-[16px] w-4 min-w-[16px]"})})]}),(s==null?void 0:s.private_link_access)==2&&i.jsxs("div",{className:"flex h-full items-center gap-2 whitespace-nowrap rounded bg-[#1f1d1a] px-2 py-2 font-iowan text-sm  font-medium text-white sm:px-4 sm:text-base",children:[i.jsx(l,{className:"h-4 min-h-[16px] w-4 min-w-[16px]",strokeWidth:2}),i.jsx("span",{className:"hidden xl:block",children:" Public Update"})]}),(s==null?void 0:s.private_link_access)==0&&i.jsxs(i.Fragment,{children:[i.jsxs("div",{className:"hidden h-full items-center gap-2 whitespace-nowrap rounded  bg-[#1f1d1a] px-2 py-2 font-iowan text-sm font-medium text-white sm:px-4 sm:text-base xl:flex",children:[i.jsx("img",{src:"/assets/lock.svg",alt:"logo",className:"h-4 min-h-[16px] w-4 min-w-[16px]"}),"Private Update"]}),i.jsx("div",{className:"flex h-full items-center gap-2 whitespace-nowrap rounded bg-[#1f1d1a] p-2  xl:hidden",children:i.jsx("img",{src:"/assets/lock.svg",alt:"logo",className:"h-4 min-h-[16px] w-4 min-w-[16px]"})})]}),i.jsx("div",{className:"hidden md:block",children:o(s,m)?i.jsx(t,{update_id:s==null?void 0:s.id}):null})]})};export{W as default};
