import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{r as s,R}from"./vendor-4cdf2bd1.js";import{A as F,a as B,u as W,M as I,bs as $,bt as O,bu as q}from"./index-f2ad9142.js";import{g as z}from"./react-audio-voice-recorder-a95781ec.js";import{b as H}from"./lucide-react-0b94883e.js";import{L as C,t as K}from"./@headlessui/react-cdd9213e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const D=()=>{const g=s.useRef(null),h=s.useRef(!0),a=s.useRef(0),v=s.useRef(0),d=s.useRef(null),[x,p]=s.useState({isSpeaking:!1,error:null,audioProgress:0,audioDuration:0}),u=s.useCallback(()=>{if(d.current&&(d.current(),d.current=null),g.current){const t=g.current;t.pause(),t.src="",t.load(),g.current=null}a.current=0,v.current=0},[]),w=s.useCallback(()=>{try{u(),p(t=>({...t,isSpeaking:!1,audioProgress:0,audioDuration:0,error:null}))}catch(t){console.error("Error stopping audio:",t)}},[u]),o=s.useCallback(()=>{if(!h.current||!g.current)return;const t=g.current,e=t.currentTime/t.duration*100;Math.abs(a.current-e)>1&&(a.current=e,p(n=>({...n,audioProgress:e})))},[]),m=s.useCallback(()=>{h.current&&(a.current=0,p(t=>({...t,isSpeaking:!1,audioProgress:0,error:null})),u())},[u]),l=s.useCallback(t=>{var e;console.error("Audio playback error:",((e=t==null?void 0:t.target)==null?void 0:e.error)||t),u(),p(n=>({...n,error:"Failed to play audio",isSpeaking:!1,audioProgress:0,audioDuration:0}))},[u]),y=s.useCallback(t=>{const e=()=>o(),n=()=>m(),c=f=>l(f);return t.addEventListener("timeupdate",e),t.addEventListener("ended",n),t.addEventListener("error",c),()=>{t.removeEventListener("timeupdate",e),t.removeEventListener("ended",n),t.removeEventListener("error",c)}},[o,m,l]),j=s.useCallback(async t=>{try{w(),p(n=>({...n,isSpeaking:!0,audioProgress:0,error:null}));const e=new Audio;if(e.preload="auto",g.current=e,d.current=y(e),e.src=t,await e.load(),await new Promise((n,c)=>{const f=()=>{e.removeEventListener("loadedmetadata",f),e.removeEventListener("error",k),n()},k=P=>{e.removeEventListener("loadedmetadata",f),e.removeEventListener("error",k),c(P)};e.addEventListener("loadedmetadata",f),e.addEventListener("error",k)}),!h.current){w();return}v.current=e.duration,p(n=>({...n,audioDuration:e.duration}));try{await e.play()}catch(n){if(n.name==="AbortError")return;throw n}}catch(e){throw console.error("Audio Playback Error:",e),l(e),e}},[w,y,l]);return s.useEffect(()=>()=>{h.current=!1,u()},[u]),{state:x,playAudioFromUrl:j,stopSpeaking:w}},V=()=>{var L,N;s.useContext(F);const{custom:g,showToast:h}=B(),{profile:a}=W(),v=(N=(L=a==null?void 0:a.companies)==null?void 0:L.find(i=>i==null?void 0:i.default_company))==null?void 0:N.name,{state:d,stopSpeaking:x,playAudioFromUrl:p}=D(),u=s.useRef(!0),[w,o]=s.useState({isListening:!1,isProcessing:!1,isSpeaking:!1,transcript:"",aiResponse:"",error:null,audioProgress:0,audioDuration:0}),[m,l]=s.useState(null),[y,j]=s.useState(!1),{startRecording:t,stopRecording:e,recordingBlob:n,isRecording:c}=z();s.useEffect(()=>{n&&(l(n),j(!0))},[n]),s.useEffect(()=>{!m||!y||f()},[m,y]);const f=async()=>{try{o(b=>({...b,isProcessing:!0}));let i=new FormData;const S=new File([m],"audio.wav",{type:"audio/wav"});i.append("file",S);const A=new I,E=await A.callTranscribe("/v3/api/custom/goodbadugly/ai/transcribe-audio",i,"POST");if(E!=null&&E.data){const b=await A.callRawAPI("/v3/api/custom/goodbadugly/integrations/ai/chat",{message:E.data,company:v||""},"POST");if(b&&!b.error){const{response:M,audioUrl:T}=b.data;o(U=>({...U,transcript:E.data,aiResponse:M,isProcessing:!1})),T&&await p(T)}else throw new Error((b==null?void 0:b.error)||"Failed to get AI response")}}catch(i){console.error("Error processing voice:",i),o(S=>({...S,error:i.message||"Failed to process voice",isProcessing:!1})),h(i.message,5e3,"error")}finally{l(null),j(!1)}},k=s.useCallback(()=>{c?(e(),o(i=>({...i,isListening:!1,isProcessing:!0}))):(x(),o(i=>({...i,transcript:"",aiResponse:"",error:null,isListening:!0,isProcessing:!1})),t())},[c,x,t,e]),P=s.useCallback(()=>{o({isListening:!1,isProcessing:!1,isSpeaking:!1,transcript:"",aiResponse:"",error:null,audioProgress:0,audioDuration:0}),x(),c&&e()},[x,c,e]);s.useEffect(()=>{o(i=>({...i,isSpeaking:d.isSpeaking,error:d.error||i.error,audioProgress:d.audioProgress,audioDuration:d.audioDuration}))},[d]),s.useEffect(()=>()=>{u.current=!1,x(),c&&e()},[c,e]);const _=s.useCallback(()=>{c&&e(),o(i=>({...i,isListening:!1,isProcessing:!1,error:null}))},[c,e]);return{state:w,toggleListening:k,clearConversation:P,abortRequests:_,isRecording:c}},pe=({onClose:g,onSuccess:h})=>{const{state:a,toggleListening:v,abortRequests:d}=V(),{playAudioFromUrl:x,stopSpeaking:p}=D(),{state:u,dispatch:w}=R.useContext(F),{profile:o}=u;console.log(o,"profile");const m=s.useRef(!1),l=s.useRef(null),[y,j]=R.useState(!1);console.log("Render cause - props:",{onClose:g,onSuccess:h}),console.log("Render cause - auth state:",u),console.log("Render cause - state:",a),R.useEffect(()=>{(async()=>{var c;if(!m.current)try{const f=new I,k=`Welcome, ${o==null?void 0:o.first_name}! I'm UpdateAI — your go-to knowledge base for everything about your company, product, and teams. Ask me anything, and I'll help you stay informed and up to speed. What's on your mind?`,P=await f.callRawAPI("/v3/api/custom/goodbadugly/integrations/polly/synthesize",{text:k},"POST");!P.error&&((c=P.data)!=null&&c.audioUrl)&&(l.current=setTimeout(()=>{m.current||(x(P.data.audioUrl).then(()=>{j(!0)}),m.current=!0)},500))}catch(f){console.error("Error playing welcome message:",f)}})()},[o==null?void 0:o.first_name]),R.useEffect(()=>()=>{p(),d(),l.current&&clearTimeout(l.current),m.current=!1},[]);const t=()=>{p(),m.current=!0,j(!1),l.current&&(clearTimeout(l.current),l.current=null),v()},e=()=>a.isListening?r.jsx($,{className:"animate-pulse"}):a.isProcessing?r.jsx(O,{className:"animate-pulse"}):r.jsxs("div",{className:"",children:[r.jsx(q,{className:"hidden md:block"}),r.jsx("img",{src:"/assets/microphone.svg",alt:"",className:"md:hidden"})]});return r.jsx(s.Fragment,{children:r.jsxs("div",{className:"overflow-y-auto pb-5 w-full bg-black text-brown-main-bg md:py-5 md:pb-10",children:[r.jsx("div",{className:"flex flex-col justify-center items-center w-full md:hidden",children:r.jsxs("div",{className:"flex gap-2 justify-start items-center md:hidden",children:[r.jsx("img",{src:"/updateai.svg",alt:"",className:"object-cover w-8 h-8"}),r.jsxs("span",{className:"flex justify-start items-center",children:[r.jsx("b",{children:"Update"}),r.jsx("span",{className:"!font-thin",children:"AI"})]})]})}),r.jsx("div",{className:"mt-[24px] text-center font-iowan text-[20px] font-[700] leading-[1.8rem] md:mt-0 md:text-left md:text-[1.5rem]",children:!a.isListening&&!a.isProcessing?`Hello ${o==null?void 0:o.first_name}, how can I help you today?`:""}),r.jsxs("div",{className:"relative mt-[2.5rem] flex w-full flex-col items-center justify-center gap-4 p-[10px] md:mt-[3.7rem]",children:[r.jsx(C,{className:"relative",children:({open:n})=>r.jsxs(r.Fragment,{children:[r.jsx("button",{onClick:t,disabled:a.isSpeaking||a.isProcessing,className:`relative h-[220px] max-h-[220px] min-h-[220px] min-w-[220px] max-w-[220px] rounded-full transition-all duration-300 md:h-[20rem] md:max-h-[20rem] md:min-h-[20rem] md:w-[20rem] md:min-w-[20rem] md:max-w-[20rem] 
                    ${a.isListening?"scale-110":"hover:scale-105"}
                    ${a.isSpeaking||a.isProcessing?"cursor-not-allowed opacity-50":"cursor-pointer"}`,children:r.jsx("div",{className:`absolute inset-0 rounded-full bg-gradient-to-r from-[#B1398B] via-[#FDBE16] to-[#22A386] p-[2px] \r
                    shadow-[0_0_10px_rgba(177,57,139,0.5),0_0_15px_rgba(253,190,22,0.6),0_0_20px_rgba(34,163,134,0.5)]\r
                    before:absolute before:inset-0 before:rounded-full before:bg-gradient-to-r before:from-[#B1398B] before:via-[#FDBE16] before:to-[#22A386] before:opacity-50 before:blur-md`,children:r.jsx("div",{className:"relative flex h-full w-full items-center justify-center rounded-full bg-brown-main-bg pb-[12px]",children:e()})})}),y&&!a.isListening&&!a.isProcessing&&!a.isSpeaking&&r.jsx(K,{show:!0,as:s.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:r.jsx(C.Panel,{static:!0,className:"absolute left-1/2 top-0 z-10 -mt-4 w-screen max-w-[200px] -translate-x-1/2 transform px-4 text-sm",children:r.jsxs("div",{className:"relative rounded-lg bg-[#1f1d1a] px-4 py-3 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:[r.jsx("div",{className:"font-medium",children:"Tap the microphone to get started!"}),r.jsx("div",{className:"absolute left-1/2 top-full h-3 w-3 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-[#1f1d1a]"})]})})})]})}),a.isListening&&r.jsx("div",{className:"absolute inset-0 left-[30px] top-[50px]  hidden h-8 w-8 items-center justify-center rounded-full md:left-[100px]",onClick:n=>{n.stopPropagation(),v()},children:r.jsx(H,{className:"h-8 text-red-500 w--8"})}),a.transcript&&r.jsx("div",{className:"text-center text-[1rem] font-medium text-brown-main-bg",children:a.transcript}),a.aiResponse&&r.jsx("div",{className:"text-center text-[1rem] font-medium text-brown-main-bg",children:a.aiResponse}),a.error&&r.jsx("div",{className:"text-center text-[1rem] font-medium text-red-500",children:a.error})]})]})})};export{pe as default};
