import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as m,b as y,h as v}from"./vendor-4cdf2bd1.js";import{M as C,A as S,G as k}from"./index-f2ad9142.js";import{u as E}from"./react-hook-form-a383372b.js";import{o as A}from"./yup-0917e80c.js";import{c as P,a as T}from"./yup-342a5df4.js";import{E as z}from"./ExportButton-eb4cf1f9.js";import{u as D}from"./useCompanies-db909c5f.js";import"./InteractiveButton-060359e0.js";import{R as $}from"./ResponsiveTableWrapperCompanies-80bd517a.js";import d from"./Loader-9c3b30f4.js";import{C as F}from"./ClipboardDocumentIcon-f03b0627.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-b50d6e2a.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./index.esm-7add6cfb.js";import"./react-icons-36ae72b7.js";import"./index-23a711b5.js";let p=new C;const L=[{header:"Logo",accessor:"logo"},{header:"Name",accessor:"name"},{header:"Action",accessor:""}],be=()=>{var n,l;const{dispatch:R,state:x}=m.useContext(S),{dispatch:f}=m.useContext(k),h=y(),[s,i]=v(),{companies:r,loading:a,refetch:V}=D(x.user),u=P({name:T()}),{register:b,handleSubmit:j,setError:G,reset:N,formState:{errors:o}}=E({resolver:A(u),defaultValues:async()=>({name:s.get("name")??""})});async function w(){p.setTable("companies"),await p.exportCSV()}m.useEffect(()=>{f({type:"SETPATH",payload:{path:"companies"}})},[]);function g(t){s.set("name",t.name),s.set("page",1),i(s)}return e.jsx("div",{className:"px-5 pt-8 md:px-8",children:a?e.jsx(d,{}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"rounded bg-brown-main-bg",children:[e.jsxs("div",{className:"item-center mb-3 flex w-full justify-between ",children:[e.jsx("h4",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"Search"}),e.jsx("div",{className:"flex"})]}),e.jsx("form",{onSubmit:j(g),className:"flex w-full flex-col gap-4",children:e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("div",{className:"flex flex-wrap gap-4 sm:items-end",children:e.jsxs("div",{className:"w-full sm:w-[180px]",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Name"}),e.jsx("input",{type:"text",...b("name"),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm text-sm text-sm font-normal font-normal font-normal capitalize leading-tight text-[#1d1f1a] shadow focus:outline-none sm:w-[180px]    sm:w-[180px] ${(n=o.name)!=null&&n.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(l=o.name)==null?void 0:l.message})]})}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("button",{type:"submit",disabled:a,className:"font-iowan-regular  rounded-md bg-primary-black/80 px-4 py-1 font-semibold text-white hover:bg-primary-black",children:"Search"}),e.jsx("button",{type:"button",onClick:()=>{N({name:"",members:""}),s.set("name",""),s.set("page",1),i(s)},disabled:a,className:"rounded-md px-4 py-1 font-semibold text-[#1f1d1a]",children:"Clear"})]})]})}),e.jsxs("div",{className:"mt-10 overflow-x-auto rounded bg-brown-main-bg p-5 px-0 md:mt-8",children:[e.jsxs("div",{className:"mb-3 flex w-full items-center justify-between text-center",children:[e.jsx("h4",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"Companies"}),e.jsx("div",{className:"flex",children:e.jsx(z,{onClick:w,className:"px-2 py-2  font-medium"})})]}),e.jsx("div",{className:`${a?"":"custom-overflow overflow-x-auto"} `,children:a?e.jsx("div",{className:"flex max-h-fit min-h-fit min-w-fit max-w-full items-center justify-center  py-5",children:e.jsx(d,{size:50})}):e.jsx(e.Fragment,{children:e.jsx($,{children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10",children:[e.jsx("thead",{children:e.jsx("tr",{children:L.map((t,c)=>e.jsx("th",{scope:"col",className:"font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3",children:t.header},c))})}),e.jsx("tbody",{className:"font-iowan-regular  divide-y divide-[#1f1d1a]/10",children:r.map((t,c)=>e.jsxs("tr",{children:[e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("img",{src:t.logo,className:"h-[50px] w-[80px] rounded",alt:""})}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:t.name}),e.jsx("td",{className:"flex h-[80px] items-center justify-start gap-2 whitespace-nowrap px-6 py-4",children:e.jsx("button",{className:"cursor-pointer px-1 text-xs font-medium text-[#1f1d1a] underline underline-offset-2",onClick:()=>{h("/stakeholder/view-companies/"+t.id,{state:t})},children:e.jsx("span",{children:"View"})})})]},t.id))})]})})})}),(r==null?void 0:r.length)==0?e.jsxs("div",{className:"mb-[20px] mt-24 flex flex-col items-center",children:[e.jsx(F,{className:"h-8 w-8 text-gray-700",strokeWidth:2}),e.jsx("p",{className:"mt-4 text-center text-base font-medium",children:"No Company Added yet"})]}):null]})]})})})};export{be as default};
