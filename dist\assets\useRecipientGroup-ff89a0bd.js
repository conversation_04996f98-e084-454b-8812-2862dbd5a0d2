import{R as i}from"./vendor-4cdf2bd1.js";import{A as f,p as c,q as o,s as l,r as y,w as g}from"./index-f2ad9142.js";const S=(G={filter:[],join:[]})=>{const{state:b,dispatch:s}=i.useContext(f),{state:C,dispatch:r}=i.useContext(f),[d,p]=i.useState({list:[],single:null}),[R,n]=i.useState({list:!1,single:!1,update:!1,delete:!1,create:!1}),h=i.useCallback((t={filter:[]})=>(async()=>{n(e=>({...e,list:!0}));try{const e=await c(r,s,"recipient_group",{filter:[...t==null?void 0:t.filter],join:["group","user","recipient_member"]});return e!=null&&e.error||p(a=>({...a,list:e==null?void 0:e.data})),e}catch(e){return{error:!0,message:e==null?void 0:e.message}}finally{n(e=>({...e,list:!1}))}})(),[s,r,c]),u=i.useCallback(t=>o(t)?l(r,"Group id is Required!"):(async()=>{n(e=>({...e,single:!0}));try{const e=await y(r,s,"recipient_group",t,{join:["group","user","recipient_member"]});if(!(e!=null&&e.error))return p(a=>({...a,single:e==null?void 0:e.data})),e==null?void 0:e.data}catch{}finally{n(e=>({...e,single:!1}))}})(),[s,r,c]),m=i.useCallback((t,e)=>{if(o(t))return l(r,"Recipient Group id is Required!");if(o(e))return l(r,"Payload is Required!");(async()=>{n(!0);try{const a=await g(r,s,"recipient_group",t,e,!1);a!=null&&a.error||u(t)}catch{}finally{n(!1)}})()},[u,s,r,g]);return{recipientGroup:d,loading:R,getRecipientGroups:h,getRecipientGroup:u,updateRecipientGroup:m}};export{S as u};
