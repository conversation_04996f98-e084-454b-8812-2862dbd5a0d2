import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{u as M,r as n,b as Y,h as B}from"./vendor-4cdf2bd1.js";import{u as G}from"./react-hook-form-a383372b.js";import{A as O,G as V,L as W,l as H,s as J}from"./index-f2ad9142.js";import{o as K}from"./yup-0917e80c.js";import{c as Q,a as g}from"./yup-342a5df4.js";import{u as X}from"./useUpdates-fb92bb6b.js";import{h as r}from"./moment-a9aaa855.js";import{A as v}from"./AcceptUpdateButton-12330f86.js";import"./react-scroll-9384d626.js";import{A as Z}from"./AddTime-1e28f1bd.js";import{R as ee}from"./tableWrapper-ca490fb1.js";import{L as N}from"./index-b8adfdf8.js";import{P as ae}from"./index-9dceff66.js";import{L as c,t as te}from"./@headlessui/react-cdd9213e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-b50d6e2a.js";import"./XMarkIcon-cfb26fe7.js";import"./index.esm-7add6cfb.js";import"./react-icons-36ae72b7.js";const se=[{header:"Company Name",accessor:"company_name"},{header:"Update Name",accessor:"update_name"},{header:"Date received",accessor:"sent_at"},{header:"Availability",accessor:"availability"},{header:"Action",accessor:""}],Me=()=>{var b,j;M();const[_,ie]=n.useState(""),[w,ne]=n.useState("");window.location.href.split("/"),n.useContext(O);const{dispatch:o}=n.useContext(V),m=Y(),[s,p]=B(),{updates:t,loading:l,refetch:x,currentPage:S,pageCount:P,pageSize:k,updatePageSize:C,previousPage:L,nextPage:U,canPreviousPage:q,canNextPage:D}=X(_,w),[le,u]=n.useState(!1);n.useState(!1);const F=Q({company_name:g(),availability:g()}),{register:A,handleSubmit:T,setError:re,reset:$,formState:{errors:h}}=G({resolver:K(F),defaultValues:async()=>{const a=s.get("company_name")??"",i=s.get("status")??"",d=s.get("availability")??"";return{company_name:a,status:i,availability:d}}});n.useEffect(()=>{o({type:"SETPATH",payload:{path:"update_requests"}})},[]);function E(a){s.set("company_name",a.company_name),s.set("status",a.status),s.set("availability",a.availability),p(s)}const z=window.location.href,I=new URL(z).origin,R=a=>{navigator.clipboard.writeText(I+a),J(o,"Link Copied")},f=t==null?void 0:t.filter(a=>a.request==0);return e.jsx(e.Fragment,{children:l?e.jsx(N,{}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"rounded bg-brown-main-bg p-5 pt-8 md:px-8",children:[e.jsx("div",{className:"item-center mb-3 flex w-full justify-between ",children:e.jsx("h4",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"Search"})}),e.jsxs("form",{onSubmit:T(E),className:"flex flex-col gap-4",children:[e.jsx("div",{className:"flex w-full flex-wrap gap-4",children:e.jsxs("div",{className:"w-full sm:w-auto",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize  text-[#1f1d1a]",children:"Company name"}),e.jsx("input",{type:"text",...A("company_name"),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2  text-sm font-normal leading-tight text-[#1f1d1a] shadow   focus:outline-none sm:w-[180px] ${(b=h.company_name)!=null&&b.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(j=h.company_name)==null?void 0:j.message})]})}),e.jsxs("div",{className:"row-start-3 flex items-center gap-4 sm:row-start-auto",children:[e.jsx("button",{type:"submit",disabled:l,className:"font-iowan-regular  rounded-md bg-primary-black/80 px-4 py-1 font-semibold text-white hover:bg-primary-black",children:"Search"}),e.jsx("button",{type:"button",onClick:()=>{$({company_name:"",status:"",availability:""}),s.delete("company_name"),s.delete("status"),s.delete("availability"),p(s)},disabled:l,className:"rounded-md px-4 py-1 font-semibold text-[#1f1d1a]",children:"Clear"})]})]}),e.jsxs("div",{className:"mt-8 w-full rounded p-5 px-0",children:[e.jsx("div",{className:"mb-6 flex w-full justify-between text-center",children:e.jsx("div",{className:"flex w-full flex-row justify-between ",children:e.jsxs("div",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:["Updates(",f?f.length:null,")"]})})}),e.jsx("div",{className:`${l?"":"custom-overflow overflow-x-auto"} `,children:l?e.jsx("div",{className:"flex max-h-fit min-h-fit min-w-fit max-w-full items-center justify-center  py-5",children:e.jsx(N,{size:50})}):e.jsx(e.Fragment,{children:e.jsx(ee,{children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10",children:[e.jsx("thead",{className:"",children:e.jsx("tr",{children:se.map((a,i)=>e.jsx("th",{scope:"col",className:"font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3",children:a.header},i))})}),e.jsx("tbody",{className:"font-iowan-regular  divide-y divide-[#1f1d1a]/10",children:t==null?void 0:t.toReversed().map(a=>{var y;(y=a==null?void 0:a.public_url)==null||y.split("/");const i=r(a.sent_at).add(a.recipient_access,"days").toDate()<new Date,d=a.sent_at?r(a.sent_at).add(a.recipient_access??0,"days").diff(r(),"days"):0;return e.jsxs("tr",{cl:!0,children:[e.jsx("td",{className:"relative whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6 md:pb-[50px]",children:e.jsx(c,{className:"relative ",children:({open:ce})=>e.jsxs(e.Fragment,{children:[e.jsx(c.Button,{as:"div",className:"max-w-[300px] cursor-auto",onMouseEnter:()=>u(a.id),onMouseLeave:()=>u(!1),children:a.company_name}),e.jsx(te,{as:n.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",className:"absolute z-[999999999999999999] w-[300px]",children:e.jsx(c.Panel,{className:"hidden px-4",children:e.jsx("div",{className:" hidden    rounded-lg bg-[#1f1d1a] p-4 px-4 text-white shadow-lg ring-1 ring-[#1f1d1a]/5 md:block",children:e.jsxs("div",{className:"flex flex-col  gap-2 text-[14px] font-medium",children:[e.jsx("span",{className:"text-[17px]",children:a.company_name}),e.jsx("p",{className:"line-clamp-5 overflow-ellipsis text-[13px] text-gray-300",children:a.description})]})})})})]})},a.id)}),e.jsx("td",{className:"whitespace-nowrap px-6 py-6 disabled:cursor-not-allowed disabled:text-gray-400 md:max-w-lg md:whitespace-normal",children:e.jsx("button",{className:"underline-offset-6 cursor-pointer font-iowan underline disabled:cursor-not-allowed disabled:text-gray-400",onClick:()=>{m(a.update_link)},disabled:(i||a.request===0)&&a.is_requested!==1,children:a.update_name.startsWith("Update ")&&a.update_name.slice(7).match(/^\d{4}-\d{2}-\d{2}$/)?`Update ${new Date(a.update_name.slice(7)).toLocaleString("en-US",{month:"short",day:"2-digit",year:"numeric"}).replace(/,/g,",")}`:a.update_name})}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:r(a.sent_at).format("MMM DD, YYYY")}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:i?e.jsx("span",{children:"0 hrs / 0 minutes (update expired)"}):e.jsxs("span",{children:[d<1?"":`${d} days,  `,a.sent_at?r(a.sent_at).add(a.recipient_access,"days").diff(r().add(d,"days"),"hours"):0," ","hrs"]})}),e.jsx("td",{className:"flex flex-col items-start justify-center gap-4 px-6 py-4 md:max-w-[500px]",children:e.jsxs("div",{className:"flex flex-row items-start gap-4 md:justify-center",children:[e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("button",{className:"flex cursor-pointer justify-start font-medium text-[#292829fd] underline hover:underline disabled:cursor-not-allowed disabled:text-gray-400",onClick:()=>{m(a.update_link)},disabled:(i||a.request===0)&&a.is_requested!==1,children:e.jsx("span",{children:"View"})}),e.jsx("button",{className:"flex cursor-pointer justify-start px-0 font-medium text-[#292829fd] underline hover:underline disabled:cursor-not-allowed disabled:text-gray-400",onClick:()=>{R(`${a.update_link}`)},disabled:(i||a.request===0||a.public_link_enabled==0)&&!a.is_requested,children:e.jsx("span",{children:"Share"})}),i?e.jsx("button",{className:"flex cursor-pointer justify-start  font-medium text-[#292829fd] underline hover:underline disabled:text-[#1f1d1a]",onClick:()=>{},children:e.jsx("span",{className:"text-green-500",children:e.jsx(Z,{data:a})})}):null]}),a.request===0&&a.is_requested!==1&&e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx(v,{refetch:x,row:a}),e.jsx(v,{reject:!0,refetch:x,row:a})]})})]})})]},a.id)})})]})})})}),(t==null?void 0:t.length)==0?e.jsxs("div",{className:"mb-[20px] mt-24 flex flex-col items-center",children:[e.jsx(W,{children:e.jsx(H,{fill:"black",className:"!h-[5rem] !w-[5rem]"})}),e.jsx("p",{className:"mt-4 text-center text-base font-medium",children:"No Company Updates"})]}):null,(t==null?void 0:t.length)>0&&e.jsx(ae,{currentPage:S,pageCount:P,pageSize:k,canPreviousPage:q,canNextPage:D,updatePageSize:C,previousPage:L,nextPage:U,dataLoading:l})]})]})})})};export{Me as default};
