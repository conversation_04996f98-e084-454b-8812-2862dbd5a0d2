import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as f,r as j}from"./vendor-4cdf2bd1.js";import{a as L,b as $,O,L as V,I as X}from"./index-f2ad9142.js";import{u as Z,a as _,b as D,c as G,d as K}from"./@stripe/react-stripe-js-5f217abb.js";import{u as H}from"./react-hook-form-a383372b.js";import{o as J}from"./yup-0917e80c.js";import{c as Q,a as c}from"./yup-342a5df4.js";import{c as N}from"./countries-912e22d5.js";import{InteractiveButton2 as U}from"./InteractiveButton-060359e0.js";import{M as m}from"./MkdInput-d37679e9.js";import{X as W}from"./XMarkIcon-6ed09631.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@hookform/resolvers-b50d6e2a.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const Y="/assets/credit_card-7d23d336.svg",ee="/assets/bank_debit-e1f2f8ba.svg",te="/assets/paypal-9bcf150e.svg",Be=({onSuccess:E=null,onClose:b=null})=>{var q,F,I;f.useState({}),f.useState(10),f.useState(!1),f.useState(!1);const[o,p]=f.useState({createCard:!1,mountCard:!1,fetchingDetails:!1}),[C,v]=j.useState("credit-card");j.useState({firstName:"",lastName:"",address:"",country:"",city:"",state:"",zipCode:""});const z=Z(),R=_(),{showToast:d,tokenExpireError:k}=L(),{sdk:w}=$(),T=Q({firstName:c().required("First name is required"),lastName:c().required("Last name is required"),address:c().required("Address is required"),country:c().required("Country is required"),city:c().required("City is required"),state:c().required("State is required"),zipCode:c().required("ZIP code is required")}),{register:n,handleSubmit:B,formState:{errors:l},watch:P,setValue:y}=H({resolver:J(T),defaultValues:{firstName:"",lastName:"",address:"",country:"",city:"",state:"",zipCode:""}}),M=async a=>{var t,r;p(s=>({...s,createCard:!0}));const i=R.getElement(D);try{const s=await z.createToken(i);if(s.error){d(s.error.message||"Something went wrong");return}const g={sourceToken:s.token.id},S=await w.createCustomerStripeCard(g);if(S.error)S.validation&&Object.values(S.validation).forEach(h=>{d(h,3e3)});else try{(await w.callRawAPI("/v3/api/custom/goodbadugly/user/billing-details",{firstName:a.firstName,lastName:a.lastName,address:a.address,country:a.country,city:a.city,state:a.state,zipCode:a.zipCode},"POST")).error?d("Card added but failed to save billing details",3e3):(d("Payment method added successfully"),E&&E())}catch(h){console.error("Billing details error:",h),d("Card added but failed to save billing details",3e3)}}catch(s){console.error("Payment error:",s);const g=((r=(t=s==null?void 0:s.response)==null?void 0:t.data)==null?void 0:r.message)??s.message;d(g,5e3),k(g)}finally{p(s=>({...s,createCard:!1})),b&&b()}},u=P("country")||"",x=P("state")||"";j.useMemo(()=>{const a=N.find(t=>t.name==u),i=a==null?void 0:a.states.find(t=>t.name==x);return(i==null?void 0:i.cities.map(t=>t.name))??[]},[u,x]);const A=async()=>{var a,i;p(t=>({...t,fetchingDetails:!0}));try{const t=await w.callRawAPI("/v3/api/custom/goodbadugly/user/billing-details",{},"GET");console.log("Billing details API response:",t),!t.error&&t.data&&(Object.keys(t.data).forEach(r=>{["country","state","city"].includes(r)||(console.log(`Setting ${r} to:`,t.data[r]),y(r,t.data[r]))}),t.data.country&&(console.log("Setting country:",t.data.country),y("country",t.data.country),setTimeout(()=>{t.data.state&&(console.log("Setting state:",t.data.state),y("state",t.data.state),setTimeout(()=>{t.data.city&&(console.log("Setting city:",t.data.city),y("city",t.data.city))},100))},100)))}catch(t){console.error("Error fetching billing details:",t);const r=((i=(a=t==null?void 0:t.response)==null?void 0:a.data)==null?void 0:i.message)??t.message;d(r,5e3),k(r)}finally{p(t=>({...t,fetchingDetails:!1}))}};return j.useEffect(()=>{A()},[]),console.log(o.mountCard,o.fetchingDetails),e.jsxs("div",{className:"relative",children:[o.fetchingDetails&&e.jsx(O,{loading:o.fetchingDetails}),e.jsx(V,{children:e.jsxs("div",{className:"relative max-h-[90%] w-full overflow-y-auto rounded bg-brown-main-bg pb-5 sm:pb-0 ",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold font-iowan",children:"Add New Payment Method"}),e.jsx("button",{onClick:b,className:"p-1 rounded-full hover:bg-opacity-80",children:e.jsx(W,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-2 mb-6",children:[e.jsxs("button",{type:"button",className:`flex items-center justify-center gap-2  rounded-[8px] border border-[#1f1d1a] bg-[#F2DFCE] px-4 py-3 ${C==="credit-card"?"border-[#1f1d1a] ":""}`,onClick:()=>v("credit-card"),children:[e.jsx("img",{src:Y,alt:"",className:"hidden w-5 h-5 sm:block"}),"Credit Card"]}),e.jsxs("button",{disabled:!0,type:"button",className:`flex items-center justify-center gap-2  rounded-[8px] border border-[#1f1d1a] px-4 py-3 ${C==="bank"?"border-[#1f1d1a] ":""}`,onClick:()=>v("bank"),children:[e.jsx("img",{src:ee,alt:"",className:"hidden w-5 h-5 sm:block"}),"Bank Debit"]}),e.jsxs("button",{disabled:!0,type:"button",className:`flex items-center justify-center gap-2  rounded-[8px] border border-[#1f1d1a]  px-4 py-3 disabled:cursor-not-allowed ${C==="paypal"?"border-[#1f1d1a] bg-brown-main-bg":""}`,onClick:()=>v("paypal"),children:[e.jsx("img",{src:te,alt:"",className:"hidden w-5 h-5 sm:block"}),"PayPal"]})]}),e.jsxs("form",{onSubmit:B(M),className:"space-y-5",children:[e.jsx("div",{className:"space-y-5",children:e.jsxs("div",{className:"grid grid-cols-1 gap-5",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 text-sm font-medium font-iowan",children:"Card Number"}),e.jsx("div",{className:"w-full rounded-[.125rem] border border-[#1f1d1a] p-3",children:e.jsx(D,{options:{style:{base:{border:"1px solid #1f1d1a",fontSize:"16px",color:"#1f1d1a",fontFamily:"iowan, system-ui","::placeholder":{color:"#1f1d1a"}},invalid:{color:"#EF4444",iconColor:"#EF4444"}}},onReady:()=>{p(a=>({...a,mountCard:!0}))}})})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-5",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 text-sm font-medium font-iowan",children:"Expires"}),e.jsx("div",{className:"w-full rounded-[.125rem] border border-[#1f1d1a] p-3",children:e.jsx(G,{options:{style:{base:{fontSize:"16px",color:"#1f1d1a",fontFamily:"iowan, system-ui","::placeholder":{color:"#1f1d1a"}},invalid:{color:"#EF4444",iconColor:"#EF4444"}}}})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 text-sm font-medium font-iowan",children:"CVV"}),e.jsx("div",{className:"w-full rounded-[.125rem] border border-[#1f1d1a] p-3",children:e.jsx(K,{options:{style:{base:{fontSize:"16px",color:"#1f1d1a",fontFamily:"iowan, system-ui","::placeholder":{color:"#1f1d1a"}},invalid:{color:"#EF4444",iconColor:"#EF4444"}}}})})]})]})]})}),e.jsxs("div",{className:"pt-5 border-t",children:[e.jsx("h3",{className:"mb-5 text-lg font-medium font-iowan",children:"Billing Information"}),e.jsxs("div",{className:"grid grid-cols-1 gap-5 md:grid-cols-2",children:[e.jsx(m,{type:"text",label:"First Name",errors:l,register:n,name:"firstName"}),e.jsx(m,{type:"text",label:"Last Name",errors:l,register:n,name:"lastName"}),e.jsx("div",{className:"col-span-full",children:e.jsx(m,{type:"text",label:"Address",errors:l,register:n,name:"address"})}),e.jsx(m,{type:"select",label:"Country",errors:l,register:n,name:"country",noneText:"Select Country",options:N.map(a=>a.name)}),e.jsx(m,{type:"select",label:"State",errors:l,register:n,name:"state",noneText:"Select State",options:u?((q=N.find(a=>a.name===u))==null?void 0:q.states.map(a=>a.name))||[]:[]}),e.jsx(m,{type:"select",label:"City",errors:l,register:n,name:"city",noneText:"Select City",options:u&&x?((I=(F=N.find(a=>a.name===u))==null?void 0:F.states.find(a=>a.name===x))==null?void 0:I.cities.map(a=>a.name))||[]:[]}),e.jsx(m,{type:"text",label:"ZIP Code",errors:l,register:n,name:"zipCode"})]})]}),e.jsxs("div",{className:"flex gap-3 justify-end pt-5 border-t",children:[e.jsx(X,{type:"button",onClick:b,className:"h-[42px] !rounded-[.125rem] !border !border-[#1f1d1a] !bg-transparent !px-4 !py-3 font-iowan",children:"Cancel"}),e.jsx(U,{type:"submit",loading:o.createCard,disabled:o.createCard||!o.mountCard,className:"!rounded-[.125rem] !bg-[#1f1d1a] !px-4 !py-3 font-iowan !font-bold !text-white disabled:opacity-50",children:o.createCard?"Processing...":"Add Card"})]})]})]})})]})};export{Be as default};
