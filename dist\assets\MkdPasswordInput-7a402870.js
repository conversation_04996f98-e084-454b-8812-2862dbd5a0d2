import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{r as n}from"./vendor-4cdf2bd1.js";import{M as l}from"./MkdInput-d37679e9.js";import{j as c,bG as d}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const $=({register:s,errors:m,name:e="password",label:i="Password",className:a,required:p=!0})=>{const[o,t]=n.useState("password");return r.jsxs("div",{className:"w-full space-y-2",children:[r.jsxs("label",{className:"block font-iowan text-[1rem] font-[700] capitalize leading-[1.5rem] text-[#1F1D1A]",htmlFor:e,children:[i,p&&r.jsx("sup",{className:"z-[99999] text-[.825rem] text-red-600",children:"*"})]}),r.jsxs("div",{className:`flex h-fit w-full appearance-none items-center rounded-[.625rem] border  pr-[.9906rem] font-inter leading-tight text-black shadow focus:outline-none focus:ring-0  ${a}`,children:[r.jsx("div",{className:"grow rounded-[.25rem]",children:r.jsx(l,{type:o,name:e,className:"!h-full w-full !border-0 !shadow-none",errors:m,register:s})}),r.jsx("div",{className:"h-[.755rem] max-h-[.755rem] min-h-[.755rem] w-[1.0294rem] min-w-[1.0294rem] max-w-[1.0294rem]",children:o==="password"?r.jsx(c,{className:"cursor-pointer",onClick:()=>t("text")}):r.jsx(d,{className:"cursor-pointer",onClick:()=>t("password")})})]})]})};export{$ as default};
