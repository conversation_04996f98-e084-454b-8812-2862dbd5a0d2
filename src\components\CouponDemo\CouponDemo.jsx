import React, { useState } from 'react';
import { useSubscription } from 'Hooks/useSubscription';
import { showToast } from 'Context/Global';

/**
 * Demo component showing how to use the coupon functionality
 * This is for demonstration purposes and can be integrated into existing components
 */
const CouponDemo = () => {
  const { createVIPCoupon, getAllCoupons, createSubscriptionWithCoupon } = useSubscription();
  const [coupons, setCoupons] = useState([]);
  const [loading, setLoading] = useState(false);

  const handleCreateVIPCoupon = async () => {
    setLoading(true);
    try {
      const result = await createVIPCoupon();
      console.log('VIP Coupon created:', result);
      showToast('VIP Coupon created successfully!', 3000, 'success');
      // Refresh coupons list
      fetchCoupons();
    } catch (error) {
      console.error('Error creating VIP coupon:', error);
      showToast('Failed to create VIP coupon', 3000, 'error');
    } finally {
      setLoading(false);
    }
  };

  const fetchCoupons = async () => {
    try {
      const result = await getAllCoupons();
      setCoupons(result.data || []);
    } catch (error) {
      console.error('Error fetching coupons:', error);
    }
  };

  const handleSubscribeWithCoupon = async (planId, couponId) => {
    setLoading(true);
    try {
      const result = await createSubscriptionWithCoupon(planId, couponId);
      console.log('Subscription created with coupon:', result);
      showToast('Subscription created successfully with coupon!', 3000, 'success');
    } catch (error) {
      console.error('Error creating subscription with coupon:', error);
      showToast('Failed to create subscription', 3000, 'error');
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    fetchCoupons();
  }, []);

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">Coupon System Demo</h2>
      
      {/* Create VIP Coupon */}
      <div className="mb-6 p-4 border rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Create VIP Coupon</h3>
        <p className="text-gray-600 mb-3">
          Creates a 100% off coupon with ID 'vip-access-100'
        </p>
        <button
          onClick={handleCreateVIPCoupon}
          disabled={loading}
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
        >
          {loading ? 'Creating...' : 'Create VIP Coupon'}
        </button>
      </div>

      {/* Available Coupons */}
      <div className="mb-6 p-4 border rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Available Coupons</h3>
        <button
          onClick={fetchCoupons}
          className="mb-3 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Refresh Coupons
        </button>
        {coupons.length > 0 ? (
          <div className="space-y-2">
            {coupons.map((coupon, index) => (
              <div key={index} className="p-2 bg-gray-50 rounded">
                <div className="font-medium">{coupon.name}</div>
                <div className="text-sm text-gray-600">
                  Code: {coupon.id || coupon.couponId} | 
                  Discount: {coupon.percent_off}% | 
                  Duration: {coupon.duration}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500">No coupons available</p>
        )}
      </div>

      {/* Example Usage */}
      <div className="p-4 border rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Example Usage</h3>
        <div className="space-y-2 text-sm">
          <p><strong>1. Admin creates coupons:</strong> Use the admin panel at /admin/coupons</p>
          <p><strong>2. Users apply coupons:</strong> Enter coupon code in the subscription modal</p>
          <p><strong>3. Programmatic usage:</strong></p>
          <pre className="bg-gray-100 p-2 rounded text-xs overflow-x-auto">
{`// Create subscription with coupon
await createSubscriptionWithCoupon('plan_123', 'vip-access-100');

// Create VIP coupon
await createVIPCoupon();

// Get all coupons
const coupons = await getAllCoupons();`}
          </pre>
        </div>
      </div>

      {/* Test Subscription */}
      <div className="mt-6 p-4 border rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Test Subscription with Coupon</h3>
        <p className="text-gray-600 mb-3">
          This would normally be handled by the SubscribeButton component
        </p>
        <button
          onClick={() => handleSubscribeWithCoupon('test_plan_id', 'vip-access-100')}
          disabled={loading}
          className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
        >
          {loading ? 'Processing...' : 'Test Subscribe with VIP Coupon'}
        </button>
      </div>
    </div>
  );
};

export default CouponDemo;
