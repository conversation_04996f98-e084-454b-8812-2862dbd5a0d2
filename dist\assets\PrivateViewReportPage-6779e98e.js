import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{y as ge,z as Ce,L as ke,aH as ts,aI as Ze,aJ as ze,aK as rs,b as ns,a as ls,t as ne,s as U,A as Re,G as es,M as le}from"./index-f2ad9142.js";import{h as I}from"./moment-a9aaa855.js";import{r,i as os,b as is}from"./vendor-4cdf2bd1.js";import cs from"./DeleteCommentButton-29b838fe.js";import{S as ms}from"./ShareButton-6a607afd.js";import{e as Ge,f as Ke,g as Qe,h as We,i as Xe}from"./index.esm-be5e1c14.js";import{c as je}from"./index.esm-7add6cfb.js";import{b as ds,c as xs}from"./index.esm-bb52b9ca.js";import fs from"./CustomEditor-4eddf399.js";import{u as ps}from"./useNote-ea33f376.js";import{u as hs}from"./useUpdateCollaborators-677ec5ee.js";import{P as ss}from"./PlusIcon-26cedb5d.js";import{L as oe,t as as}from"./@headlessui/react-cdd9213e.js";import{u as us}from"./useNotes-19835bdc.js";import{L as bs}from"./index-b8adfdf8.js";import{L as qe}from"./LockClosedIcon-a004efdc.js";import{L as ws}from"./LockOpenIcon-34dcf8cd.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./XMarkIcon-cfb26fe7.js";import"./InteractiveButton-060359e0.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./yup-0917e80c.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-hook-form-a383372b.js";import"./index.esm-3e7472af.js";import"./react-icons-36ae72b7.js";import"./yup-342a5df4.js";import"./@editorjs/editorjs-3bc58744.js";import"./@editorjs/paragraph-9d333c59.js";import"./@editorjs/header-da8c369a.js";import"./@editorjs/list-86b325f6.js";import"./@editorjs/link-7a38da73.js";import"./@editorjs/delimiter-89018da8.js";import"./@editorjs/checklist-1b2b7ac3.js";const js=({data:a,summary:h})=>{const[v,u]=r.useState(!1),[j,b]=r.useState(null),[s,C]=r.useState(1),[N,y]=r.useState(1),[o,T]=r.useState(1),[R,M]=r.useState(""),[Q,F]=r.useState("stop"),[_,H]=r.useState({duration:0,elapsedTime:0,autoPlay:!1}),w=r.useRef(window.speechSynthesis),J=r.useRef(null),K=r.useCallback(x=>{const S=((a==null?void 0:a.notes)||[]).map(g=>{const{blocks:ee}=ge(g.content,{blocks:[]});if(ee.length===0)return null;const re=Ce(ee);return{title:g==null?void 0:g.type,para:re}}).filter(Boolean),k=h==null?void 0:h.content,X=((a==null?void 0:a.questions)||[]).map(g=>({title:g==null?void 0:g.question})),Z=[...[{title:a==null?void 0:a.name,para:I(a==null?void 0:a.date).format("MM/DD/YYYY")},{title:"Financial Overview"},{title:"MMR",para:a==null?void 0:a.mrr},{title:"ARR",para:a==null?void 0:a.arr},{title:"Cash",para:a==null?void 0:a.cash},{title:"Burnrate",para:a==null?void 0:a.burnrate},{title:"Runway (months)",para:a==null?void 0:a.runway},{title:"Investment Overview"},{title:"Investment stage",para:a==null?void 0:a.investment_stage},{title:"Invested to date",para:a==null?void 0:a.invested_to_date},{title:"Investors on Cap Table",para:a==null?void 0:a.investors_on_cap_table},{title:"Valuation of last round",para:a==null?void 0:a.valuation_at_last_round},{title:"Date of last round",para:(a==null?void 0:a.date_of_last_round)&&I(a==null?void 0:a.date_of_last_round).format("MM/DD/YYYY")}],...S,{title:"Asks"},...X,...k||[]],q=[{title:a==null?void 0:a.name,para:I(a==null?void 0:a.date).format("MM/DD/YYYY")},...S,{title:"Asks"},...X,...k||[]];let E="";switch(x){case"entire update":E=Z.map(g=>`${g.title}: ${g.para||""}`).join(". ");break;case"update no metrics":E=q.map(g=>`${g.title}: ${g.para||""}`).join(". ");break;case"summary":E=k||"No summary available.";break;default:E="Please select an option to speak."}return E},[a,h]),f=r.useCallback(x=>{w.current.cancel(),F("playing");const c=new SpeechSynthesisUtterance(x);J.current=c,c.voice=j,c.pitch=s,c.rate=N,c.volume=o;let S=0;c.onstart=k=>{S=0,H(B=>({...B,duration:0}))},c.onboundary=k=>{k.charIndex>0&&(console.log("event",k),H(B=>({...B,elapsedTime:0})))},c.onend=()=>{console.log("Total duration of audio:",S),F("stop")},c.onend=()=>F("stop"),c.onerror=k=>console.error("Speech synthesis error",k),w.current.speak(c)},[j,s,N,o]),L=r.useCallback((x,c="selection")=>{c==="selection"&&(w.current.cancel(),F("stop"));const S=K(x);(["selection"].includes(c)&&(_!=null&&_.autoPlay)||["play"].includes(c))&&f(S),M(x)},[K,f,_]),ie=r.useCallback(()=>{u(!0),F("pause"),w.current.pause()},[]),V=r.useCallback(()=>{u(!1),F("playing"),w.current.resume()},[]);r.useCallback(()=>{u(!1),F("stop"),w.current.cancel()},[]);const W=r.useCallback(x=>{if(y(x),J.current&&w.current.speaking){w.current.cancel();const c=new SpeechSynthesisUtterance(J.current.text);Object.assign(c,J.current),c.rate=x,J.current=c,w.current.speak(c)}},[]);return r.useCallback(()=>W(Math.min(N+.25,2)),[W,N]),r.useCallback(()=>W(Math.max(N-.25,.5)),[W,N]),Array.from({length:15}),r.useEffect(()=>{const x=()=>{const c=w.current.getVoices();b(c[0])};return w.current.addEventListener("voiceschanged",x),()=>{w.current.removeEventListener("voiceschanged",x),w.current.cancel()}},[]),e.jsx(e.Fragment,{children:e.jsxs("div",{className:"hidden w-full md:flex md:grow",children:[e.jsx("style",{jsx:!0,children:`
          /* HTML: <div class="loader"></div> */
          .loader {
            width: 100%;
            height: 0.5rem;
            background: repeating-linear-gradient(
                -45deg,
                #000 0 0.9375rem,
                #0000 0 1.25rem
              )
              right/200% 100%;
            animation: l3 12s infinite linear;
          }
          @keyframes l3 {
            100% {
              background-position: left;
            }
          }
        `}),e.jsxs("div",{className:"flex w-full flex-col flex-wrap items-start gap-[1rem] font-iowan font-semibold md:flex-row md:items-center",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("label",{htmlFor:"TTS",className:"text-[.8125rem]",children:"Listen to:"}),e.jsxs("select",{name:"type",id:"TTS",className:"focus:shadow-outline  w-full max-w-[12.5rem] rounded-[.125rem] border border-[#1f1d1a] bg-brown-main-bg px-3 py-2 font-iowan leading-tight text-[#1f1d1a]  focus:outline-none",onChange:x=>L(x.target.value,"selection"),children:[e.jsx("option",{value:"",children:"Select"}),e.jsx("option",{value:"entire update",children:"Entire update"}),e.jsx("option",{value:"update no metrics",children:"Update w/o metrics"}),e.jsx("option",{value:"summary",children:"Summary only"})]})]}),e.jsxs("div",{className:"flex w-full items-end justify-center gap-[.5rem] md:w-auto md:grow",children:[e.jsxs("div",{className:"flex grow flex-col items-center justify-center gap-[.5rem]",children:[e.jsxs("div",{className:"flex flex-row items-center justify-center gap-[1.5rem]",children:[e.jsx(ke,{children:e.jsx(ts,{className:"cursor-pointer",onClick:()=>{}})}),["stop","pause"].includes(Q)?e.jsx(ke,{children:e.jsx(Ze,{className:"cursor-pointer",onClick:()=>{Q==="stop"?L(R,"play"):V()}})}):e.jsx(ke,{children:e.jsx(ze,{className:"cursor-pointer",onClick:ie})}),e.jsx(ke,{children:e.jsx(rs,{className:"cursor-pointer",onClick:()=>{}})})]}),e.jsxs("div",{className:"grid w-full min-w-full max-w-full grid-cols-[auto_18.75rem_auto] grid-rows-1 items-center justify-center gap-[.5rem]",children:[e.jsx("code",{className:"w-fit min-w-fit max-w-fit"}),e.jsx("div",{className:" h-[.5rem] max-h-[.5rem] min-h-[.5rem] w-[18.75rem] min-w-[18.75rem] max-w-[18.75rem] gap-[.5rem] rounded-full bg-[#1F1D1A33]",children:e.jsx("div",{className:"h-[.5rem] max-h-[.5rem] min-h-[.5rem] w-full min-w-full max-w-full flex-nowrap justify-between  rounded-full bg-transparent",children:e.jsx("div",{className:`rounded-full ${["playing"].includes(Q)?"loader":""}`})})}),e.jsx("code",{className:"w-fit min-w-fit max-w-fit"})]})]}),e.jsxs("div",{className:"flex flex-col items-center justify-center gap-[1.3125rem] ",children:[e.jsx("span",{children:"Auto Play"}),e.jsx("div",{className:"relative h-[.75rem] w-[2rem] rounded-full bg-[#D2C6BC] ",children:e.jsx("button",{onClick:()=>{H(x=>({...x,autoPlay:!(x!=null&&x.autoPlay)}))},className:`absolute inset-y-0 m-auto flex h-[1rem] w-[1rem] items-center justify-center rounded-full bg-primary-black ${_!=null&&_.autoPlay?"right-0":""}`,children:_!=null&&_.autoPlay?e.jsx(Ze,{className:"!h-[50%] !w-[50%]",pathFill:"#FFF0E5"}):e.jsx(ze,{className:"!h-[50%] !w-[50%]",pathFill:"#FFF0E5"})})})]})]})]})]})})},gs=js;function vs(a){const{sdk:h}=ns(),{authDispatch:v,globalDispatch:u}=ls(),[j,b]=r.useState([]),[s,C]=r.useState(!1),[N,y]=r.useState(!1),o=r.useCallback(async(T=null)=>{const R=T||a||null;if(R)try{C(!0);const M=await h.callRawAPI(`/v3/api/custom/goodbadugly/updates/${R}/comments`);C(!1),b(M==null?void 0:M.comments),console.log(M==null?void 0:M.comments)}catch(M){y(!0),C(!1),ne(v,M.message),U(u,M.message,5e3,"error")}},[]);return r.useEffect(()=>{o()},[]),{error:N,loading:s,allComments:j,refetch:o}}const Ns=({commentId:a,noteId:h,updateId:v,replyComment:u,setReplyComment:j,handleReplyComment:b,refetchAll:s})=>{const[C,N]=r.useState(!1);return e.jsxs("div",{children:[e.jsxs("button",{className:"flex h-[36px] w-full max-w-[124px] cursor-pointer flex-row items-center justify-center gap-3 rounded border-[2px] border-[#1f1d1a] font-iowan",onClick:()=>N(!C),children:[e.jsx(ss,{className:"h-5 w-5"}),e.jsx("span",{children:" Reply"})]}),C&&e.jsxs("div",{className:"mt-2",children:[e.jsx("textarea",{value:u,onChange:y=>j(y.target.value),className:"w-full border border-[#1f1d1a] bg-transparent p-2",rows:"3"}),e.jsx("button",{className:"mt-2 h-[36px] w-[124px] rounded bg-[#1f1d1a] px-4 py-2 font-iowan text-sm text-white",onClick:()=>b(a,h,v,s),children:"Submit Reply"})]})]})},ys=({note:a,html:h,userCommentOnNote:v,setComment:u,data:j,comment:b,refetchAll:s,refetchAllComment:C,update_id:N,update:y})=>{const{state:o,dispatch:T}=r.useContext(Re),{dispatch:R}=r.useContext(es),[M,Q]=r.useState(""),[F,_]=r.useState(""),[H,w]=r.useState(!1),[J,K]=r.useState(!1),[f,L]=r.useState(!1),[ie,V]=r.useState(!1),[W,x]=r.useState(!1),{refetch:c}=ps(a.id,a),S=new le,[k,B]=r.useState({}),{updateCollaborators:X}=o.role==="collaborator"?hs(N,a.id):{updateCollaborators:[]},ce=o.user,Z=X.find(n=>n.collaborator_id===ce&&n.note_id===a.id)||null;r.useEffect(()=>{Z&&L(!0)},[Z]),console.log(Z);const q=r.useCallback(async n=>{if(k[n])return k[n];try{const D=await new le().callRawAPI(`/v4/api/records/user/${n}`,{},"GET"),A={...k,[n]:D};return B(A),D}catch(m){return console.error("Error fetching user details:",m),null}},[k]);r.useEffect(()=>{(async()=>{var A,i;const m=[...(b==null?void 0:b.update_comment_replies)||[],...((i=(A=y.find(O=>O.id===a.id))==null?void 0:A.comments.find(O=>(O==null?void 0:O.id)===(b==null?void 0:b.id)))==null?void 0:i.update_comment_replies)||[]],D=[...new Set(m.map(O=>O.user_id))];for(const O of D)await q(O)})()},[b,y,a.id,q]);const E=async(n,m,D,A)=>{try{const i=await S.callRawAPI(`/v3/api/custom/goodbadugly/updates/comments/replies/${n}`,{reply:F,note_id:m,update_id:D},"POST");U(R,"Reply added successfully"),_(""),A()}catch(i){ne(T,i.message),U(R,i.message,5e3,"error")}};async function g(n,m){try{V(!0),await S.callRawAPI(`/v4/api/records/notes/${m}`,{type:n},"PUT"),s(),V(!1),U(R,"Saved")}catch(D){ne(T,D.message),U(R,D.message,5e3,"error"),V(!1)}}const ee=()=>{x(!0)},re=async n=>{console.log(n),K(!0);try{const m=await S.callRawAPI(`/v3/api/custom/goodbadugly/updates/${n==null?void 0:n.update_id}/comments`,{comment:b,note_id:n.id},"POST");K(!1),c(),s(),C(),w(!1)}catch(m){K(!1),Q(m.message),ne(T,m.message),U(R,m.message,5e3,"error")}};async function ve(n){try{await new le().callRawAPI(`/v4/api/records/notes/${n}`,{},"DELETE"),U(R,"Deleted",3e3,"success"),s()}catch(m){ne(T,m.message),U(R,m.message,5e3,"error")}}const Ne=n=>{w(H===n?!1:n)},ye=r.useMemo(()=>e.jsx(fs,{data:j,report:!0,note_id:a.id,editorID:`editorjs-container-${a.id}`,afterEdit:c,setUpdated:x,updateSaved:ee,editing:f}),[f]);return e.jsxs("section",{className:"border-b-[2px] border-b-[#1f1d1a] pb-4",children:[e.jsxs("div",{className:"flex flex-row justify-between",children:[e.jsxs("div",{id:a.type,className:"flex w-full flex-col",children:[e.jsx("input",{className:`no-box-shadow w-full border-none pb-4 ${a.type==="Section title"?"bg-brown-main-bg":"bg-transparent"}  p-0 text-xl font-semibold ring-transparent`,defaultValue:a.type,readOnly:!ie,onBlur:n=>{let m;m&&clearTimeout(m),m=setTimeout(()=>g(n.target.value,a.id),500)}}),ye,e.jsxs("div",{className:"mt-5",children:[v.length!==0&&e.jsx("div",{className:"flex flex-row items-center gap-5 text-[14px] font-semibold"}),v==null?void 0:v.map(n=>{var m,D,A;return e.jsxs(e.Fragment,{children:[e.jsx("div",{children:e.jsx("div",{className:"flex flex-row items-end gap-2",children:e.jsxs("div",{className:"my-3 flex flex-col",children:[e.jsxs("div",{className:"flex flex-row items-center gap-2",children:[e.jsx("img",{className:"h-7 w-7 rounded-[50%] object-cover",src:"/default.png"}),e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("span",{className:"flex flex-row items-center gap-1 text-[14px] font-semibold capitalize text-[#1f1d1a]",children:[n.first_name+" "+n.last_name,((m=o==null?void 0:o.profile)==null?void 0:m.user_id)===n.user_id&&e.jsx(cs,{comment:n,refetch:C})]}),e.jsx("span",{className:"text-[12px] text-[#1f1d1a]",children:I(n.update_at).fromNow()})]})]}),e.jsx("span",{className:"mt-2",children:n.comment})]})})}),o.role==="investor"&&e.jsx(Ns,{commentId:n.id,noteId:a.id,updateId:N,replyComment:F,setReplyComment:_,handleReplyComment:E,refetchAll:s}),y&&y.some(i=>i.id===a.id&&i.comments&&i.comments.some(O=>O.id===n.id))&&e.jsx("div",{className:"my-5 ml-8 flex flex-col gap-2 border-l-2 border-black/40",children:[...(n==null?void 0:n.update_comment_replies)||[],...((A=(D=y.find(i=>i.id===a.id))==null?void 0:D.comments.find(i=>i.id===n.id))==null?void 0:A.update_comment_replies)||[]].map(i=>e.jsxs("div",{className:"mt-2 pl-4",children:[e.jsxs("div",{className:"flex flex-row items-center gap-2",children:[e.jsx("img",{className:"h-7 w-7 rounded-[50%] object-cover",src:"/default.png"}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-[12px] font-semibold capitalize text-[#1f1d1a]",children:i!=null&&i.first_name?(i==null?void 0:i.first_name)+" "+(i==null?void 0:i.last_name):"user"}),e.jsx("span",{className:"text-[10px] text-[#1f1d1a]",children:I(i==null?void 0:i.created_at).fromNow()})]})]}),e.jsx("span",{className:"mt-1 text-sm",children:i==null?void 0:i.reply})]},i.id))})]})})]}),o.role==="investor"&&e.jsxs("div",{className:"mt-3 flex h-[41px] w-full max-w-[174px] cursor-pointer flex-row items-center justify-center gap-3 rounded border-[2px] border-[#1f1d1a] font-iowan",onClick:()=>Ne(a.id),children:[e.jsx(ss,{className:"h-5 w-5"}),e.jsx("span",{children:"Add Comment"})]})]}),e.jsx(oe,{className:"relative",children:({open:n,close:m})=>e.jsxs(e.Fragment,{children:[e.jsx(oe.Button,{as:"div",className:"cursor-pointer",children:o.role!=="investor"&&Z&&e.jsx(ds,{size:20})}),e.jsx(as,{as:r.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(oe.Panel,{className:"absolute right-0 z-10 mt-1 w-screen max-w-[120px] transform rounded-md border-2 border-[#1F1D1A] bg-brown-main-bg  ",children:e.jsx("div",{className:"space-y-3 p-2",children:e.jsx("div",{onClick:async()=>{await ve(a==null?void 0:a.id),m()},className:"flex cursor-pointer flex-row items-center gap-2 border-b-[1px] border-b-gray-300 p-1 px-2",children:e.jsx("div",{className:"flex flex-col",children:e.jsx("span",{className:"font-iowan text-sm font-semibold text-red-500",children:"Delete"})})})})})})]})})]}),H===a.id&&e.jsxs("form",{className:"mt-7 space-y-3",children:[e.jsx("textarea",{name:"comment",id:"comment",cols:"10",rows:"5",className:"w-full rounded border border-[#1f1d1a] bg-transparent ",onChange:n=>u(n.target.value)}),e.jsx("div",{className:"flex flex-row justify-end",children:e.jsx("button",{className:"ml-auto h-[36px] w-full max-w-[120px] rounded bg-black font-iowan text-sm text-white",onClick:n=>{n.preventDefault(),n.stopPropagation(),re(a)},children:J?"Submitting...":"Send"})})]})]},a.id)},ks=ys,Cs=({questionId:a,updateId:h,questionAnswer:v,setQuestionAnswer:u,handleRespondQuestion:j,showResponseInput:b,setShowResponseInput:s})=>e.jsx("div",{className:"mt-2",children:b&&e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("textarea",{className:"w-full rounded-md border border-[#1f1d1a]   bg-transparent p-2",value:v,onChange:C=>u(C.target.value),placeholder:"Type your response here"}),e.jsx("button",{className:"self-end rounded-md bg-black px-4 py-2 text-white",onClick:()=>j(a,h,v,u,s),children:"Submit"})]})}),_s=({question:a,updateId:h,handleRespondQuestion:v})=>{var y;const[u,j]=r.useState(!1),[b,s]=r.useState(""),{dispatch:C,state:N}=r.useContext(Re);return e.jsxs("div",{className:"border-b-[1px] border-[#1F1D1A1A] ",children:[e.jsxs("div",{className:"md:start flex flex-col items-start justify-between p-2 md:flex-row",children:[e.jsx("span",{className:"overflow-hidden truncate text-ellipsis break-words text-[14px] font-[600] md:w-[50%]",children:a==null?void 0:a.question}),e.jsx("button",{className:"font-iowan  flex h-[36px] w-[174px] cursor-pointer flex-row items-center justify-center  gap-5 rounded border-[2px] border-[#1f1d1a] px-3 py-2 text-sm md:items-end",onClick:()=>j(!u),children:"Respond"})]},a.id),u&&N.role!=="collaborator"&&e.jsx(Cs,{questionId:a==null?void 0:a.question_id,updateId:h,questionAnswer:b,setQuestionAnswer:s,handleRespondQuestion:v,showResponseInput:u,setShowResponseInput:j}),(y=a==null?void 0:a.answers)!=null&&y.length?a==null?void 0:a.answers.map(o=>e.jsxs("div",{className:"mt-2 pl-4",children:[e.jsxs("div",{className:"flex flex-row items-center gap-2",children:[e.jsx("img",{className:"h-7 w-7 rounded-[50%] object-cover",src:(o==null?void 0:o.photo)||"/default.png"}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-[12px] font-semibold capitalize text-[#1f1d1a]",children:o!=null&&o.first_name?(o==null?void 0:o.first_name)+" "+(o==null?void 0:o.last_name):"user"}),e.jsxs("span",{className:"text-[10px] text-[#1f1d1a]",children:[" ",I(o==null?void 0:o.create_at).fromNow()]})]})]}),e.jsx("span",{className:"mt-1 text-sm leading-7",children:o==null?void 0:o.reply})]},o==null?void 0:o.id)):null]})},Ss=_s;function va(){var B,X,ce,Z,q,E,g,ee,re,ve,Ne,ye,n,m,D,A,i,O,Me,Fe,Oe,Ie,Ae,He,Pe,$e,Te,Le,Ve,Ee,Ye,Ue,Je,Be;const a=localStorage.getItem("token"),{update_id:h}=os();us(h);const[v,u]=r.useState(!0);r.useState(!1);const{state:j,dispatch:b}=r.useContext(Re),[s,C]=r.useState(null),[N,y]=r.useState([]),{allComments:o,refetch:T}=vs(h),[R,M]=r.useState(""),[Q,F]=r.useState(!1);r.useState({});const{dispatch:_}=r.useContext(es),[H,w]=r.useState();r.useState(!1);const[J,K]=r.useState(),[f,L]=r.useState({financialOverview:!1,investmentOverview:!1,tmhrOverview:!1,productOverview:!1,marketingOverview:!1});r.useState(!1),r.useState("");const ie=new le,V=is(),W=async(l,p,P,Y,z)=>{try{const $=await ie.callRawAPI(`/v3/api/custom/goodbadugly/updates/questions/answers/${l}`,{answer:P,update_id:p},"POST");U(_,"Response added successfully"),Y(""),c()}catch($){ne(b,$.message),U(_,$.message,5e3,"error")}};async function x(){var l,p,P,Y,z,$,G,me,de,xe,fe,pe,he,ue;u(!0);try{let se=function(d){return d==null?void 0:d.replace(/<[^>]*>/g,"")};const be=new le,t=await be.callRawAPI(`/v3/api/custom/goodbadugly/private/reports/${h}?token=${a}`,void 0,"GET");C(t.update),y((l=t==null?void 0:t.update)==null?void 0:l.notes);const ae=(P=(p=t==null?void 0:t.update)==null?void 0:p.notes)==null?void 0:P.map(d=>{const{blocks:we}=ge(d==null?void 0:d.content,{blocks:[]});if(we.length==0)return null;const De=Ce(we);return{title:d==null?void 0:d.type,item:De}}),_e=ae==null?void 0:ae.map(d=>se(d==null?void 0:d.item)).join(`
`),Se=[{role:"user",content:` Financial overview. MRR: ${(Y=t==null?void 0:t.update)==null?void 0:Y.mrr}, ARR: ${(z=t==null?void 0:t.update)==null?void 0:z.arr}, Cash: ${($=t==null?void 0:t.update)==null?void 0:$.cash}, Burnrate: ${(G=t==null?void 0:t.update)==null?void 0:G.burnrate}, Runway (months): ${(me=t==null?void 0:t.update)==null?void 0:me.burnrate}.: investment overview. Investment stage: ${(de=t==null?void 0:t.update)==null?void 0:de.investment_stage}, Invested to date: ${(xe=t==null?void 0:t.update)==null?void 0:xe.invested_to_date}, Fund managers on Cap Table: ${(fe=t==null?void 0:t.update)==null?void 0:fe.investors_on_cap_table}, Valuation of last round: ${(pe=t==null?void 0:t.update)==null?void 0:pe.valuation_at_last_round}, Date of last round: ${((he=t==null?void 0:t.update)==null?void 0:he.date_of_last_round)&&I((ue=t==null?void 0:t.update)==null?void 0:ue.date_of_last_round).format("MM/DD/YYYY")}.  notes: ${_e}. 
          summerize the above report. ignore all undefined values and don't say it in your response`}],te=await be.callRawAPI("/v3/api/custom/goodbadugly/ai/ask",{prompt:Se},"POST");w(te==null?void 0:te.data),u(!1)}catch(se){M(se.message)}u(!1)}async function c(){var l,p,P,Y,z,$,G,me,de,xe,fe,pe,he,ue;try{let se=function(d){return d==null?void 0:d.replace(/<[^>]*>/g,"")};const be=new le,t=await be.callRawAPI(`/v3/api/custom/goodbadugly/private/reports/${h}?token=${a}`,void 0,"GET");C(t.update),y((l=t==null?void 0:t.update)==null?void 0:l.notes);const ae=(P=(p=t==null?void 0:t.update)==null?void 0:p.notes)==null?void 0:P.map(d=>{const{blocks:we}=ge(d==null?void 0:d.content,{blocks:[]});if(we.length==0)return null;const De=Ce(we);return{title:d==null?void 0:d.type,item:De}}),_e=ae==null?void 0:ae.map(d=>se(d==null?void 0:d.item)).join(`
`),Se=[{role:"user",content:` Financial overview. MRR: ${(Y=t==null?void 0:t.update)==null?void 0:Y.mrr}, ARR: ${(z=t==null?void 0:t.update)==null?void 0:z.arr}, Cash: ${($=t==null?void 0:t.update)==null?void 0:$.cash}, Burnrate: ${(G=t==null?void 0:t.update)==null?void 0:G.burnrate}, Runway (months): ${(me=t==null?void 0:t.update)==null?void 0:me.burnrate}.: investment overview. Investment stage: ${(de=t==null?void 0:t.update)==null?void 0:de.investment_stage}, Invested to date: ${(xe=t==null?void 0:t.update)==null?void 0:xe.invested_to_date}, Fund managers on Cap Table: ${(fe=t==null?void 0:t.update)==null?void 0:fe.investors_on_cap_table}, Valuation of last round: ${(pe=t==null?void 0:t.update)==null?void 0:pe.valuation_at_last_round}, Date of last round: ${((he=t==null?void 0:t.update)==null?void 0:he.date_of_last_round)&&I((ue=t==null?void 0:t.update)==null?void 0:ue.date_of_last_round).format("MM/DD/YYYY")}.  notes: ${_e}. 
          summerize the above report. ignore all undefined values and don't say it in your response`}],te=await be.callRawAPI("/v3/api/custom/goodbadugly/ai/ask",{prompt:Se},"POST");w(te==null?void 0:te.data),u(!1)}catch(se){M(se.message)}}console.log(s==null?void 0:s.notes),r.useEffect(()=>{const l=p=>{(p.key==="F12"||p.ctrlKey&&p.shiftKey&&p.key==="I"||p.ctrlKey&&p.shiftKey&&p.key==="J")&&p.preventDefault()};return window.addEventListener("keydown",l),()=>{window.removeEventListener("keydown",l)}},[]),r.useEffect(()=>{x()},[]);const S=s!=null&&s.sent_at?I(s.sent_at).add(s.recipient_access??0,"days").diff(I(),"days"):0;if(!j.isAuthenticated&&!v){const l="https://goodbadugly.manaknightdigital.com/fundmanager/login",p=encodeURIComponent("/fundmanager/update_requests"),P=`${l}?redirect_uri=${p}`;return window.location.href=P,e.jsx("div",{className:"flex h-screen -scroll-mt-6 items-center justify-center overflow-y-auto scroll-smooth bg-brown-main-bg ",children:e.jsx("h2",{children:"Redirecting to Login ...."})})}if(v)return e.jsx("div",{className:"flex h-screen w-full items-center justify-center text-7xl text-gray-700 ",children:e.jsx(bs,{size:40})});if(R)return e.jsx("div",{className:"flex h-screen w-full items-center justify-center text-7xl text-gray-700 ",children:R});const k=[...(B=s==null?void 0:s.collaborators)==null?void 0:B.slice(0,4),{id:"member",...s==null?void 0:s.company}];return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"sh-screen -scroll-mt-6 overflow-y-auto scroll-smooth bg-brown-main-bg py-8 ",children:[e.jsx("div",{className:"mx-auto w-[96%] px-2 pt-6 md:px-[40px]  lg:px-[60px] xl:max-w-[1400px]",children:e.jsxs("div",{onClick:()=>{j.role==="investor"?V("/fundmanager/dashboard"):j.role==="collaborator"?V("/collaborator/dashboard"):j.role==="member"?V("/member/dashboard"):j.role==="stakeholder"&&V("/stakeholder/dashboard")},className:"mb-5 flex cursor-pointer items-center gap-3 ",children:[e.jsx(xs,{className:"h-6 w-6 md:h-10 md:w-10"}),e.jsx("p",{className:"text-base font-semibold",children:"Updatestack Dashboard"})]})}),e.jsxs("div",{className:"mx-auto w-[96%] px-2 pt-6 md:px-[40px]  lg:px-[60px] xl:max-w-[1400px]",children:[e.jsxs("div",{className:"flex w-full flex-col-reverse items-start justify-between gap-3 sm:flex-row",children:[e.jsxs("div",{className:"mt-[30px] flex w-full flex-wrap items-start md:mt-0 lg:flex-nowrap",children:[e.jsx("div",{className:"pr-3 sm:pr-8 xl:pr-12",children:e.jsx("img",{src:((X=s==null?void 0:s.company)==null?void 0:X.logo)||"/default.png",alt:"",className:"h-[60px] min-h-[60px] w-[60px] min-w-[40px] rounded-[50%] object-cover sm:h-[80px] sm:min-h-[80px] sm:w-[80px] sm:min-w-[80px] md:h-32 md:min-h-32 md:w-32 md:min-w-32"})}),e.jsxs("div",{className:"w-[100px] md:hidden",children:[e.jsx("h1",{className:"font-Inter text-xl font-semibold capitalize md:text-2xl",children:((ce=s==null?void 0:s.company)==null?void 0:ce.name)||"{{Company Name}}"}),e.jsx("a",{href:(Z=s==null?void 0:s.company)==null?void 0:Z.website,target:"_blank",rel:"noopener noreferrer",className:"mt-1 block w-fit font-Inter text-base font-semibold text-[#1f1d1a] underline sm:text-base md:mt-0",children:(q=s==null?void 0:s.company)==null?void 0:q.website}),e.jsxs("div",{className:"mt-3 flex gap-2 md:mt-3",children:[((E=s==null?void 0:s.company)==null?void 0:E.socials)&&JSON.parse((g=s==null?void 0:s.company)==null?void 0:g.socials).facebook&&e.jsx("a",{href:JSON.parse((ee=s==null?void 0:s.company)==null?void 0:ee.socials).facebook,target:"_blank",rel:"noopener noreferrer",children:e.jsx(Ge,{className:"h-5 w-5 sm:h-10 sm:w-10"})}),((re=s==null?void 0:s.company)==null?void 0:re.socials)&&JSON.parse((ve=s==null?void 0:s.company)==null?void 0:ve.socials).github&&e.jsx("a",{href:JSON.parse((Ne=s==null?void 0:s.company)==null?void 0:Ne.socials).github,target:"_blank",rel:"noopener noreferrer",children:e.jsx(Ke,{className:"h-5 w-5 sm:h-10 sm:w-10"})}),((ye=s==null?void 0:s.company)==null?void 0:ye.socials)&&JSON.parse((n=s==null?void 0:s.company)==null?void 0:n.socials).linkedin&&e.jsx("a",{href:JSON.parse((m=s==null?void 0:s.company)==null?void 0:m.socials).linkedin,target:"_blank",rel:"noopener noreferrer",children:e.jsx(Qe,{className:"h-5 w-5 sm:h-10 sm:w-10"})}),((D=s==null?void 0:s.company)==null?void 0:D.socials)&&JSON.parse(s==null?void 0:s.company.socials).x&&e.jsx("a",{href:JSON.parse(s==null?void 0:s.company.socials).x,target:"_blank",rel:"noopener noreferrer",children:e.jsx(We,{className:"h-5 w-5 sm:h-10 sm:w-10"})}),((A=s==null?void 0:s.company)==null?void 0:A.socials)&&JSON.parse(s==null?void 0:s.company.socials).youtube&&e.jsx("a",{href:JSON.parse(s==null?void 0:s.company.socials).youtube,target:"_blank",rel:"noopener noreferrer",children:e.jsx(Xe,{className:"h-5 w-5 sm:h-10 sm:w-10"})})]})]}),e.jsx("div",{className:"flex w-full items-start justify-between gap-4 md:gap-2",children:e.jsxs("div",{className:"w-full",children:[e.jsx("h1",{className:"hidden font-Inter text-xl font-semibold capitalize md:block md:text-2xl",children:((i=s==null?void 0:s.company)==null?void 0:i.name)||"{{Company Name}}"}),e.jsx("a",{href:(O=s==null?void 0:s.company)==null?void 0:O.website,target:"_blank",rel:"noopener noreferrer",className:"mt-5  hidden w-fit font-Inter text-base font-semibold text-[#1f1d1a] underline sm:text-base md:mt-0 md:block",children:(Me=s==null?void 0:s.company)==null?void 0:Me.website}),e.jsxs("div",{className:"mt-5 hidden gap-2 md:mt-3 md:flex ",children:[((Fe=s==null?void 0:s.company)==null?void 0:Fe.socials)&&JSON.parse((Oe=s==null?void 0:s.company)==null?void 0:Oe.socials).facebook&&e.jsx("a",{href:JSON.parse((Ie=s==null?void 0:s.company)==null?void 0:Ie.socials).facebook,target:"_blank",rel:"noopener noreferrer",children:e.jsx(Ge,{className:"h-5 w-5 sm:h-10 sm:w-10"})}),((Ae=s==null?void 0:s.company)==null?void 0:Ae.socials)&&JSON.parse((He=s==null?void 0:s.company)==null?void 0:He.socials).github&&e.jsx("a",{href:JSON.parse((Pe=s==null?void 0:s.company)==null?void 0:Pe.socials).github,target:"_blank",rel:"noopener noreferrer",children:e.jsx(Ke,{className:"h-5 w-5 sm:h-10 sm:w-10"})}),(($e=s==null?void 0:s.company)==null?void 0:$e.socials)&&JSON.parse((Te=s==null?void 0:s.company)==null?void 0:Te.socials).linkedin&&e.jsx("a",{href:JSON.parse((Le=s==null?void 0:s.company)==null?void 0:Le.socials).linkedin,target:"_blank",rel:"noopener noreferrer",children:e.jsx(Qe,{className:"h-5 w-5 sm:h-10 sm:w-10"})}),((Ve=s==null?void 0:s.company)==null?void 0:Ve.socials)&&JSON.parse(s==null?void 0:s.company.socials).x&&e.jsx("a",{href:JSON.parse(s==null?void 0:s.company.socials).x,target:"_blank",rel:"noopener noreferrer",children:e.jsx(We,{className:"h-5 w-5 sm:h-10 sm:w-10"})}),((Ee=s==null?void 0:s.company)==null?void 0:Ee.socials)&&JSON.parse(s==null?void 0:s.company.socials).youtube&&e.jsx("a",{href:JSON.parse(s==null?void 0:s.company.socials).youtube,target:"_blank",rel:"noopener noreferrer",children:e.jsx(Xe,{className:"h-5 w-5 sm:h-10 sm:w-10"})})]}),e.jsx("p",{className:"mt-2 max-w-[100%] overflow-hidden  text-ellipsis font-iowan text-[16px] font-semibold leading-6 sm:max-w-[400px] xl:max-w-[900px]",children:(Ye=s==null?void 0:s.company)==null?void 0:Ye.description})]})})]}),e.jsxs("div",{className:"flex w-full flex-row items-start justify-end gap-3 sm:w-auto md:justify-normal ",children:[(s==null?void 0:s.private_link_access)==1&&e.jsxs("p",{className:"flex items-center gap-2 whitespace-nowrap rounded bg-[#1f1d1a] px-2 py-3 font-iowan text-sm font-medium text-white sm:px-4 sm:text-base",children:[e.jsx(qe,{className:"h-4 w-4 ",strokeWidth:2}),"Private Update"]}),(s==null?void 0:s.private_link_access)==2&&e.jsxs("p",{className:"flex items-center gap-2 whitespace-nowrap rounded bg-[#1f1d1a] px-2 py-2 font-iowan text-sm  font-medium text-white sm:px-4 sm:text-base",children:[e.jsx(ws,{className:"h-4 w-4",strokeWidth:2}),"Public Update"]}),(s==null?void 0:s.private_link_access)==0&&e.jsxs("p",{className:"flex items-center gap-2 whitespace-nowrap rounded  bg-[#1f1d1a] px-2 py-2 font-iowan text-sm font-medium text-white sm:px-4 sm:text-base",children:[e.jsx(qe,{className:"h-4 w-4",strokeWidth:2}),"Private Update"]}),(s==null?void 0:s.private_link_access)==1?null:e.jsx(ms,{update_id:h})]})]}),e.jsx("hr",{className:"my-6 hidden border-[1px] border-[#1f1d1a] md:block"}),e.jsxs("h1",{className:"mt-2 mt-5 flex items-center gap-2 text-3xl font-bold xl:text-5xl",children:[e.jsx("span",{className:"text-[24px] font-semibold sm:text-[32px]",children:s==null?void 0:s.name})," ","-"," ",e.jsx("span",{className:"text-[22px] font-normal sm:text-[28px]",children:I(s==null?void 0:s.date).format("MMM DD, YYYY")})]}),e.jsxs("div",{className:"mt-8 flex flex-col flex-wrap items-start gap-3 gap-5 md:flex-row md:items-center md:justify-between md:gap-0",children:[e.jsxs("div",{className:"flex flex-row flex-wrap items-center gap-[25px]",children:[e.jsxs("p",{className:"flex flex-col items-center gap-2 font-iowan text-base font-semibold",children:["Contributors:"," ",e.jsx("div",{className:"flex -space-x-1",children:k.map(l=>e.jsx(oe,{className:"relative",children:({open:p})=>e.jsxs(e.Fragment,{children:[e.jsx(oe.Button,{onMouseEnter:()=>F(l==null?void 0:l.id),onMouseLeave:()=>F(!1),as:"div",className:"cursor-pointer",children:e.jsx("img",{className:"h-7 w-7 rounded-[50%] object-cover",src:l.photo||l.logo||"/default.png"})}),e.jsx(as,{as:r.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",show:l.id===Q,onMouseEnter:()=>F(l==null?void 0:l.id),onMouseLeave:()=>F(!1),className:" absolute z-[999999999999999999] max-w-[300px]",children:e.jsx(oe.Panel,{className:"absolute left-0 z-10 mt-3 w-fit -translate-x-1/2 transform whitespace-nowrap px-4",children:e.jsx("div",{className:"overflow-hidden rounded-lg bg-[#1f1d1a] p-4 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:e.jsxs("div",{className:"text-sm font-medium",children:[l.first_name||l.name," ",l.last_name||""]})})})})]})},l.id))})]}),e.jsx(gs,{data:s,summary:H})]}),e.jsxs("p",{className:"font-sem flex flex-col items-end gap-2 font-iowan text-base font-semibold",children:["Available for:",e.jsxs("span",{children:[S<1?"":`${S} days,  `,s!=null&&s.sent_at?I(s==null?void 0:s.sent_at).add(s==null?void 0:s.recipient_access,"days").diff(I().add(S,"days"),"hours"):0," ","hrs"]})]})]}),e.jsx("hr",{className:"my-5 hidden border-[1px] border-black/40 md:block"}),e.jsxs("section",{className:"mb-10 mt-[40px] flex flex-col items-center gap-4 md:mb-0 md:mt-4 md:flex-row md:items-start ",children:[e.jsxs("aside",{className:" block w-full max-w-[260px] flex-grow  ",children:[e.jsxs("div",{children:[e.jsx("div",{className:"border-2 border-[#1f1d1a]",children:e.jsxs("ul",{className:"divide-y divide-gray-900",children:[(Ue=s==null?void 0:s.notes)==null?void 0:Ue.map(l=>{const{blocks:p}=ge(l.content,{blocks:[]});return p.length==0?null:e.jsxs("li",{className:"line-clamp-2 flex flex-row items-center justify-between overflow-hidden truncate text-ellipsis whitespace-nowrap  break-words font-iowan text-[18px] font-bold font-semibold md:items-center",children:[" ",e.jsx("a",{className:"line-clamp-2 block overflow-hidden truncate text-ellipsis whitespace-nowrap break-words px-4 py-6",href:`#${l.type}`,children:l.type})]},l.id)}),e.jsxs("li",{className:"flex flex-row  items-center justify-between font-iowan text-[18px] font-semibold md:items-center",children:[e.jsx("a",{className:"block px-4 py-6",href:"#investor-asks",children:"Asks"}),e.jsx("div",{className:"mr-4 flex h-[20px] w-[20px] flex-row items-center justify-center rounded-full bg-[#990F3D] text-white",children:s==null?void 0:s.questions.length})]})]})}),e.jsx("div",{className:"mt-8 w-full text-center",children:e.jsx("button",{onClick:()=>{var l;window.location.href=(l=s==null?void 0:s.company)==null?void 0:l.contact_link},className:"h-[52px] w-full border-2 border-[#1f1d1a] font-iowan font-semibold text-black",children:"📆 Book a meeting"})})]}),e.jsxs("h5",{className:"hidden flex-col gap-2 font-iowan font-medium md:mt-[400px]  md:flex",children:[e.jsx("span",{children:" Automated by"}),e.jsxs("svg",{width:"235",height:"32",viewBox:"0 0 235 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M44.5426 3.15602V23.7534H6.3694V7.25965H14.0265H25.4559H41.0116V3.15402L2.86341 3.13623V3.15838H2.24316V27.8768H48.6688V3.15838L44.5426 3.15602Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M9.90039 16.8807V21.0041H41.0117V10.0083H25.4561H14.0266H9.90039V14.1318H36.8856V16.8807H9.90039Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M57.5254 16.1642V4.65234H61.4958V15.9798C61.4958 18.2857 62.9878 19.7463 65.0994 19.7463C67.2057 19.7463 68.6983 18.2857 68.6983 15.9798V4.65234H72.6687V16.1642C72.6687 20.4915 69.4289 23.5145 65.0994 23.5145C60.7652 23.5145 57.5254 20.4915 57.5254 16.1642Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M83.8299 8.46875C88.0879 8.46875 91.237 11.6764 91.237 15.9917C91.237 20.302 88.0879 23.5363 83.8299 23.5363C82.3658 23.5363 81.0365 23.1014 79.9409 22.3374V28.2447H76.1182V8.8718H78.6459L79.2986 10.1664C80.4994 9.09513 82.067 8.46934 83.8305 8.46934L83.8299 8.46875ZM87.3666 15.9911C87.3666 13.7549 85.7386 12.0886 83.5178 12.0886C81.2975 12.0886 79.6642 13.7596 79.6642 15.9911C79.6642 18.2232 81.2975 19.894 83.5178 19.894C85.7386 19.894 87.3666 18.2279 87.3666 15.9911Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M108.182 3.73926V23.1119H105.359L104.908 21.9264C103.721 22.9433 102.193 23.5368 100.48 23.5368C96.1903 23.5368 93.0459 20.3025 93.0459 15.9922C93.0459 11.677 96.1903 8.46928 100.48 8.46928C101.94 8.46928 103.265 8.89632 104.359 9.64886V3.73926H108.182ZM104.645 15.9922C104.645 13.7601 103.012 12.0894 100.791 12.0894C98.5705 12.0894 96.9424 13.7554 96.9424 15.9922C96.9424 18.229 98.5705 19.8951 100.791 19.8951C103.012 19.8951 104.645 18.2191 104.645 15.9922Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M126.251 8.85033V23.0907H123.254L122.926 21.8163C121.712 22.887 120.128 23.5146 118.344 23.5146C114.091 23.5146 110.928 20.2808 110.928 15.9705C110.928 11.6769 114.091 8.46924 118.344 8.46924C120.159 8.46924 121.767 9.11347 122.989 10.2108L123.398 8.85033H126.251ZM122.505 15.9705C122.505 13.7337 120.877 12.0677 118.656 12.0677C116.435 12.0677 114.802 13.7384 114.802 15.9705C114.802 18.2027 116.435 19.8734 118.656 19.8734C120.877 19.8734 122.505 18.2073 122.505 15.9705Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M138.57 19.6836V23.1117H135.854C132.743 23.1117 130.832 21.1869 130.832 18.0269V11.9669H128.275V11.1347L133.86 5.15576H134.59V8.87158H138.492V11.9669H134.655V17.5165C134.655 18.8921 135.44 19.6836 136.833 19.6836L138.57 19.6836Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M154.698 17.0703H143.997C144.33 19.0138 145.598 20.0839 147.452 20.0839C148.781 20.0839 149.871 19.4511 150.452 18.4319H154.469C153.441 21.5363 150.735 23.5144 147.452 23.5144C143.278 23.5144 140.119 20.259 140.119 15.9914C140.119 11.7028 143.256 8.46875 147.452 8.46875C151.794 8.46875 154.772 11.8395 154.772 15.9411C154.772 16.3175 154.746 16.6939 154.698 17.0703ZM144.098 14.4407H150.95C150.409 12.6997 149.168 11.7555 147.452 11.7555C145.751 11.7555 144.542 12.7553 144.098 14.4407Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M156.726 17.13H160.722C160.722 18.9137 162.186 19.7748 163.807 19.7748C165.299 19.7748 166.758 18.981 166.758 17.6047C166.758 16.174 165.087 15.7806 163.1 15.3135C160.336 14.628 156.98 13.8196 156.98 9.84266C156.98 6.30187 159.57 4.30713 163.628 4.30713C167.841 4.30713 170.236 6.57262 170.236 10.2501H166.318C166.318 8.66074 165.016 7.91523 163.533 7.91523C162.248 7.91523 160.946 8.46404 160.946 9.6726C160.946 10.9716 162.538 11.365 164.478 11.8324C167.272 12.5446 170.78 13.4221 170.78 17.5573C170.78 21.5439 167.627 23.4786 163.833 23.4786C159.624 23.4786 156.726 21.0978 156.726 17.13Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M182.162 19.6836V23.1117H179.446C176.335 23.1117 174.424 21.1869 174.424 18.0269V11.9669H171.867V11.1347L177.452 5.15576H178.182V8.87158H182.083V11.9669H178.247V17.5165C178.247 18.8921 179.032 19.6836 180.425 19.6836L182.162 19.6836Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M199.033 8.85033V23.0907H196.037L195.708 21.8163C194.494 22.887 192.91 23.5146 191.126 23.5146C186.874 23.5146 183.71 20.2808 183.71 15.9705C183.71 11.6769 186.874 8.46924 191.126 8.46924C192.942 8.46924 194.549 9.11347 195.772 10.2108L196.18 8.85033H199.033ZM195.287 15.9705C195.287 13.7337 193.659 12.0677 191.438 12.0677C189.218 12.0677 187.584 13.7384 187.584 15.9705C187.584 18.2027 189.218 19.8734 191.438 19.8734C193.659 19.8734 195.287 18.2073 195.287 15.9705Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M209.283 23.5141C205.029 23.5141 201.775 20.2154 201.775 15.9484C201.775 11.6811 205.008 8.46875 209.304 8.46875C212.965 8.46875 215.807 10.8062 216.494 14.311H212.698C212.079 12.9113 210.806 12.0889 209.283 12.0889C207.209 12.0889 205.65 13.7649 205.65 15.97C205.65 18.1758 207.235 19.8946 209.283 19.8946C210.827 19.8946 212.049 19.0293 212.694 17.5067H216.542C215.876 21.0864 212.981 23.5147 209.283 23.5147L209.283 23.5141Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M227.762 23.1119L223.237 16.2996V23.1119H219.414V3.73926H223.237V15.199L227.481 8.87174H231.884L227.101 15.6527L232.393 23.1119H227.762Z",fill:"#1F1D1A"})]})]})]}),e.jsxs("div",{className:"min-h-[640px]  flex-grow border-0 border-l-[#1f1d1a] md:min-h-[640px]  md:border-l-[2px]  md:px-5 md:pl-5",children:[e.jsxs("div",{className:"grid grid-cols-2 border-b-[2px] border-b-[#1f1d1a] pb-5",children:[(s==null?void 0:s.show_financial_metrics)==1?e.jsx("div",{className:"rounded-sm border-r border-r-gray-900 p-2 md:p-4",children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex flex-col items-start justify-between md:flex-row md:items-center",children:[e.jsx("h4",{className:"mb-3 text-[14px] font-semibold sm:text-[16px] md:mb-0 xl:text-[18px] 2xl:text-[20px] ",children:"Financial Overview"}),e.jsxs("div",{className:"flex cursor-pointer flex-row items-center gap-3",onClick:()=>L({...f,financialOverview:!f.financialOverview}),children:[e.jsx("span",{className:"font-iowan font-bold underline",children:"Expand"}),e.jsx(je,{})]})]}),f.financialOverview?e.jsxs("ul",{className:"mt-4",children:[e.jsxs("li",{className:"flex flex-col flex-col gap-3 gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:flex-row sm:items-center sm:justify-between sm:gap-0 sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"MRR:"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s==null?void 0:s.mrr})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"ARR:"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s==null?void 0:s.arr})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"Cash:"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s==null?void 0:s.cash})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"Burnrate:"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s==null?void 0:s.burnrate})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"Runway (months):"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s==null?void 0:s.burnrate})]})]}):null]})}):null,(s==null?void 0:s.show_investment_metrics)==1?e.jsx("div",{className:"",children:e.jsxs("div",{className:"rounded-sm p-4",children:[e.jsxs("div",{className:"flex flex-col items-start justify-between md:flex-row md:items-center",children:[e.jsx("h4",{className:"mb-3 whitespace-nowrap text-[14px] font-semibold sm:text-[16px] md:mb-0 md:whitespace-normal lg:whitespace-nowrap xl:text-[18px] 2xl:text-[20px] ",children:"Investment Overview"}),e.jsxs("div",{className:"flex cursor-pointer flex-row items-center gap-3",onClick:()=>L({...f,investmentOverview:!f.investmentOverview}),children:[e.jsx("span",{className:"font-iowan font-bold underline",children:"Expand"}),e.jsx(je,{})]})]}),f.investmentOverview?e.jsxs("ul",{className:"mt-4",children:[e.jsxs("li",{className:"flex flex-col flex-col gap-3 gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:flex-row sm:items-center sm:justify-between sm:gap-0 sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"Investment stage:"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s==null?void 0:s.investment_stage})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"Invested to date:"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s==null?void 0:s.invested_to_date})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  whitespace-nowrap font-medium",children:"Fund managers on Cap Table:"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s==null?void 0:s.investors_on_cap_table})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"Valuation of last round:"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s==null?void 0:s.valuation_at_last_round})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"Date of last round:"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s!=null&&s.date_of_last_round?I(s==null?void 0:s.date_of_last_round).format("MM/DD/YYYY"):"N/A"})]})]}):null]})}):null,(s==null?void 0:s.show_hr_metric)==1?e.jsx("div",{className:"rounded-sm border-r border-r-gray-900 p-2 md:p-4",children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"flex flex-col items-start justify-between md:flex-row md:items-center",children:[e.jsx("h4",{className:"mb-3 whitespace-nowrap text-[14px] font-semibold sm:text-[16px] md:mb-0 md:whitespace-normal lg:whitespace-nowrap xl:text-[18px] 2xl:text-[20px] ",children:"Team/HR Overview"}),e.jsxs("div",{className:"flex cursor-pointer flex-row items-center gap-3",onClick:()=>L({...f,tmhrOverview:!f.tmhrOverview}),children:[e.jsx("span",{className:"font-iowan font-bold underline",children:"Expand"}),e.jsx(je,{})]})]}),f.tmhrOverview?e.jsxs("ul",{className:"mt-4",children:[e.jsxs("li",{className:"flex flex-col flex-col gap-3 gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:flex-row sm:items-center sm:justify-between sm:gap-0 sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"Headcount:"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s.headcount})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"Turnover rate (annual):"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s.turnover})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"Retention rate (annual):"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s.retention_hr})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"Employee satisfaction (employee NPS):"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s.satisfaction})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"Revenue per employee:"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s.revenue_per_employee})]})]}):null]})}):null,(s==null?void 0:s.show_product_metric)==1?e.jsx("div",{children:e.jsxs("div",{className:"rounded-sm p-4",children:[e.jsxs("div",{className:"flex flex-col items-start justify-between md:flex-row md:items-center",children:[e.jsx("h4",{className:"mb-3 text-[14px] font-semibold sm:text-[16px] md:mb-0 xl:text-[18px] 2xl:text-[20px] ",children:"Product Overview"}),e.jsxs("div",{className:"flex cursor-pointer flex-row items-center gap-3",onClick:()=>L({...f,productOverview:!f.productOverview}),children:[e.jsx("span",{className:"font-iowan font-bold underline",children:"Expand"}),e.jsx(je,{})]})]}),f.productOverview?e.jsxs("ul",{className:"mt-4",children:[e.jsxs("li",{className:"flex flex-col flex-col gap-3 gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:flex-row sm:items-center sm:justify-between sm:gap-0 sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"New Product Releases:"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s.npr})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"New Product Sales:"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s.nps})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"ROI:"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s.roi})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"R&D (as a % of sales) :"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s.rd})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"On-time delivery:"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s.on_time_delivery})]})]}):null]})}):null,(s==null?void 0:s.show_marketing_metrics)==1?e.jsx("div",{className:"rounded-sm border-r border-r-gray-900 p-2 md:p-4",children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"flex flex-col items-start justify-between md:flex-row md:items-center",children:[e.jsx("h4",{className:"mb-3 text-[14px] font-semibold sm:text-[16px] md:mb-0 xl:text-[18px] 2xl:text-[20px] ",children:"Marketing Overview"}),e.jsxs("div",{className:"flex cursor-pointer flex-row items-center gap-3",onClick:()=>L({...f,marketingOverview:!f.marketingOverview}),children:[e.jsx("span",{className:"font-iowan font-bold underline",children:"Expand"}),e.jsx(je,{})]})]}),f.marketingOverview?e.jsxs("ul",{className:"mt-4",children:[e.jsxs("li",{className:"flex flex-col flex-col gap-3 gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:flex-row sm:items-center sm:justify-between sm:gap-0 sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"Customer Acquisition Cost (CAC):"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s.cac})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"Activations / Daily active users:"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s.marketing_activations})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"Retention / Churn rate (Monthly):"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s.churn_rate})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"Annual contract value (ACV):"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s.acv})]}),e.jsxs("li",{className:"flex flex-col items-start justify-start gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0",children:[e.jsx("span",{className:"font-iowan-regular  font-medium",children:"Customer Satisfaction Score (CSAT/NPS):"})," ",e.jsx("span",{className:"block sm:ml-auto",children:s.csat})]})]}):null]})}):null]}),e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsxs("div",{className:"mt-10 space-y-10",children:[(Je=s==null?void 0:s.notes)==null?void 0:Je.map((l,p)=>{const P=o.filter(G=>(G==null?void 0:G.note_id)===(l==null?void 0:l.id)),Y=ge(l.content,{blocks:[{id:"zbGZFPM-iI",type:"paragraph",data:{text:""}}]}),{blocks:z}=Y;if(z.length==0)return null;const $=Ce(z);return e.jsx(ks,{html:$,note:l,data:Y,update:N,update_id:h,refetchAll:c,comment:J,setComment:K,refetchAllComment:T,userCommentOnNote:P})}),e.jsxs("section",{id:"investor-asks",className:"bg-[#F2DFCE]  p-5 ",children:[e.jsx("h4",{className:"text-lg font-bold",children:"Asks"}),e.jsx("div",{className:"flex flex-col gap-4",children:(s==null?void 0:s.questions.length)>0?(Be=s==null?void 0:s.questions)==null?void 0:Be.map(l=>e.jsx(Ss,{question:l,updateId:h,handleRespondQuestion:W},l.id)):e.jsx("span",{children:"Empty Questions"})})]})]}),e.jsx("hr",{className:"my-8 hidden border-[2px] border-[#1f1d1a] md:block"}),e.jsxs("div",{className:"bg-[#F2DFCE] p-1 p-3 px-3",children:[e.jsx("span",{className:"text-[18px] font-bold",children:"Summary"}),e.jsx("p",{children:e.jsx("p",{className:"font-regular mt-2 font-iowan text-[16px] font-medium leading-7",dangerouslySetInnerHTML:{__html:H==null?void 0:H.content}})})]})]})]})]})]})]})})}export{va as default};
