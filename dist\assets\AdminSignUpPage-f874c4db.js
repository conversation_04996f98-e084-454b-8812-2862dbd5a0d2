import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as j,r as P,u as A,b as $,L as F}from"./vendor-4cdf2bd1.js";import{u as G}from"./react-hook-form-a383372b.js";import{o as I}from"./yup-0917e80c.js";import{c as _,a as y}from"./yup-342a5df4.js";import{M as q,A as D,G as z,I as B,s as N}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const M="/assets/login-new-bg-b92ed446.png";let O=new q;const de=()=>{var m,c,d,x;const v=_({email:y().email().required(),password:y().required()}).required(),{dispatch:k}=j.useContext(D),{dispatch:i}=j.useContext(z),[r,a]=P.useState(!1),S=A(),L=new URLSearchParams(S.search).get("redirect_uri"),R=$(),{register:l,handleSubmit:C,setError:n,formState:{errors:t}}=G({resolver:I(v)}),E=async p=>{var u,g,h,f;try{a(!0);const s=await O.register(p.email,p.password,"admin");if(!s.error)k({type:"LOGIN",payload:s}),N(i,"Succesfully Registered",4e3,"success"),R(L??"/admin/dashboard");else if(a(!1),s.validation){const b=Object.keys(s.validation);for(let o=0;o<b.length;o++){const w=b[o];n(w,{type:"manual",message:s.validation[w]})}}}catch(s){a(!1),console.log("Error",s),N(i,s==null?void 0:s.message,4e3,"error"),n("email",{type:"manual",message:(g=(u=s==null?void 0:s.response)==null?void 0:u.data)!=null&&g.message?(f=(h=s==null?void 0:s.response)==null?void 0:h.data)==null?void 0:f.message:s==null?void 0:s.message})}};return e.jsx("div",{className:"m-auto h-screen max-h-screen min-h-screen",children:e.jsxs("div",{className:"flex h-full max-h-full min-h-full w-full justify-center",children:[e.jsxs("section",{className:"flex w-full flex-col items-center justify-center bg-brown-main-bg md:w-1/2",children:[e.jsxs("form",{onSubmit:C(E),className:"mt-[9.375rem] flex w-full max-w-md flex-col px-6",children:[e.jsx("h1",{className:"mb-8 text-center text-3xl font-semibold md:text-5xl md:font-bold",children:"Register"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",autoComplete:"off",placeholder:"Email",...l("email"),className:`focus:shadow-outline mb-3 w-full resize-none appearance-none rounded border-2 bg-transparent p-2 px-4 py-2 leading-tight  text-gray-700 shadow focus:outline-none active:outline-none ${(m=t.email)!=null&&m.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(c=t.email)==null?void 0:c.message})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"password",children:"Password"}),e.jsx("input",{autoComplete:"off",type:"password",name:"password",placeholder:"********",...l("password"),className:`focus:shadow-outline mb-3 w-full flex-grow appearance-none rounded border-2 p-2 px-4 py-2 leading-tight text-[#1f1d1a] shadow focus:outline-none active:outline-none ${(d=t.password)!=null&&d.message?"border-red-500":""}`}),e.jsx("button",{type:"button",className:"absolute right-1 top-[20%]",children:e.jsx("img",{src:"/invisible.png",alt:"",className:"mr-2 w-6"})}),e.jsx("p",{className:"text-xs italic text-red-500",children:(x=t.password)==null?void 0:x.message})]}),e.jsx(B,{type:"submit",className:"flex items-center justify-center rounded bg-gradient-to-l from-[#33d4b7_9.11%] to-[#0d9895_69.45%] py-2 tracking-wide text-white outline-none  focus:outline-none",loading:r,disabled:r,children:e.jsx("span",{children:"Register"})})]}),e.jsx("div",{className:"oauth flex w-full max-w-md grow flex-col gap-4 px-6 text-[#344054]",children:e.jsx("div",{children:e.jsxs("h3",{className:"mt-5 text-center text-sm normal-case text-gray-800",children:["Already have an account?"," ",e.jsxs(F,{className:"my-text-gradient mb-8 self-end text-sm font-semibold",to:"/admin/login",children:["Login"," "]})," "]})})}),e.jsxs("p",{className:"my-5 h-10 text-center text-xs text-gray-500",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]}),e.jsx("section",{className:"hidden w-1/2 md:block",style:{backgroundImage:`url(${M})`,backgroundSize:"cover",backgroundPosition:"center center"}})]})})};export{de as default};
