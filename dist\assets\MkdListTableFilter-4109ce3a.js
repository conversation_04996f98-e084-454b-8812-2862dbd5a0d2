import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as p,R as k}from"./vendor-4cdf2bd1.js";import{bp as L,F as I,bq as A,n as y,L as j,I as P,aa as B}from"./index-f2ad9142.js";import q from"./SetColumns-98262f06.js";import{M as U}from"./index-d526f96e.js";import{_ as M}from"./qr-scanner-cf010ec4.js";import{R as W}from"./index.esm-3e7472af.js";import{S as T}from"./index-f1d75e77.js";import{A as R}from"./AddButton-51d1b2cd.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./MkdInput-d37679e9.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./index-afef2e72.js";import"./lucide-react-0b94883e.js";import"./react-icons-36ae72b7.js";const w={ROWS:"rows",COLUMNS:"columns",FILTER:"filter",SORT:"sort"},H=({columns:i=[],columnData:t=null,selectedOptions:d=[],setOpenFilter:f=null,setOpenColumns:r=null,display:a=[w.FILTER]})=>e.jsxs("div",{className:"flex w-fit items-center justify-start gap-3",children:[a.includes(w.ROWS)?e.jsxs("button",{type:"button",className:"flex items-center gap-2",children:[e.jsx(L,{}),e.jsx("span",{children:"2000/2005"}),e.jsx("span",{children:"Rows"})]}):null,a.includes(w.COLUMNS)?e.jsxs("button",{type:"button",onClick:()=>r&&r(),className:"flex items-center gap-2",children:[e.jsx(L,{}),e.jsxs("span",{children:[i.filter(h=>!["Row","Action"].includes(h==null?void 0:h.header)&&(h==null?void 0:h.selected_column)).length,"/",i.filter(h=>!["Row","Action"].includes(h==null?void 0:h.header)).length]}),e.jsx("span",{children:"Columns"})]}):null,a.includes(w.FILTER)?e.jsxs("button",{type:"button",className:"flex cursor-pointer items-center justify-between gap-2 rounded-md px-3 py-1",onClick:()=>f&&f(),children:[e.jsx(I,{}),e.jsx("span",{className:"grow",children:"Filters"}),(d==null?void 0:d.length)>0&&e.jsx("span",{className:"flex !h-6 !w-6 items-center justify-center rounded-full bg-gray-800 text-start  text-white",children:(d==null?void 0:d.length)>0?d==null?void 0:d.length:null})]}):null,a.includes(w.SORT)?e.jsxs("button",{type:"button",className:"flex items-center gap-2",children:[e.jsx(A,{}),e.jsx("span",{children:"Sort"})]}):null]}),J=p.lazy(()=>M(()=>import("./ModalSidebarHeader-786e0b1a.js"),["assets/ModalSidebarHeader-786e0b1a.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/AddButton-51d1b2cd.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"])),$=p.lazy(()=>M(()=>import("./MkdDebounceInput-279c13be.js"),["assets/MkdDebounceInput-279c13be.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"])),G=({selectedOptions:i=[],columns:t=[],onColumnClick:d,setShowFilterOptions:f})=>e.jsx("div",{className:"absolute top-[-2000%] z-10 m-auto h-[31.25rem] max-h-[31.25rem] min-h-[31.25rem] w-[12.5rem] min-w-[12.5rem] max-w-[12.5rem] overflow-y-auto bg-white p-2 text-gray-600 opacity-0  shadow-md transition-all hover:top-[80%] hover:opacity-100 focus:top-[80%] focus:opacity-100 peer-focus:top-[80%] peer-focus:opacity-100 peer-focus-visible:top-[80%] peer-focus-visible:opacity-100",children:t.map(r=>{if(r!=null&&r.hasOwnProperty("isFilter")&&r.isFilter&&r.hasOwnProperty("selected_column")&&(r!=null&&r.selected_column))return e.jsx("button",{type:"button",className:"h-[2.25rem] w-full cursor-pointer text-left font-inter text-[.875rem] font-[400] capitalize leading-[1.25rem] tracking-[-0.006em] text-black",onClick:()=>{r.join?d(r==null?void 0:r.header):d(r==null?void 0:r.accessor)},children:y(r==null?void 0:r.header,{casetype:"capitalize",separator:""})},r==null?void 0:r.header);if(!(r!=null&&r.hasOwnProperty("isFilter"))&&(r!=null&&r.hasOwnProperty("selected_column"))&&!["row","action","photo","image","file","note","files","photos","images","image","thumbnial","thumbnails"].includes(r==null?void 0:r.header.toLowerCase())&&r!=null&&r.selected_column)return e.jsx("button",{type:"button",className:" h-[2.25rem] w-full cursor-pointer text-left font-inter text-[.875rem] font-[400] capitalize leading-[1.25rem] tracking-[-0.006em] text-black",onClick:()=>{r.join?d((r==null?void 0:r.filter_field)||(r==null?void 0:r.header)):d(r==null?void 0:r.accessor)},children:y(r.header,{casetype:"capitalize",separator:""})},r.header)}).filter(Boolean)}),K=({columnData:i=null,option:t=null,setOptionValue:d=null})=>e.jsxs(e.Fragment,{children:[["user"].includes(i==null?void 0:i.join)?e.jsx(T,{table:"user",className:"flex w-full flex-col items-start ",uniqueKey:"id",displaySeparator:"-",label:y(i==null?void 0:i.accessor,{casetype:"capitalize",separator:" "}),display:[i==null?void 0:i.accessor],placeholder:i==null?void 0:i.accessor,filter:["role,cs,user","is_company,eq,1"],onSelect:(f,r)=>{r?d("value","",t==null?void 0:t.uid):d("value",f==null?void 0:f.id,t==null?void 0:t.uid)},value:t==null?void 0:t.value}):null,["warehouse","warehouse_location","location_type","campaign","division","rate_card"].includes(i==null?void 0:i.join)?e.jsx(T,{className:"flex w-full flex-col items-start ",uniqueKey:"id",displaySeparator:"-",table:i==null?void 0:i.join,label:y(i==null?void 0:i.accessor,{casetype:"capitalize",separator:" "}),display:[i==null?void 0:i.accessor],placeholder:i==null?void 0:i.accessor,onSelect:(f,r)=>{r?d("value","",t==null?void 0:t.uid):d("value",f==null?void 0:f.id,t==null?void 0:t.uid)},value:t==null?void 0:t.value}):null]}),Q=p.memo(K),X=({onSubmit:i,columns:t=[],selectedOptions:d=[],onColumnClick:f=null,setOptionValue:r=null,setSelectedOptions:a=null,onOptionValueChange:h=null,onClose:F})=>{const[_,v]=p.useState(!1);return e.jsxs("div",{className:"filter-form-holder  z-[9999999] grid h-full max-h-full min-h-full w-full min-w-full max-w-full grid-cols-1 grid-rows-[auto_1fr_auto_auto] overflow-hidden rounded-md bg-white p-5 shadow-xl",children:[e.jsxs("div",{className:"relative flex items-center justify-end",children:[e.jsx(R,{type:"button",onClick:()=>v(s=>!s),className:"!shadow-0 !text-sub-500 peer !h-fit !max-h-fit !min-h-fit w-fit  !border-0 !bg-white !p-0 !py-0 font-[700] !underline",children:"Add Filter"}),e.jsx(j,{children:e.jsx(G,{onColumnClick:s=>f&&f(s),setShowFilterOptions:v,columns:t,selectedOptions:d})})]}),e.jsx("div",{className:"overflow-y-auto",children:e.jsx("div",{className:"!h-full !max-h-full !min-h-full w-full overflow-y-auto",children:d==null?void 0:d.map((s,c)=>e.jsxs("div",{className:"mb-2 grid w-full grid-cols-[1fr_auto]  justify-between gap-2 text-gray-600",children:[t!=null&&t.length?e.jsx(e.Fragment,{children:t.map((l,b)=>l!=null&&l.selected_column&&(l==null?void 0:l.accessor)===(s==null?void 0:s.accessor)||(l==null?void 0:l.header)===(s==null?void 0:s.accessor)||(l==null?void 0:l.filter_field)===(s==null?void 0:s.accessor)?l!=null&&l.mappingExist?e.jsx(e.Fragment,{children:e.jsxs("div",{className:"grid w-full grid-cols-1 items-start justify-start",children:[e.jsx("label",{className:"mb-2 block cursor-pointer text-left text-sm font-bold text-gray-700",htmlFor:s==null?void 0:s.uid,children:y(l==null?void 0:l.accessor,{casetype:"capitalize",separator:"space"})}),e.jsxs("select",{className:"!border-soft-200 !h-[3rem] !max-h-[3rem] !min-h-[3rem] appearance-none rounded-md border outline-0 focus:border-primary focus:ring-primary",onChange:x=>{r&&r("value",x.target.value,s==null?void 0:s.uid)},value:s==null?void 0:s.value,children:[e.jsx("option",{value:"",selected:!(s!=null&&s.value)}),Object.keys(l==null?void 0:l.mappings).map((x,n)=>e.jsx("option",{value:x,selected:x===(s==null?void 0:s.value),children:l==null?void 0:l.mappings[x]},n))]})]})}):l!=null&&l.join?e.jsx("div",{className:"flex w-full items-end justify-start",children:e.jsx(Q,{columnData:l,option:s,setOptionValue:r})},b):e.jsx("div",{className:"flex w-full items-end justify-start  !px-[.0625rem]",children:e.jsx(j,{children:e.jsx($,{type:"text",placeholder:"Enter value",label:y(l==null?void 0:l.accessor,{casetype:"capitalize",separator:" "}),setValue:x=>{r&&r("value",x,s==null?void 0:s.uid)},value:s==null?void 0:s.value,showIcon:!1,className:"!border-soft-200 !h-[3rem] !max-h-[3rem] !min-h-[3rem] !w-full !min-w-full !max-w-full !rounded-md !border !px-3 !py-2 !leading-tight !text-gray-700 !outline-none focus:border-primary focus:ring-primary",onReady:x=>{}})})},b):null)}):null,e.jsx(W,{className:"!text-sub-500 cursor-pointer self-end text-2xl",onClick:()=>{a(l=>l.filter(b=>b.uid!==(s==null?void 0:s.uid)))}})]},c))})}),e.jsxs("div",{className:"mt-5  flex w-full  gap-5",children:[e.jsx(R,{type:"button",onClick:()=>F(),className:"!border-soft-200 !text-sub-500 !grow self-end !bg-transparent font-bold",children:"Cancel"}),e.jsx(P,{type:"button",onClick:()=>{i&&(F(),i())},className:"!grow self-end rounded px-4 py-2 font-bold capitalize text-white",children:"Apply and Close"})]}),e.jsx("div",{className:"flex items-center justify-center",children:e.jsx(R,{type:"button",onClick:()=>a(()=>[]),disabled:(d==null?void 0:d.length)===0,className:"!shadow-0 !text-sub-500 w-fit !border-0 !bg-white font-[700] !underline",children:"Clear all Filters"})})]})},Y=p.memo(X),Le=({columns:i,table:t="",onSubmit:d,columnData:f,columnId:r=0,setColumns:a,setColumnId:h,searchField:F,setColumnData:_,onColumnClick:v,setOptionValue:s,selectedOptions:c,columnModel:l="",setSelectedOptions:b,filterDisplays:x=[],setFilterConditions:n,onOptionValueChange:z})=>{const[E,N]=k.useState(!1),[u,C]=k.useState(!1);return k.useState(!1),e.jsx(e.Fragment,{children:e.jsxs("div",{className:"relative flex w-fit items-center justify-between rounded bg-white",children:[e.jsx("div",{className:"flex w-full flex-col items-start justify-between gap-4 text-gray-700 md:flex-row  md:items-center",children:e.jsx(j,{children:e.jsx(H,{columns:f==null?void 0:f.columns,columnData:f,selectedOptions:c,display:[w.FILTER,...x],setOpenColumns:()=>C(!0),setOpenFilter:()=>N(g=>!g)})})}),e.jsx(j,{children:e.jsx(U,{isModalActive:E,closeModalFn:()=>N(!1),customMinWidthInTw:"md:!w-[25%] !w-full ",showHeader:!0,title:e.jsxs("div",{className:"flex items-center gap-2 font-inter text-[1.125rem] font-bold leading-[1.5rem] text-[#18181B]",children:[e.jsx(I,{})," Filter"]}),side:"left",headerClassName:"bg-white text-black",headerContentClassName:"text-black",closePosition:2,headerContent:e.jsx(J,{onToggleModal:()=>N(!1),cancelText:e.jsx(B,{className:"!h-[.755rem] !w-[.755rem]"})}),classes:{modalBody:"bg-white"},children:e.jsx(j,{children:e.jsx(Y,{onSubmit:d,columns:f==null?void 0:f.columns,onColumnClick:v,setOptionValue:s,selectedOptions:c,setSelectedOptions:b,onOptionValueChange:z,onClose:()=>N(!1)})})})}),e.jsx(j,{children:e.jsx(j,{children:e.jsx(q,{isOpen:u,columnModel:l,columns:f==null?void 0:f.columns,onClose:()=>C(!1),columnData:f,onUpdate:g=>{_(S=>({...S,...g}))},onSuccess:g=>{C(!1),_(S=>({...S,...g}))}})})})]})})};export{Le as default};
