import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{R as r,b as l,h as m}from"./vendor-4cdf2bd1.js";import{A as p,G as u,M as d,c as g}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const E=()=>{const{dispatch:s}=r.useContext(p);r.useContext(u);const e=l(),[i,h]=m(),n=async()=>{let c=new d;try{let o=i.get("token")??null;const a=await c.magicLoginVerify(o);a.error?e("/user/login"):(s({type:"LOGIN",payload:a}),e(`/${g(a.role)}/dashboard`))}catch{e("/user/login")}};return r.useEffect(()=>{(async()=>await n())()}),t.jsx(t.Fragment,{children:t.jsx("div",{className:"flex min-h-screen justify-center items-center min-w-full",children:t.jsx("svg",{className:"w-24 h-24 animate-spin",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"})})})})};export{E as default};
