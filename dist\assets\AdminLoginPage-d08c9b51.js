import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as N,u as _,b as D}from"./vendor-4cdf2bd1.js";import{u as q}from"./react-hook-form-a383372b.js";import{o as T}from"./yup-0917e80c.js";import{c as z,a as v}from"./yup-342a5df4.js";import{b as O,a as R,L as U,a9 as $,I as B}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const ce=()=>{var i,c;const{sdk:A}=O(),{authDispatch:L,showToast:o}=R(),[a,l]=N.useState(!1),S=_(),k=D(),I=new URLSearchParams(S.search).get("redirect_uri"),E=z({email:v().email().required(),password:v().required()}).required(),{register:r,handleSubmit:F,setError:m,formState:{errors:t}}=q({resolver:T(E)});N.useEffect(()=>{localStorage.removeItem("admin_token")},[]);const P=async d=>{var x,f,p,u,h,g,b,w;try{l(!0);const s=await A.login(d.email,d.password,"admin");if(!s.error)L({type:"LOGIN",payload:s}),o("Successfully Logged In",4e3,"success"),k(I??"/admin/dashboard");else if(l(!1),s.validation){const j=Object.keys(s.validation);for(let n=0;n<j.length;n++){const y=j[n];m(y,{type:"manual",message:s.validation[y]})}}}catch(s){l(!1),o((f=(x=s==null?void 0:s.response)==null?void 0:x.data)!=null&&f.message?(u=(p=s==null?void 0:s.response)==null?void 0:p.data)==null?void 0:u.message:s==null?void 0:s.message,4e3,"error"),console.log("Error",s),m("email",{type:"manual",message:(g=(h=s==null?void 0:s.response)==null?void 0:h.data)!=null&&g.message?(w=(b=s==null?void 0:s.response)==null?void 0:b.data)==null?void 0:w.message:s==null?void 0:s.message})}};return e.jsx("main",{className:"h-screen max-h-screen min-h-screen bg-brown-main-bg bg-cover bg-no-repeat",children:e.jsxs("div",{className:"flex h-full max-h-full min-h-full items-center",children:[e.jsxs("div",{className:"relative hidden h-full max-h-full min-h-full flex-col items-center justify-center bg-black px-5 md:flex md:w-1/2 md:px-[6.9375rem]",children:[e.jsx("div",{className:"my-2 text-xl font-semibold text-[#262626]",children:e.jsx(U,{children:e.jsx($,{className:"!h-[3.25rem] !w-[23.8519rem]"})})}),e.jsx("div",{className:"mb-3 flex flex-col items-center justify-center gap-2",children:e.jsxs("span",{className:"whitespace-nowrap text-center font-iowan text-[24px] font-semibold text-[#FFF0E5] sm:text-[2.5rem]",children:["Welcome to Your ",e.jsx("br",{})," Admin Dashboard"]})}),e.jsxs("div",{className:"absolute inset-x-0 bottom-[3.75rem] m-auto flex h-[1.1875rem] max-h-[1.1875rem] min-h-[1.1875rem] w-full justify-center gap-[1.5rem] md:flex-row",children:[e.jsx("a",{target:"_blank",className:"text-center font-inter text-[1rem] font-[500] leading-[1.21rem] text-[#A5A5A3]",href:"https://updatestack.com/privacy-policy",children:"Privacy Policy"}),e.jsx("hr",{className:"h-[.125rem] w-full border-[.125rem] border-[#A5A5A3] bg-[#A5A5A3] md:h-full md:w-[0.125rem]"}),e.jsx("a",{target:"_blank",className:"text-center font-inter text-[1rem] font-[500] leading-[1.21rem] text-[#A5A5A3]",href:"https://updatestack.com/terms-of-use",children:"Terms of Service"})]})]}),e.jsx("div",{className:"flex h-full max-h-full min-h-full w-full flex-col items-center justify-center px-5 md:w-1/2 md:px-[6.9375rem]",children:e.jsx("div",{className:"flex w-full grow flex-col items-center justify-center gap-[2rem]",children:e.jsxs("form",{className:"w-full min-w-full max-w-full space-y-[1rem] 2xl:space-y-[2rem]",onSubmit:F(P),children:[e.jsx("h2",{className:"font-iowan text-[2rem] font-[700] leading-[3.25rem] text-[#1F1D1A]",children:"Sign in"}),e.jsxs("div",{className:"mt-[.875rem] flex flex-col space-y-1 text-sm",children:[e.jsx("label",{htmlFor:"email",className:"font-iowan text-[16px] font-[700]",children:"Email"}),e.jsx("input",{id:"email",className:"h-[44px] border-[2px] border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] outline-none focus:border-[#1f1d1a] focus:shadow-none focus:outline-none",type:"text",placeholder:"<EMAIL>",...r("email")}),e.jsx("p",{className:"text-xs italic text-red-500",children:(i=t==null?void 0:t.email)==null?void 0:i.message})]}),e.jsxs("div",{className:"flex flex-col space-y-1 text-sm",children:[e.jsx("label",{htmlFor:"password",className:"font-iowan text-[16px] font-[700]",children:"Password"}),e.jsx("div",{className:"flex h-[44px] items-center rounded-sm border-[2px] border-[#1f1d1a] bg-transparent px-2 py-1 text-[#1f1d1a]",children:e.jsx("input",{id:"password",className:"w-[95%] border-none bg-transparent p-1 text-sm font-normal text-[#1f1d1a] shadow-[0] outline-none focus:border-none focus:shadow-none focus:outline-none",type:"password",placeholder:"********",...r("password"),style:{boxShadow:"0 0 transparent"}})}),e.jsx("p",{className:"text-xs italic text-red-500",children:(c=t==null?void 0:t.password)==null?void 0:c.message})]}),e.jsx(B,{type:"submit",className:"mt-6 flex h-[44px] w-full items-center justify-center rounded-sm bg-[#1f1d1a] py-2 tracking-wide text-white outline-none focus:outline-none",loading:a,disabled:a,children:e.jsx("span",{className:"capitalize",children:a?"Logging In":"Log In"})})]})})})]})})};export{ce as default};
