import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as m,h as A,b as F}from"./vendor-4cdf2bd1.js";import{M as I,a as Y,u as V,L as S,I as B,d as T,av as L,C as R,ab as H,g as y,i as O}from"./index-f2ad9142.js";import{u as q}from"./react-hook-form-a383372b.js";import{o as z}from"./yup-0917e80c.js";import{c as G,a as f}from"./yup-342a5df4.js";import{A as K}from"./AddButton-51d1b2cd.js";import{M as Z}from"./index-d07d87ac.js";import{u as J}from"./useGetEngagements-c9be41e4.js";import"./index-b8adfdf8.js";import"./moment-a9aaa855.js";import"./index-9dceff66.js";import{M as x}from"./MkdInput-d37679e9.js";import{N as Q}from"./index-590fd997.js";import{u as W}from"./useDate-c1da5729.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";new I;const X=[{header:"ID",accessor:"id",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Title",accessor:"update_name",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Section",accessor:"section",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Comment",accessor:"comment",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Commenter",accessor:"commenter",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Date Sent",accessor:"sent_at",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Status",accessor:"status",isSorted:!0,isSortedDesc:!1,mappingExist:!0,mappings:{1:{text:"Sent",bg:"#9DD321",color:"black"},0:{text:"Draft",bg:"#BCBBBA",color:"black"}},selected_column:!0},{header:"Action",accessor:""}],U=["date_from","date_to","update_title","section","commenter","comment"],Ve=()=>{const n=m.useRef(null),l=m.useRef(null),{globalDispatch:b}=Y(),[c,j]=A(),N=F(),{profile:d}=V(),{convertDate:v}=W(),{data:o,updateData:w,refetch:D}=J(),E=G({date_from:f(),date_to:f(),update_title:f(),section:f(),commenter:f()}),{register:u,handleSubmit:C,setError:ee,reset:k,setValue:_,formState:{errors:p}}=q({resolver:z(E)}),M=t=>{var r;const a=(r=t==null?void 0:t.data)==null?void 0:r.map(s=>({...s,sent_at:e.jsxs("span",{className:"flex items-center gap-2",children:[e.jsx(R,{}),v(s==null?void 0:s.sent_at,{formatMatcher:"best fit",month:"2-digit",day:"2-digit",year:"2-digit"})]}),commenter:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-[1.5rem] w-[1.5rem] overflow-hidden rounded-full border",children:s!=null&&s.commenter_photo?e.jsx("img",{src:s==null?void 0:s.commenter_photo,alt:"commenter",style:{width:"100%",height:"100%",objectFit:"cover"}}):e.jsx(H,{className:"h-full w-full"})}),e.jsxs("div",{children:[y(s==null?void 0:s.commenter_first_name)," ",y(s==null?void 0:s.commenter_last_name)]})]})}));return{...t,data:a}},P=t=>{console.log("_data >>",t),Object.entries(t).forEach(([a,r])=>{O(r)?c.delete(a):c.set(a,r)}),j(c)},$=t=>{console.error("FORM:_ERROR >>",t)},g=(t,a=2)=>t<10?`0${t}`:t;return m.useEffect(()=>{b({type:"SETPATH",payload:{path:"engagements"}})},[]),m.useEffect(()=>{b({type:"SETPATH",payload:{path:"engagements"}})},[]),m.useEffect(()=>{var t;o!=null&&o.reload&&(l!=null&&l.current)&&((t=l==null?void 0:l.current)==null||t.click(),w({reload:!1}))},[o==null?void 0:o.reload]),m.useEffect(()=>{var t;n!=null&&n.current&&((t=n==null?void 0:n.current)==null||t.click())},[c]),e.jsx(m.Fragment,{children:e.jsxs("div",{className:"  grid h-full max-h-full min-h-full w-full grid-rows-[auto_auto_auto_1fr] space-y-[1rem] rounded  bg-brown-main-bg p-5 px-5 md:px-8",children:[e.jsxs("div",{className:"my-[16px] flex w-full items-center justify-between gap-5",children:[e.jsx("h4",{className:" text-[1rem] font-semibold md:text-xl",children:"Engagements"}),e.jsx("div",{className:"flex w-fit items-center justify-start gap-5",children:e.jsx(S,{})})]}),e.jsx("form",{onSubmit:C(P,$),className:"flex w-full items-center gap-5",children:e.jsxs("div",{className:"grid w-full grid-cols-[repeat(auto-fill,minmax(8rem,1fr))] items-end  gap-2 md:w-[75%]",children:[e.jsx("div",{className:"!grow",children:e.jsx(x,{type:"custom_date",errors:p,register:u,onChange:t=>{var h;const{month:a,day:r,year:s}=(h=t==null?void 0:t.target)==null?void 0:h.value,i=`${s}-${g(a)}-${g(r)}`;console.log("moment(day, month, year).format(YYYY-MM-DD)",i),_("date_from",i)},name:"date_from",label:"From",placeholder:"Date From",className:"!h-[2.25rem] !rounded-[0.125rem] !border !py-[0.5rem]"})}),e.jsx("div",{className:"!grow",children:e.jsx(x,{type:"custom_date",errors:p,register:u,onChange:t=>{var h;const{month:a,day:r,year:s}=(h=t==null?void 0:t.target)==null?void 0:h.value,i=`${s}-${g(a)}-${g(r)}`;console.log("moment(day, month, year).format(YYYY-MM-DD)",i),_("date_to",i)},name:"date_to",label:"To",placeholder:"Date To",className:"!h-[2.25rem] !rounded-[0.125rem] !border !py-[0.5rem]"})}),e.jsx("div",{className:"!grow",children:e.jsx(x,{type:"text",errors:p,register:u,name:"update_title",label:"Title",placeholder:"Search Title",className:"!h-[2.25rem] !rounded-[0.125rem] !border !py-[0.5rem]"})}),e.jsx("div",{className:"!grow",children:e.jsx(x,{type:"text",errors:p,register:u,name:"section",label:"Section",placeholder:"Search Section",className:"!h-[2.25rem] !rounded-[0.125rem] !border !py-[0.5rem]"})}),e.jsx("div",{className:"!grow",children:e.jsx(x,{type:"text",errors:p,register:u,name:"commenter",label:"Commenter",placeholder:"Search Commenter",className:"!h-[2.25rem] !rounded-[0.125rem] !border !py-[0.5rem]"})}),e.jsx(B,{type:"submit",className:"flex !h-[2.25rem] !w-fit !min-w-fit !max-w-fit items-center justify-center whitespace-nowrap !rounded-[0.125rem] !border bg-[#1f1d1a]  !py-[0.5rem] px-2 !text-[1rem] tracking-wide text-white outline-none focus:outline-none md:px-5",color:"black",disabled:!(d!=null&&d.id),children:"Search"}),e.jsx(K,{onClick:()=>{k();for(const t of U)c.delete(t);j(c)},showPlus:!1,className:"!w-fit !min-w-fit !max-w-fit !border-0 !bg-transparent !p-0 !font-inter !text-[1rem] !font-[600] !leading-[1.21rem] !tracking-wide !text-black !underline !shadow-none ",children:"Clear"})]})}),e.jsx("div",{className:"h-[.125rem] w-full border-[.125rem] border-black bg-black "}),d!=null&&d.id?e.jsx(Z,{showSearch:!1,useDefaultColumns:!0,defaultColumns:[...X],noDataComponent:{use:!0,component:e.jsx(S,{children:e.jsx(Q,{})})},onUpdateCurrentTableData:t=>{const a=M(o);t(a)},externalData:{...o,fetch:D},hasFilter:!1,tableRole:"admin",table:"order",actionId:"id",join:["user","division","campaign","warehouse"],actions:{select:{show:!1,action:null,multiple:!1},remove:{show:!1,action:null,multiple:!0,children:"Remove",icon:e.jsx(T,{fill:"#292D32"}),locations:["dropdown"],bind:{column:["member_status"],action:"hide",operator:"eq",ifValue:[0]}},view:{show:!0,action:t=>{var r,s;const a=(s=(r=o==null?void 0:o.data)==null?void 0:r.find(i=>(i==null?void 0:i.id)===t[0]))==null?void 0:s.update_id;N(`/member/update/private/view/${a}`)},multiple:!1,children:"View",showChildren:!0,locations:["buttons"]},resend:{show:!1,action:null,multiple:!0,children:"Resend",showChildren:!0,icon:e.jsx(L,{fill:"#292D32"}),locations:["buttons"],bind:{column:["member_status"],action:"hide",operator:"eq",ifValue:[1]}},view_all:{show:!1,type:"static",action:()=>{},children:e.jsx(e.Fragment,{children:"View All"}),className:"!gap-2 !bg-transparent !text-black !underline !shadow-none !border-0"},add:{show:!1,multiple:!0,children:"+ Add"},export:{show:!1,action:null,multiple:!0}},defaultPageSize:20,showPagination:!0,maxHeight:"md:grid-rows-[inherit] grid-rows-[inherit]",actionPostion:["dropdown","buttons"],refreshRef:n,updateRef:l}):null]})})};export{Ve as default};
