import{b as x,a as D,t as I,s as C}from"./index-f2ad9142.js";import{r as s}from"./vendor-4cdf2bd1.js";function $(l,n){const{sdk:o}=x(),{authDispatch:y,globalDispatch:h,getMany:f,custom:d}=D(),[c,i]=s.useState(n??{}),[E,m]=s.useState({list:[],single:null,delete:null}),[b,u]=s.useState({list:!1,single:!1,update:!1,delete:!1,create:!1}),g=s.useCallback(async()=>{u(!0);try{const e=await o.callRawAPI(`/v4/api/records/notes/${l}`);i(e.model)}catch(e){I(y,e.message),C(h,e.message,5e3,"error")}u(!1)},[]),w=s.useCallback((e={filter:[],exposure:"private",update_id:null})=>(async()=>{u(a=>({...a,list:!0}));const t={private:async()=>await f("notes",{filter:[...e==null?void 0:e.filter],join:["update_comments"],order:"create_at",direction:"asc"}),public:async()=>await d({endpoint:`/v3/api/custom/goodbadugly/updates/notes/${e==null?void 0:e.update_id}`,method:"GET"})};try{const a=t==null?void 0:t[e==null?void 0:e.exposure];if(!a)return;const r=await a();return r!=null&&r.error?[]:(m(v=>({...v,list:r==null?void 0:r.data})),r==null?void 0:r.data)}catch{return[]}finally{u(a=>({...a,list:!1}))}})(),[f]),k=s.useCallback((e={updateId:null,noteId:null})=>(async()=>{u(t=>({...t,delete:!0}));try{const t=await d({endpoint:`/v3/api/custom/goodbadugly/updates/notes/${e==null?void 0:e.updateId}/${e==null?void 0:e.noteId}`,method:"DELETE"});return t!=null&&t.error||m(a=>({...a,delete:t})),t}catch(t){return{error:!0,message:t==null?void 0:t.message}}finally{u(t=>({...t,delete:!1}))}})(),[d]);return s.useEffect(()=>{l&&g()},[l]),s.useEffect(()=>{c!=null&&c.id||i(n)},[n]),{loading:b,note:c,notes:E,refetch:g,getNotes:w,customDeleteNote:k}}export{$ as u};
