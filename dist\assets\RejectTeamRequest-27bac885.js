import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{A as n}from"./index-8c774937.js";import{au as s,L as p}from"./index-f2ad9142.js";import{r as l}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const F=({id:m,memberId:r,onClose:a,onSuccess:i})=>{const{handleInvitation:o}=s(),e=async()=>{await o(m,"reject",r),i==null||i()};return t.jsx(l.Fragment,{children:t.jsx("div",{className:"relative h-fit max-h-fit min-h-[6.25rem] w-[25rem] min-w-[25rem] ",children:t.jsx(p,{children:t.jsx(n,{customMessage:t.jsx(t.Fragment,{children:"Reject Team Invitation?"}),onClose:a,onSuccess:e,action:"Reject",mode:"manual",multiple:!1,inputConfirmation:!1,className:"action-confirmation-modal"})})})})};export{F as default};
