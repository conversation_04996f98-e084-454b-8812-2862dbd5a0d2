import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as o,b as S}from"./vendor-4cdf2bd1.js";import{u as F}from"./react-hook-form-a383372b.js";import{o as T}from"./yup-0917e80c.js";import{c as q,a as l}from"./yup-342a5df4.js";import{G as k,M as $,s as L,t as M}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const se=({setSidebar:i})=>{var b,h,g,f,j,w;const[d,m]=o.useState(!1),E=q({slug:l().required(),subject:l().required(),html:l().required(),tag:l().required()}).required(),{dispatch:C}=o.useContext(k),{dispatch:c}=o.useContext(k),A=S(),{register:a,handleSubmit:p,setError:x,formState:{errors:s}}=F({resolver:T(E)}),u=async r=>{let y=new $;m(!0);try{y.setTable("email");const t=await y.callRestAPI({slug:r.slug,subject:r.subject,html:r.html,tag:r.tag},"POST");if(!t.error)A("/admin/email"),L(c,"Added");else if(t.validation){const N=Object.keys(t.validation);for(let n=0;n<N.length;n++){const v=N[n];x(v,{type:"manual",message:t.validation[v]})}}}catch(t){console.log("Error",t),x("subject",{type:"manual",message:t.message}),M(C,t.message)}m(!1)};return o.useEffect(()=>{c({type:"SETPATH",payload:{path:"email"}})},[]),e.jsxs("div",{className:"mx-auto rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsxs("div",{className:"flex gap-3 items-center",children:[e.jsx("svg",{onClick:()=>i(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"text-lg font-semibold",children:"Add Email"})]}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#F4F4F4]",onClick:()=>i(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[#1f1d1a] px-3 py-2 text-white shadow-sm",onClick:async()=>{await p(u)(),i(!1)},disabled:d,children:d?"Saving...":"Save"})]})]}),e.jsxs("form",{className:"p-4 w-full max-w-lg text-left",onSubmit:p(u),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"slug",children:"Email Type"}),e.jsx("input",{type:"text",placeholder:"Email Type",...a("slug"),className:"px-3 py-2 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:shadow-outline } bg-brown-main-bg focus:outline-none"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"subject",children:"Subject"}),e.jsx("input",{type:"text",placeholder:"subject",...a("subject"),className:`shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] shadow focus:w-full   focus:outline-none  ${(b=s.subject)!=null&&b.message?"border-red-500":""} `}),e.jsx("p",{className:"text-xs italic text-red-500",children:(h=s.subject)==null?void 0:h.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"tag",children:"Tags"}),e.jsx("input",{type:"text",placeholder:"tag",...a("tag"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(g=s.tag)!=null&&g.message?"border-red-500":""} `}),e.jsx("p",{className:"text-xs italic text-red-500",children:(f=s.tag)==null?void 0:f.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"html",children:"Email Body"}),e.jsx("textarea",{placeholder:"Email Body",className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none ${(j=s.html)!=null&&j.message?"border-red-500":""}`,...a("html"),rows:15}),e.jsx("p",{className:"text-xs italic text-red-500",children:(w=s.html)==null?void 0:w.message})]})]})]})};export{se as default};
