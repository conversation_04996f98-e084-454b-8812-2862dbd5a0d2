import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as a}from"./vendor-4cdf2bd1.js";import{c as S}from"./react-hook-form-a383372b.js";import{G as F,A as T,M as _,t as D,s as L}from"./index-f2ad9142.js";import{a as $,C as z}from"./CreateGroupModal-d6bb962a.js";import{W as o,t as A}from"./@headlessui/react-cdd9213e.js";function V({control:j,name:i,setValue:f,allowedRoles:I,label:y="Group name",setGroupName:m=null,errors:n,onReady:p}){var b,v;const r=a.useRef(null),[l,N]=a.useState([]),[c,d]=a.useState(""),{dispatch:C}=a.useContext(F),{dispatch:k,state:E}=a.useContext(T);a.useState(!1);const[G,x]=a.useState(!0),u=S({control:j,name:i}),h=c===""?l:l.filter(t=>t.group_name.toLowerCase().replace(/\s+/g,"").includes(c.toLowerCase().replace(/\s+/g,"")));async function g(){x(!0);try{const s=await new _().callRawAPI(`/v4/api/records/group?filter=user_id,in,'NULL',${E.user}`,void 0,"GET");N(s.list)}catch(t){D(k,t.message),L(C,t.message,5e3,"error")}x(!1)}return a.useEffect(()=>{g()},[]),a.useEffect(()=>{p&&p()},[l==null?void 0:l.length]),a.useEffect(()=>{var t;m&&m(((t=l.find(s=>s.id==u))==null?void 0:t.group_name)??"")},[u]),e.jsxs(e.Fragment,{children:[e.jsx(o,{value:c||u,onChange:f,children:e.jsxs("div",{className:"relative z-50 w-full",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"mb-1 block font-iowan  text-[16px] font-semibold capitalize text-[#1f1d1a]",children:y}),e.jsx("div",{className:"",children:e.jsx("button",{className:"font-medium",type:"button",onClick:()=>{var t;(t=r==null?void 0:r.current)==null||t.click()},children:"+ New Group"})})]}),e.jsxs("div",{className:"w-full",children:[e.jsxs(o.Button,{className:"relative w-full cursor-default rounded-[2px] text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 ",children:[e.jsx("div",{className:"w-full",children:e.jsx(o.Input,{className:`focus:shadow-outline  h-[2.6rem] w-full appearance-none rounded-[2px] border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal leading-tight text-[#1f1d1a]    placeholder:text-[14px] placeholder:text-[#1f1d1a] focus:outline-none md:h-[44px] ${(b=n==null?void 0:n[i])!=null&&b.message?"border-red-500":""}`,defaultValue:u,placeholder:"Type to search",displayValue:t=>{const s=l.find(w=>w.id==t);return(s==null?void 0:s.group_name)??""},onChange:t=>{d(t.target.value),t.target.value===""&&d("")},onBlur:()=>d(""),name:i,autoComplete:"off"})}),e.jsx("div",{className:"absolute inset-y-0 right-3 flex items-center",children:e.jsx($,{className:"h-5 w-5 text-[#1f1d1a]","aria-hidden":"true"})})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(v=n==null?void 0:n[i])==null?void 0:v.message})]}),e.jsx(A,{as:a.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx(o.Options,{className:"custom-overflow absolute z-30 mt-1 max-h-[18.75rem]  w-full overflow-y-auto rounded-md border bg-brown-main-bg py-1 pt-6 text-base shadow-lg ring-1 ring-[#1f1d1a]/5 focus:outline-none",children:G||h.length===0&&c!==""?e.jsx("div",{className:"relative cursor-default select-none bg-brown-main-bg px-2 py-2 pl-10 text-gray-700",children:"Nothing found."}):h.map(t=>e.jsx(o.Option,{className:({active:s})=>`relative cursor-default select-none py-2  pl-10 pr-4 ${s?"bg-primary-black text-white":"text-gray-900"}`,value:t.id,children:({selected:s,active:w})=>e.jsx(e.Fragment,{children:e.jsx("span",{className:"block truncate font-medium",children:t.group_name})})},t.id))})})]})}),e.jsx(z,{type:"base",buttonRef:r,afterCreate:t=>{g(),f(t)}})]})}export{V as S};
