import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import"./vendor-4cdf2bd1.js";import{A as m}from"./AddButton-51d1b2cd.js";import{L as e}from"./index-f2ad9142.js";import{C as s}from"./@hassanmojab/react-modern-calendar-datepicker-b4938049.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const N=({selectedDay:o,setSelectedDay:t,showDate:i=!0,showTime:l=!0,onSave:a})=>r.jsxs(r.Fragment,{children:[r.jsx("style",{children:`.custom-selected-date {
          background-color: #000 !important;
          color: #fff !important;
          border-radius: 50% !important;
        }`}),r.jsxs("div",{children:[i&&r.jsx(s,{value:o,onChange:t,calendarClassName:"!bg-brown-main-bg !shadow-none !w-full !h-full !p-0",colorPrimary:"#000"}),r.jsx(e,{children:r.jsx(m,{className:"!h-full !w-full ",showPlus:!1,onClick:a,children:"Save"})})]})]});export{N as default};
