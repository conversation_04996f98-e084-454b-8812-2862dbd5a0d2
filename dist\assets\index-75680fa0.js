import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{T as p}from"./index-cb9e08c3.js";import{G as b,A as j,M as d,t as u,s as l}from"./index-f2ad9142.js";import{r as s}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./@headlessui/react-cdd9213e.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function R(){s.useState(!1),s.useState(!1),s.useState(!1);const[t,x]=s.useState(),{dispatch:o}=s.useContext(b),{dispatch:c}=s.useContext(j);async function m(){try{const n=await new d().callRawAPI("/v2/api/lambda/preference",void 0,"GET");x(n)}catch(a){u(c,a.message),l(o,a.message,5e3,"error")}}async function i(a,n){console.log(a);try{await new d().callRawAPI("/v2/api/lambda/preference",{payload:a},"POST"),m(),l(o,n)}catch(r){u(c,r.message),l(o,r.message,5e3,"error")}}s.useEffect(()=>{m()},[]);const f=(t==null?void 0:t.enable_email_notification)===1?{enable_email_notification:0}:{enable_email_notification:1},h=(t==null?void 0:t.enable_sms_notification)===1?{enable_sms_notification:0}:{enable_sms_notification:1};return e.jsxs("div",{className:"p-5 px-4 pt-8 md:px-8",children:[e.jsx("h3",{className:"text-xl",children:"Notification Schedule"}),e.jsx("p",{className:"mt-1 text-base font-iowan-regular",children:"Create a schedule for future notifications"}),e.jsxs("div",{className:"mt-8 flex max-w-[340px] flex-col items-start justify-between gap-6 text-xl sm:flex-row sm:items-center",children:[e.jsx("p",{className:"text-base font-iowan",children:"Email Notifications"}),e.jsx("div",{className:"flex gap-3 gap-4 items-center font-iowan-regular",children:e.jsx(p,{enabled:(t==null?void 0:t.enable_email_notification)==1,setEnabled:()=>i(f,"Email updated")})}),e.jsxs("div",{className:"flex hidden relative gap-3 items-center",children:[e.jsx("div",{className:"absolute z-10  h-full w-full cursor-not-allowed opacity-[50]"}),e.jsx(p,{enabled:(t==null?void 0:t.enable_sms_notification)==1,setEnabled:()=>i(h,"SMS updated"),className:"opacity-[0.3]"}),e.jsx("span",{className:"font-iowan-regular  opacity-[0.5]",children:"SMS Notifications"})]})]}),e.jsxs("div",{className:"mt-7 flex max-w-[340px] flex-col justify-between gap-6 sm:flex-row sm:items-center",children:[e.jsx("div",{className:"font-iowan text-[16px]",children:"Notification Frequency"}),e.jsxs("select",{name:"",id:"",className:"borderh-[41.6px] pr- font w-[120px] rounded-md border-[#1f1d1a] bg-transparent text-sm sm:text-sm",onChange:a=>i({notification_frequency:Number(a.target.value)},"Frequency updated"),children:[e.jsxs("option",{value:"",hidden:!0,children:[(t==null?void 0:t.notification_frequency)||"Set"," hours"]}),e.jsx("option",{value:"6",children:"6 hours"}),e.jsx("option",{value:"12",children:"12 hours"}),e.jsx("option",{value:"24",children:"24 hours"}),e.jsx("option",{value:"36",children:"36 hours"}),e.jsx("option",{value:"48",children:"48 hours"}),e.jsx("option",{value:"72",children:"72 hours"})]})]})]})}export{R as default};
