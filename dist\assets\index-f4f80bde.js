import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as o,h as D,b as I}from"./vendor-4cdf2bd1.js";import{A as T,G as _,M as A,t as R,s as M}from"./index-f2ad9142.js";import{u as V}from"./react-hook-form-a383372b.js";import{o as B}from"./yup-0917e80c.js";import{c as G,a as W}from"./yup-342a5df4.js";import{E as q}from"./ExportButton-eb4cf1f9.js";import"./InteractiveButton-060359e0.js";import"./tableWrapper-ca490fb1.js";import z from"./Loader-9c3b30f4.js";import{P as H}from"./index-9dceff66.js";import{R as K}from"./tableWrapperDashboard-6c11f374.js";import{C as J}from"./ClipboardDocumentIcon-f03b0627.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-b50d6e2a.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./index.esm-7add6cfb.js";import"./react-icons-36ae72b7.js";import"./index-23a711b5.js";function O(v){const[g,x]=o.useState(!1),[b,r]=o.useState([]),[u,i]=o.useState(0),[t,h]=D(),{dispatch:j}=o.useContext(T),{dispatch:w}=o.useContext(_),m=parseInt(t.get("limit")||"30"),a=parseInt(t.get("page")||"1"),c=t.get("name")||"";async function f(){x(!0);try{const N=await new A().callRawAPI(`/v4/api/records/company_member?join=companies|company_id&filter=member_id,eq,${v}&filter=member_role,cs,collaborator${c?`&filter=name,cs,${c}`:""}&page=${a},${m}&order=id,asc`);r(N.list.map(l=>{var p;return{id:l.company_id,logo:(p=l.companies)==null?void 0:p.logo,name:l.companies.name,user_id:l.companies.user_id}})),i(N.total||1)}catch(n){R(j,n.message),M(w,n.message,5e3,"error")}x(!1)}o.useEffect(()=>{f()},[m,a,c]);const d=Math.ceil(u/m);return{loading:g,companies:b,refetch:f,currentPage:a,pageCount:d,pageSize:m,updatePageSize:n=>{t.set("limit",n.toString()),t.set("page","1"),h(t)},previousPage:()=>{a>1&&(t.set("page",(a-1).toString()),h(t))},nextPage:()=>{a<d&&(t.set("page",(a+1).toString()),h(t))},canPreviousPage:a>1,canNextPage:a<d}}let $=new A;const Q=[{header:"Logo",accessor:"logo"},{header:"Name",accessor:"name"},{header:"Action",accessor:""}],ze=()=>{var S,k;const{dispatch:v,state:g}=o.useContext(T),{dispatch:x}=o.useContext(_),b=I(),[r,u]=D(),{companies:i,loading:t,refetch:h,currentPage:j,pageCount:w,pageSize:m,updatePageSize:a,previousPage:c,nextPage:f,canPreviousPage:d,canNextPage:y}=O(g.user),P=G({name:W()}),{register:C,handleSubmit:n,setError:N,reset:l,formState:{errors:p}}=V({resolver:B(P),defaultValues:async()=>({name:r.get("name")??""})});async function L(){$.setTable("companies"),await $.exportCSV()}o.useEffect(()=>{x({type:"SETPATH",payload:{path:"companies"}})},[]);function F(s){r.set("name",s.name),r.set("page",1),u(r)}return e.jsx(e.Fragment,{children:t?e.jsx(z,{}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"rounded bg-brown-main-bg p-5 pt-8 shadow md:px-8",children:[e.jsxs("div",{className:"item-center mb-3 flex w-full justify-between ",children:[e.jsx("h4",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"Search"}),e.jsx("div",{className:"flex"})]}),e.jsx("form",{onSubmit:n(F),className:"",children:e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("div",{className:"flex flex-row flex-wrap gap-4",children:e.jsxs("div",{className:"w-full sm:w-auto",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Name"}),e.jsx("input",{type:"text",...C("name"),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm text-sm text-sm font-normal font-normal font-normal capitalize leading-tight text-[#1d1f1a] shadow focus:outline-none   sm:w-[150px] sm:w-[180px] ${(S=p.name)!=null&&S.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(k=p.name)==null?void 0:k.message})]})}),e.jsxs("div",{className:"flex items-end gap-4",children:[e.jsx("button",{type:"submit",disabled:t,className:"font-iowan-regular  rounded-md bg-primary-black/80 px-4 py-1 font-semibold text-white hover:bg-primary-black",children:"Search"}),e.jsx("button",{type:"button",onClick:()=>{l({name:"",members:""}),r.set("name",""),r.set("page",1),u(r)},disabled:t,className:"rounded-md px-4 py-1 font-semibold text-[#1f1d1a]",children:"Clear"})]})]})}),e.jsxs("div",{className:"custom-overflow mt-10 w-full overflow-x-auto rounded bg-brown-main-bg p-5 px-0 md:mt-8",children:[e.jsxs("div",{className:"mb-3 flex w-full items-center justify-between text-center",children:[e.jsx("h4",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"Companies"}),e.jsx("div",{className:"flex",children:e.jsx(q,{onClick:L,className:"px-2 py-2  font-medium"})})]}),e.jsx("div",{className:`${t?"":"custom-overflow overflow-x-auto"} `,children:t?e.jsx("div",{className:"flex max-h-fit min-h-fit min-w-fit max-w-full items-center justify-center  py-5",children:e.jsx(z,{size:50})}):e.jsx(e.Fragment,{children:e.jsx(K,{children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10",children:[e.jsx("thead",{children:e.jsx("tr",{children:Q.map((s,E)=>e.jsx("th",{scope:"col",className:"font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3",children:s.header},E))})}),e.jsx("tbody",{className:"font-iowan-regular  divide-y divide-[#1f1d1a]/10",children:i.map((s,E)=>e.jsxs("tr",{children:[e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("img",{src:s.logo,className:"h-[50px] w-[80px] rounded",alt:""})}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:s.name}),e.jsx("td",{className:"flex items-center justify-start gap-2 whitespace-nowrap px-6 py-4",children:e.jsx("button",{className:"h-[80px] cursor-pointer text-[#292829fd] underline hover:underline disabled:cursor-not-allowed disabled:text-gray-400",onClick:()=>{b("/collaborator/view-companies/"+s.id,{state:s})},children:e.jsx("span",{children:"View"})})})]},s.id))})]})})})}),(i==null?void 0:i.length)==0?e.jsxs("div",{className:"mb-[20px] mt-24 flex flex-col items-center",children:[e.jsx(J,{className:"h-8 w-8 text-gray-700",strokeWidth:2}),e.jsx("p",{className:"mt-4 text-center text-base font-medium",children:"No Company Added yet"})]}):null]}),i.length>0&&e.jsx(H,{currentPage:j,pageCount:w,pageSize:m,canPreviousPage:d,canNextPage:y,updatePageSize:a,previousPage:c,nextPage:f,dataLoading:t})]})})})};export{ze as default};
