import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{o as I}from"./yup-0917e80c.js";import{A as D,G as R,ab as q,E as L,I as v,M as P,s as w,t as S}from"./index-f2ad9142.js";import{r as y}from"./vendor-4cdf2bd1.js";import{u as U}from"./react-hook-form-a383372b.js";import{c as M,a as l}from"./yup-342a5df4.js";import{S as V,a as $,b as O}from"./SelectCity-65a3e859.js";import"./InteractiveButton-060359e0.js";import{M as d}from"./MkdInput-d37679e9.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./countries-912e22d5.js";import"./ChevronDownIcon-8b7ce98c.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";function Pe(){const{dispatch:p,state:r}=y.useContext(D),{dispatch:b}=y.useContext(R),C=M({first_name:l().required("This field is required"),last_name:l().required("This field is required"),role:l().required("This field is required"),company_name:l().required("This field is required"),country:l().nullable(),state:l().nullable(),city:l().nullable(),contact_link:l().nullable()}),{register:n,handleSubmit:E,setError:G,reset:j,watch:T,setValue:m,formState:{errors:c,defaultValues:s,dirtyFields:a,isSubmitting:_,isDirty:B},control:x}=U({resolver:I(C),defaultValues:{first_name:r.profile.first_name,last_name:r.profile.last_name,title:r.profile.title,company_name:r.company.name,photo:r.profile.photo,country:r.profile.country,state:r.profile.state,city:r.profile.city,contact_link:r.profile.contact_link}});console.log(r.profile,"djhdh");async function k(){try{const h=await new P().getProfilePreference();p({type:"SET_PROFILE",payload:h})}catch(t){S(p,t.message),w(b,t.message,5e3,"error")}}async function A(t){var h;try{const i=new P;let g="";if(a.photo||a.first_name||a.last_name){if(a.photo&&t.photo instanceof FileList&&t.photo.length>0){const F=await i.upload(t.photo[0]);g=i.baseUrl()+F.url}await i.callRawAPI(`/v4/api/records/user/${r.user}`,{first_name:t.first_name,last_name:t.last_name,...a.photo&&{photo:g}},"PUT"),await k()}a.company_name&&(await i.callRawAPI(`/v4/api/records/companies/${r.company.id}`,{name:t.company_name},"PUT"),p({type:"REFETCH_COMPANY"})),a.country||a.state||a.city,await i.callRawAPI("/v4/api/records/profile",{updateCondition:{user_id:r.user},country:t.country,state:t.state,city:t.city,contact_link:t.contact_link,title:t.role},"PUT"),await k(),j({...s,first_name:t.first_name,last_name:t.last_name,...a.photo&&{photo:g},company_name:t.company_name,country:t.country,state:t.state,city:t.city,title:t.role,contact_link:(h=o==null?void 0:o.profile)==null?void 0:h.contact_link},{keepDefaultValues:!1}),w(b,"Changes saved")}catch(i){S(p,i.message),w(b,i.message,5e3,"error")}}const[f,u,o]=T(["photo","country","state"]);y.useEffect(()=>{u===""&&(m("state",""),m("city","")),o===""&&m("city","")},[u,o]);const N=y.useMemo(()=>f instanceof FileList&&f.length>0?URL.createObjectURL(f[0]):null,[f]);return e.jsxs("div",{className:"mx-auto px-4 py-6 shadow-lg md:px-8",children:[e.jsx("div",{className:"mb-4 font-iowan text-[20px] font-[700] md:text-[1.5rem] md:leading-[1.865rem] ",children:"Personal Info"}),e.jsx("p",{className:"mb-6 font-inter text-[1rem] font-[400] leading-[1.21rem]",children:"Update your personal info"}),e.jsx("div",{className:"mb-6 flex items-center",children:e.jsxs("div",{className:"relative flex h-[7.5rem] w-[7.5rem] items-center justify-center rounded-full border border-[#1F1D1A] bg-transparent",children:[N||s!=null&&s.photo?e.jsx("img",{src:N||(s==null?void 0:s.photo)||"/default.png",alt:"profile",className:"h-full min-h-full w-full min-w-full rounded-[50%] object-cover sm:h-full sm:min-h-full sm:w-full sm:min-w-full"}):e.jsx(q,{}),e.jsxs("label",{htmlFor:"photo",className:"absolute bottom-0 right-0 flex h-8 w-8 cursor-pointer items-center justify-center rounded-full border border-[#1F1D1A] bg-brown-main-bg",children:[e.jsx("input",{type:"file",id:"photo",...n("photo"),className:"hidden"}),e.jsx(L,{})]})]})}),e.jsxs("form",{onSubmit:E(A),children:[e.jsxs("div",{className:"flex w-full flex-col items-start gap-0 md:w-[45%]",children:[e.jsxs("div",{className:"mb-6 grid w-full grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsx("div",{children:e.jsx(d,{type:"text",id:"first-name",label:"First Name",name:"first_name",errors:c,register:n,className:"mt-1 block w-full rounded-md  !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500"})}),e.jsx("div",{children:e.jsx(d,{type:"text",id:"last-name",name:"last_name",label:"Last Name",errors:c,register:n,className:"mt-1 block w-full rounded-md  !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500"})})]}),e.jsx("div",{className:"mb-6 grid w-full grid-cols-1 gap-4",children:e.jsx("div",{children:e.jsx(d,{type:"text",id:"title",label:"Title",name:"title",errors:c,register:n,className:"mt-1 block w-full rounded-md !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500"})})}),e.jsx("div",{className:"mb-6 w-full",children:e.jsx(d,{type:"text",id:"member",label:"member",name:"company_name",errors:c,register:n,className:"mt-1 block w-full rounded-md !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500"})}),e.jsx("div",{className:"mb-6 w-full",children:e.jsx(V,{control:x,name:"country",setValue:t=>m("country",t)})}),e.jsxs("div",{className:"mb-6 grid w-full grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsx("div",{className:"",children:e.jsx($,{control:x,name:"state",setValue:t=>m("state",t),country:u})}),e.jsx("div",{className:"",children:e.jsx(O,{control:x,name:"city",setValue:t=>m("city",t),country:u,state:o})})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"mb-4 font-iowan text-[20px] font-[700] md:text-[1.5rem] md:leading-[1.865rem] ",children:"Personal Calendar Link"}),e.jsx("p",{className:"mb-6 font-inter text-[1rem] font-[400] leading-[1.21rem]",children:"This link will be displayed on reports for fund managers and stakeholders, allowing them to schedule a meeting with you."}),e.jsx(d,{type:"text",id:"calendar-link",name:"contact_link",errors:c,register:n,className:"mt-1 block w-full rounded-md  !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500",placeholder:"http://calendly.com/example"})]})]}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx(v,{type:"button",className:"flex h-[2.75rem] w-fit items-center justify-center whitespace-nowrap rounded-[.0625rem] !border !border-black bg-transparent px-2 py-2 font-iowan !text-[1rem] tracking-wide text-black md:px-5",color:"black",onClick:()=>{var t;j({...s,first_name:data.first_name,last_name:data.last_name,...a.photo&&{photo:url},company_name:data.company_name,country:data.country,state:data.state,city:data.city,contact_link:(t=o==null?void 0:o.profile)==null?void 0:t.contact_link},{keepDefaultValues:!1})},children:"Discard Changes"}),e.jsx(v,{className:" flex h-[2.75rem] w-fit items-center justify-center whitespace-nowrap rounded-[.0625rem] bg-[#1f1d1a] px-2 py-2 font-iowan !text-[1rem] tracking-wide text-white md:px-5",loading:_,disabled:_,type:"submit",children:"Save Changes"})]})]})]})}export{Pe as default};
