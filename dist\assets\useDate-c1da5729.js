import{r as t}from"./vendor-4cdf2bd1.js";import{a as n}from"./index-f2ad9142.js";const r=()=>(n(),t.useState({subscription:null,object:null}),t.useState({subscription:!1}),{convertDate:(e,o={year:"2-digit",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})=>{const i={year:"2-digit",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",...o};return new Date(e).toLocaleDateString(void 0,i)}}),m=r;export{m as u};
