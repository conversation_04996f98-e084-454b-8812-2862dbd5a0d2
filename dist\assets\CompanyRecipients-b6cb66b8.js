import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as m,b as j}from"./vendor-4cdf2bd1.js";import{G as v,A as S,u as N,I as k,ax as R,h as l,s as n,m as E}from"./index-f2ad9142.js";import{c as A,a as P}from"./yup-342a5df4.js";import{u as T}from"./react-hook-form-a383372b.js";import{M as _}from"./MkdInput-d37679e9.js";import{l as $}from"./logo5-2e16f0f2.js";import{o as C}from"./yup-0917e80c.js";import{u as I}from"./useLocalStorage-46cb237c.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./@hookform/resolvers-b50d6e2a.js";const he=({role:L=R.STARTUP,updateStep:q=null})=>{const{dispatch:a,state:{recipientCreation:r}}=m.useContext(v),{dispatch:u}=m.useContext(S);m.useState([1]),j();const{localStorageData:D,setLocalStorage:d}=I(["step"]),{profile:i,updateProfile:c}=N({isPublic:!0}),f=A({email:P().required("This field is required").test("is-valid-emails","Some emails are invalid",s=>s?s.split(",").map(t=>t.trim()).every(t=>/^\S+@\S+\.\S+$/.test(t)):!1)}),{register:x,handleSubmit:h,setError:p,watch:G,formState:{errors:g}}=T({resolver:C(f)}),b=async()=>{try{l(a,!0,"skipStep"),c({step:5,is_onboarded:1}),d("step",5),l(a,!1,"skipStep"),window.location.href="/member/dashboard"}catch(s){console.log("Error",s),l(a,!1,"skipStep"),n(a,s.message,5e3,"error"),p("team_name",{type:"manual",message:s.message})}},w=async s=>{const o=[];for(const t of s.email.split(",").map(y=>y.trim()))/^\S+@\S+\.\S+$/.test(t)?o.push(t):n(a,`${t} is not a valid email`,5e3,"error");try{const t=await E(a,u,{endpoint:"/v3/api/custom/goodbadugly/member/recipients",method:"POST",payload:{emails:o}},"recipientCreation");t!=null&&t.error||(c({step:5,is_onboarded:1}),d("step",5),window.location.href="/member/dashboard")}catch(t){console.log("Error",t),n(a,t.message,5e3,"error"),p("team_name",{type:"manual",message:t.message})}};return e.jsxs("div",{className:"w-full md:mt-[50px] md:w-[60%] md:max-w-[500px] md:px-6 lg:w-[70%] xl:mt-[160px]",children:[e.jsx("style",{children:`
        .bd {
          border: 1px solid #1f1d1a;
        }
          .style {
            
          }
      `}),e.jsx("div",{className:"sticky right-0 top-0 z-[9] flex h-[4.5rem] w-full flex-row items-center justify-between bg-[#1f1d1a] px-8 md:hidden",children:e.jsx("img",{src:$,alt:"logo",className:"h-10 w-[180px]"})}),e.jsx("form",{onSubmit:h(w,s=>{console.log("ERROR >>",s)}),className:"flex h-full max-h-full min-h-full w-full flex-col items-center justify-center px-4 md:px-0",children:e.jsxs("div",{className:"mt-5 w-full md:mt-8",children:[e.jsxs("div",{className:"space-y-5",children:[e.jsxs("div",{className:"l font-iowan text-[2.25rem] font-[700] leading-[3rem] md:text-[2.5rem] ",children:["Welcome ",i==null?void 0:i.first_name]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-inter text-[1rem] font-[600] leading-[1.5rem]",children:"Next, add a recipient user to your account"}),e.jsx("h5",{className:"font-inter text-[1rem] font-[400] leading-[1.5rem]",children:"Invite team members, investors, or limited partners to receive your updates via UpdateStack"})]}),e.jsx("p",{className:"font-inter text-[1rem] font-[400] leading-[1.5rem]",children:"Add recipients using their business email if adding multiple recipients, separate each email by a comma"})]}),e.jsx("div",{className:"scrollbar-hide h-full max-h-full min-h-full w-full overflow-y-auto pb-5",children:e.jsx("div",{className:"my-5 mt-4 grid grid-cols-1 gap-4 md:mt-2",children:e.jsxs("div",{className:"w-full items-end justify-start gap-2 space-y-[1rem]",children:[e.jsx("div",{className:"mt-4 flex flex-col gap-[1rem] md:flex-row",children:e.jsx("div",{className:"grid grow grid-cols-1",children:e.jsx(_,{type:"text",name:"email",errors:g,label:"Email",register:x,className:"focus:shadow-outline !h-[2.75rem] w-full appearance-none !rounded-[.125rem] border border-[#1f1d1a] !bg-transparent text-sm leading-tight text-[#1d1f1a] shadow focus:outline-none",placeholder:"<EMAIL>,<EMAIL>"})})}),e.jsxs("div",{className:"flex w-full flex-col items-center gap-[1rem] ",children:[e.jsx(k,{type:"submit",className:"my-4 flex h-[2.75rem] !w-full items-center justify-center rounded-sm bg-[#1f1d1a] !px-3 py-2 tracking-wide text-white outline-none focus:outline-none",loading:r==null?void 0:r.loading,disabled:r==null?void 0:r.loading,children:e.jsx("span",{className:"capitalize",children:"Add User(s)"})}),e.jsx("button",{type:"button",onClick:()=>b(),className:"w-fit cursor-pointer whitespace-nowrap text-sm font-medium md:text-base",children:"Skip,  Add Later →"})]})]})})})]})})]})};export{he as default};
