import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as n,r as i,b as _,i as z}from"./vendor-4cdf2bd1.js";import"./index-79ee4c46.js";import{u as L}from"./useGroups-a4f9b4a0.js";import{u as R}from"./useUpdateGroups-731b4247.js";import{A as E,u as U,p as N,q as P,s as A,r as Z,w as O,G as J,o as K}from"./index-f2ad9142.js";import{P as b}from"./index-4342bf32.js";import{u as Q}from"./useCompanyMember-0033d2de.js";import{B as V}from"./BottomSheet-be14d402.js";import{f as W}from"./lucide-react-0b94883e.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const X=(d={filter:[]})=>{const{state:f,dispatch:s}=n.useContext(E),{state:u,dispatch:o}=n.useContext(E),[x,p]=n.useState([]),[C,k]=n.useState(null),[w,m]=n.useState(!1),{profile:g}=U({isPublic:!1}),h=n.useCallback((r={filter:[]})=>{(async()=>{m(!0);try{const a=await N(o,s,"group",{filter:[...r==null?void 0:r.filter]});a!=null&&a.error||p(()=>a==null?void 0:a.data)}catch{}finally{m(!1)}})()},[g,s,o,N]),j=n.useCallback(r=>{if(P(r))return A(o,"Group id is Required!");(async()=>{m(!0);try{const a=await Z(o,s,"group",r);a!=null&&a.error||k(()=>a==null?void 0:a.data)}catch{}finally{m(!1)}})()},[g,s,o,N]),t=n.useCallback((r,a)=>{if(P(r))return A(o,"Group id is Required!");if(P(a))return A(o,"Payload is Required!");(async()=>{m(!0);try{const y=await O(o,s,"group",r,a,!1);y!=null&&y.error||h()}catch{}finally{m(!1)}})()},[h,s,o,O]);return n.useEffect(()=>{h(d)},[]),{groups:x,group:C,loading:w,getGroups:h,getGroup:j,updateGroup:t}},Y=({isOpen:d,onClose:f,next:s=1,createGroupModalRef:u,title:o="Getting Started",data:x=null,onNext:p,updateNext:C})=>{const[k,w]=i.useState(d),[m,g]=i.useState(d);i.useEffect(()=>{w(d),g(d)},[d]);const h=_();i.useContext(J),i.useContext(E);const{id:j}=z();R(j);const{groups:t}=L(),{profile:r,updateProfile:a}=U({isPublic:!1}),{group:y,groups:l,loading:$}=X(),{loading:v,companyMember:S,getMyCompanyMembers:B}=Q(),D=()=>{a({awareness:(r==null?void 0:r.awareness)+1}),q()},q=i.useCallback(()=>{w(!1),g(!1),f(!0)},[f]),F=i.useCallback(c=>{c&&c.stopPropagation(),w(!1),f(!0)},[f]),H=i.useCallback(c=>{c&&c.stopPropagation(),g(!1),f(!0)},[f]);i.useEffect(()=>{l!=null&&l.length&&(t!=null&&t.length)&&[1,2].includes(s)&&C(3),l!=null&&l.length&&!(t!=null&&t.length)&&[1,2].includes(s)&&C(2)},[l==null?void 0:l.length,t==null?void 0:t.length]),i.useEffect(()=>{B()},[]);const T=()=>{var c;return e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("div",{className:"px-4 py-3 border-b border-gray-200 md:px-6 md:py-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex gap-2 items-center md:gap-3",children:[e.jsx("h2",{className:"font-iowan text-[16px] font-semibold text-gray-900 md:text-xl",children:"Start Here: Follow These Steps"}),e.jsx(W,{className:"w-4 h-4 text-gray-500 md:h-5 md:w-5"})]}),e.jsx("button",{onClick:()=>D(),className:"rounded-md bg-transparent px-3 py-1 font-iowan text-[16px] font-medium text-black underline focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2 md:px-4 md:py-2 md:text-sm",children:"Skip"})]}),e.jsx("p",{className:"hidden mt-1 text-sm text-black font-iowan-regular md:block md:text-base",children:"Complete these steps to set up your first update"})]}),e.jsxs("div",{className:"flex flex-col divide-y divide-gray-100",children:[e.jsx(b,{id:1,active:s==1,title:"Add a Team Member",onClick:()=>{h(`/member/company/teams?trigger=add&update_id=${x==null?void 0:x.updateId}`)},paragraph:"Invite recipients to view and collaborate on our updates",badge:{show:!0,loading:v==null?void 0:v.myMembers,created:!!((c=S==null?void 0:S.myMembers)!=null&&c.length)}}),e.jsx(b,{id:2,active:s==2,title:"Create a group",onClick:()=>{var I;u!=null&&u.current&&(q(),(I=u==null?void 0:u.current)==null||I.click())},paragraph:"Add and organize verified team members within a group",badge:{show:!0,loading:$,created:!!(l!=null&&l.length)}}),e.jsx(b,{id:3,active:s==3,title:"Create an Update",paragraph:"Select an update template or create from a blank",badge:{show:!1,loading:!1,created:!!(t!=null&&t.length)},onClick:()=>p(4)}),e.jsx(b,{id:4,active:s==4,title:"Add Recipient/Group",paragraph:"Add either a team member or a group as a recipient",badge:{show:!1,loading:!1,created:!!(t!=null&&t.length)},onClick:()=>p(5)}),e.jsx(b,{id:5,active:s==5,title:"Add Collaborators",paragraph:"Invite team members to collaborate on your update",badge:{show:!1,loading:!1,created:!!(t!=null&&t.length)},onClick:()=>p(6)}),e.jsx(b,{id:6,active:s==6,title:"Send Update",paragraph:"Once complete, send an update to your team or investors",badge:{show:!1,loading:!1,created:!!(t!=null&&t.length)},onClick:()=>p(7)})]}),e.jsx("div",{className:"px-4 py-3 border-t border-gray-200 md:px-6 md:py-4",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("p",{className:"text-base text-black font-iowan md:text-lg",children:["Step ",s," of 6"]}),e.jsx("button",{onClick:()=>D(),className:"text-base font-medium text-gray-900 font-iowan md:text-lg",children:"Skip for now"})]})})]})};return d?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"hidden md:block",children:e.jsx(K,{modalHeader:!0,title:o,isOpen:k,modalCloseClick:F,classes:{modal:"",modalDialog:"!bg-brown-main-bg md:!min-w-[50%] md:!w-1/2 h-fit overflow-y-auto max-h-[90%] !w-full",modalContent:"!bg-brown-main-bg"},children:T()})}),e.jsx("div",{className:"md:hidden",children:e.jsx(V,{isOpen:m,onClose:H,title:o,TitleclassName:"font-iowan text-lg",children:T()})})]}):null},ke=i.memo(Y);export{ke as default};
