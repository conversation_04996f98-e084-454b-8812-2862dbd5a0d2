import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{r as a,R as re}from"./vendor-4cdf2bd1.js";import{G as we,A as ye,f as oe,n as te,p as ke}from"./index-f2ad9142.js";import"./MkdInput-d37679e9.js";import"./react-quill-a78e6fc7.js";import{v as P}from"./uuid-82fce61c.js";import{c as Ne}from"./@popperjs/core-f3391c26.js";import{c as se}from"./lucide-react-0b94883e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const Se=k=>k?`max-h-[${k}]`:"max-h-[18.75rem]",Ae=({onSelect:k,showBorder:ve=!0,display:w="display",value:h,uniqueKey:x,selector:Ve=null,disabled:A=!1,placeholder:le="- search -",label:W="Select",maxHeight:ae="18.75rem",height:$e="fit-content",selectedOptions:J=[],table:F="",errors:I,name:Z,className:H="w-[23rem]",inputClassName:Q="",join:v=[],filter:U=[],mode:Ce="static",useExternalData:V=!1,externalDataLoading:ne=!1,externalDataOptions:j=[],onReady:X=null,refreshRef:ce=null,clearRef:fe=null,showSearchIcon:ue=!1,required:me=!1,dataRetrievalState:T=null,displaySeparator:y="",customOptions:$=[],onPopoverStateChange:Y=null,popoverShown:D,containerRef:g})=>{var p;const C=a.useRef(null);a.useRef(null),a.useRef(null);const{dispatch:ie,state:de}=re.useContext(we),{dispatch:he}=re.useContext(ye),G=de[T??F],[f,M]=a.useState([]),ge=P({namespace:"SearchableDropdown"});P({namespace:"SearchableDropdown"}),P({namespace:"SearchableDropdown"});const[d,N]=a.useState(!1),[L,q]=a.useState(""),[E,O]=a.useState(null),[b,S]=a.useState([]);a.useState(!1);const[Le,K]=a.useState(null),_=a.useMemo(()=>U,[U]),be=a.useMemo(()=>JSON.stringify(j),[j]),R=async()=>{var r;const e=await ke(ie,he,F,{..._!=null&&_.length?{filter:_}:null,...v&&(v!=null&&v.length)?{join:v}:null},T&&T);if(!e.error){if(F==="user"){const u=(r=e==null?void 0:e.data)==null?void 0:r.map(i=>i!=null&&i.first_name||i!=null&&i.last_name?{...i,full_name:te(`${i.first_name} ${i.last_name}`,{casetype:"capitalize",separator:" "})}:i);M(()=>[...u]),S(()=>[...u])}else M(()=>[...e==null?void 0:e.data]),S(()=>[...e==null?void 0:e.data]);if(h){const u=e==null?void 0:e.data.find(i=>i[x]==h);u&&O(()=>u)}X&&X(e==null?void 0:e.data)}},o=(e,r,u)=>{if(typeof r=="string")return String(e[r]);if(typeof r=="object")if(Array.isArray(r)){if(r.some(l=>typeof l!="string"))return String(e[Object.keys(e)[0]]);const z=r.map(l=>e[l]);return String(z.length?z.join(` ${y} `):z.join(" "))}else{if(![1,2].includes(Object.keys(r).length)||Object.keys(r).some(s=>!["and","or"].includes(s)))return String(e[Object.keys(e)[0]]);const l=r.and,m=r.or;if(l&&m){if(typeof l=="string"&&typeof m=="string"){const s=e[l],n=e[m];return String(s||n||e[Object.keys(e)[0]])}if(Array.isArray(l)&&Array.isArray(m)){if(l.some(c=>!e[c])){const c=m.map(ee=>{if(e[ee])return e[ee]}).filter(Boolean);return String(c.length?c.length?c.join(` ${y} `):c.join(" "):e[Object.keys(e)[0]])}const n=l.map(c=>{if(e[c])return e[c]}).filter(Boolean);return String(n.length?n.join(` ${y} `):n.join(" "))}if(Array.isArray(l)&&typeof m=="string"){if(l.some(c=>!e[c])){const c=e[m];return String(c||e[Object.keys(e)[0]])}const n=l.map(c=>{if(e[c])return e[c]}).filter(Boolean);return String(n.length?n.join(` ${y} `):n.join(" "))}if(Array.isArray(m)&&typeof l=="string"){const s=e[l];if(s)return String(s);const n=m.map(c=>{if(e[c])return e[c]}).filter(Boolean);return String(n.length?n.length?n.join(` ${y} `):n.join(" "):e[Object.keys(e)[0]])}}else if(l&&!m){if(typeof l=="string"){const s=e[l];return String(s||e[Object.keys(e)[0]])}if(Array.isArray(l)){const s=l.map(n=>{if(e[n])return e[n]}).filter(Boolean);return String(s.length?s.length?s.join(` ${y} `):s.join(" "):e[Object.keys(e)[0]])}}else if(!l&&m){if(typeof m=="string"){const s=e[m];return String(s||e[Object.keys(e)[0]])}if(Array.isArray(m)){const s=m.map(n=>{if(e[n])return e[n]}).filter(Boolean);return String(s.length?s.length?s.join(` ${y} `):s.join(" "):e[Object.keys(e)[0]])}}}},B=(e,r=!1,u=!0)=>{if(!(u&&!k)){if(r)return O(null),k(null,!0),N(!1);O(e),u&&k(e),L&&q(""),f.length>b&&S(f),d&&N(!1)}};a.useCallback(e=>{if(x)return!!J.find(u=>u[x]===e[x])},[f,b,J]);const xe=a.useCallback(()=>{if(d||L)return L;if(E)return o(E,w);if(h&&f&&(f!=null&&f.length)){const e=f==null?void 0:f.find(r=>r[x]===Number(h));return e?o(e,w):""}else return""},[d,h,L,B,f]),je=a.useCallback(e=>{if(q(e),e){const r=f.filter(u=>o(u,w).toLowerCase().includes(e.toLowerCase()));S(r)}else S(f)},[L]);return a.useEffect(()=>{if(d&&(g!=null&&g.current)&&C.current){const e=Ne(g.current,C.current,{placement:"bottom-start",modifiers:[{name:"flip",options:{fallbackPlacements:["top-start"]}},{name:"preventOverflow",options:{boundary:"viewport",padding:8}},{name:"offset",options:{offset:[0,4]}}]});return K(e),()=>{e.destroy(),K(null)}}},[d,g]),a.useEffect(()=>{const e=r=>{g!=null&&g.current&&C.current&&!g.current.contains(r.target)&&!C.current.contains(r.target)&&N(!1)};return d&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[d,g]),a.useEffect(()=>{!V&&!(f!=null&&f.length)&&R()},[V]),a.useEffect(()=>{V&&(M(()=>[...j]),S(()=>[...j]))},[V,be]),a.useEffect(()=>{Y&&!D&&d&&Y(!0)},[D,d]),t.jsxs(t.Fragment,{children:[t.jsx("button",{ref:ce,type:"button",hidden:!0,onClick:()=>{if(V){if(b!=null&&b.length){const e=b.find(r=>r[x]===h);e&&B(e,!1,!1)}else if(j!=null&&j.length){const e=j.find(r=>r[x]===h);e&&B(e,!1,!1)}}else R()}}),t.jsx("button",{ref:fe,type:"button",hidden:!0,onClick:()=>O(null)}),t.jsxs("div",{className:`relative ${H}`,children:[W&&t.jsxs("label",{className:"mb-2 block  font-iowan text-[1rem] font-bold text-[#1f1d1a]",children:[W,me&&t.jsx("sup",{className:"text-[.825rem] text-red-600",children:"*"})]}),ne||G!=null&&G.loading?t.jsx(oe,{count:1,counts:[2],className:`!h-[3rem] !max-h-[3rem] !min-h-[3rem] !gap-0 overflow-hidden rounded-[.125rem] !bg-[#ebebeb] !p-0 ${H} ${Q}`}):t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:`flex h-[3rem] w-full items-center justify-normal rounded-[.125rem] border border-primary-black pl-3 shadow ${Q} ${A?"bg-gray-200":"bg-brown-main-bg"}`,children:[ue&&!A&&t.jsx("div",{className:"!w-4 ",children:t.jsx("svg",{className:"h-4 w-4 text-gray-500 dark:text-gray-400","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 20 20",children:t.jsx("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"})})}),t.jsx("div",{className:"grow",children:t.jsx("input",{type:"text",disabled:A,placeholder:le,id:ge,className:`${A?"bg-gray-200":"bg-brown-main-bg"} showListButton h-full w-full appearance-none rounded-[.125rem] border-0 px-3 py-2  leading-tight text-black placeholder:text-[#1fd1a] focus:outline-none focus:outline-0 focus:ring-0`,value:xe(),onFocus:()=>{d||N(!0)},onChange:e=>{var r,u;return je((u=(r=e.target.value)==null?void 0:r.trimStart())==null?void 0:u.toLowerCase())},onKeyDown:e=>{e.key==="Enter"&&e.preventDefault()}})}),!A&&t.jsxs("div",{onClick:()=>{N(!d)},className:"mr-3 flex cursor-pointer flex-col items-center justify-center",children:[t.jsx(se,{className:"h-4 w-5 p-0",stroke:"#1f1d1a"}),t.jsx(se,{className:"h-4 w-5 rotate-180 p-0",stroke:"#1f1d1a"})]})]}),d&&t.jsx("div",{ref:C,className:`fixed z-[999] w-[calc(309-15%)] overflow-y-auto rounded-md border border-primary-black bg-brown-main-bg py-3 text-sm shadow-lg md:w-[309px] ${Se(ae)}`,children:t.jsxs("div",{className:"",children:[t.jsx("div",{className:`flex h-[2.8rem] min-h-[2.8rem] cursor-pointer items-center justify-start gap-5 truncate px-3 py-2 text-sm font-normal capitalize hover:bg-primary-black/40 hover:text-white 
                    ${!E&&!h?"bg-primary-black text-white":"text-primary-black"}`,onClick:()=>B(null,!0),children:"None"}),$.length&&$.find(e=>e==null?void 0:e.show)?$==null?void 0:$.map((e,r)=>{if(e!=null&&e.show)return t.jsxs("div",{title:e!=null&&e.children&&typeof(e==null?void 0:e.children)=="string"?e==null?void 0:e.children:e!=null&&e.icon&&typeof(e==null?void 0:e.icon)=="string"?e==null?void 0:e.icon:null,className:"flex h-[2.8rem] min-h-[2.8rem] cursor-pointer items-center justify-start gap-3 truncate px-3 py-2 text-sm font-normal capitalize text-primary-black hover:bg-primary-black/40 hover:text-white ",onClick:()=>(e==null?void 0:e.action)&&(e==null?void 0:e.action()),children:[e!=null&&e.icon?e.icon:null,e!=null&&e.children?e.children:null]},r)}):null,b.length?b==null?void 0:b.map((e,r)=>(e==null?void 0:e.searchableType)==="section"?t.jsx("div",{disabled:!0,className:"flex h-[2.8rem] min-h-[2.8rem] w-full items-center justify-start gap-5 truncate bg-black px-3 py-2 text-sm font-bold capitalize text-white",children:e==null?void 0:e.display},r):t.jsx("button",{type:"button",title:e&&o(e,w),className:`flex h-[2.8rem] min-h-[2.8rem] w-full cursor-pointer items-center justify-start gap-5 truncate px-3 py-2 text-sm font-normal capitalize hover:bg-primary-black/40 hover:text-white  ${E&&(h&&h===e[x]||o(e,w)===o(E,w))?"bg-primary-black text-white":"text-primary-black"} `,onClick:()=>B(e),children:o(e,w)},r)):null]})}),I&&I[Z]&&t.jsx("p",{className:"text-field-error absolute inset-x-0 top-[90%] m-auto mt-2 text-[.8rem] italic text-red-500",children:te((p=I[Z])==null?void 0:p.message,{casetype:"capitalize",separator:" "})})]})]})]})},ar=a.memo(Ae);export{ar as default};
