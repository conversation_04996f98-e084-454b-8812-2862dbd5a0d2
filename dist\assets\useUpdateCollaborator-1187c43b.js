import{r as C}from"./vendor-4cdf2bd1.js";import{a as I,b as P,u as S}from"./index-f2ad9142.js";const k=(a={updateId:null,filter:[],join:[]})=>{const{getMany:w,custom:h,showToast:U,globalState:m}=I(),{operations:E}=P(),{profile:d}=S(),[g,j]=C.useState({updateCollaborators:[],updateContributors:[],isCollaborator:!1,collaborator:null}),[v,b]=C.useState({updateCollaborator:!1,updateContributors:!1}),y=async(l={filter:[],join:[]})=>{var c,f;if(b(t=>({...t,updateCollaborator:!0})),m!=null&&m.isPublicView){console.log("Skipping update collaborator fetch - public view");return}try{const t=await w("update_collaborators",{filter:[...(c=l==null?void 0:l.filter)!=null&&c.length?l==null?void 0:l.filter:[]],join:[...(f=l==null?void 0:l.join)!=null&&f.length?l==null?void 0:l.join:[]]});if(!(t!=null&&t.error)){const e=t==null?void 0:t.data,r=e==null?void 0:e.find(o=>o.collaborator_id==(d==null?void 0:d.id));j(o=>({...o,updateCollaborators:e,isCollaborator:!!r,collaborator:r}))}}catch(t){console.log(t)}finally{b(t=>({...t,updateCollaborator:!1}))}},x=C.useCallback((l=null)=>l?(async()=>{var c,f,t,e;b(r=>({...r,updateContributors:!0}));try{const r=await h({endpoint:`/v3/api/custom/goodbadugly/updates/collaborators/${l}`,method:"GET"});return r!=null&&r.error||j(o=>({...o,updateContributors:r==null?void 0:r.data})),r}catch(r){return{error:!0,message:(f=(c=r==null?void 0:r.response)==null?void 0:c.data)!=null&&f.message?(e=(t=r==null?void 0:r.response)==null?void 0:t.data)==null?void 0:e.message:r==null?void 0:r.message}}finally{b(r=>({...r,updateContributors:!1}))}})():U("Please provide an update id",5e3,"error"),[b,h]);return C.useEffect(()=>{var l,c;a!=null&&a.updateId&&(d!=null&&d.id)&&y({filter:[`update_id,${E.EQUAL},${a==null?void 0:a.updateId}`,...(l=a==null?void 0:a.filter)!=null&&l.length?a==null?void 0:a.filter:[]],join:[...(c=a==null?void 0:a.join)!=null&&c.length?a==null?void 0:a.join:[]]})},[a==null?void 0:a.updateId,a==null?void 0:a.filter,a==null?void 0:a.join,d==null?void 0:d.id]),{loading:v,getUpdateCollaborator:y,data:g,fetchUpdateContributors:x,collaborators:g==null?void 0:g.updateCollaborators}};export{k as u};
