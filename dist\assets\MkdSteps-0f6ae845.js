import{j as m}from"./@nextui-org/listbox-0f38ca19.js";import"./vendor-4cdf2bd1.js";import{a}from"./index-bf8349b8.js";import{L as p}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const s=t=>Array.from({length:t+t-1}).map((e,i)=>[i%2,i].includes(0)?"auto":"1fr").join(" "),S=({onClick:t,steps:r=[],className:e,currentStep:i})=>m.jsx("div",{style:{gridTemplateColumns:s(r==null?void 0:r.length)},className:`relative grid grid-rows-1 items-center justify-between gap-3 px-5 text-base font-semibold sm:px-0 ${e}`,children:r.map((n,o)=>m.jsx(p,{children:m.jsx(a,{step:n,stepKey:o,onClick:t,stepSize:r.length,currentStep:i},o)},o))});export{S as default};
