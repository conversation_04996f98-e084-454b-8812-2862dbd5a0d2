import{A as l,G as f,M as d,t as h,s as g}from"./index-f2ad9142.js";import{r as t}from"./vendor-4cdf2bd1.js";function G(e){const[c,a]=t.useState(!1),[n,u]=t.useState([]),{dispatch:p}=t.useContext(l),{dispatch:i}=t.useContext(f);async function r(){a(!0);try{const s=await new d().callRawAPI(`/v3/api/custom/goodbadugly/member/updates/${e}/groups`);console.log("LIST >>",s==null?void 0:s.list),u(s.list)}catch(o){h(p,o.message),g(i,o.message,5e3,"error")}a(!1)}return t.useEffect(()=>{e&&r()},[e]),{loading:c,updateGroups:n,refetch:r}}export{G as u};
