import{j as l}from"./@nextui-org/listbox-0f38ca19.js";import"./vendor-4cdf2bd1.js";import{y as d}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const D=({update:s=null})=>{var r,t,o,n,C,c;return console.log("update >>",s,(t=(r=s==null?void 0:s.notes)==null?void 0:r.list)==null?void 0:t.filter(e=>(e==null?void 0:e.content)!==null)),l.jsx(l.Fragment,{children:l.jsxs("aside",{className:" block w-full max-w-[16.25rem] flex-grow  ",children:[l.jsxs("div",{children:[l.jsx("div",{className:"border-2 border-[#1f1d1a]",children:l.jsxs("ul",{className:"divide-y divide-gray-900",children:[(o=s==null?void 0:s.notes)!=null&&o.length?(C=(n=s==null?void 0:s.notes)==null?void 0:n.filter(e=>(e==null?void 0:e.content)!==null))==null?void 0:C.sort((e,i)=>{const m=e.order!==void 0&&e.order!==null?e.order:Number.MAX_SAFE_INTEGER,x=i.order!==void 0&&i.order!==null?i.order:Number.MAX_SAFE_INTEGER;return m-x}).map(e=>{const{blocks:i}=d(e.content,{blocks:[]});return i.length==0?null:l.jsxs("li",{className:"line-clamp-2 flex flex-row items-center justify-between overflow-hidden truncate text-ellipsis whitespace-nowrap  break-words font-iowan text-[18px] font-bold md:items-center",children:[" ",l.jsx("a",{className:"line-clamp-2 block overflow-hidden truncate text-ellipsis whitespace-nowrap break-words px-4 py-6",href:`#${e.type}`,children:e.type})]},e.id)}):null,l.jsxs("li",{className:"flex flex-row  items-center justify-between font-iowan text-[18px] font-semibold md:items-center",children:[l.jsx("a",{className:"block px-4 py-6",href:"#investor-asks",children:"Asks"}),l.jsx("div",{className:"mr-4 flex h-[20px] w-[20px] flex-row items-center justify-center rounded-full bg-[#990F3D] text-white",children:((c=s==null?void 0:s.update_questions)==null?void 0:c.length)||0})]})]})}),l.jsx("div",{className:"mt-8 w-full text-center",children:l.jsx("button",{onClick:()=>{var e;window.location.href=(e=s==null?void 0:s.companies)==null?void 0:e.contact_link},className:"h-[52px] w-full border-2 border-[#1f1d1a] font-iowan font-semibold text-black",children:"📆 Book a meeting"})})]}),l.jsxs("h5",{className:"hidden flex-col gap-2 font-iowan font-medium md:mt-[400px]  md:flex",children:[l.jsx("span",{children:" Automated by"}),l.jsxs("svg",{width:"235",height:"32",viewBox:"0 0 235 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[l.jsx("path",{d:"M44.5426 3.15602V23.7534H6.3694V7.25965H14.0265H25.4559H41.0116V3.15402L2.86341 3.13623V3.15838H2.24316V27.8768H48.6688V3.15838L44.5426 3.15602Z",fill:"#1F1D1A"}),l.jsx("path",{d:"M9.90039 16.8807V21.0041H41.0117V10.0083H25.4561H14.0266H9.90039V14.1318H36.8856V16.8807H9.90039Z",fill:"#1F1D1A"}),l.jsx("path",{d:"M57.5254 16.1642V4.65234H61.4958V15.9798C61.4958 18.2857 62.9878 19.7463 65.0994 19.7463C67.2057 19.7463 68.6983 18.2857 68.6983 15.9798V4.65234H72.6687V16.1642C72.6687 20.4915 69.4289 23.5145 65.0994 23.5145C60.7652 23.5145 57.5254 20.4915 57.5254 16.1642Z",fill:"#1F1D1A"}),l.jsx("path",{d:"M83.8299 8.46875C88.0879 8.46875 91.237 11.6764 91.237 15.9917C91.237 20.302 88.0879 23.5363 83.8299 23.5363C82.3658 23.5363 81.0365 23.1014 79.9409 22.3374V28.2447H76.1182V8.8718H78.6459L79.2986 10.1664C80.4994 9.09513 82.067 8.46934 83.8305 8.46934L83.8299 8.46875ZM87.3666 15.9911C87.3666 13.7549 85.7386 12.0886 83.5178 12.0886C81.2975 12.0886 79.6642 13.7596 79.6642 15.9911C79.6642 18.2232 81.2975 19.894 83.5178 19.894C85.7386 19.894 87.3666 18.2279 87.3666 15.9911Z",fill:"#1F1D1A"}),l.jsx("path",{d:"M108.182 3.73926V23.1119H105.359L104.908 21.9264C103.721 22.9433 102.193 23.5368 100.48 23.5368C96.1903 23.5368 93.0459 20.3025 93.0459 15.9922C93.0459 11.677 96.1903 8.46928 100.48 8.46928C101.94 8.46928 103.265 8.89632 104.359 9.64886V3.73926H108.182ZM104.645 15.9922C104.645 13.7601 103.012 12.0894 100.791 12.0894C98.5705 12.0894 96.9424 13.7554 96.9424 15.9922C96.9424 18.229 98.5705 19.8951 100.791 19.8951C103.012 19.8951 104.645 18.2191 104.645 15.9922Z",fill:"#1F1D1A"}),l.jsx("path",{d:"M126.251 8.85033V23.0907H123.254L122.926 21.8163C121.712 22.887 120.128 23.5146 118.344 23.5146C114.091 23.5146 110.928 20.2808 110.928 15.9705C110.928 11.6769 114.091 8.46924 118.344 8.46924C120.159 8.46924 121.767 9.11347 122.989 10.2108L123.398 8.85033H126.251ZM122.505 15.9705C122.505 13.7337 120.877 12.0677 118.656 12.0677C116.435 12.0677 114.802 13.7384 114.802 15.9705C114.802 18.2027 116.435 19.8734 118.656 19.8734C120.877 19.8734 122.505 18.2073 122.505 15.9705Z",fill:"#1F1D1A"}),l.jsx("path",{d:"M138.57 19.6836V23.1117H135.854C132.743 23.1117 130.832 21.1869 130.832 18.0269V11.9669H128.275V11.1347L133.86 5.15576H134.59V8.87158H138.492V11.9669H134.655V17.5165C134.655 18.8921 135.44 19.6836 136.833 19.6836L138.57 19.6836Z",fill:"#1F1D1A"}),l.jsx("path",{d:"M154.698 17.0703H143.997C144.33 19.0138 145.598 20.0839 147.452 20.0839C148.781 20.0839 149.871 19.4511 150.452 18.4319H154.469C153.441 21.5363 150.735 23.5144 147.452 23.5144C143.278 23.5144 140.119 20.259 140.119 15.9914C140.119 11.7028 143.256 8.46875 147.452 8.46875C151.794 8.46875 154.772 11.8395 154.772 15.9411C154.772 16.3175 154.746 16.6939 154.698 17.0703ZM144.098 14.4407H150.95C150.409 12.6997 149.168 11.7555 147.452 11.7555C145.751 11.7555 144.542 12.7553 144.098 14.4407Z",fill:"#1F1D1A"}),l.jsx("path",{d:"M156.726 17.13H160.722C160.722 18.9137 162.186 19.7748 163.807 19.7748C165.299 19.7748 166.758 18.981 166.758 17.6047C166.758 16.174 165.087 15.7806 163.1 15.3135C160.336 14.628 156.98 13.8196 156.98 9.84266C156.98 6.30187 159.57 4.30713 163.628 4.30713C167.841 4.30713 170.236 6.57262 170.236 10.2501H166.318C166.318 8.66074 165.016 7.91523 163.533 7.91523C162.248 7.91523 160.946 8.46404 160.946 9.6726C160.946 10.9716 162.538 11.365 164.478 11.8324C167.272 12.5446 170.78 13.4221 170.78 17.5573C170.78 21.5439 167.627 23.4786 163.833 23.4786C159.624 23.4786 156.726 21.0978 156.726 17.13Z",fill:"#1F1D1A"}),l.jsx("path",{d:"M182.162 19.6836V23.1117H179.446C176.335 23.1117 174.424 21.1869 174.424 18.0269V11.9669H171.867V11.1347L177.452 5.15576H178.182V8.87158H182.083V11.9669H178.247V17.5165C178.247 18.8921 179.032 19.6836 180.425 19.6836L182.162 19.6836Z",fill:"#1F1D1A"}),l.jsx("path",{d:"M199.033 8.85033V23.0907H196.037L195.708 21.8163C194.494 22.887 192.91 23.5146 191.126 23.5146C186.874 23.5146 183.71 20.2808 183.71 15.9705C183.71 11.6769 186.874 8.46924 191.126 8.46924C192.942 8.46924 194.549 9.11347 195.772 10.2108L196.18 8.85033H199.033ZM195.287 15.9705C195.287 13.7337 193.659 12.0677 191.438 12.0677C189.218 12.0677 187.584 13.7384 187.584 15.9705C187.584 18.2027 189.218 19.8734 191.438 19.8734C193.659 19.8734 195.287 18.2073 195.287 15.9705Z",fill:"#1F1D1A"}),l.jsx("path",{d:"M209.283 23.5141C205.029 23.5141 201.775 20.2154 201.775 15.9484C201.775 11.6811 205.008 8.46875 209.304 8.46875C212.965 8.46875 215.807 10.8062 216.494 14.311H212.698C212.079 12.9113 210.806 12.0889 209.283 12.0889C207.209 12.0889 205.65 13.7649 205.65 15.97C205.65 18.1758 207.235 19.8946 209.283 19.8946C210.827 19.8946 212.049 19.0293 212.694 17.5067H216.542C215.876 21.0864 212.981 23.5147 209.283 23.5147L209.283 23.5141Z",fill:"#1F1D1A"}),l.jsx("path",{d:"M227.762 23.1119L223.237 16.2996V23.1119H219.414V3.73926H223.237V15.199L227.481 8.87174H231.884L227.101 15.6527L232.393 23.1119H227.762Z",fill:"#1F1D1A"})]})]})]})})};export{D as default};
