import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as t,i as z}from"./vendor-4cdf2bd1.js";import{A as F,G as D,M as A,t as T,s as w,I as Z,T as te,a1 as W,K as P,u as se,a as ae,y as H,P as Y,Q as J,V as ne,a2 as ie}from"./index-f2ad9142.js";import{o as U}from"./yup-0917e80c.js";import{u as G}from"./react-hook-form-a383372b.js";import{c as B,a as E,b as K}from"./yup-342a5df4.js";import{InteractiveButton2 as O}from"./InteractiveButton-060359e0.js";import{X as I}from"./XMarkIcon-cfb26fe7.js";import{t as N,L as V,S as _}from"./@headlessui/react-cdd9213e.js";import{b as re,u as le,a as oe,c as ce,L as de}from"./useUpdateCollaborators-611befa1.js";import{c as me}from"./index.esm-be5e1c14.js";import{h as $}from"./moment-a9aaa855.js";import{E as ue}from"./ExclamationTriangleIcon-2f987159.js";import{T as xe}from"./@editorjs/editorjs-3bc58744.js";import{n as pe}from"./@editorjs/paragraph-9d333c59.js";import{c as he}from"./@editorjs/header-da8c369a.js";import{d as fe}from"./@editorjs/list-86b325f6.js";import{I as be}from"./@editorjs/link-7a38da73.js";import{n as ge}from"./@editorjs/delimiter-89018da8.js";import{f as je}from"./@editorjs/checklist-1b2b7ac3.js";import"./lodash-82bd9112.js";import{g as ve}from"./react-audio-voice-recorder-a95781ec.js";import{S as ye,M as we}from"./MicrophoneIcon-ed3ea0f8.js";import{u as Ne}from"./useNote-ea33f376.js";import{T as q}from"./index-cb9e08c3.js";import{L as _e}from"./LockClosedIcon-a004efdc.js";import{L as Ce}from"./index-b8adfdf8.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-b50d6e2a.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./react-icons-36ae72b7.js";function ke(){const[s,u]=t.useState(!1),[c,n]=t.useState([]),{dispatch:a,state:r}=t.useContext(F),{dispatch:l}=t.useContext(D);async function i(){u(!0);try{const m=await new A().callRawAPI(`/v3/api/custom/goodbadugly/member/get-recipient-groups?user_id=${r.company.user_id}&limit=1000`,void 0,"GET");n(m.list)}catch(d){T(a,d.message),w(l,d.message,5e3,"error")}u(!1)}return t.useEffect(()=>{i()},[]),{loading:s,groups:c}}const Se=({updateGroups:s,refetch:u})=>{var j,k;const{id:c}=z(),{dispatch:n,state:a}=t.useContext(F),{groups:r}=ke(),{dispatch:l}=t.useContext(D),i=B({group_id:E().required("This field is required")}),{register:d,handleSubmit:m,formState:{isSubmitting:o,errors:f},reset:y}=G({resolver:U(i),defaultValues:{group_id:""}});async function x(h){try{await new A().callRawAPI("/v4/api/records/update_group",{update_id:c,group_id:h.group_id},"POST"),w(l,"Group added successfully",5e3,"success"),y({group_id:""}),u()}catch(b){T(n,b.message),w(l,b.message,5e3,"error")}}async function g(h){try{await new A().callRawAPI(`/v4/api/records/update_group/${h}`,{},"DELETE"),w(l,"Group removed successfully",5e3,"success"),u()}catch(b){T(n,b.message),w(l,b.message,5e3,"error")}}return e.jsxs("div",{className:"flex items-start gap-12",children:[e.jsx("p",{className:"whitespace-nowrap font-semibold",children:"Add recipients"}),e.jsxs("div",{className:"flex flex-col items-end",children:[e.jsxs(Listbox,{value:selectedCompany,onChange:setSelectedCompany,as:"div",className:"relative mt-5 w-full text-left",children:[e.jsxs(Listbox.Button,{className:"relative inline-flex w-full rounded-md border px-4 py-2 text-lg font-medium focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75",children:[selectedCompany.id?selectedCompany.name:"Select Company",e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center border-l border-black/60 p-2",children:e.jsx(ChevronDownIcon,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})})]}),e.jsx(N,{as:t.Fragment,enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:e.jsxs(Listbox.Options,{className:"absolute right-0 mt-2 w-full origin-top divide-y divide-gray-100 rounded-md bg-brown-main-bg shadow-lg ring-1 ring-[#1f1d1a]/5 focus:outline-none",children:[e.jsx("div",{className:"",children:companies.map(h=>e.jsx(Listbox.Option,{value:h,children:({active:b})=>e.jsx("button",{className:`${b?"bg-brown-main-bg":""} w-full px-4 py-2 text-left text-lg font-medium`,children:h.name})},h.id))}),e.jsx("button",{className:"mt-6 p-4 font-medium text-gray-500 underline",onClick:()=>setAddCompanyModal(!0),children:"+Add New Recipient"})]})})]}),e.jsxs("form",{className:"flex gap-4",onSubmit:m(x),children:[e.jsxs("div",{children:[e.jsxs("select",{...d("group_id"),className:`focus:shadow-outline w-full appearance-none rounded border px-12 py-2 leading-tight text-[#1f1d1a] shadow focus:outline-none ${(j=f.group_id)!=null&&j.message?"border-red-500":""}`,children:[e.jsx("option",{value:"",children:"Select Group"}),r.filter(h=>!s.some(b=>b.group_id==h.id)).map(h=>e.jsx("option",{value:h.id,children:h.group_name},h.id))]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(k=f.group_id)==null?void 0:k.message})]}),e.jsx(O,{loading:o,disabled:o,type:"submit",className:"rounded bg-primary-black px-4 py-2 text-white focus:outline-none",children:"Add"})]}),e.jsx("div",{className:"mt-2 flex flex-wrap justify-end gap-4",children:s.map(h=>e.jsx(V,{className:"relative",children:({open:b})=>e.jsxs(e.Fragment,{children:[e.jsxs(V.Button,{as:"div",className:"inline-flex items-center gap-3 rounded bg-brown-main-bg px-4 py-2 hover:text-gray-900",children:[h.group_name,e.jsx("button",{type:"button",onClick:p=>{p.stopPropagation(),g(h.id)},className:"ml-1 focus:outline-none",children:e.jsx(I,{className:"h-5 w-5",strokeWidth:2})})]},h.id),e.jsx(N,{as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(V.Panel,{className:"absolute left-0 z-10 mt-3 w-fit -translate-x-1/2 transform whitespace-nowrap px-4",children:e.jsx("div",{className:"overflow-hidden  rounded-lg bg-[#1f1d1a] p-4 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:e.jsxs("div",{className:"text-sm font-medium",children:[h.members.length==0?e.jsx("span",{children:"No members"}):null,h.members.map((p,v,C)=>e.jsxs("span",{className:"mr-2",children:[p.first_name," ",p.last_name," ",v==C.length-1?"":"•"]},p.id))]})})})})]})}))})]})]})};function Te({investors:s}){var k,h,b,p;const{id:u}=z(),{questions:c,refetch:n}=re(u),[a,r]=t.useState(!1),{dispatch:l}=t.useContext(F),{dispatch:i}=t.useContext(D),d=B({question:E().required("This field is required"),investor_id:E().required("This field is required")}),{register:m,handleSubmit:o,formState:{isSubmitting:f,errors:y},reset:x}=G({resolver:U(d),defaultValues:{question:"",investor_id:""}});async function g(v){try{await new A().callRawAPI("/v4/api/records/update_questions",{update_id:u,investor_id:v.investor_id,question:v.question},"POST"),w(i,"Question added successfully",5e3,"success"),x({question:"",investor_id:""}),n()}catch(C){T(l,C.message),w(i,C.message,5e3,"error")}}async function j(v){r(!0);try{await new A().callRawAPI(`/v4/api/records/update_questions/${v}`,{},"DELETE"),w(i,"Question deleted successfully",5e3,"success"),n()}catch(C){T(l,C.message),w(i,C.message,5e3,"error")}r(!1)}return e.jsxs("div",{className:"mt-12",children:[e.jsx("h4",{className:"text-xl font-bold",children:"Asks"}),e.jsx("div",{className:"mt-4 space-y-2",children:c.map(v=>e.jsxs("div",{className:"group flex max-w-xl items-start justify-between",children:[e.jsxs("p",{children:["- ",v.question," - ",v.user.email]}),e.jsx("button",{className:"hidden hover:text-red-500 group-hover:block",onClick:()=>j(v.id),disabled:a,children:e.jsx(me,{size:15})})]},v.id))}),e.jsxs("form",{onSubmit:o(g),className:"mt-4 flex items-center gap-4",children:[e.jsxs("div",{className:"flex max-w-5xl flex-grow items-center gap-6",children:[e.jsxs("div",{className:"flex-grow",children:[e.jsx("input",{type:"text",...m("question"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(k=y.question)!=null&&k.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(h=y.question)==null?void 0:h.message})]}),e.jsxs("div",{children:[e.jsxs("select",{className:`focus:shadow-outline w-full max-w-[200px] appearance-none rounded border border border-[#1f1d1a] bg-transparent py-2 pl-6 pr-8 font-Inter text-sm font-normal leading-tight text-[#1d1f1a] shadow shadow-none focus:outline-none ${(b=y.investor_id)!=null&&b.message?"border-red-500":""}`,...m("investor_id"),children:[e.jsx("option",{value:"",children:"-select fund manager-"}),s.map(v=>e.jsxs("option",{value:v.id,children:[v.first_name," ",v.last_name]},v.id))]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(p=y.investor_id)==null?void 0:p.message})]})]}),e.jsx(O,{type:"submit",loading:f,disabled:f||a,className:"rounded bg-primary-black px-4 py-2 font-bold text-white",children:"Ask"})]})]})}function Fe(s){const[u,c]=t.useState(!1),[n,a]=t.useState([]),{dispatch:r}=t.useContext(F),{dispatch:l}=t.useContext(D);async function i(){c(!0);try{const m=await new A().callRawAPI(`/v3/api/custom/goodbadugly/startup/updates/${s}/groups`);a(m.list)}catch(d){T(r,d.message),w(l,d.message,5e3,"error")}c(!1)}return t.useEffect(()=>{s&&i()},[s]),{loading:u,updateGroups:n,refetch:i}}function Ae({afterRestore:s}){const[u,c]=t.useState(!1),{dispatch:n}=t.useContext(F),{dispatch:a}=t.useContext(D),[r,l]=t.useState(!1),{id:i}=z();async function d(){l(!0);try{const m=new te;await m.delete("notes",{update_id:i});for(let o=0;o<W.length;o++){const f=W[o];await m.create("notes",{type:f,update_id:i,status:0})}c(!1),s(),w(a,"Default template restored")}catch(m){T(n,m.message),w(a,m.message,5e3,"error")}l(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"font-medium text-primary-black underline",onClick:()=>c(!0),children:"Clear template"}),e.jsx(N,{appear:!0,show:u,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>c(!1),children:[e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Are you sure"}),e.jsx("button",{onClick:()=>c(!1),type:"button",children:e.jsx(I,{className:"h-6 w-6"})})]}),e.jsx("p",{className:"mt-2",children:"Are you sure you want to restore to default?"}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>c(!1),children:"Cancel"}),e.jsx(Z,{loading:r,disabled:r,onClick:d,className:"disabled:bg-disabledblack h-[36px] rounded-lg bg-primary-black py-2 text-center font-iowan font-semibold text-white transition-colors duration-100",children:"Yes, restore"})]})]})})})})]})})]})}function De({update:s,afterEdit:u}){var y,x,g,j,k,h,b,p;const[c,n]=t.useState(!1),{dispatch:a}=t.useContext(F),{dispatch:r}=t.useContext(D),l=B({mrr:E().required("This field is required"),arr:E().required("This field is required"),cash:E().required("This field is required"),burnrate:E().required("This field is required")}),{register:i,handleSubmit:d,formState:{isSubmitting:m,errors:o}}=G({resolver:U(l),defaultValues:{mrr:s.mrr,arr:s.arr,cash:s.cash,burnrate:s.burnrate}});async function f(v){try{await new A().callRawAPI(`/v4/api/records/updates/${s.id}`,{mrr:v.mrr,arr:v.arr,burnrate:v.burnrate,cash:v.cash,sync:P.MANUAL},"PUT"),n(!1),u()}catch(C){T(a,C.message),w(r,C.message,5e3,"error")}}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded-md bg-primary-black p-2 font-iowan font-medium text-white",onClick:()=>n(!0),children:"Edit manually"}),e.jsx(N,{appear:!0,show:c,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{as:"form",className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",onSubmit:d(f),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Edit update"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(I,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"MRR"}),e.jsx("input",{type:"text",...i("mrr"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(y=o.mrr)!=null&&y.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(x=o.mrr)==null?void 0:x.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"ARR"}),e.jsx("input",{type:"text",...i("arr"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(g=o.arr)!=null&&g.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(j=o.arr)==null?void 0:j.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Cash"}),e.jsx("input",{type:"text",...i("cash"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(k=o.cash)!=null&&k.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(h=o.cash)==null?void 0:h.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Burnrate"}),e.jsx("input",{type:"text",...i("burnrate"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(b=o.burnrate)!=null&&b.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(p=o.burnrate)==null?void 0:p.message})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>n(!1),children:"Cancel"}),e.jsx(O,{loading:m,disabled:m,type:"submit",className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})]})]})})})})]})})]})}function Ee({update:s,refetchUpdate:u}){const[c,n]=t.useState(!1);t.useContext(F),t.useContext(D);const{profile:a}=se(),{getMany:r,showToast:l}=ae();t.useState(!1);const[i,d]=t.useState(!1),[m,o]=t.useState(!1),[f,y]=t.useState({stripe:null,plaid:null}),x=async()=>{var g,j,k,h,b,p;if(a!=null&&a.id)try{o(!0);const v=await r("stripe_integration",{filter:[`user_id,eq,${a==null?void 0:a.id}`,`company_id,eq,${(j=(g=a==null?void 0:a.companies)==null?void 0:g[0])==null?void 0:j.id}`]}),C=await r("plaid_integration",{filter:[`user_id,eq,${a==null?void 0:a.id}`,`company_id,eq,${(h=(k=a==null?void 0:a.companies)==null?void 0:k[0])==null?void 0:h.id}`]});y({stripe:((b=v==null?void 0:v.data)==null?void 0:b[0])||null,plaid:((p=C==null?void 0:C.data)==null?void 0:p[0])||null})}catch(v){console.error(v),l("Error fetching integrations",5e3,"error")}finally{o(!1)}};return useEffect(()=>{(async()=>a!=null&&a.id&&await x())()},[a==null?void 0:a.id]),e.jsxs(e.Fragment,{children:[e.jsxs("button",{className:"flex w-full items-center justify-between text-sm font-medium text-[#1f1d1a] md:text-[18px]",onClick:()=>n(!0),children:["Show financial metrics"," ",e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.5 18.3333H2.5C2.15833 18.3333 1.875 18.05 1.875 17.7083C1.875 17.3667 2.15833 17.0833 2.5 17.0833H17.5C17.8417 17.0833 18.125 17.3667 18.125 17.7083C18.125 18.05 17.8417 18.3333 17.5 18.3333Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M15.8495 2.89999C14.2328 1.28332 12.6495 1.24166 10.9912 2.89999L9.98283 3.90832C9.89949 3.99166 9.86616 4.12499 9.89949 4.24166C10.5328 6.44999 12.2995 8.21666 14.5078 8.84999C14.5412 8.85832 14.5745 8.86666 14.6078 8.86666C14.6995 8.86666 14.7828 8.83332 14.8495 8.76666L15.8495 7.75832C16.6745 6.94166 17.0745 6.14999 17.0745 5.34999C17.0828 4.52499 16.6828 3.72499 15.8495 2.89999Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M13.0089 9.60832C12.7673 9.49166 12.5339 9.37499 12.3089 9.24166C12.1256 9.13332 11.9506 9.01666 11.7756 8.89166C11.6339 8.79999 11.4673 8.66666 11.3089 8.53332C11.2923 8.52499 11.2339 8.47499 11.1673 8.40832C10.8923 8.17499 10.5839 7.87499 10.3089 7.54166C10.2839 7.52499 10.2423 7.46666 10.1839 7.39166C10.1006 7.29166 9.95892 7.12499 9.83392 6.93332C9.73392 6.80832 9.61726 6.62499 9.50892 6.44166C9.37559 6.21666 9.25892 5.99166 9.14226 5.75832C9.1246 5.72049 9.10752 5.68286 9.09096 5.64544C8.96798 5.36767 8.60578 5.28647 8.39098 5.50126L3.61726 10.275C3.50892 10.3833 3.40892 10.5917 3.38392 10.7333L2.93392 13.925C2.85059 14.4917 3.00892 15.025 3.35892 15.3833C3.65892 15.675 4.07559 15.8333 4.52559 15.8333C4.62559 15.8333 4.72559 15.825 4.82559 15.8083L8.02559 15.3583C8.17559 15.3333 8.38392 15.2333 8.48392 15.125L13.2517 10.3572C13.468 10.1409 13.3864 9.76972 13.105 9.64967C13.0734 9.63615 13.0414 9.62238 13.0089 9.60832Z",fill:"#1F1D1A"})]})]}),e.jsx(N,{appear:!0,show:c,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Financial Overview"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(I,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal md:text-base",children:[s.sync==P.MANUAL?"*":"","MRR"]}),e.jsx("p",{children:s.mrr})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal md:text-base",children:[s.sync==P.MANUAL?"*":"","ARR"]}),e.jsx("p",{children:s.arr})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal md:text-base",children:[s.sync==P.MANUAL?"*":"","Cash"]}),e.jsx("p",{children:s.cash})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal md:text-base",children:[s.sync==P.MANUAL?"*":"","Burnrate"]}),e.jsx("p",{children:s.burnrate})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm font-normal md:text-base",children:"Last sync"}),e.jsxs("p",{children:[" ",s.last_sync?$(s.last_sync).format("DD MMM hh:mm a z"):"N/A"]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:s.id?e.jsx(De,{update:s,afterEdit:u,loading:m}):null}),i?e.jsx("div",{className:"mt-4 border border-black/60 bg-brown-main-bg p-3",children:e.jsxs("p",{className:"flex items-center gap-3 font-semibold",children:[e.jsx(ue,{className:"h-5 text-yellow-500",strokeWidth:2}),"No integrations setup currently."]})}):null]})]})})})})]})})]})}function Pe({update:s,afterEdit:u}){var y,x,g,j,k,h,b,p,v,C;const[c,n]=t.useState(!1),{dispatch:a}=t.useContext(F),{dispatch:r}=t.useContext(D),l=B({investment_stage:E().required("This field is required"),invested_to_date:E().required("This field is required"),investors_on_cap_table:E().required("This field is required"),valuation_at_last_round:E().required("This field is required"),date_of_last_round:E().required("This field is required")}),{register:i,handleSubmit:d,formState:{isSubmitting:m,errors:o}}=G({resolver:U(l),defaultValues:{investment_stage:s.investment_stage,invested_to_date:s.invested_to_date,investors_on_cap_table:s.investors_on_cap_table,valuation_at_last_round:s.valuation_at_last_round,date_of_last_round:s.date_of_last_round}});async function f(L){try{await new A().callRawAPI(`/v4/api/records/updates/${s.id}`,{investment_stage:L.investment_stage,invested_to_date:L.invested_to_date,valuation_at_last_round:L.valuation_at_last_round,investors_on_cap_table:L.investors_on_cap_table,date_of_last_round:L.date_of_last_round,investment_details_sync:P.MANUAL},"PUT"),n(!1),u()}catch(R){T(a,R.message),w(r,R.message,5e3,"error")}}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded-md bg-primary-black p-2 font-iowan font-medium text-white",onClick:()=>n(!0),children:"Edit manually"}),e.jsx(N,{appear:!0,show:c,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{as:"form",className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",onSubmit:d(f),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Edit update"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(I,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Investment Stage"}),e.jsx("input",{type:"text",...i("investment_stage"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(y=o.investment_stage)!=null&&y.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(x=o.investment_stage)==null?void 0:x.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Invested to Date"}),e.jsx("input",{type:"text",...i("invested_to_date"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(g=o.invested_to_date)!=null&&g.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(j=o.invested_to_date)==null?void 0:j.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Fund managers on Cap Table"}),e.jsx("input",{type:"text",...i("investors_on_cap_table"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(k=o.investors_on_cap_table)!=null&&k.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(h=o.investors_on_cap_table)==null?void 0:h.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Valuation at Last Round"}),e.jsx("input",{type:"text",...i("valuation_at_last_round"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(b=o.valuation_at_last_round)!=null&&b.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(p=o.valuation_at_last_round)==null?void 0:p.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Date of Last Round"}),e.jsx("input",{type:"date",...i("date_of_last_round"),className:`focus:shadow-outline w-full max-w-[500px]  appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 pr-5 text-sm font-normal leading-tight   text-[#1d1f1a] shadow focus:outline-none sm:pr-3 ${(v=o.date_of_last_round)!=null&&v.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(C=o.date_of_last_round)==null?void 0:C.message})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>n(!1),children:"Cancel"}),e.jsx(O,{loading:m,disabled:m,type:"submit",className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})]})]})})})})]})})]})}function Ie({update:s,refetchUpdate:u}){const[c,n]=t.useState(!1);return t.useContext(F),t.useContext(D),t.useState(!1),e.jsxs(e.Fragment,{children:[e.jsxs("button",{className:"flex w-full items-center justify-between text-[16px] font-medium text-[#1f1d1a] md:text-[18px]",onClick:()=>n(!0),children:["Show investment metrics"," ",e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.5 18.3333H2.5C2.15833 18.3333 1.875 18.05 1.875 17.7083C1.875 17.3667 2.15833 17.0833 2.5 17.0833H17.5C17.8417 17.0833 18.125 17.3667 18.125 17.7083C18.125 18.05 17.8417 18.3333 17.5 18.3333Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M15.8495 2.89999C14.2328 1.28332 12.6495 1.24166 10.9912 2.89999L9.98283 3.90832C9.89949 3.99166 9.86616 4.12499 9.89949 4.24166C10.5328 6.44999 12.2995 8.21666 14.5078 8.84999C14.5412 8.85832 14.5745 8.86666 14.6078 8.86666C14.6995 8.86666 14.7828 8.83332 14.8495 8.76666L15.8495 7.75832C16.6745 6.94166 17.0745 6.14999 17.0745 5.34999C17.0828 4.52499 16.6828 3.72499 15.8495 2.89999Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M13.0089 9.60832C12.7673 9.49166 12.5339 9.37499 12.3089 9.24166C12.1256 9.13332 11.9506 9.01666 11.7756 8.89166C11.6339 8.79999 11.4673 8.66666 11.3089 8.53332C11.2923 8.52499 11.2339 8.47499 11.1673 8.40832C10.8923 8.17499 10.5839 7.87499 10.3089 7.54166C10.2839 7.52499 10.2423 7.46666 10.1839 7.39166C10.1006 7.29166 9.95892 7.12499 9.83392 6.93332C9.73392 6.80832 9.61726 6.62499 9.50892 6.44166C9.37559 6.21666 9.25892 5.99166 9.14226 5.75832C9.1246 5.72049 9.10752 5.68286 9.09096 5.64544C8.96798 5.36767 8.60578 5.28647 8.39098 5.50126L3.61726 10.275C3.50892 10.3833 3.40892 10.5917 3.38392 10.7333L2.93392 13.925C2.85059 14.4917 3.00892 15.025 3.35892 15.3833C3.65892 15.675 4.07559 15.8333 4.52559 15.8333C4.62559 15.8333 4.72559 15.825 4.82559 15.8083L8.02559 15.3583C8.17559 15.3333 8.38392 15.2333 8.48392 15.125L13.2517 10.3572C13.468 10.1409 13.3864 9.76972 13.105 9.64967C13.0734 9.63615 13.0414 9.62238 13.0089 9.60832Z",fill:"#1F1D1A"})]})]}),e.jsx(N,{appear:!0,show:c,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Investment Overview"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(I,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal md:text-base",children:[s.investment_details_sync==P.MANUAL?"*":"","Investment Stage"]}),e.jsx("p",{children:s.investment_stage??"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal  md:text-base",children:[s.investment_details_sync==P.MANUAL?"*":"","Invested to Date"]}),e.jsx("p",{children:s.invested_to_date??"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal  md:text-base",children:[s.investment_details_sync==P.MANUAL?"*":"","Fund managers on Cap Table"]}),e.jsx("p",{children:s.investors_on_cap_table??"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal  md:text-base",children:[s.investment_details_sync==P.MANUAL?"*":"","Valuation at Last Round"]}),e.jsx("p",{children:s.valuation_at_last_round??"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm font-normal  md:text-base",children:"Date of Last Round"}),e.jsxs("p",{children:[" ",s.date_of_last_round?$(s.date_of_last_round).format("DD MMM"):"N/A"]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:s.id?e.jsx(Pe,{update:s,afterEdit:u}):null})]})]})})})})]})})]})}const $e={paragraph:pe,checkList:je,list:fe,header:he,delimiter:ge,link:be},Le=t.memo(({data:s,editing:u=!0,editorID:c,note_id:n,afterEdit:a,setUpdated:r,updateSaved:l,report:i=!1})=>{const{dispatch:d}=t.useContext(F),{dispatch:m}=t.useContext(D),[o,f]=t.useState(!1);console.log(c);async function y(g){try{const k=await new A().callRawAPI(`/v4/api/records/notes/${n}`,{content:JSON.stringify(g)},"PUT");i&&a()}catch(j){T(d,j.message),w(m,j.message,5e3,"error")}}const x=t.useRef();return t.useEffect(()=>{if(!x.current){const g=new xe({holder:c,minHeight:30,readOnly:!u,tools:$e,data:s,async onChange(j,k){const h=await j.saver.save();y(h)},onReady:()=>{x.current=g}})}return()=>{x.current&&x.current.destroy&&x.current.destroy()}},[]),t.useEffect(()=>{x.current&&x.current.readOnly.toggle(!u)},[u]),e.jsx("div",{className:`${u?"editorjs-container border border-green-300":"editorjs-container-transparent  "} rounded   px-3 py-2`,id:c,onBlur:()=>f(!0)})});function Re({note_id:s,afterDelete:u}){const[c,n]=t.useState(!1),{dispatch:a}=t.useContext(F),{dispatch:r}=t.useContext(D),[l,i]=t.useState(!1);async function d(){i(!0);try{await new A().callRawAPI(`/v4/api/records/notes/${s}`,{},"DELETE"),n(!1),u()}catch(m){T(a,m.message),w(r,m.message,5e3,"error")}i(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded bg-red-500 px-4 py-2 font-medium text-white",onClick:()=>n(!0),children:"Delete"}),e.jsx(N,{appear:!0,show:c,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Are you sure you want to delete this entry?"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(I,{className:"h-6 w-6"})})]}),e.jsx("p",{className:"mt-2",children:"This action cannot be undone."}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>n(!1),children:"Cancel"}),e.jsx(O,{loading:l,disabled:l,onClick:d,className:"rounded-lg bg-[#1f1d1a] py-2 text-center font-iowan font-semibold text-white transition-colors duration-100 disabled:bg-opacity-60",children:"Yes, delete"})]})]})})})})]})})]})}function Me({note:s,afterAsking:u}){const[c,n]=t.useState(!1),{dispatch:a}=t.useContext(F),{dispatch:r}=t.useContext(D),l=B({expandOrShorten:E().nullable().optional(),rephrase:K(),correctGrammar:K()}),{register:i,handleSubmit:d,formState:{isSubmitting:m},reset:o}=G({resolver:U(l),defaultValues:{expandOrShorten:"",rephrase:!1,correctGrammar:!1}});function f(x){return!x||!x.blocks?"":x.blocks.map(g=>g.type==="header"?`${g.data.text}`:g.type==="list"?g.data.items.map(j=>`• ${j}`).join(`
`):g.type==="paragraph"?g.data.text:"").join(`

`)}async function y(x){try{let g=f(H(s.content,{blocks:[]}));const j=new A;if(x.expandOrShorten||x.correctGrammar||x.rephrase){const h=`
        ${x.expandOrShorten?`${x.expandOrShorten} text. 
`:""}
        ${x.rephrase?`Rewrite text. 
`:""}
        ${x.correctGrammar?`Fix grammar. 
`:""}
            ${g}
        `.trim(),b=[{role:"system",content:"You are an expert auditor auditing for a startup. create and update for the investor do not explain"},{role:"user",content:h}];g=(await j.callRawAPI("/v3/api/custom/goodbadugly/ai/ask",{temperature:1,prompt:b},"POST")).data[0].message.content,await j.callRawAPI(`/v4/api/records/notes/${s.id}`,{content:JSON.stringify({time:Date.now(),blocks:[{id:Math.floor(Math.random()*999)+1,type:"list",data:{style:"unordered",items:[g]}}]})},"PUT")}u(),n(!1),o({expandOrShorten:"",rephrase:!1,correctGrammar:!1})}catch(g){T(a,g.message),w(r,g.message,5e3,"error")}}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded bg-primary-black px-4 py-2 font-medium text-white",onClick:()=>{n(!0),u()},children:"Ask AI"}),e.jsx(N,{appear:!0,show:c,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"w-full max-w-3xl transform overflow-hidden rounded-md bg-brown-main-bg bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",as:"form",onSubmit:d(y),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_.Title,{as:"h3",className:"text-xl font-semibold leading-6 text-gray-900",children:"Ask AI"}),e.jsx("button",{onClick:()=>n(!1),type:"button",children:e.jsx(I,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"bold mt-4 flex items-center gap-4 font-iowan text-lg",children:[e.jsx("input",{type:"radio",...i("expandOrShorten"),value:"expand"}),e.jsx("label",{children:"Expand"}),e.jsx("input",{type:"radio",...i("expandOrShorten"),value:"shorten"}),e.jsx("label",{children:"Shorten"})]}),e.jsxs("div",{className:"bold flex items-center gap-4 font-iowan text-lg",children:[e.jsx("input",{type:"checkbox",...i("rephrase")}),e.jsx("label",{children:"Rephrase"})]}),e.jsxs("div",{className:"bold flex items-center gap-4 font-iowan text-lg",children:[e.jsx("input",{type:"checkbox",...i("correctGrammar")}),e.jsx("label",{children:"Correct Grammar"})]}),e.jsx(O,{type:"submit",loading:m,disabled:m,className:"mt-6 w-full rounded bg-primary-black px-4 py-2 font-bold text-white",children:"Send request"})]})})})})]})})]})}function ze({note:s,afterEdit:u}){const[c,n]=t.useState(!1),{dispatch:a}=t.useContext(F),{dispatch:r}=t.useContext(D),[l,i]=t.useState(!1),[d,m]=t.useState(null),[o,f]=t.useState(!1),{startRecording:y,stopRecording:x,recordingBlob:g,isRecording:j}=ve();t.useEffect(()=>{g&&(console.log("setting blob"),m(g))},[g]),t.useEffect(()=>{!d||!l||k()},[d,l]);async function k(){n(!0);try{let b=new FormData;const p=new File([d],"audio.wav",{type:"audio/wav"});b.append("file",p),console.log("f",d);const C=await new A().callTranscribe("/v3/api/custom/goodbadugly/ai/transcribe-audio",b,"POST");console.log(C);const L=H(s.content,{blocks:[]});h(JSON.stringify({time:Date.now(),blocks:[...L.blocks,{id:Math.floor(Math.random()*999)+1,type:"list",data:{style:"unordered",items:[C.data]}}]}))}catch(b){T(a,b.message),w(r,b.message,5e3,"error")}n(!1),i(!1),m(null)}async function h(b){f(!0);try{await new A().callRawAPI(`/v4/api/records/notes/${s.id}`,{content:b},"PUT"),u()}catch(p){T(a,p.message),w(r,p.message,5e3,"error")}f(!1)}return e.jsx("button",{onClick:async()=>{j?(x(),i(!0)):y()},disabled:c||o,className:`focus:shadow-outline ${j?"animate-pulse":""}`,title:"Transcribe more",children:j?e.jsx("div",{className:"rounded-[50%] bg-red-500 p-2",children:e.jsx(ye,{className:"h-6 text-white"})}):e.jsx(we,{className:"h-6 w-6 text-primary-black",strokeWidth:2})})}function Oe({note:s,afterEdit:u}){const{dispatch:c}=t.useContext(F),{dispatch:n}=t.useContext(D),[a,r]=t.useState(!1),[l,i]=t.useState(""),[d,m]=t.useState(null);t.useEffect(()=>{i(s.type)},[s]);async function o(f){r(!0);try{await new A().callRawAPI(`/v4/api/records/notes/${s.id}`,{type:f},"PUT"),u(),w(n,"Saved")}catch(y){T(c,y.message),w(n,y.message,5e3,"error")}r(!1)}return e.jsx(e.Fragment,{children:e.jsx("input",{className:"no-box-shadow w-full rounded border-none bg-transparent p-0 text-xl font-bold ring-transparent",value:l,onChange:f=>{i(f.target.value),d&&clearTimeout(d);const y=setTimeout(()=>o(f.target.value),2e3);m(y)},readOnly:a})})}const Q=t.memo(({note:s,refetch:u,profilePic:c})=>{var d;const{dispatch:n,state:a}=t.useContext(F),{note:r,refetch:l}=Ne(s.id,s),i=H(r.content,{blocks:[{id:"zbGZFPM-iI",type:"paragraph",data:{text:""}}]});return console.log(a),e.jsx("div",{className:"flex flex-col gap-4 justify-between",children:e.jsxs("div",{className:"flex flex-col justify-between",children:[e.jsxs("div",{className:"flex flex-row justify-between items-center w-full",children:[e.jsx("div",{className:"flex gap-3 items-center group",children:e.jsx(Oe,{note:r,afterEdit:l})}),c&&e.jsx("img",{className:"h-7 w-7 rounded-[50%] object-cover",src:((d=a==null?void 0:a.profile)==null?void 0:d.photo)||"/default.png"})]}),e.jsx("div",{className:"self-end mt-4 space-y-4 w-full",children:e.jsxs("div",{className:"",children:[" ",e.jsx(Le,{data:i,note_id:r.id,editorID:`editorjs-container-${r.id}`,afterEdit:l}),e.jsxs("div",{className:"flex justify-between items-center mt-4",children:[e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsx(Me,{note:r,afterAsking:l}),e.jsx(Re,{note_id:r.id,afterDelete:u}),e.jsx(ze,{note:r,afterEdit:l})]}),e.jsxs("p",{className:"font-medium",children:["Last saved:"," ",Math.abs($(r.update_at).diff($(),"hours"))<24?$(r.update_at).format("hh:mm a z"):Math.abs($(r.update_at).diff($(),"years"))>0?$(r.update_at).format("MM/DD/YYYY"):$(r.update_at).format("DD MMM hh:mm a z")]})]})]})})]})})});function qe({afterAdd:s}){const[u,c]=t.useState(!1),{dispatch:n}=t.useContext(F),{dispatch:a}=t.useContext(D),[r,l]=t.useState(!1),[i,d]=t.useState(""),{id:m}=z();async function o(){l(!0);try{await new A().callRawAPI("/v4/api/records/notes",{type:i,update_id:m,status:0},"POST"),c(!1),d(""),s(),w(a,"New section added")}catch(f){T(n,f.message),w(a,f.message,5e3,"error")}l(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"mt-6 block w-full rounded-[2px] bg-primary-black/90 py-2 font-semibold text-white",onClick:()=>c(!0),children:"Add section +"}),e.jsx(N,{appear:!0,show:u,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>c(!1),children:[e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-sm shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Add section name"}),e.jsx("button",{onClick:()=>c(!1),type:"button",children:e.jsx(I,{className:"h-6 w-6"})})]}),e.jsx("div",{className:"mt-6",children:e.jsx("input",{type:"text",value:i,onChange:f=>d(f.target.value),className:"focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none "})}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a]/30 py-2 text-center font-iowan font-medium",type:"button",onClick:()=>c(!1),children:"Cancel"}),e.jsx(Z,{loading:r,disabled:r,onClick:o,className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})]})]})})})})]})})]})}function Ue({update:s,afterEdit:u}){const{dispatch:c}=t.useContext(F),{dispatch:n}=t.useContext(D),[a,r]=t.useState(!1),[l,i]=t.useState(""),{id:d}=z(),[m,o]=t.useState(null);t.useEffect(()=>{i(s.name)},[s]);async function f(y){r(!0);try{await new A().callRawAPI(`/v4/api/records/updates/${d}`,{name:y},"PUT"),u(),w(n,"Saved")}catch(x){T(c,x.message),w(n,x.message,5e3,"error")}r(!1)}return e.jsx(e.Fragment,{children:e.jsx("input",{className:"no-box-shadow focus:shadow-outline appearance-none border-none bg-brown-main-bg p-0 text-3xl font-bold focus:outline-none",value:l,onChange:y=>{i(y.target.value),m&&clearTimeout(m);const x=setTimeout(()=>f(y.target.value),2e3);o(x)},readOnly:a,style:{width:`${l.length+3}ch`}})})}function Ge({update:s,refetch:u}){const[c,n]=t.useState(!1),{dispatch:a}=t.useContext(F),{dispatch:r}=t.useContext(D),[l,i]=t.useState(!1),[d,m]=t.useState(s.private_link_open==Y.YES),{id:o}=z(),[f,y]=t.useState(s.public_link_id),[x,g]=t.useState(s.private_link_access),[j,k]=t.useState(s.public_link_enabled==1);async function h(){i(!0);try{await new A().callRawAPI(`/v4/api/records/updates/${o}`,{private_link_access:x,private_link_open:d?Y.YES:Y.NO,public_link_id:f,public_link_enabled:j?1:0},"PUT"),u(),w(r,"Saved"),n(!1)}catch(p){T(a,p.message),w(r,p.message,5e3,"error")}i(!1)}function b(){y(ne(15)),w(r,"New link generated")}return e.jsxs(e.Fragment,{children:[e.jsxs("button",{className:"flex items-center gap-2 font-medium",onClick:()=>n(!0),children:[e.jsx(_e,{className:"h-4",strokeWidth:2}),J[s.private_link_access]]}),e.jsx(N,{appear:!0,show:c,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>n(!1),children:[e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(N.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"relative mb-12 w-full max-w-4xl transform overflow-visible rounded-md bg-brown-main-bg p-8 text-left align-middle text-base shadow-xl transition-all",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(_.Title,{as:"h3",className:"text-xl font-bold text-gray-900",children:"Share this update"})}),e.jsxs("div",{className:"mt-6 flex items-center gap-6 rounded-md bg-brown-main-bg p-4 font-medium text-gray-800",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-lg font-semibold text-[#1f1d1a]",children:"Restricted link"}),e.jsx("p",{children:"Logged in users that have access to your update will be able to view it on update sent with the following link."})]}),e.jsx("select",{className:"focus:shadow-outline appearance-none rounded border py-2 pl-3 pr-8 leading-tight text-[#1f1d1a] shadow focus:outline-none ",value:x,onChange:p=>g(p.target.value),children:Object.entries(J).map(([p,v])=>e.jsx("option",{value:p,children:v},v))})]}),e.jsxs("div",{className:"mt-6 flex items-center gap-12 rounded-md bg-blue-100 px-4 py-3",children:[e.jsx("input",{id:"private-link-input",onFocus:p=>p.target.select(),value:`${window.location.origin}/reports/view/${o}`,className:"no-box-shadow flex-grow border-none bg-transparent px-2 py-0 text-lg font-medium text-primary-black ring-transparent selection:bg-green-200"}),e.jsx("button",{type:"button",className:"font-semibold text-primary-black",onClick:async()=>{var p;await navigator.clipboard.writeText(`${window.location.origin}/reports/view/${o}`),(p=document.getElementById("private-link-input"))==null||p.select(),w(r,"Copied")},children:"Copy link"})]}),e.jsxs("div",{className:"mt-4 flex items-center gap-4",children:[e.jsx(q,{enabled:d,setEnabled:m}),e.jsx("p",{children:"If allowed access via update privacy, enable logged out users to view by providing their email"})]}),e.jsxs("div",{className:"mt-6 rounded-md border border-black/60 p-4",children:[e.jsxs("p",{className:"font-semibold",children:["Shareable link ",j?"":"(disabled)"]}),e.jsx("p",{className:"mt-2",children:"Anyone with the shareable link can view this update."}),e.jsxs("div",{className:"mt-6 flex items-center gap-12 rounded-md bg-red-100 px-4 py-3",children:[e.jsx("input",{value:f?`${window.location.origin}/reports/public/view/${o}/${f}`:"",onFocus:p=>p.target.select(),className:`no-box-shadow flex-grow border-none bg-transparent px-2 py-0 text-lg font-medium text-red-500 selection:bg-orange-200 focus:outline-transparent ${j?"":"opacity-50"}`,disabled:!j,id:"public-link-input"}),e.jsx("button",{type:"button",className:"font-semibold text-red-500",disabled:!f||!j,onClick:async()=>{var p;await navigator.clipboard.writeText(`${window.location.origin}/reports/public/view/${o}/${f}`),(p=document.getElementById("public-link-input"))==null||p.select(),w(r,"Copied")},children:"Copy link"})]}),e.jsxs("div",{className:"mt-6 flex items-center gap-3",children:[e.jsx("button",{className:"rounded-md bg-primary-black px-4 py-2 font-iowan font-medium text-white",onClick:b,children:"Generate New Shareable Link"}),s.public_link_id&&j?e.jsx("button",{className:"rounded-md border border-[#1f1d1a] px-4 py-2 font-iowan text-[14px] font-medium  xl:text-[16px]",onClick:()=>k(!1),children:"Disable Shareable Link"}):null,s.public_link_id&&!j?e.jsx("button",{className:"rounded-md border border-[#1f1d1a] px-4 py-2 font-iowan text-[14px] font-medium  xl:text-[16px]",onClick:()=>k(!0),children:"Enable Shareable Link"}):null]})]}),e.jsx("div",{className:"mt-12 flex justify-end",children:e.jsx(Z,{loading:l,disabled:l,onClick:h,className:"disabled:bg-disabledblack rounded-md bg-primary-black px-6 py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})}),e.jsx("button",{onClick:()=>n(!1),type:"button",className:"absolute right-0 top-0 -translate-y-1/2 translate-x-1/2 rounded-[50%] bg-brown-main-bg p-1 shadow-md",children:e.jsx(I,{className:"h-6 w-6 text-gray-900",strokeWidth:5})})]})})})})]})})]})}const Pt=()=>{const{dispatch:s}=t.useContext(D),{dispatch:u,state:c}=t.useContext(F),{id:n}=z(),{update:a,loading:r,refetch:l}=le(n),{notes:i,refetch:d}=oe(n),{updateGroups:m,refetch:o}=Fe(n),[f,y]=t.useState(""),[x,g]=t.useState("standard"),[j,k]=t.useState(!1),[h,b]=t.useState("7"),[p,v]=t.useState(!1),{updateCollaborators:C,refetch:L}=ce(n);t.useEffect(()=>{y(a.date),b(a.recipient_access||"7")},[a]),t.useEffect(()=>{s({type:"SETPATH",payload:{path:"updates"}})},[]);async function R(S){v(!0);try{await new A().callRawAPI(`/v4/api/records/updates/${n}`,{[S]:a[S]?0:1},"PUT"),l(),w(s,"Updated")}catch(M){T(u,M.message),w(s,M.message,5e3,"error")}v(!1)}return r?e.jsx(Ce,{}):(console.log(i),e.jsxs("div",{className:"mx-auto rounded px-5 py-5 shadow-md xl:px-12",children:[e.jsxs("h2",{className:"text-xl font-normal",children:[c.company.name," ",a.status==ie.DRAFT?e.jsx("span",{className:"ml-4 bg-brown-main-bg text-xl text-gray-400/90",children:"DRAFT IN PROGRESS"}):null]}),e.jsxs("div",{className:"mt-8 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(Ue,{update:a,afterEdit:l}),e.jsx("div",{children:e.jsx("input",{className:"border pr-5 sm:pr-3",type:"date",value:f,onChange:S=>y(S.target.value)})})]}),e.jsx(Ae,{afterRestore:d})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("p",{className:"font-medium",children:"Privacy:"}),e.jsx(Ge,{update:a,refetch:l}),e.jsxs("button",{className:"flex items-center gap-2 rounded-md bg-brown-main-bg px-3 py-1",children:[e.jsx(de,{className:"h-4",strokeWidth:2}),"Share"]})]})]}),e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"mt-12 flex items-start justify-between",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(q,{enabled:a.show_financial_metrics==1,setEnabled:()=>R("show_financial_metrics")}),e.jsx(Ee,{update:a,refetchUpdate:l})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(q,{enabled:a.show_company_metrics==1,setEnabled:()=>R("show_company_metrics")}),e.jsx("p",{className:"text-xl font-semibold text-gray-700",children:"Show company/team metrics"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(q,{enabled:a.show_marketing_metrics==1,setEnabled:()=>R("show_marketing_metrics")}),e.jsx("p",{className:"text-xl font-semibold text-gray-700",children:"Show marketing metrics"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(q,{enabled:a.show_investment_metrics==1,setEnabled:()=>R("show_investment_metrics")}),e.jsx(Ie,{update:a,refetchUpdate:l})]})]}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"flex w-full max-w-xl justify-end",children:e.jsx(Se,{updateGroups:m,refetch:o})}),e.jsxs("div",{className:"mt-6 flex items-center justify-end gap-6",children:[e.jsx("p",{className:"font-semibold",children:"Select update template"}),e.jsx("div",{children:e.jsxs("select",{value:x,onChange:S=>g(S.target.value),className:"focus:shadow-outline w-full appearance-none rounded border bg-[#FFF4EC] py-2 pl-6 pr-8 leading-tight text-[#1f1d1a] shadow focus:outline-none ",children:[e.jsx("option",{value:"",children:"Select Template"}),e.jsx("option",{value:"standard",children:"Standard template"})]})})]})]})]}),e.jsx("div",{className:"mt-10 space-y-12",children:i.map(S=>C.some(M=>M.note_id===S.id)?e.jsx(Q,{note:S,refetch:d,profilePic:!0},S.id):e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute z-[100] h-full w-full cursor-not-allowed"}),e.jsx(Q,{note:S,refetch:d},S.id)]}))}),e.jsx(qe,{afterAdd:d}),e.jsx(Te,{investors:m.flatMap(S=>S.members).filter((S,M,X)=>X.findIndex(ee=>ee.id===S.id)===M)}),e.jsxs("div",{className:"mt-8 flex flex-col items-start justify-between md:flex-row md:items-center",children:[e.jsxs("label",{className:"flex items-center gap-3 text-lg font-medium capitalize capitalize",children:[e.jsx("input",{type:"checkbox",checked:j,onChange:()=>k(S=>!S)})," ","CC myself"]}),e.jsxs("div",{className:"flex w-fit flex-wrap items-center gap-4",children:[e.jsx("p",{className:"whitespace-nowrap ",children:"Give recipients access for "}),e.jsxs("select",{value:h,onChange:S=>b(S.target.value),className:"focus:shadow-outline w-full appearance-none rounded border py-2 pl-6 pr-8 leading-tight text-[#1f1d1a] shadow focus:outline-none ",children:[e.jsx("option",{value:"7",children:"7 days"}),e.jsx("option",{value:"30",children:"One month"})]}),e.jsx("a",{href:`/collaborator/updates/${n}/preview`,target:"_blank",className:"focus:shadow-outline w-fit whitespace-nowrap rounded bg-primary-black px-4 py-2 font-bold text-white focus:outline-none",children:"Preview Update"})]})]})]})]}))};export{Pt as default};
