import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{r as i,u as t}from"./vendor-4cdf2bd1.js";import{_ as a}from"./qr-scanner-cf010ec4.js";import{a as m}from"./index-f08e5be1.js";import{L as e,S as l}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const p=i.lazy(()=>a(()=>import("./AdminHeader-ac473d0a.js"),["assets/AdminHeader-ac473d0a.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-87e46e89.js"])),d=({children:o})=>{const{pathname:s}=t();return s.includes("updates")&&s.includes("preview")?o:r.jsxs("div",{id:"admin_wrapper",className:"grid h-full max-h-full min-h-full w-full max-w-full grid-cols-[auto_1fr] grid-rows-1 bg-brown-main-bg ",children:[r.jsx(e,{children:r.jsx(p,{})}),r.jsxs("div",{className:"grid h-full w-full grid-cols-1 grid-rows-[auto_1fr]",children:[r.jsx(e,{children:r.jsx(m,{})}),r.jsx(i.Suspense,{fallback:r.jsx("div",{className:"flex h-screen w-full items-center justify-center",children:r.jsx(l,{size:40,color:"#1f1d1a"})}),children:r.jsx("div",{className:"w-full overflow-y-auto overflow-x-hidden",children:o})})]})]})},S=i.memo(d);export{S as default};
