import{_ as g}from"./qr-scanner-cf010ec4.js";import{r as e,i as E}from"./vendor-4cdf2bd1.js";import{j as a}from"./@nextui-org/listbox-0f38ca19.js";import{A as _,G as y,M as w,s as u,t as b}from"./index-f2ad9142.js";import"./lodash-82bd9112.js";import{u as v}from"./useFundProfile-6ed8f0b3.js";import{M as C}from"./index.esm-6fcccbfe.js";import{_ as j}from"./MoonLoader-6f2b5db4.js";const B=e.lazy(()=>g(()=>import("./BackButton-0b2ae6c2.js"),["assets/BackButton-0b2ae6c2.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"]));function O({update:T,afterEdit:P}){const{dispatch:d}=e.useContext(_),{dispatch:r}=e.useContext(y),[c,i]=e.useState(!1),[t,l]=e.useState("");E();const[m,p]=e.useState(null),{loading:f,profileData:o,refetch:x}=v();async function h(n){i(!0);try{await new w().callRawAPI("/v3/api/goodbadugly/customer/fund-profile",{display_name:n},"POST"),x(),u(r,"Company name saved")}catch(s){b(d,s.message),u(r,s.message,5e3,"error")}i(!1)}return console.log("profileData >>",o),e.useEffect(()=>{l(o==null?void 0:o.display_name)},[o]),a.jsx("div",{className:"flex flex-row items-center",children:f?a.jsx(j,{size:14}):a.jsxs(a.Fragment,{children:[a.jsx("input",{className:"no-box-shadow focus:shadow-outline appearance-none border-none bg-brown-main-bg p-0 text-3xl text-[18px] font-bold capitalize focus:outline-none",defaultValue:"Company name",value:t,onChange:n=>{l(n.target.value),m&&clearTimeout(m);const s=setTimeout(()=>h(n.target.value),2e3);p(s)},readOnly:c,style:{width:`${t?(t==null?void 0:t.length)+1:14}ch`}}),t?null:a.jsx(C,{size:16})]})})}export{B,O as E};
