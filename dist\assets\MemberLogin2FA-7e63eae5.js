import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as a,h,b}from"./vendor-4cdf2bd1.js";import{G as g,A as w,M as f}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const J=()=>{const{dispatch:x}=a.useContext(g),[t,i]=a.useState(""),{dispatch:u}=a.useContext(w),[o,m]=a.useState(),[p,j]=h(),r=JSON.parse(JSON.parse(atob(p.get("t")))),d=b();let n=new f;a.useEffect(()=>{n.authorize2FA(r.token).then(s=>{m(s)})},[]);const c=async s=>{s.preventDefault();try{const l=await n.authorize2FA(r.token);(await n.verify2FA(l.access_token,t)).error||(u({type:"LOGIN",payload:r}),d("/member/dashboard"),showToast(x,"Succesfully Logged In",4e3,"success"))}catch(l){console.log(l)}};return e.jsx("div",{className:"",children:o?e.jsx("div",{className:"mx-auto  mt-[5rem] w-full max-w-xs",children:o.type==="qr"?e.jsxs("div",{children:[e.jsx("h1",{className:"mb-5 text-center text-lg",children:"Scan QR Code to Continue Login"}),e.jsxs("form",{onSubmit:c,children:[e.jsx("input",{className:"h-[100%] w-[70%]",value:t,onChange:s=>i(s.target.value),type:"password",placeholder:"xxxxxxxx"}),e.jsx("button",{disabled:t.length<6,className:`${t.length<6?"bg-blue-300":"bg-blue-500 hover:bg-blue-700"} focus:shadow-outline  w-[30%] px-4 py-2 font-bold text-white  focus:outline-none disabled:cursor-not-allowed`,type:"submit",children:"Continue"})]}),o!=null&&o.qr_code?e.jsx("img",{src:o.qr_code,className:"mb-4 h-[300px] w-[100%] border bg-brown-main-bg px-8 pb-8 pt-6 shadow-lg"}):"",e.jsxs("p",{className:"text-center text-xs text-gray-500",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]}):e.jsxs("div",{className:"py-[100px]",children:[e.jsx("h1",{className:"mb-5 text-center text-lg",children:"Enter code sent to your device"}),e.jsxs("form",{onSubmit:c,children:[e.jsx("input",{className:"h-[100%] w-[70%]",value:t,onChange:s=>i(s.target.value),type:"password",placeholder:"xxxxxxxx"}),e.jsx("button",{disabled:t.length<6,className:`${t.length<6?"bg-blue-300":"bg-blue-500 hover:bg-blue-700"} focus:shadow-outline  w-[30%] px-4 py-2 font-bold text-white  focus:outline-none disabled:cursor-not-allowed`,type:"submit",children:"Continue"})]})]})}):""})};export{J as default};
