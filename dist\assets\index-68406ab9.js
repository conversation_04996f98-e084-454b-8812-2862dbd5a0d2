import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{o as q}from"./yup-0917e80c.js";import{A as P,G as _,ab as T,E as L,I as j,M as R,s as v,t as A}from"./index-f2ad9142.js";import{r as p}from"./vendor-4cdf2bd1.js";import{u as I}from"./react-hook-form-a383372b.js";import{c as U,a as t}from"./yup-342a5df4.js";import{S as M,a as V,b as Y}from"./SelectCity-65a3e859.js";import{M as m}from"./MkdInput-d37679e9.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./countries-912e22d5.js";import"./ChevronDownIcon-8b7ce98c.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";function ve(){const{dispatch:b,state:r}=p.useContext(P),{dispatch:h}=p.useContext(_),N=U({name:t().required("This field is required"),email:t().required("This field is required"),website:t().required("This field is required"),year_founded:t().required("This field is required"),description:t().required("This field is required"),country:t(),state:t(),city:t()}),{register:o,handleSubmit:k,setError:O,reset:C,watch:S,setValue:i,formState:{errors:a,isSubmitting:x,dirtyFields:g,isDirty:$,defaultValues:l},control:f}=I({resolver:q(N),defaultValues:{name:r.company.name,email:r.company.email,website:r.company.website,year_founded:r.company.year_founded,description:r.company.description,logo:r.company.logo,country:r.company.country,state:r.company.state,city:r.company.city}}),E=()=>{const s={name:r.company.name,email:r.company.email,website:r.company.website,year_founded:r.company.year_founded,description:r.company.description,logo:r.company.logo,country:r.company.country,state:r.company.state,city:r.company.city};C(s),u&&URL.revokeObjectURL(u)};async function D(s){try{const n=new R;let w="";if(g.logo&&s.logo instanceof FileList&&s.logo.length>0){const F=await n.upload(s.logo[0]);w=n.baseUrl()+F.url}await n.callRawAPI(`/v4/api/records/companies/${r.company.id}`,{name:s.name,email:s.email,website:s.website,year_founded:s.year_founded,description:s.description,...g.logo&&{logo:w},country:s.country,state:s.state,city:s.city},"PUT"),b({type:"REFETCH_COMPANY"}),v(h,"Changes saved")}catch(n){A(b,n.message),v(h,n.message,5e3,"error")}}const[c,d,y]=S(["logo","country","state"]);p.useEffect(()=>{d===""&&(i("state",""),i("city","")),y===""&&i("city","")},[d,y]);const u=p.useMemo(()=>c instanceof FileList&&c.length>0?URL.createObjectURL(c[0]):null,[c]);return e.jsx("div",{className:"mx-auto grid h-full max-h-full min-h-full w-full grid-cols-1 grid-rows-1 overflow-auto px-8 py-6 shadow-lg",children:e.jsxs("form",{className:"grid h-full max-h-full min-h-full w-full grid-cols-1 grid-rows-[1fr_auto]",onSubmit:k(D),children:[e.jsxs("div",{className:"h-full max-h-full min-h-full w-full overflow-auto",children:[e.jsx("div",{className:"mb-4 font-iowan text-[20px] font-[700] md:text-[1.5rem] md:leading-[1.865rem] ",children:"Company Profile"}),e.jsx("p",{className:"mb-6 font-inter text-[1rem] font-[400] leading-[1.21rem]",children:"Update your company details"}),e.jsx("div",{className:"mb-4 font-iowan text-[1rem] font-[700] leading-[1.5rem]",children:"Company Logo"}),e.jsx("div",{className:"mb-6 flex items-center",children:e.jsxs("div",{className:"relative flex h-[7.5rem] w-[7.5rem] items-center justify-center rounded-full border border-[#1F1D1A] bg-transparent",children:[u||l!=null&&l.logo?e.jsx("img",{src:u||(l==null?void 0:l.logo)||"/default.png",alt:"profile",className:"h-full min-h-full w-full min-w-full rounded-[50%] object-cover sm:h-full sm:min-h-full sm:w-full sm:min-w-full"}):e.jsx(T,{}),e.jsxs("label",{htmlFor:"logo",className:"absolute bottom-0 right-0 flex h-8 w-8 cursor-pointer items-center justify-center rounded-full border border-[#1F1D1A] bg-brown-main-bg",children:[e.jsx("input",{type:"file",id:"logo",...o("logo"),className:"hidden"}),e.jsx(L,{})]})]})}),e.jsx("div",{children:e.jsxs("div",{className:"flex w-full flex-col items-start gap-0 md:w-[45%]",children:[e.jsxs("div",{className:"mb-6 grid w-full grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsx("div",{children:e.jsx(m,{type:"text",label:"Display Name",name:"name",errors:a,register:o,className:"mt-1 block w-full rounded-md  !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500"})}),e.jsx("div",{children:e.jsx(m,{type:"text",id:"contact-email",name:"email",label:"Contact Email",errors:a,register:o,className:"mt-1 block w-full rounded-md  !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500"})})]}),e.jsxs("div",{className:"mb-6 grid w-full grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsx("div",{children:e.jsx(m,{type:"text",label:"Website",name:"website",errors:a,register:o,className:"mt-1 block w-full rounded-md !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500"})}),e.jsx("div",{children:e.jsx(m,{type:"text",label:"Year Founded",name:"year_founded",errors:a,register:o,className:"mt-1 block w-full rounded-md !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500"})})]}),e.jsx("div",{className:"mb-6 w-full",children:e.jsx(M,{control:f,name:"country",setValue:s=>i("country",s)})}),e.jsxs("div",{className:"mb-6 grid w-full grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsx("div",{className:"",children:e.jsx(V,{control:f,name:"state",setValue:s=>i("state",s),country:d})}),e.jsx("div",{className:"",children:e.jsx(Y,{control:f,name:"city",setValue:s=>i("city",s),country:d,state:y})})]}),e.jsxs("div",{className:"mb-6 w-full",children:[e.jsx("div",{className:"mb-4 font-iowan text-[20px] font-[700] md:text-[1.5rem] md:leading-[1.865rem] ",children:"Short Description"}),e.jsx("p",{className:"mb-6 font-inter text-[1rem] font-[400] leading-[1.21rem]",children:"Describe what your company does in 250 characters or less."}),e.jsx(m,{type:"textarea",name:"description",errors:a,register:o,rows:"5",maxLength:250,className:"mt-1 block w-full resize-none  rounded-md !border !border-black shadow-sm focus:border-gray-500 focus:ring-gray-500"})]}),e.jsxs("div",{className:"mb-6 hidden h-[8.25rem] w-full space-y-[1.5rem] rounded-[.25rem] bg-[#F2DFCE] p-[1rem] shadow-sm",children:[e.jsxs("div",{className:"flex w-full items-center justify-between gap-[2rem] ",children:[e.jsx("div",{className:" text-center font-iowan text-[1rem] font-[700] leading-[1.5rem]",children:"Profile visibility"}),e.jsx("div",{className:"",children:e.jsx(m,{type:"mapping",name:"privacy",errors:a,options:["private","public"],mapping:{private:"Private",public:"Public"},register:o,className:"block !h-[2.25rem] !w-[7.3125rem] rounded-md !border  !border-black py-2 shadow-sm focus:border-gray-500 focus:ring-gray-500"})})]}),e.jsx("p",{className:"mb-6 font-inter text-[1rem] font-[400] leading-[1.21rem]",children:"Your profile is currently only accessible to those who have a direct link. Your profile is not discoverable on any search results."})]})]})})]}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx(j,{type:"button",className:"flex h-[2.75rem] w-fit items-center justify-center whitespace-nowrap rounded-[.0625rem] !border !border-black bg-transparent px-2 py-2 font-iowan !text-[1rem] tracking-wide text-black md:px-5",color:"black",onClick:E,children:"Discard Changes"}),e.jsx(j,{className:" flex h-[2.75rem] w-fit items-center justify-center whitespace-nowrap rounded-[.0625rem] bg-[#1f1d1a] px-2 py-2 font-iowan !text-[1rem] tracking-wide text-white md:px-5",loading:x,disabled:x,type:"submit",children:"Save Changes"})]})]})})}export{ve as default};
