import{j as m}from"./@nextui-org/listbox-0f38ca19.js";import{r as s,L as i}from"./vendor-4cdf2bd1.js";import{L as c}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const w=({onClose:t,onSuccess:p,options:e=[{name:"",route:""}]})=>m.jsx(c,{children:m.jsx(s.Fragment,{children:m.jsx("div",{className:"grid grid-cols-2 flex-wrap gap-2 text-center",children:e==null?void 0:e.map((r,a)=>m.jsx(i,{onClick:t,to:r==null?void 0:r.route,className:"cursor-pointer",children:r==null?void 0:r.name},a))})})});export{w as RouteChange,w as default};
