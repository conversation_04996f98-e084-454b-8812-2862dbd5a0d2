import{A as M,G as C,M as G,t as N,s as k}from"./index-f2ad9142.js";import{r as s,h as w}from"./vendor-4cdf2bd1.js";function v(E){const[p,i]=s.useState(!1),[l,h]=s.useState([]),[o,x]=s.useState(0),[r,n]=w(),{dispatch:P}=s.useContext(M),{dispatch:b}=s.useContext(C),e=r.get("limit")||30,t=r.get("page")||1,c=r.get("group_name"),u=r.get("members")??"";async function g(){i(!0);try{const m=await new G().callRawAPI(`/v3/api/custom/goodbadugly/member/get-recipient-groups?group_name=${c||""}&members=${u||""}&limit=${e}&page=${t}`);h(m.list),x(m.total)}catch(a){N(P,a.message),k(b,a.message,5e3,"error")}i(!1)}const d=a=>{n({limit:a,page:1})},f=()=>{n({page:Math.max(1,Number(t)-1),limit:e})},S=()=>{console.log(o,e);const a=Math.ceil(o/e);console.log(Math.min(a,t+1)),n({page:Math.max(1,Number(t)+1),limit:e})};return s.useEffect(()=>{g()},[e,t,c,u]),{loading:p,groups:l,refetch:g,totalCount:o,currentPage:Number(t),pageSize:Number(e),updatePageSize:d,previousPage:f,nextPage:S,canPreviousPage:t>1,canNextPage:t<Math.ceil(o/e)}}export{v as u};
