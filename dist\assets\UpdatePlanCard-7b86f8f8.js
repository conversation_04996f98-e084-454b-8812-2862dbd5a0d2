import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as h}from"./vendor-4cdf2bd1.js";import{n as x,bv as d}from"./index-f2ad9142.js";import{a as j,S as g,b as u}from"./ManagePlanModal-a02a3ade.js";import{P as p}from"./index-ff987cdd.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const b={free:`The quickest and easiest way to try UpdateStack - Free as long as you
        want. Request unlimited team updates, upgrade to create and send updates`,pro:"More power for small teams who want to get and remain on the same page while improving productivity with automated, consistent company updates",business:`For growth stage business leaders who need to keep their teams connected
        while increasing productivity with consistent company updates`,enterprise:` For businesses operating at scale who want to maximize performance and
        productivity at every level with automated, consistent company updates`},w={free:[e.jsxs(e.Fragment,{children:["Request ",e.jsx("b",{children:"unlimited"})," updates"]}),e.jsxs(e.Fragment,{children:["Add ",e.jsx("b",{children:"unlimited"})," team Members"]}),e.jsx(e.Fragment,{children:"Engage with team members"}),e.jsx(e.Fragment,{children:"Manage updates in one place"}),e.jsx(e.Fragment,{children:"Share updates with partners"})],pro:[e.jsxs(e.Fragment,{children:["Send updates to ",e.jsx("b",{children:"unlimited"})," recipients"]}),e.jsxs(e.Fragment,{children:["Access up to ",e.jsx("b",{children:"5"})," new fund managers/month"]}),e.jsx(e.Fragment,{children:"One simple dashboard for automated KPI tracking"}),e.jsxs(e.Fragment,{children:[e.jsx("b",{children:"Unlimited"})," team members"]}),e.jsxs(e.Fragment,{children:["Send up to ",e.jsx("b",{children:"3"})," updates per month"]}),e.jsxs(e.Fragment,{children:[e.jsx("b",{children:"Standard"})," Update Template"]}),e.jsxs(e.Fragment,{children:[e.jsx("b",{children:"3"})," Integrations"]})],business:[e.jsxs(e.Fragment,{children:["Send updates to ",e.jsx("b",{children:"unlimited"})," recipients"]}),e.jsxs(e.Fragment,{children:["Access up to ",e.jsx("b",{children:"25"})," new fund managers/month"]}),e.jsx(e.Fragment,{children:"One simple dashboard for automated KPI tracking"}),e.jsxs(e.Fragment,{children:[e.jsx("b",{children:"Unlimited"})," team members"]}),e.jsxs(e.Fragment,{children:["Send up to ",e.jsx("b",{children:"10"})," updates per month"]}),e.jsxs(e.Fragment,{children:[e.jsx("b",{children:"Premium/Branded"})," Update Templates"]}),e.jsxs(e.Fragment,{children:[e.jsx("b",{children:"10"})," Integrations"]}),e.jsxs(e.Fragment,{children:["Access to ",e.jsx(d,{})," UpdateAI"]})],enterprise:[e.jsxs(e.Fragment,{children:["Send updates to ",e.jsx("b",{children:"unlimited"})," recipients"]}),e.jsxs(e.Fragment,{children:["Access up to ",e.jsx("b",{children:"100"})," new fund managers/month"]}),e.jsx(e.Fragment,{children:"One simple dashboard for automated KPI tracking"}),e.jsxs(e.Fragment,{children:[e.jsx("b",{children:"Unlimited"})," team members"]}),e.jsxs(e.Fragment,{children:["Send ",e.jsx("b",{children:"unlimited"})," updates per month"]}),e.jsxs(e.Fragment,{children:[e.jsx("b",{children:"Premium/Branded"})," Update Templates"]}),e.jsxs(e.Fragment,{children:[e.jsx("b",{children:"Unlimited"})," Integrations"]}),e.jsxs(e.Fragment,{children:["Access to ",e.jsx(d,{})," UpdateAI"]})]},f={pro:72,business:120,enterprise:216},K=({onSuccess:a,currentPlan:m,plan:s=null,exchangeRates:n,allPlans:c=[],currency:t="$",focusYearly:r=!0})=>{const i=w[s==null?void 0:s.name];return e.jsx(h.Fragment,{children:e.jsxs("div",{class:"relative h-full w-full rounded-[4px] border-2 border-black bg-[#FFF4EC]",children:[["pro"].includes(s==null?void 0:s.name)?e.jsxs("svg",{class:"absolute -top-5 right-4",width:"60",height:"60",viewBox:"0 0 60 60",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("g",{"clip-path":"url(#clip0_3545_503)",children:e.jsx("path",{d:"M44.6515 54.8089L44.6512 54.8088L30.5812 44.7588L30 44.3436L29.4188 44.7588L15.3488 54.8088L15.3484 54.809C13.5281 56.1106 11 54.8094 11 52.5725V12.5C11 10.7761 11.6848 9.12279 12.9038 7.90381C14.1228 6.68482 15.7761 6 17.5 6H42.5C44.2239 6 45.8772 6.68482 47.0962 7.90381C48.3152 9.12279 49 10.7761 49 12.5V52.57C49 54.807 46.4692 56.1081 44.6515 54.8089Z",fill:"#F2DFCE",stroke:"black","stroke-width":"2"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_3545_503",children:e.jsx("rect",{width:"60",height:"60",fill:"white"})})})]}):null,e.jsxs("div",{class:"flex h-[465px] max-h-[465px] min-h-[465px] w-full flex-col p-6",children:[e.jsxs("div",{class:"mb-4 flex items-center gap-3",children:[e.jsx(p,{name:s==null?void 0:s.name}),e.jsx("h2",{class:"font-Ionwan-regular text-xl text-[#1F1D1A]",children:x(s==null?void 0:s.name,{casetype:"capitalize",separator:" "})})]}),e.jsx("div",{class:"mb-4",children:e.jsx(j,{plan:s,name:s==null?void 0:s.name,focusYearly:r,currency:t,exchangeRates:n})}),e.jsxs("div",{class:"grow space-y-[14px]",children:[["free"].includes(s==null?void 0:s.name)||!r?null:e.jsx(g,{plan:s,name:s==null?void 0:s.name,savings:f[s==null?void 0:s.name],currency:t,exchangeRates:n}),e.jsx("p",{class:"mb-24 font-Inter text-[16px] font-[400] leading-[24px] text-[#3A3835]",children:b[s==null?void 0:s.name]})]}),e.jsx(u,{plan:s,name:s==null?void 0:s.name,currency:t,allPlans:c,onSuccess:a,focusYearly:r,currentPlan:m})]}),e.jsx("div",{class:"my-6 mb-8 h-[1px] w-full bg-[#1f1d1a]"}),e.jsx("ul",{class:"pro-plan-list flex flex-col gap-4 space-y-2 p-6 font-Inter leading-6 text-[#3A3835]",children:i==null?void 0:i.map((o,l)=>e.jsxs("li",{className:"flex items-start gap-3 font-Inter",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",className:"shrink-0",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12 2C17.523 2 22 6.477 22 12C22 17.523 17.523 22 12 22C6.477 22 2 17.523 2 12C2 6.477 6.477 2 12 2ZM12 4C9.87827 4 7.84344 4.84285 6.34315 6.34315C4.84285 7.84344 4 9.87827 4 12C4 14.1217 4.84285 16.1566 6.34315 17.6569C7.84344 19.1571 9.87827 20 12 20C14.1217 20 16.1566 19.1571 17.6569 17.6569C19.1571 16.1566 20 14.1217 20 12C20 9.87827 19.1571 7.84344 17.6569 6.34315C16.1566 4.84285 14.1217 4 12 4ZM15.535 8.381C15.7145 8.19975 15.9566 8.09397 16.2115 8.08532C16.4665 8.07667 16.7152 8.1658 16.9066 8.33447C17.098 8.50313 17.2177 8.73858 17.2412 8.99262C17.2647 9.24666 17.1902 9.50008 17.033 9.701L16.95 9.795L11.364 15.382C11.1733 15.5726 10.9193 15.6866 10.6502 15.7023C10.381 15.7179 10.1155 15.6342 9.904 15.467L9.808 15.382L7.05 12.624C6.86875 12.4445 6.76297 12.2024 6.75432 11.9475C6.74567 11.6925 6.8348 11.4438 7.00347 11.2524C7.17213 11.061 7.40758 10.9413 7.66162 10.9178C7.91566 10.8943 8.16908 10.9688 8.37 11.126L8.464 11.21L10.586 13.331L15.536 8.381H15.535Z",fill:"#3A3835"})}),e.jsx("span",{class:"flex grow flex-wrap items-center gap-2",children:o})]},l))})]})})};export{K as UpdatePlanCard,K as default};
