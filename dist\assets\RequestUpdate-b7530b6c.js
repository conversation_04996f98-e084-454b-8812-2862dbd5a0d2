import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{A as Q,a as H,L as g,I as O,M as J,g as X}from"./index-f2ad9142.js";import{r as m}from"./vendor-4cdf2bd1.js";import"./index-f1d75e77.js";import{u as Y}from"./useCompanyMember-0033d2de.js";import{C as Z}from"./ChevronUpDownIcon-e0f342e0.js";import{W as h,t as D}from"./@headlessui/react-cdd9213e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const Ne=({onClose:ee,onSuccess:y,containerRef:te})=>{var U,$,S,A;const{state:_}=m.useContext(Q),i=(U=_==null?void 0:_.profile)==null?void 0:U.details;console.log(i,"proifle");const{getMany:ae,create:T,showToast:f,custom:E,getSingle:P}=H(),[N,C]=m.useState("existing"),[t,l]=m.useState({fetchingData:!1,teamMembers:[],selectedTeamMember:null,type:null,inviteEmail:"",requestUpdateLoading:!1,inviteAndRequestUpdateLoading:!1}),[M,F]=m.useState({inviteEmail:{error:!1,message:""}}),{companyMember:{myMembers:se},loading:v,getMyCompanyMembers:z}=Y({filter:[`company_id,eq,${(S=($=i==null?void 0:i.companies)==null?void 0:$[0])==null?void 0:S.id}`]}),[q,B]=m.useState(""),V=(s=[])=>{const n={};if(s.forEach(o=>{t[o]||(n[o]={error:!0,message:"This field is required"})}),Object.keys(n).length>0)F(n);else{const o={};s.forEach(r=>{o[r]=t[r]}),W(o)}},W=async s=>{var n,o,r,c,u;try{l(p=>({...p,inviteAndRequestUpdateLoading:!0}));const d=await new J().callRawAPI(`/v3/api/custom/goodbadugly/member/${(o=(n=i==null?void 0:i.companies)==null?void 0:n[0])==null?void 0:o.id}/invite-to-team`,{email:X(s.inviteEmail)},"POST");if(console.log("result >>",d,"tet"),!(d!=null&&d.error)){const{data:p,error:w,message:j}=await T("updates",{name:"Update Title",user_id:(r=d==null?void 0:d.data)==null?void 0:r.user_id,company_id:(c=d==null?void 0:d.data)==null?void 0:c.company_id,status:-1,date:new Date().toISOString().split("T")[0]},!1);if(w)f(j,5e3,"error");else{const x=await E({endpoint:"/v3/api/custom/goodbadugly/users/request-update",method:"POST",payload:{requesting_user_id:i==null?void 0:i.id,update_id:p,status:0,is_requested:1,user_id:(u=d==null?void 0:d.data)==null?void 0:u.user_id}});x!=null&&x.error||(l(b=>({...b,inviteEmail:""})),f("Invite Sent and update requested",5e3,"success"),y&&y())}}}catch(a){f(a==null?void 0:a.message,5e3,"error"),console.log("error >>",a)}finally{l(a=>({...a,inviteAndRequestUpdateLoading:!1}))}},G=async()=>{var s,n,o;try{l(c=>({...c,fetchingData:!0}));const r=await z({filter:[`company_id,eq,${(n=(s=i==null?void 0:i.companies)==null?void 0:s[0])==null?void 0:n.id}`]});if(!(r!=null&&r.error)){const c=(o=r==null?void 0:r.data)==null?void 0:o.map(async a=>{var p,w,j,x,b,I,k,R;const d=await P("user",(p=a==null?void 0:a.user)==null?void 0:p.id,{join:["companies|user_id"],method:"GET",allowToast:!1});return{...a,full_name:`${(w=a==null?void 0:a.user)==null?void 0:w.first_name} ${(j=a==null?void 0:a.user)==null?void 0:j.last_name}`,email:(x=a==null?void 0:a.user)==null?void 0:x.email,user_id:(b=a==null?void 0:a.user)==null?void 0:b.id,company_id:(R=(k=(I=d==null?void 0:d.data)==null?void 0:I.companies)==null?void 0:k[0])==null?void 0:R.id}}),u=await Promise.all(c);l(a=>({...a,teamMembers:u}))}}catch{}finally{l(r=>({...r,fetchingData:!1}))}},K=async()=>{var s,n,o;if(!(t!=null&&t.selectedTeamMember))return f("Please select a team member",5e3,"error");try{l(a=>({...a,requestUpdateLoading:!0}));const{data:r,error:c,message:u}=await T("updates",{name:"Update Title",user_id:(s=t==null?void 0:t.selectedTeamMember)==null?void 0:s.user_id,company_id:(n=t==null?void 0:t.selectedTeamMember)==null?void 0:n.company_id,status:-1,date:new Date().toISOString().split("T")[0]},!1);if(c)f(u,5e3,"error");else{const a=await E({endpoint:"/v3/api/custom/goodbadugly/users/request-update",method:"POST",payload:{requesting_user_id:i==null?void 0:i.id,update_id:r,status:0,is_requested:1,user_id:(o=t==null?void 0:t.selectedTeamMember)==null?void 0:o.user_id}});a!=null&&a.error||(l(d=>({...d,inviteEmail:"",selectedTeamMember:null,type:null})),y&&y())}}catch(r){console.log("error >>",r)}finally{l(r=>({...r,requestUpdateLoading:!1}))}};m.useEffect(()=>{i!=null&&i.id&&G()},[i==null?void 0:i.id]);const L=q===""?t.teamMembers:t.teamMembers.filter(s=>`${s.full_name} ${s.email}`.toLowerCase().includes(q.toLowerCase()));return console.log(v,"membersloading"),e.jsx(m.Fragment,{children:e.jsxs("div",{className:"h-fit max-h-fit min-h-fit w-full space-y-5 pt-0",children:[e.jsxs("div",{className:"flex justify-between border-b border-gray-200",children:[e.jsx("button",{className:`whitespace-nowrap px-4 py-2 text-[12px] font-medium md:text-sm ${N==="existing"?"border-b-2 border-[#1f1d1a] text-[#1f1d1a]":"text-gray-500 hover:text-gray-700"}`,onClick:()=>C("existing"),children:"Existing Team Members"}),e.jsx("button",{className:`whitespace-nowrap px-4 py-2 text-[12px] font-medium md:text-sm ${N==="invite"?"border-b-2 border-[#1f1d1a] text-[#1f1d1a]":"text-gray-500 hover:text-gray-700"}`,onClick:()=>C("invite"),children:"Invite New Member"})]}),N==="existing"?e.jsxs("div",{className:"flex w-full flex-col gap-3",children:[e.jsx(g,{children:e.jsxs("div",{className:"w-full",children:[e.jsx("label",{className:"mb-2 block font-iowan text-[1rem] font-[700] capitalize leading-[1.5rem] text-[#1F1D1A]",children:"My Team Members"}),e.jsx(g,{children:e.jsx(h,{value:t.selectedTeamMember,onChange:s=>{l(n=>({...n,selectedTeamMember:s,type:s?2:null}))},children:e.jsxs("div",{className:"relative mt-0",children:[e.jsxs(h.Button,{className:"relative h-[42px] w-full text-left",children:[e.jsx(h.Input,{className:"focus:shadow-outline h-[42px] w-full appearance-none rounded border bg-brown-main-bg px-3 py-2 leading-tight text-[#1f1d1a] shadow focus:outline-none",placeholder:"Search team members...",displayValue:s=>s?`${s.full_name} | ${s.email}`:"",onChange:s=>B(s.target.value)}),e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-2",children:e.jsx(Z,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})})]}),e.jsx(D,{as:m.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx(h.Options,{className:"absolute z-[99999] mt-1 max-h-60 w-full overflow-auto rounded-md bg-brown-main-bg py-1 text-base shadow-lg focus:outline-none sm:text-sm",children:v!=null&&v.myMembers?e.jsx("div",{className:"relative cursor-default select-none px-4 py-2 text-gray-700",children:"Loading Members Please wait..."}):L.length===0&&q!==""?e.jsx("div",{className:"relative cursor-default select-none px-4 py-2 text-gray-700",children:"Nothing found."}):L.map(s=>e.jsx(h.Option,{className:({active:n})=>`relative cursor-default select-none py-2 pl-10 pr-4 ${n?"bg-[#1f1d1a] text-white":"text-gray-900"}`,value:s,children:({selected:n,active:o})=>e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:`block truncate ${n?"font-medium":"font-normal"}`,children:[s.full_name," | ",s.email]}),n?e.jsx("span",{className:`absolute inset-y-0 left-0 flex items-center pl-3 ${o?"text-white":"text-[#1f1d1a]"}`}):null]})},s.id))})})]})})})]})}),e.jsx(g,{children:e.jsx(O,{disabled:t==null?void 0:t.requestUpdateLoading,loading:t==null?void 0:t.requestUpdateLoading,onClick:K,className:"mt-3 !h-[42px] !w-full text-white",children:"Request Update"})})]}):e.jsx(g,{children:e.jsxs("div",{className:"flex w-full flex-col items-start justify-center",children:[e.jsx("div",{className:"flex grow flex-col items-start justify-start gap-4"}),e.jsxs("div",{className:"flex w-full grow flex-col items-center justify-between gap-6",children:[e.jsxs("div",{className:"flex w-full grow flex-col items-start justify-start gap-2",children:[e.jsx("label",{className:"block font-iowan text-[1rem] font-[700] capitalize leading-[1.5rem] text-[#1F1D1A]",htmlFor:"invite-email",children:"Email"}),e.jsx("input",{autoComplete:"new-password","aria-autocomplete":"none",type:"text",id:"invite-email",name:"invite-email",value:t==null?void 0:t.inviteEmail,onChange:s=>{l(n=>{var o;return{...n,inviteEmail:(o=s==null?void 0:s.target)==null?void 0:o.value.trim().toLowerCase()}})},placeholder:"Enter their email",className:`focus:shadow-outline h-[3rem] w-full appearance-none rounded-sm border bg-brown-main-bg p-[.75rem_1rem_.75rem_1rem] text-sm font-normal leading-tight text-[#1f1d1a] shadow focus:outline-none focus:ring-0 ${(A=M==null?void 0:M.inviteEmail)!=null&&A.message?"!border-red-500":"border-[#1f1d1a]"}`})]}),e.jsx(g,{children:e.jsx(O,{loading:t==null?void 0:t.inviteAndRequestUpdateLoading,disabled:t==null?void 0:t.inviteAndRequestUpdateLoading,onClick:()=>{V(["inviteEmail"])},className:"h-[42px] !w-full text-white",children:"Invite & Request Update"})})]})]})})]})})};export{Ne as default};
