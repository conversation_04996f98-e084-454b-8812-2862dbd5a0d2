import{j as l}from"./@nextui-org/listbox-0f38ca19.js";import"./vendor-4cdf2bd1.js";import{L as b}from"./index-f2ad9142.js";import{A as F}from"./AddButton-51d1b2cd.js";import{a as M}from"./MkdListTableBindOperations-38051783.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const k=({row:n,columns:O,actions:i,actionId:f="id",setDeleteId:u=null})=>l.jsx(l.Fragment,{children:l.jsx("div",{className:"z-3 relative flex h-fit w-fit items-center gap-2",children:l.jsx(b,{children:Object.keys(i).filter(r=>{var o,t,m,e,h,d;return((o=i[r])==null?void 0:o.show)&&((t=i[r])==null?void 0:t.locations)&&((e=(m=i[r])==null?void 0:m.locations)==null?void 0:e.length)&&((d=(h=i[r])==null?void 0:h.locations)==null?void 0:d.includes("buttons"))}).map((r,o)=>{var t,m,e,h,d,c,j,x,w,g,v,a,C,L,N,z,A,B,P,R;if((t=i[r])!=null&&t.bind)switch((e=(m=i[r])==null?void 0:m.bind)==null?void 0:e.action){case"hide":if(!M(i[r],n))return l.jsx(b,{children:l.jsxs(F,{title:typeof((h=i[r])==null?void 0:h.children)=="function"?"":((d=i[r])==null?void 0:d.children)??r,onClick:()=>{var p,s;["delete"].includes(r)?u&&u(n[f]):(p=i[r])!=null&&p.action&&((s=i[r])==null||s.action([n[f]]))},showPlus:!1,className:"!border-soft-200 !flex  !h-[2rem] !w-fit !justify-center !overflow-visible !bg-brown-main-bg !text-black !shadow-none",children:[(c=i[r])!=null&&c.icon?(j=i[r])==null?void 0:j.icon:null,((x=i[r])==null?void 0:x.showChildren)&&(typeof((w=i[r])==null?void 0:w.children)=="function"?(g=i[r])==null?void 0:g.children({row:n,actionId:f}):((v=i[r])==null?void 0:v.children)||r)]},o)},o);break}return(a=i[r])!=null&&a.bind?null:l.jsx(b,{children:l.jsxs(F,{title:typeof((C=i[r])==null?void 0:C.children)=="function"?"":((L=i[r])==null?void 0:L.children)??r,onClick:()=>{var p,s,E;["delete"].includes(r)&&!((p=i[r])!=null&&p.action)?u&&u(n[f]):(s=i[r])!=null&&s.action&&((E=i[r])==null||E.action([n[f]]))},showPlus:!1,className:"!border-soft-200 !flex !h-[2rem] !w-fit !justify-center !overflow-visible !bg-brown-main-bg !text-black !shadow-none",children:[(N=i[r])!=null&&N.icon?(z=i[r])==null?void 0:z.icon:null,((A=i[r])==null?void 0:A.showChildren)&&(typeof((B=i[r])==null?void 0:B.children)=="function"?(P=i[r])==null?void 0:P.children({row:n,actionId:f}):((R=i[r])==null?void 0:R.children)||r)]},o)},o)})})})});export{k as default};
