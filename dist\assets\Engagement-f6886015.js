import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as n,h as E,b as M,L as K}from"./vendor-4cdf2bd1.js";import{A as T,G as L,M as A,t as W,s as q,L as B,a3 as J}from"./index-f2ad9142.js";import{u as O}from"./react-hook-form-a383372b.js";import{o as z}from"./yup-0917e80c.js";import{c as I,a as w}from"./yup-342a5df4.js";import"./index-d07d87ac.js";import{R as Q}from"./tableWrapper-ca490fb1.js";import{L as U}from"./index-b8adfdf8.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-b50d6e2a.js";import"./index.esm-7add6cfb.js";import"./react-icons-36ae72b7.js";function F(s){const[i,m]=n.useState(!1),[c,b]=n.useState([]),[o]=E(),{dispatch:p}=n.useContext(T),{dispatch:j}=n.useContext(L),u=o.get("limit")||30,x=o.get("page"),a=o.get("search"),l=o.get("datefrom"),d=o.get("dateto"),_=o.get("update_title"),f=o.get("section"),v=o.get("commenter");async function y(){m(!0);try{const h=await new A().callRawAPI(`/v3/api/custom/goodbadugly/startup/get-engagements?comment=${a||""}&from=${l||""}&to=${d||""}&title=${_||""}&section=${f||""}&commenter=${v||""}`),N=[{name:"joe",jig:"leeg"},{name:"nas",jig:"tag"},{name:"josa",jig:"bar"},{name:"joe",jig:"rar"}];b(h==null?void 0:h.engagements),console.log(h)}catch(g){W(p,g.message),q(j,g.message,5e3,"error")}m(!1)}return n.useEffect(()=>{y()},[u,x,a,l,d]),{loading:i,engagements:c,refetch:y}}const X=()=>{var C,k,P,$;const[s,i]=E(),[m,c]=n.useState(""),[b,o]=n.useState(new Date().toISOString().split("T")[0]),{engagements:p,refetch:j,loading:u}=F(),[x,a]=n.useState(!1),[l,d]=n.useState(""),[_,f]=n.useState({});s.get("update_title"),s.get("section"),s.get("commenter");const v=I({update_title:w(),section:w(),commenter:w()}),y=Object.values(p.reduce((t,r)=>(t[r.section]=r,t),{})),g=Object.values(p.reduce((t,r)=>(t[r.commenter]=r,t),{})),h=Object.values(p.reduce((t,r)=>(t[r.title]=r,t),{})),{register:N,handleSubmit:G,setError:Z,reset:D,resetField:R,formState:{errors:S}}=O({resolver:z(v),defaultValues:{update_title:s.get("update_title")||"",section:s.get("section")||"",commenter:s.get("commenter")||""}});n.useEffect(()=>{const t={update_title:s.get("update_title")||"",section:s.get("section")||"",commenter:s.get("commenter")||"",datefrom:s.get("datefrom")||"",dateto:s.get("dateto")||"",search:s.get("search")||""};f(t),console.log(t),["update_title","section","commenter"].forEach(r=>R(r,{defaultValue:t[r]})),c(t.datefrom),d(t.search)},[s,D]);function V(t){const r={datefrom:m,dateto:b,update_title:t.update_title,section:t.section,commenter:t.commenter,search:l};f(r),i(r),a(!x)}const H=t=>{c(t.target.value)};return console.log(l),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("label",{className:"mb-2 block  text-lg font-bold capitalize capitalize text-[#1f1d1a]",children:"Search"}),e.jsx("form",{className:"flex w-full flex-row flex-wrap",onSubmit:t=>{t.preventDefault(),G(V)()},children:e.jsxs("div",{className:"flex w-full flex-row flex-wrap items-center",children:[e.jsxs("div",{className:"flex w-full flex-row flex-wrap items-end gap-4",children:[e.jsxs("div",{className:"flex w-full flex-row items-center sm:w-auto",children:[e.jsx("input",{type:"text",value:l,placeholder:"Search comment",onChange:t=>d(t.target.value),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm text-sm text-sm font-normal font-normal font-normal capitalize leading-tight text-[#1d1f1a] shadow focus:outline-none sm:w-[180px] ${(C=S.group_name)!=null&&C.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(k=S.group_name)==null?void 0:k.message})]}),e.jsxs("div",{className:"w-full sm:w-auto",children:[e.jsx("div",{className:"flex w-full flex-row items-center justify-between gap-4",children:e.jsx("div",{className:"w-[100%] sm:w-auto",children:e.jsx("input",{type:"date",className:`h-[34.5px] w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 pr-5 text-sm text-sm text-sm font-normal font-normal font-normal leading-tight text-[#1d1f1a] shadow sm:w-[180px] sm:pr-3 sm:pr-3 ${(P=S.date)!=null&&P.message?"border-red-500":""}`,value:m,onChange:H})})}),e.jsx("p",{className:"text-field-error italic text-red-500",children:($=S.date)==null?void 0:$.message})]}),e.jsxs("select",{...N("update_title"),className:"focus:shadow-outline w-full appearance-none text-ellipsis rounded border border-[#1f1d1a] bg-transparent py-2 pl-3 pr-8 text-sm font-normal capitalize leading-tight text-[#1f1d1a] shadow focus:outline-none sm:w-[180px]",children:[e.jsxs("option",{value:"",disabled:!0,children:[" ","- title -"," "]}),h.map(t=>e.jsx("option",{value:t.title,children:t.title},t.id))]}),e.jsxs("select",{...N("section"),className:"focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent  py-2 pl-3 pr-8 text-sm font-normal leading-tight text-[#1f1d1a] shadow focus:outline-none sm:w-[180px]",children:[e.jsxs("option",{value:"",disabled:!0,children:[" ","- Section -"," "]}),y.map(t=>e.jsx("option",{value:t.section,children:t.section},t.id))]}),e.jsxs("select",{...N("commenter"),className:"focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent  py-2 pl-3 pr-8 text-sm font-normal leading-tight text-[#1f1d1a] shadow focus:outline-none sm:w-[180px]",children:[e.jsxs("option",{value:"",disabled:!0,children:[" ","- Commenter -"," "]}),g.map(t=>e.jsx("option",{value:t.commenter,children:t.commenter},t.id))]})]}),e.jsxs("div",{className:"mt-4 flex items-center gap-4 self-end",children:[e.jsx("button",{type:"submit",disabled:u,className:"font-iowan-regular rounded-md bg-primary-black/80 px-4 py-1 font-semibold text-white hover:bg-primary-black",children:"Search"}),e.jsx("button",{type:"button",onClick:()=>{D({update_title:"",section:"",commenter:""}),d(""),c(""),s.delete("search"),s.delete("datefrom"),s.delete("dateto"),s.delete("update_title"),s.delete("section"),s.delete("commenter"),i(s)},disabled:u,className:"rounded-md px-4 py-1 font-semibold text-[#1f1d1a]",children:"Clear"})]})]})})]})};new A;const Y=[{header:"Title ",accessor:"title "},{header:"Section",accessor:"section "},{header:"Comment ",accessor:"comment "},{header:"Commenter ",accessor:"commenter "},{header:"Date/Time ",accessor:"time/date "},{header:"Action ",accessor:"Action "}],Ee=()=>{var x;const{dispatch:s,state:i}=n.useContext(T),{dispatch:m}=n.useContext(L);M();const[c,b]=E(),{engagements:o,refetch:p,loading:j}=F(i.user),u=I({group_name:w(),members:w()});return O({resolver:z(u),defaultValues:async()=>{const a=c.get("group_name")??"",l=c.get("members")??"";return{group_name:a,members:l}}}),n.useEffect(()=>{m({type:"SETPATH",payload:{path:"recipient_group"}})},[]),n.useEffect(()=>{m({type:"SETPATH",payload:{path:"engagements"}})},[]),e.jsx("div",{className:"px-5 pt-8 md:px-8",children:j?e.jsx(U,{}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"rounded bg-brown-main-bg",children:[e.jsx("div",{className:"flex justify-between mb-3 w-full item-center"}),e.jsx(X,{}),e.jsxs("div",{className:"overflow-x-auto p-5 px-0 mt-10 rounded bg-brown-main-bg md:mt-8",children:[e.jsx("div",{className:"flex justify-between items-center mb-3 w-full text-center",children:e.jsx("h4",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"Engagements"})}),e.jsx("div",{className:"w-full custom-overflow sm:overflow-x-auto",children:e.jsx(Q,{children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10",children:[e.jsx("thead",{children:e.jsx("tr",{children:Y.map((a,l)=>e.jsx("th",{scope:"col",className:"font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3",children:a.header},l))})}),e.jsx("tbody",{className:"font-iowan-regular  divide-y divide-[#1f1d1a]/10",children:(x=o==null?void 0:o.sort((a,l)=>new Date(l.update_at)-new Date(a.update_at)))==null?void 0:x.map(a=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-4 py-2 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:a.title}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:a.section}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:a.comment.length<33?a.comment:substring(0,33-3)+"..."}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:a.commenter}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:new Date(a.date).toLocaleString()}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:e.jsx(K,{className:"cursor-pointer font-medium text-[#292829fd] underline hover:underline disabled:cursor-not-allowed disabled:text-gray-400",to:`${a.update_link}#comment-${a.id}`,state:{commentId:a.id,noteId:a.note_id},children:"View"})})]},a.id))})]})})}),(o==null?void 0:o.length)==0?e.jsxs("div",{className:"mb-[20px] mt-24 flex flex-col items-center",children:[e.jsx(B,{children:e.jsx(J,{fill:"black",className:"!h-[5rem] !w-[5rem]"})}),e.jsx("p",{className:"mt-4 text-base font-medium text-center",children:"No Engagement"})]}):null]})]})})})};export{Ee as default};
