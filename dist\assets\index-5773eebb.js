import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{o as V}from"./yup-0917e80c.js";import{A as z,G as L,M as U,s as S,t as A}from"./index-f2ad9142.js";import{r as l}from"./vendor-4cdf2bd1.js";import{u as M}from"./react-hook-form-a383372b.js";import{c as Y,a as r}from"./yup-342a5df4.js";import{S as I,a as O,b as B}from"./SelectCity-952884e6.js";import{u as G}from"./useFundProfile-6ed8f0b3.js";import{b as H}from"./index.esm-54e24cf9.js";import{InteractiveButton2 as K}from"./InteractiveButton-060359e0.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./countries-912e22d5.js";import"./ChevronDownIcon-8b7ce98c.js";import"./react-icons-36ae72b7.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";function Pe(){var h,y,g,w,j,N,_,v;const{dispatch:x,state:J}=l.useContext(z),{dispatch:p}=l.useContext(L),{loading:Q,profileData:e,refetch:k}=G(),E=Y({display_name:r().required("This field is required"),email:r().required("This field is required"),website:r().required("This field is required"),year_founded:r().required("This field is required"),country:r(),state:r(),city:r()}),{register:i,handleSubmit:P,setError:W,reset:u,watch:q,setValue:a,formState:{errors:o,isSubmitting:f,dirtyFields:T,isDirty:X,defaultValues:Z},control:m}=M({resolver:V(E),defaultValues:{display_name:e==null?void 0:e.display_name,email:e==null?void 0:e.contact_email,website:e==null?void 0:e.fund_website,year_founded:e==null?void 0:e.year_founded,logo:e==null?void 0:e.url,country:e==null?void 0:e.country,state:e==null?void 0:e.state,city:e==null?void 0:e.city}});l.useEffect(()=>{u({display_name:e==null?void 0:e.display_name,email:e==null?void 0:e.contact_email,website:e==null?void 0:e.fund_website,year_founded:e==null?void 0:e.year_founded,logo:e==null?void 0:e.url,country:e==null?void 0:e.country,state:e==null?void 0:e.state,city:e==null?void 0:e.city})},[e]);async function $(s){try{const n=new U;let C="";if(T.logo&&s.logo instanceof FileList&&s.logo.length>0){const R=await n.upload(s.logo[0]);C=n.baseUrl()+R.url}await n.callRawAPI("/v3/api/goodbadugly/customer/fund-profile",{display_name:s.display_name,contact_email:s.email,fund_website:s.website,year_founded:s.year_founded,country:s.country,state:s.state,city:s.city,profile_photo:C,private:0},"POST"),k(),x({type:"REFETCH_COMPANY"}),S(p,"Changes saved")}catch(n){A(x,n.message),S(p,n.message,5e3,"error")}}const[d,c,b]=q(["logo","country","state"]);console.log(e),l.useEffect(()=>{c===""&&(a("state",""),a("city","")),b===""&&a("city","")},[]);const F=l.useMemo(()=>d instanceof FileList&&d.length>0?URL.createObjectURL(d[0]):null,[d]);return t.jsxs("div",{className:"w-full max-w-7xl items-start gap-12 p-4 pt-8 sm:p-8 lg:flex  lg:p-12 lg:px-8",children:[t.jsxs("div",{className:"max-w-full lg:max-w-xs",children:[t.jsxs("div",{className:"flex flex-col items-center rounded-md border border-[#1f1d1a] px-16 py-8",children:[t.jsx("img",{src:F||(e==null?void 0:e.profile_photo)||"/default.png",alt:"logo",className:"h-20 min-h-20 w-20 min-w-20 rounded-[50%] object-cover sm:h-40 sm:min-h-40 sm:w-40 sm:min-w-40"}),t.jsxs("label",{className:"md:max-w-auto mt-4 flex max-w-[200px] cursor-pointer flex-row items-center justify-center gap-3 rounded-lg border border-[#1f1d1a] px-3 py-2 text-center text-lg font-medium capitalize",children:[t.jsx("input",{type:"file",...i("logo"),className:"hidden"}),t.jsx(H,{className:"min-h-5 min-w-5"}),t.jsxs("span",{className:"font-iowan-regular  whitespace-nowrap text-sm",children:[" ","Upload Logo"]})]})]}),t.jsxs("div",{className:"mt-6 rounded-md border border-[#1f1d1a] bg-transparent px-6 py-8",children:[t.jsx("p",{className:"text-sm font-semibold",children:"Profile visibility"}),t.jsx("select",{className:"focus:shadow-outline mt-3 appearance-none rounded border border-[#1f1d1a] bg-transparent py-2 pl-3 pr-8 text-sm font-normal leading-tight text-[#1f1d1a] shadow focus:outline-none ",children:t.jsx("option",{children:"Private"})}),t.jsx("p",{className:"mt-6 text-sm",children:"Your profile is currently only accessible to those who have a direct link. Your profile is not discoverable on any search results"})]})]}),t.jsxs("form",{className:"max-w-[500px] flex-grow",onSubmit:P($),children:[t.jsxs("div",{className:"mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:mt-[-8px]",children:[t.jsxs("div",{className:"",children:[t.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize text-[#1f1d1a]",children:"Display name"}),t.jsx("input",{type:"text",autoComplete:"off",...i("display_name"),className:`no-box-shadow h-[41.6px] w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] placeholder:text-sm focus:outline-none ${(h=o.display_name)!=null&&h.message?"border-red-500":""}`}),t.jsx("p",{className:"text-field-error italic text-red-500",children:(y=o.display_name)==null?void 0:y.message})]}),t.jsxs("div",{className:"",children:[t.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize text-[#1f1d1a]",children:"Contact email"}),t.jsx("input",{type:"text",autoComplete:"off",...i("email"),className:`no-box-shadow h-[41.6px] w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] placeholder:text-sm focus:outline-none ${(g=o.email)!=null&&g.message?"border-red-500":""}`}),t.jsx("p",{className:"text-field-error italic text-red-500",children:(w=o.email)==null?void 0:w.message})]})]}),t.jsxs("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[t.jsxs("div",{className:"mt-6 sm:mt-8",children:[t.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize text-[#1f1d1a]",children:"Company website"}),t.jsx("input",{type:"text",autoComplete:"off",...i("website"),className:`no-box-shadow h-[41.6px] w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] placeholder:text-sm focus:outline-none ${(j=o.website)!=null&&j.message?"border-red-500":""}`}),t.jsx("p",{className:"text-field-error italic text-red-500",children:(N=o.website)==null?void 0:N.message})]}),t.jsxs("div",{className:"sm:mt-8",children:[t.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize text-[#1f1d1a]",children:"Year founded"}),t.jsx("input",{type:"text",autoComplete:"off",...i("year_founded"),className:`no-box-shadow h-[41.6px] w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] placeholder:text-sm focus:outline-none ${(_=o.year_founded)!=null&&_.message?"border-red-500":""}`}),t.jsx("p",{className:"text-field-error italic text-red-500",children:(v=o.year_founded)==null?void 0:v.message})]})]}),t.jsx(I,{control:m,name:"country",setValue:s=>a("country",s)}),t.jsx(O,{control:m,name:"state",setValue:s=>a("state",s),country:c}),t.jsx(B,{control:m,name:"city",setValue:s=>a("city",s),country:c,state:b}),t.jsxs("div",{className:"mt-6 flex items-center justify-end gap-4",children:[" ",t.jsx("button",{className:"h-[40px] whitespace-nowrap rounded-md border border-[#1f1d1a] px-4 py-2 font-iowan text-[10px] font-medium  sm:text-[12px]  xl:text-[14px]",type:"button",onClick:()=>u(),children:"Discard Changes"}),t.jsx(K,{loading:f,disabled:f,type:"submit",className:`whitespace-nowr disabled:bg-disabledblack rounded-md bg-primary-black px-6 py-2 text-center !text-[10px] font-semibold font-semibold text-white transition-colors \r
duration-100 sm:!text-[12px]  xl:!text-sm`,children:"Save changes"})]})]})]})}export{Pe as default};
