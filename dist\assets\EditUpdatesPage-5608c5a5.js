import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as t,i as Me,b as et,L as Ye,u as Yt,h as Kt}from"./vendor-4cdf2bd1.js";import{G as ie,A as ne,u as tt,s as P,m as St,t as X,M as ae,J as Ut,I as Ft,p as Bt,x as Ht,v as Zt,a as ut,K as G,y as At,N as Gt,P as dt,Q as Xe,o as es,w as Vt,V as Dt,L as Tt,W as ts,X as ss,Y as Et,Z as as,T as ns}from"./index-f2ad9142.js";import{o as Fe}from"./yup-0917e80c.js";import{u as Ae}from"./react-hook-form-a383372b.js";import{c as Le,a as Q,b as Rt}from"./yup-342a5df4.js";import{b as rs,c as is}from"./index.esm-be5e1c14.js";import{B as ls,a as os}from"./index.esm-bb52b9ca.js";import{M as Mt}from"./index-713720be.js";import{a as It,b as cs}from"./index.esm-3e7472af.js";import{M as Wt}from"./index-dc002f62.js";import{C as ds}from"./CreateGroupModal-d6bb962a.js";import{B as Te}from"./BottomSheet-be14d402.js";import{u as ms}from"./useUpdateQuestions-4dea6f0c.js";import{InteractiveButton2 as Ee}from"./InteractiveButton-060359e0.js";import{X as oe}from"./XMarkIcon-cfb26fe7.js";import{t as g,S as _,L as V}from"./@headlessui/react-cdd9213e.js";import{u as xs}from"./useUpdate-3cd97540.js";import{u as us}from"./useNotes-19835bdc.js";import{u as hs}from"./useUpdateGroups-731b4247.js";import{h as U}from"./moment-a9aaa855.js";import{I as Ne}from"./InformationCircleIcon-5be2f140.js";import{u as Qt}from"./useDate-c1da5729.js";import{E as ht}from"./ExclamationTriangleIcon-2f987159.js";import{C as fs}from"./index-c523e7e9.js";import{g as ps}from"./react-audio-voice-recorder-a95781ec.js";import{S as bs,M as gs}from"./MicrophoneIcon-ed3ea0f8.js";import{u as js}from"./useNote-ea33f376.js";import"./lodash-82bd9112.js";import{C as ws}from"./Collaborators-6025a01b.js";import{I as ys}from"./index.esm-7add6cfb.js";import{P as vs}from"./index.esm-c839cefc.js";import{G as Pe}from"./react-icons-36ae72b7.js";import{c as mt}from"./index.esm-6fcccbfe.js";import{T as qe}from"./index-cb9e08c3.js";import{_ as Lt}from"./qr-scanner-cf010ec4.js";import{u as Xt}from"./useSubscription-dc563085.js";import{C as Ns,B as Cs}from"./react-spinners-b860a5a3.js";import{D as _s,C as ks,P as Ss}from"./react-beautiful-dnd-2e1128e9.js";import{C as Ts}from"./index-4342bf32.js";import{u as Ms}from"./useCompanyMember-0033d2de.js";import{u as Fs}from"./useRecipientGroup-ff89a0bd.js";import{u as As}from"./useUpdateCollaborator-1187c43b.js";import{A as Ls}from"./index-afef2e72.js";import{c as Ps,U as Ds}from"./index-f08e5be1.js";import{C as zt}from"./ClockIcon-a30de2d8.js";import{X as Es}from"./XMarkIcon-6ed09631.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-b50d6e2a.js";import"./MkdCustomInput-aaf80542.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./index-b8adfdf8.js";import"./react-quill-a78e6fc7.js";import"./useUpdateCollaborators-677ec5ee.js";import"./PlusIcon-26cedb5d.js";import"./ChevronUpDownIcon-e0f342e0.js";import"./@emotion/react-e6c5671d.js";import"./redux-9de11428.js";import"./react-select-8cfd0d8f.js";import"./react-h5-audio-player-939d37df.js";import"./@mantine/core-38f49ae4.js";function Rs({title:s,titleId:d,...o},r){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":d},o),s?t.createElement("title",{id:d},s):null,t.createElement("path",{fillRule:"evenodd",d:"M6.75 2.25A.75.75 0 0 1 7.5 3v1.5h9V3A.75.75 0 0 1 18 3v1.5h.75a3 3 0 0 1 3 3v11.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V7.5a3 3 0 0 1 3-3H6V3a.75.75 0 0 1 .75-.75Zm13.5 9a1.5 1.5 0 0 0-1.5-1.5H5.25a1.5 1.5 0 0 0-1.5 1.5v7.5a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5v-7.5Z",clipRule:"evenodd"}))}const Is=t.forwardRef(Rs),zs=Is;function Os({title:s,titleId:d,...o},r){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":d},o),s?t.createElement("title",{id:d},s):null,t.createElement("path",{fillRule:"evenodd",d:"M7.72 12.53a.75.75 0 0 1 0-1.06l7.5-7.5a.75.75 0 1 1 1.06 1.06L9.31 12l6.97 6.97a.75.75 0 1 1-1.06 1.06l-7.5-7.5Z",clipRule:"evenodd"}))}const $s=t.forwardRef(Os),qs=$s;function Ys({title:s,titleId:d,...o},r){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":d},o),s?t.createElement("title",{id:d},s):null,t.createElement("path",{fillRule:"evenodd",d:"M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z",clipRule:"evenodd"}))}const Us=t.forwardRef(Ys),Bs=Us,Jt=t.forwardRef(({groups:s=[],refetch:d,updateGroups:o=[],refetchUpdateGroups:r,isOwner:l=!1,update:c},j)=>{const p=t.useRef(),[T,u]=t.useState(!1),[h,b]=t.useState(!1),[F,v]=t.useState([]),[w,N]=t.useState([]),{dispatch:M}=t.useContext(ie),{dispatch:A,state:x}=t.useContext(ne);t.useState([]);const[f,L]=t.useState([]),[y,k]=t.useState(""),[i,W]=t.useState(null),{id:S}=Me();tt({isPublic:!1});const B=et(),q=Le({group_id:Q().required("This field is required")});console.log(s,"current"),Ae({resolver:Fe(q),defaultValues:{group_id:""}});async function E(a=[]){if(console.log(a,"current"),(a==null?void 0:a.length)===0){P(M,"Please select at least one group",5e3,"error");return}try{W(()=>a[0]);const z=await St(M,A,{method:"POST",endpoint:"/v3/api/custom/goodbadugly/updates/recipient/group",payload:{update_id:S,groups:a}});if(console.log("Group add response:",z),!(z!=null&&z.error)){let J="Group(s) added successfully";if(a.length===1&&a[0].group_name){const xe=a[0];xe.type===1||xe.recipient_member&&xe.recipient_member.length>1?J=`Group "${xe.group_name}" added successfully`:J=`${xe.group_name} added successfully`}P(M,J,5e3,"success"),c!=null&&c.sent_at&&N(xe=>[...xe,...a]),r&&r(),d&&d()}}catch(z){X(A,z.message),P(M,z.message,5e3,"error")}finally{W(null)}}const H=a=>!!(o!=null&&o.find(z=>z.group_id==a))||null;async function le(a){const z=o==null?void 0:o.find(J=>(J==null?void 0:J.group_id)==(a==null?void 0:a.id));if(z)try{W(()=>a);const J=await St(M,A,{method:"DELETE",endpoint:`/v3/api/custom/goodbadugly/updates/recipient/${z==null?void 0:z.id}`});if(!(J!=null&&J.error)){let xe="Group removed successfully";a&&a.group_name&&(a.type===1?xe=`Group "${a.group_name}" removed successfully`:xe=`${a.group_name} removed successfully`),P(M,xe,5e3,"success"),c!=null&&c.sent_at&&N(Re=>Re.filter(fe=>fe.id!==a.id)),r(),d()}}catch(J){X(A,J.message),P(M,J.message,5e3,"error")}finally{W(()=>null)}}t.useEffect(()=>{if(y){const a=s==null?void 0:s.map(z=>(z==null?void 0:z.group_name.toLowerCase().replace(/\s+/g,"").includes(y.toLowerCase().replace(/\s+/g,"")))?z:null).filter(Boolean);L(()=>[...a])}else L(()=>[...s])},[y,s==null?void 0:s.length]),console.log(o,s,w,"group"),t.useEffect(()=>{if(c!=null&&c.sent_at&&w.length>0){const a=w.map(z=>s.find(J=>J.id===z)).filter(Boolean);v(a)}else v([])},[w,c==null?void 0:c.sent_at,s]),t.useEffect(()=>{N([])},[S]);const ee=async()=>{try{(await new ae().callRawAPI(`/v3/api/custom/goodbadugly/updates/${S}/send-report`,{specific_groups:w.map(J=>J.id)},"POST")).error||(P(M,"Update sent to new recipients successfully"),v([]),b(!1),d&&d())}catch(a){X(A,a.message),P(M,a.message,5e3,"error")}},R=()=>e.jsxs("div",{className:"!shadow-[#1f1d1a]/12 !shadow-0 !flex !w-full min-w-full max-w-full  !flex-col !rounded-[.125rem] !border-0 !border-transparent !bg-brown-main-bg !p-4 md:!border md:!border-[#1f1d1a]",children:[(c==null?void 0:c.sent_at)&&w.length>0&&e.jsxs("div",{className:"mb-4 flex items-center gap-2 border-b border-gray-200 pb-4",children:[e.jsx("input",{type:"checkbox",id:"sendToNewOnly",checked:h,onChange:a=>b(a.target.checked),className:"h-4 w-4 rounded border-gray-300"}),e.jsxs("label",{htmlFor:"sendToNewOnly",className:"text-sm",children:["Send update to newly added recipients (",w.length,")"]}),h&&e.jsx("button",{onClick:ee,className:"ml-auto rounded bg-[#1f1d1a] px-3 py-1 text-sm text-white hover:bg-[#1f1d1a]/90",children:"Send"})]}),e.jsxs("div",{className:"relative line-clamp-1 inline-flex h-[2.5rem] w-full cursor-not-allowed overflow-x-hidden truncate rounded-[.125rem] !border !border-[#1f1d1a] text-left text-sm font-medium focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-2",children:e.jsx(ls,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})}),e.jsx("input",{type:"text",value:y,onChange:a=>k(a.target.value),className:"h-[2.5rem] w-full rounded-[.125rem] border border-transparent bg-transparent px-4 py-2 pl-10 text-sm outline-none focus:border-transparent focus:outline-none focus:ring-2 focus:ring-transparent",placeholder:"Search"})]}),e.jsx("div",{className:"hide-scrollbar relative left-[.0625rem] flex max-h-[17.5rem] flex-col justify-start gap-[6px] overflow-y-auto rounded bg-brown-main-bg pt-[10px] md:pt-[0]",children:(f==null?void 0:f.length)===0&&y!==""?e.jsx("div",{className:"relative cursor-default select-none bg-brown-main-bg p-2 text-[#1f1d1a]",children:"Nothing found."}):f==null?void 0:f.map(a=>e.jsx("button",{type:"button",disabled:!!i,"aria-label":"group name",onClick:()=>{H(a==null?void 0:a.id)?le(a):E([a])},className:"flex h-[1.75rem] min-h-[1.75rem] cursor-pointer items-center justify-between gap-2 rounded-[.125rem] border-b border-b-[#1F1D1A1A]  pb-[20px] pt-6 transition-all ease-out sm:h-[1.5rem] sm:max-h-[2.75rem] sm:min-h-[2.75rem] md:pb-2 md:pt-2",children:e.jsxs("span",{className:"flex w-full items-center justify-between gap-3 text-[12px] sm:text-sm",children:[e.jsx("span",{className:"whitespace-nowrap hover:font-bold",children:a==null?void 0:a.group_name}),(i==null?void 0:i.id)==(a==null?void 0:a.id)?e.jsx(Wt,{loading:!0,color:"#1f1d1a"}):H(a==null?void 0:a.id)?e.jsx(rs,{}):null]})},a==null?void 0:a.id))}),e.jsx("button",{type:"button",onClick:()=>{var a;p!=null&&p.current&&((a=p==null?void 0:p.current)==null||a.click())},className:"mt-3 flex cursor-pointer items-center gap-2 rounded-[.125rem] p-2 text-left font-inter text-sm font-[600] leading-[1.21rem] underline transition-all ease-out   sm:text-[1rem]",children:"+ Add new Group"}),e.jsx("button",{type:"button",onClick:()=>{B(`/member/company/teams?trigger=add&update_id=${S}`)},className:"mt-2 flex cursor-pointer items-center gap-2 rounded-[.125rem] p-2 text-left font-inter text-sm font-[600] leading-[1.21rem] underline transition-all ease-out   sm:text-[1rem]",children:"+ Add new Recipient"})]});return e.jsxs("div",{className:`w-fit min-w-fit max-w-fit sm:self-end ${l?"":"hidden"}`,children:[e.jsx("div",{className:"hidden md:block",children:e.jsx(Mt,{display:e.jsxs("div",{className:"mt-2 flex w-fit min-w-fit max-w-fit flex-col gap-2 sm:mt-0 sm:flex-row sm:items-center",children:[e.jsx("span",{ref:j,className:"whitespace-nowrap",children:"Add Recipients"}),e.jsxs("button",{disabled:!l,className:"relative flex h-[2.25rem] w-fit items-center justify-center gap-[.625rem] whitespace-nowrap rounded-[.1875rem] border border-[#1f1d1a] bg-brown-main-bg px-[1rem] py-[.5rem] font-iowan text-[#1f1d1a]",children:["Select Recipients",e.jsx(It,{size:25})]})]}),openOnClick:!0,backgroundColor:"#fff0e5",place:"bottom",tooltipClasses:"!w-fit  !rounded-[.125rem] !border-[#1f1d1a] !bg-brown-main-bg !p-0",classNameArrow:"!border-b !border-r !border-primary-black",children:R()})}),e.jsxs("div",{className:"md:hidden",children:[e.jsxs("div",{className:"mt-2 flex w-fit min-w-fit max-w-fit flex-col gap-2 sm:mt-0 sm:flex-row sm:items-center",children:[e.jsx("span",{ref:j,className:"whitespace-nowrap",children:"Add Recipients"}),e.jsxs("button",{disabled:!l,onClick:()=>u(!0),className:"relative flex h-[2.25rem] w-fit items-center justify-center gap-[.625rem] whitespace-nowrap rounded-[.1875rem] border border-[#1f1d1a] bg-brown-main-bg px-[1rem] py-[.5rem] font-iowan text-[#1f1d1a]",children:["Select Recipients",e.jsx(It,{size:25})]})]}),e.jsx(Te,{isOpen:T,onClose:()=>u(!1),title:"",children:R()})]}),e.jsx(ds,{afterCreate:()=>{r(),d()},buttonRef:p})]})}),Hs=Object.freeze(Object.defineProperty({__proto__:null,default:Jt},Symbol.toStringTag,{value:"Module"}));function Zs({investors:s}){const{id:d}=Me(),{questions:o,refetch:r}=ms(d),[l,c]=t.useState(!1),[j,p]=t.useState(!1),[T,u]=t.useState(!1),[h,b]=t.useState(!1),[F,v]=t.useState(!1),[w,N]=t.useState(""),[M,A]=t.useState(""),[x,f]=t.useState(!1),{dispatch:L}=t.useContext(ne),{dispatch:y}=t.useContext(ie),k=Le({question:Q().required("This field is required"),investor_id:Q().required("This field is required")});Ae({resolver:Fe(k),defaultValues:{question:"",investor_id:""}});const i=t.useCallback(a=>{a&&a.stopPropagation(),window.innerWidth<768?u(!0):p(!0)},[]),W=t.useCallback(a=>{a&&a.stopPropagation(),p(!1)},[]),S=t.useCallback(a=>{a&&a.stopPropagation(),u(!1)},[]),B=t.useCallback(a=>{a&&a.stopPropagation(),b(!1)},[]),q=t.useCallback(a=>{a&&a.stopPropagation(),v(!1)},[]),E=()=>{if(!w.trim()){P(y,"Please enter a question",3e3,"error");return}p(!1),u(!1),window.innerWidth<768?v(!0):b(!0)},H=async()=>{if(!M){P(y,"Please select a respondent",3e3,"error");return}f(!0);try{await new ae().callRawAPI("/v4/api/records/update_questions",{update_id:d,investor_id:M,question:w},"POST"),P(y,"Question added successfully",5e3,"success"),N(""),A(""),b(!1),v(!1),r()}catch(a){X(L,a.message),P(y,a.message,5e3,"error")}f(!1)};async function le(a){c(!0);try{await new ae().callRawAPI(`/v4/api/records/update_questions/${a}`,{},"DELETE"),P(y,"Question deleted successfully",5e3,"success"),r()}catch(z){X(L,z.message),P(y,z.message,5e3,"error")}c(!1)}const ee=()=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h3",{className:"font-iowan text-lg font-semibold leading-6 text-gray-900 md:text-xl",children:"Ask a Question"}),e.jsx("button",{onClick:window.innerWidth<768?S:W,type:"button",children:e.jsx(oe,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-2",children:[e.jsx("p",{className:"mb-4 text-sm text-black",children:"Create the question"}),e.jsx("textarea",{value:w,onChange:a=>N(a.target.value),className:"h-32 w-full rounded-sm border border-[#1f1d1a] bg-brown-main-bg p-3 placeholder:text-[#1f1d1a]/50 focus:outline-none",placeholder:"Enter the question..."})]}),e.jsxs("div",{className:"mt-4 flex justify-end gap-3",children:[e.jsx("button",{type:"button",className:"rounded-sm border border-[#1f1d1a] bg-transparent px-4 py-2 text-sm font-medium text-[#1f1d1a] hover:bg-gray-100",onClick:window.innerWidth<768?S:W,children:"Cancel"}),e.jsx("button",{type:"button",className:"rounded-sm bg-[#1f1d1a] px-4 py-2 text-sm font-medium text-white hover:bg-[#1f1d1a]/90",onClick:E,children:"Continue"})]})]}),R=()=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h3",{className:"font-iowan text-lg font-semibold leading-6 text-gray-900 md:text-xl",children:"Select Respondent"}),e.jsx("button",{onClick:window.innerWidth<768?q:B,type:"button",children:e.jsx(oe,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-2",children:[e.jsx("p",{className:"mb-4 text-sm text-black",children:"Create an ask by selecting:"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Respondent"}),e.jsxs("select",{value:M,onChange:a=>A(a.target.value),className:"h-10 w-full rounded-sm border border-[#1f1d1a] bg-brown-main-bg px-3 focus:outline-none",children:[e.jsx("option",{value:"",children:"Select respondent"}),s.map(a=>e.jsx("option",{value:a.id,children:a!=null&&a.first_name&&(a!=null&&a.last_name)?`${a.first_name} ${a.last_name}`:a.email},a.id))]})]})]}),e.jsxs("div",{className:"mt-4 flex justify-end gap-3",children:[e.jsx("button",{type:"button",className:"rounded-sm border border-[#1f1d1a] bg-transparent px-4 py-2 text-sm font-medium text-[#1f1d1a] hover:bg-gray-100",onClick:window.innerWidth<768?q:B,children:"Cancel"}),e.jsx("button",{type:"button",className:"rounded bg-[#1f1d1a] px-4 py-2 text-sm font-medium text-white hover:bg-[#1f1d1a]/90",onClick:H,disabled:x,children:x?"Saving...":"Save"})]})]});return e.jsxs("div",{className:"mt-12 min-h-[100px] rounded bg-[#F2DFCE] p-5",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h4",{className:"text-xl font-semibold sm:font-bold",children:"Asks"}),e.jsx("span",{className:"font-iowan-regular text-[12px] font-medium text-black/80 sm:text-sm md:text-base",children:"(only selected respondents will see questions they are asked)"})]}),e.jsx("div",{className:"mt-4 space-y-2",children:o.map(a=>{var z;return e.jsxs("div",{className:"group flex max-w-xl flex-col items-start justify-between",children:[e.jsxs("p",{className:"flex items-center gap-2 font-iowan",children:[a.question,"?",e.jsx("button",{className:"hidden hover:text-red-500 group-hover:block",onClick:()=>le(a.id),disabled:l,children:e.jsx(is,{size:15})})]}),e.jsxs("div",{className:"mt-2 flex items-center gap-2",children:[e.jsx("img",{src:((z=a==null?void 0:a.user)==null?void 0:z.photo)||"/default.png",alt:"avatar",className:"h-[20px] min-h-[20px] w-[20px] min-w-[20px] rounded-full object-cover"}),e.jsx("p",{className:"font-iowan text-[14px] text-black",children:a.user.first_name+" "+a.user.last_name})]})]},a.id)})}),e.jsx("div",{className:"my-5 h-[2px] w-full bg-[#1f1d1a]/10"}),e.jsx("div",{className:"mt-4",children:e.jsx("p",{onClick:i,className:"focus:shadow-outline flex h-[33px] w-full cursor-pointer appearance-none items-center rounded-sm border border-[#1f1d1a] bg-brown-main-bg px-3 py-2 leading-tight text-[#1f1d1a] shadow outline-none focus:outline-none md:h-[44px]",children:"Ask a question..."})}),e.jsx("div",{className:"hidden md:block",children:e.jsx(g,{appear:!0,show:j,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50]",onClose:W,children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsx(_.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-lg border border-[#1f1d1a] bg-brown-main-bg p-6 text-left align-middle shadow-xl transition-all",children:ee()})})})})]})})}),e.jsx("div",{className:"md:hidden",children:e.jsx(Te,{isOpen:T,onClose:S,className:"px-1",children:ee()})}),e.jsx("div",{className:"hidden md:block",children:e.jsx(g,{appear:!0,show:h,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50]",onClose:B,children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsx(_.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-lg border border-[#1f1d1a] bg-brown-main-bg p-6 text-left align-middle shadow-xl transition-all",children:R()})})})})]})})}),e.jsx("div",{className:"md:hidden",children:e.jsx(Te,{isOpen:F,onClose:q,className:"px-1",children:R()})})]})}function Vs({afterRestore:s,template:d,isOwner:o=!1}){const[r,l]=t.useState(!1),{dispatch:c}=t.useContext(ne),{dispatch:j}=t.useContext(ie),[p,T]=t.useState(!1),{id:u}=Me();console.log(u,"update_id");const h=Ut.filter(F=>F.title===d);async function b(){var F,v,w;T(!0);try{const N=await Bt(j,c,"notes",{filter:[`update_id,eq,${u}`]});if(console.log(N,"noteResult"),((F=N==null?void 0:N.data)==null?void 0:F.length)>0){const M=N.data.map(x=>Ht(j,c,"notes",x.id));await Promise.all(M),console.log("start");const A=(w=(v=h[0])==null?void 0:v.template)==null?void 0:w.map(x=>Zt(j,c,"notes",{update_id:u,type:x,status:0}));await Promise.all(A),console.log("end")}l(!1),s(),P(j,"Default template restored")}catch(N){X(c,N.message),P(j,N.message,5e3,"error")}T(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"font-iowan font-medium text-[#1f1d1a] underline underline-offset-2",onClick:()=>l(!0),disabled:!o,children:"Clear template"}),e.jsx(g,{appear:!0,show:r,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>l(!1),children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"overflow-y-auto fixed inset-0",children:e.jsx("div",{className:"flex justify-center items-center p-4 min-h-full text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"overflow-hidden p-6 w-full max-w-md text-base text-left align-middle rounded-md shadow-xl transition-all transform bg-brown-main-bg",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Are you sure"}),e.jsx("button",{onClick:()=>l(!1),type:"button",children:e.jsx(oe,{className:"w-6 h-6"})})]}),e.jsx("p",{className:"mt-2",children:"Are you sure you want to restore to default?"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mt-6",children:[e.jsx("button",{className:"rounded-[.125rem] border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>l(!1),children:"Cancel"}),e.jsx(Ft,{loading:p,disabled:p,onClick:b,className:"disabled:bg-disabledblack h-[36px] !w-auto !rounded-[.125rem] bg-primary-black py-2 text-center font-iowan font-semibold text-white transition-colors duration-100",children:"Yes, restore"})]})]})})})})]})})]})}const Ot=({children:s})=>{const[d,o]=t.useState(!1),r=t.useRef(null),l=()=>{r.current&&clearTimeout(r.current),o(!0)},c=()=>{r.current=setTimeout(()=>{o(!1)},100)};return e.jsx(V,{className:"inline relative",children:e.jsxs("div",{onMouseEnter:l,onMouseLeave:c,children:[e.jsx(V.Button,{className:"ml-0",children:e.jsx(Ne,{className:"w-4 h-4 cursor-pointer mt-[3px]",pathClasses:"text-[#1f1d1a]",stroke:"white"})}),d&&e.jsx(g,{show:!0,as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(V.Panel,{static:!0,className:"absolute left-0 z-10 mt-3 w-screen max-w-[200px] -translate-x-[30%] transform px-4 text-sm",onMouseEnter:l,onMouseLeave:c,children:e.jsxs("div",{className:"relative rounded-lg bg-[#1f1d1a] px-4 py-3 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:[e.jsx("div",{className:"font-medium",children:s}),e.jsx("div",{className:"absolute left-1/2 top-0 h-3 w-3 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-[#1f1d1a]"})]})})})]})})};function Ws({update:s,afterEdit:d,setLastSyncFromModifyModal:o,integrations:r,loading:l}){var W,S,B,q;const[c,j]=t.useState(!1);tt();const{getMany:p,showToast:T}=ut(),{dispatch:u}=t.useContext(ne),{dispatch:h}=t.useContext(ie),[b,F]=t.useState({mrr:!1,arr:!1}),v=E=>{if(!E)return"";const le=E.toString().replace(/[^\d.]/g,"").split(".");le.length>2&&le.splice(2);const ee=le.join("."),R=parseFloat(ee);return isNaN(R)?"":le.length===2?ee:R.toFixed(2)},w=(E,H)=>{const le=E.target.value;if(le===""){f(H,"");return}f(H,v(le))},N=(E,H)=>{H==="stripe"&&k(E)},M=Le({mrr:Q().required("This field is required"),arr:Q().required("This field is required"),cash:Q(),burnrate:Q()}),{register:A,handleSubmit:x,setValue:f,formState:{isSubmitting:L,errors:y}}=Ae({resolver:Fe(M),defaultValues:{mrr:s.mrr,arr:s.arr,cash:s.cash,burnrate:s.burnrate}}),k=async E=>{if(!r.stripe){T("No Stripe integration found. Please set up Stripe integration first.",5e3,"error");return}F(H=>({...H,[E]:!0}));try{const H=new ae,le=E==="mrr"?"?pull_mrr=1":"?pull_arr=1",ee=await H.callRawAPI(`/v3/api/custom/goodbadugly/integrations/stripe/metrics/${r.stripe.id}/${s.id}${le}`,{},"GET");ee.data&&(o(ee.data.last_sync_at),E==="mrr"&&ee.data.mrr!==void 0?(f("mrr",ee.data.mrr.toFixed(2).toString()),T("MRR pulled from Stripe successfully",3e3,"success")):E==="arr"&&ee.data.arr!==void 0&&(f("arr",ee.data.arr.toFixed(2).toString()),T("ARR pulled from Stripe successfully",3e3,"success")))}catch(H){X(u,H.message),T(h,H.message,5e3,"error")}finally{F(H=>({...H,[E]:!1}))}};async function i(E){try{await new ae().callRawAPI(`/v4/api/records/updates/${s.id}`,{mrr:E.mrr,arr:E.arr,burnrate:s.burnrate,cash:s.cash,sync:G.MANUAL},"PUT"),j(!1),d()}catch(H){X(u,H.message),T(h,H.message,5e3,"error")}}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"p-2 font-medium text-white rounded-md bg-primary-black font-iowan",onClick:()=>j(!0),children:"Modify"}),e.jsx(g,{appear:!0,show:c,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>j(!1),children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"overflow-y-auto fixed inset-0",children:e.jsx("div",{className:"flex justify-center items-center p-4 min-h-full text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{as:"form",className:"overflow-hidden p-6 w-full max-w-md text-base text-left align-middle rounded-md shadow-xl transition-all transform bg-brown-main-bg",onSubmit:x(i),children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Edit update"}),e.jsx("button",{onClick:()=>j(!1),type:"button",children:e.jsx(oe,{className:"w-6 h-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:"flex justify-between items-center",children:e.jsxs("label",{className:"mb-2 flex items-center gap-1 text-sm font-semibold capitalize text-[#1f1d1a]",children:["MRR",e.jsx(Ot,{children:"Monthly Recurring Revenue - The predictable revenue generated each month from subscriptions and recurring payments."})]})}),e.jsxs("div",{className:"flex relative gap-2 items-center",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx("span",{className:"absolute left-3 top-1/2 text-gray-500 -translate-y-1/2",children:"$"}),e.jsx("input",{type:"text",...A("mrr"),onChange:E=>w(E,"mrr"),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent py-2 pl-7 pr-3 text-sm font-normal leading-tight text-[#1d1f1a] shadow focus:outline-none ${(W=y.mrr)!=null&&W.message?"border-red-500":""}`})]}),e.jsxs(V,{className:"relative",children:[e.jsxs(V.Button,{className:"flex items-center gap-2 rounded border border-[#1f1d1a] px-3 py-2 text-xs capitalize text-[#1f1d1a]",children:["Fetch from",e.jsx("svg",{width:"10",height:"6",viewBox:"0 0 10 6",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M1 1L5 5L9 1",stroke:"#1F1D1A",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})]}),e.jsx(V.Panel,{className:"absolute right-0 z-10 mt-2 w-48 rounded-md ring-1 ring-black ring-opacity-5 shadow-lg bg-brown-main-bg",children:e.jsx("div",{className:"py-1",children:e.jsxs("button",{type:"button",onClick:()=>N("mrr","stripe"),className:"flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-brown-main-bg/60",disabled:l||b.mrr,children:[e.jsx("img",{src:"https://cdn.brandfetch.io/idxAg10C0L/w/400/h/400/theme/dark/icon.jpeg",alt:"Stripe",className:`mr-2 h-4 w-4 ${b.mrr?"animate-spin":""}`}),b.mrr?"Fetching...":"Stripe"]})})})]})]}),e.jsx("p",{className:"italic text-red-500 text-field-error",children:(S=y.mrr)==null?void 0:S.message}),e.jsx("p",{className:"mt-1 text-xs text-gray-700",children:"Last 30 days"})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:"flex justify-between items-center",children:e.jsxs("label",{className:"mb-2 flex items-center gap-1 text-sm font-semibold capitalize text-[#1f1d1a]",children:["ARR",e.jsx(Ot,{children:"Annual Recurring Revenue - The predictable revenue generated each year from subscriptions and recurring payments."})]})}),e.jsxs("div",{className:"flex relative gap-2 items-center",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx("span",{className:"absolute left-3 top-1/2 text-gray-500 -translate-y-1/2",children:"$"}),e.jsx("input",{type:"text",...A("arr"),onChange:E=>w(E,"arr"),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent py-2 pl-7 pr-3 text-sm font-normal leading-tight text-[#1d1f1a] shadow focus:outline-none ${(B=y.arr)!=null&&B.message?"border-red-500":""}`})]}),e.jsxs(V,{className:"relative",children:[e.jsxs(V.Button,{className:"flex items-center gap-2 rounded border border-[#1f1d1a] px-3 py-2 text-xs capitalize text-[#1f1d1a]",children:["Fetch from",e.jsx("svg",{width:"10",height:"6",viewBox:"0 0 10 6",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M1 1L5 5L9 1",stroke:"#1F1D1A",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})]}),e.jsx(V.Panel,{className:"absolute right-0 z-10 mt-2 w-48 rounded-md ring-1 ring-black ring-opacity-5 shadow-lg bg-brown-main-bg",children:e.jsx("div",{className:"py-1",children:e.jsxs("button",{type:"button",onClick:()=>N("arr","stripe"),className:"flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-brown-main-bg/60",disabled:l||b.arr,children:[e.jsx("img",{src:"https://cdn.brandfetch.io/idxAg10C0L/w/400/h/400/theme/dark/icon.jpeg",alt:"Stripe",className:`mr-2 h-4 w-4 ${b.arr?"animate-spin":""}`}),b.arr?"Fetching...":"Stripe"]})})})]})]}),e.jsx("p",{className:"italic text-red-500 text-field-error",children:(q=y.arr)==null?void 0:q.message}),e.jsx("p",{className:"mt-1 text-xs text-gray-700",children:"Last 12 months"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mt-6",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>j(!1),children:"Cancel"}),e.jsx(Ee,{loading:L,disabled:L,type:"submit",className:"py-2 font-semibold text-center text-white rounded-lg transition-colors duration-100 disabled:bg-disabledblack bg-primary-black",children:"Save"})]})]})})})})]})})]})}const xt=({children:s,last:d=!1})=>{const[o,r]=t.useState(!1),l=t.useRef(null),c=()=>{l.current&&clearTimeout(l.current),r(!0)},j=()=>{l.current=setTimeout(()=>{r(!1)},100)};return e.jsx(V,{className:"relative inline",children:e.jsxs("div",{onMouseEnter:c,onMouseLeave:j,children:[e.jsx(V.Button,{className:"ml-0",children:e.jsx(Ne,{className:"h-4 w-4 cursor-pointer",pathClasses:"text-[#1f1d1a]",stroke:"white"})}),o&&e.jsx(g,{show:!0,as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(V.Panel,{static:!0,className:`absolute left-0 z-10 mt-3 w-screen max-w-[200px] -translate-x-[35%] transform px-4 text-sm ${d?"top-0":"top-full"}`,onMouseEnter:c,onMouseLeave:j,children:e.jsxs("div",{className:"relative rounded-lg bg-[#1f1d1a] px-4 py-3 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:[e.jsx("div",{className:"font-medium",children:s}),e.jsx("div",{className:"absolute left-1/2 top-0 h-3 w-3 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-[#1f1d1a]"})]})})})]})})};function Qs({update:s,refetchUpdate:d,showMetric:o}){var x,f;const[r,l]=t.useState(!1);t.useContext(ne),t.useContext(ie),t.useState(!1);const[c,j]=t.useState(!1),[p,T]=t.useState(!1),{profile:u}=tt(),{getMany:h,showToast:b}=ut(),{convertDate:F}=Qt(),[v,w]=t.useState(!1),[N,M]=t.useState({stripe:null,plaid:null}),A=async()=>{var L,y,k,i,W,S;if(u!=null&&u.id)try{w(!0);const B=await h("stripe_integration",{filter:[`user_id,eq,${u==null?void 0:u.id}`,`company_id,eq,${(y=(L=u==null?void 0:u.companies)==null?void 0:L[0])==null?void 0:y.id}`]}),q=await h("plaid_integration",{filter:[`user_id,eq,${u==null?void 0:u.id}`,`company_id,eq,${(i=(k=u==null?void 0:u.companies)==null?void 0:k[0])==null?void 0:i.id}`]});M({stripe:((W=B==null?void 0:B.data)==null?void 0:W[0])||null,plaid:((S=q==null?void 0:q.data)==null?void 0:S[0])||null})}catch(B){console.error(B),b("Error fetching integrations",5e3,"error")}finally{w(!1)}};return t.useEffect(()=>{(async()=>u!=null&&u.id&&await A())()},[u==null?void 0:u.id]),console.log(N),e.jsxs(e.Fragment,{children:[e.jsxs("button",{className:"flex w-full items-center justify-between text-[16px] font-medium text-[#1f1d1a] md:text-[18px] ",onClick:()=>l(o),children:[e.jsx("span",{children:"Show financial metrics"})," ",o?e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.5 18.3333H2.5C2.15833 18.3333 1.875 18.05 1.875 17.7083C1.875 17.3667 2.15833 17.0833 2.5 17.0833H17.5C17.8417 17.0833 18.125 17.3667 18.125 17.7083C18.125 18.05 17.8417 18.3333 17.5 18.3333Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M15.8495 2.89999C14.2328 1.28332 12.6495 1.24166 10.9912 2.89999L9.98283 3.90832C9.89949 3.99166 9.86616 4.12499 9.89949 4.24166C10.5328 6.44999 12.2995 8.21666 14.5078 8.84999C14.5412 8.85832 14.5745 8.86666 14.6078 8.86666C14.6995 8.86666 14.7828 8.83332 14.8495 8.76666L15.8495 7.75832C16.6745 6.94166 17.0745 6.14999 17.0745 5.34999C17.0828 4.52499 16.6828 3.72499 15.8495 2.89999Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M13.0089 9.60832C12.7673 9.49166 12.5339 9.37499 12.3089 9.24166C12.1256 9.13332 11.9506 9.01666 11.7756 8.89166C11.6339 8.79999 11.4673 8.66666 11.3089 8.53332C11.2923 8.52499 11.2339 8.47499 11.1673 8.40832C10.8923 8.17499 10.5839 7.87499 10.3089 7.54166C10.2839 7.52499 10.2423 7.46666 10.1839 7.39166C10.1006 7.29166 9.95892 7.12499 9.83392 6.93332C9.73392 6.80832 9.61726 6.62499 9.50892 6.44166C9.37559 6.21666 9.25892 5.99166 9.14226 5.75832C9.1246 5.72049 9.10752 5.68286 9.09096 5.64544C8.96798 5.36767 8.60578 5.28647 8.39098 5.50126L3.61726 10.275C3.50892 10.3833 3.40892 10.5917 3.38392 10.7333L2.93392 13.925C2.85059 14.4917 3.00892 15.025 3.35892 15.3833C3.65892 15.675 4.07559 15.8333 4.52559 15.8333C4.62559 15.8333 4.72559 15.825 4.82559 15.8083L8.02559 15.3583C8.17559 15.3333 8.38392 15.2333 8.48392 15.125L13.2517 10.3572C13.468 10.1409 13.3864 9.76972 13.105 9.64967C13.0734 9.63615 13.0414 9.62238 13.0089 9.60832Z",fill:"#1F1D1A"})]}):!1]}),e.jsx(g,{appear:!0,show:r,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>l(!1),children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Financial Metrics"}),e.jsx("button",{onClick:()=>l(!1),type:"button",children:e.jsx(oe,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex flex-row items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","MRR",e.jsx(xt,{children:"Monthly Recurring Revenue - The predictable revenue generated each month from subscriptions and recurring payments."})]}),e.jsx("p",{children:s.mrr})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex flex-row items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","ARR",e.jsx(xt,{children:"Annual Recurring Revenue - The yearly value of all recurring revenue normalized for a 12-month period."})]}),e.jsx("p",{children:s.arr})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex flex-row items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","Cash",e.jsx(xt,{children:"Available cash and cash equivalents that can be quickly accessed for business operations and investments."})]}),e.jsx("p",{children:s.cash})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex flex-row items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","Burnrate",e.jsx(xt,{last:!0,children:"The rate at which a company spends its cash reserves on operating expenses over time."})]}),e.jsx("p",{children:s.burnrate})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"font-normal",children:"Last sync"}),e.jsxs("p",{children:[" ",p?F(p,{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!0,year:"numeric",timeZoneName:"short",timeZone:"America/Los_Angeles"}).replace(", "," - "):N!=null&&N.stripe?F(Math.max(new Date(((x=N.stripe)==null?void 0:x.last_sync_at)||0).getTime(),new Date(((f=N.plaid)==null?void 0:f.last_sync_at)||0).getTime()),{month:"short",day:"numeric",year:"numeric",hour:"2-digit",minute:"2-digit",hour12:!0,timeZoneName:"short",timeZone:"America/Los_Angeles"}).replace(", "," - "):"N/A"]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:s.id?e.jsx(Ws,{update:s,loading:v,integrations:N,setLastSyncFromModifyModal:T,afterEdit:d}):null}),c?e.jsxs("div",{className:"mt-4 border border-black/60 bg-brown-main-bg p-3",children:[e.jsxs("p",{className:"flex items-center gap-3 font-semibold",children:[e.jsx(ht,{className:"h-5 text-yellow-500",strokeWidth:2}),"No integrations setup currently."]}),e.jsxs("p",{className:"mt-1",children:["In order to populate financial information in your updates, you must setup integrations with your current financial services"," ",e.jsx(Ye,{to:"/member/integrations",className:"font-semibold text-primary-black underline",children:"here"})]})]}):null]})]})})})})]})})]})}function Xs({update:s,afterEdit:d}){var F,v,w,N,M,A,x,f,L,y;const[o,r]=t.useState(!1),{dispatch:l}=t.useContext(ne),{dispatch:c}=t.useContext(ie),j=Le({investment_stage:Q().required("This field is required"),invested_to_date:Q().required("This field is required"),investors_on_cap_table:Q().required("This field is required"),valuation_at_last_round:Q().required("This field is required"),date_of_last_round:Q().required("This field is required")}),{register:p,handleSubmit:T,formState:{isSubmitting:u,errors:h}}=Ae({resolver:Fe(j),defaultValues:{investment_stage:s.investment_stage,invested_to_date:s.invested_to_date,investors_on_cap_table:s.investors_on_cap_table,valuation_at_last_round:s.valuation_at_last_round,date_of_last_round:s.date_of_last_round}});async function b(k){try{await new ae().callRawAPI(`/v4/api/records/updates/${s.id}`,{investment_stage:k.investment_stage,invested_to_date:k.invested_to_date,valuation_at_last_round:k.valuation_at_last_round,investors_on_cap_table:k.investors_on_cap_table,date_of_last_round:k.date_of_last_round,investment_details_sync:G.MANUAL},"PUT"),r(!1),d()}catch(i){X(l,i.message),P(c,i.message,5e3,"error")}}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded-md bg-primary-black p-2 font-iowan font-medium text-white",onClick:()=>r(!0),children:"Edit manually"}),e.jsx(g,{appear:!0,show:o,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>r(!1),children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{as:"form",className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",onSubmit:T(b),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Edit update"}),e.jsx("button",{onClick:()=>r(!1),type:"button",children:e.jsx(oe,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Investment Stage"}),e.jsx("input",{type:"text",...p("investment_stage"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(F=h.investment_stage)!=null&&F.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(v=h.investment_stage)==null?void 0:v.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Invested to Date"}),e.jsx("input",{type:"text",...p("invested_to_date"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(w=h.invested_to_date)!=null&&w.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(N=h.invested_to_date)==null?void 0:N.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Fund managers on Cap Table"}),e.jsx("input",{type:"text",...p("investors_on_cap_table"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(M=h.investors_on_cap_table)!=null&&M.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(A=h.investors_on_cap_table)==null?void 0:A.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Valuation at Last Round"}),e.jsx("input",{type:"text",...p("valuation_at_last_round"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(x=h.valuation_at_last_round)!=null&&x.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(f=h.valuation_at_last_round)==null?void 0:f.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Date of Last Round"}),e.jsx("input",{type:"date",...p("date_of_last_round"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 pr-5 text-sm font-normal leading-tight   text-[#1d1f1a] shadow focus:outline-none sm:pr-3 ${(L=h.date_of_last_round)!=null&&L.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(y=h.date_of_last_round)==null?void 0:y.message})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>r(!1),children:"Cancel"}),e.jsx(Ee,{loading:u,disabled:u,type:"submit",className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})]})]})})})})]})})]})}function Js({update:s,refetchUpdate:d,showMetric:o}){const[r,l]=t.useState(!1);return t.useContext(ne),t.useContext(ie),t.useState(!1),e.jsxs(e.Fragment,{children:[e.jsxs("button",{className:"flex w-full items-center justify-between text-[16px] font-medium text-[#1f1d1a] md:text-[18px]",onClick:()=>l(o),children:["Show investment metrics"," ",o?e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.5 18.3333H2.5C2.15833 18.3333 1.875 18.05 1.875 17.7083C1.875 17.3667 2.15833 17.0833 2.5 17.0833H17.5C17.8417 17.0833 18.125 17.3667 18.125 17.7083C18.125 18.05 17.8417 18.3333 17.5 18.3333Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M15.8495 2.89999C14.2328 1.28332 12.6495 1.24166 10.9912 2.89999L9.98283 3.90832C9.89949 3.99166 9.86616 4.12499 9.89949 4.24166C10.5328 6.44999 12.2995 8.21666 14.5078 8.84999C14.5412 8.85832 14.5745 8.86666 14.6078 8.86666C14.6995 8.86666 14.7828 8.83332 14.8495 8.76666L15.8495 7.75832C16.6745 6.94166 17.0745 6.14999 17.0745 5.34999C17.0828 4.52499 16.6828 3.72499 15.8495 2.89999Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M13.0089 9.60832C12.7673 9.49166 12.5339 9.37499 12.3089 9.24166C12.1256 9.13332 11.9506 9.01666 11.7756 8.89166C11.6339 8.79999 11.4673 8.66666 11.3089 8.53332C11.2923 8.52499 11.2339 8.47499 11.1673 8.40832C10.8923 8.17499 10.5839 7.87499 10.3089 7.54166C10.2839 7.52499 10.2423 7.46666 10.1839 7.39166C10.1006 7.29166 9.95892 7.12499 9.83392 6.93332C9.73392 6.80832 9.61726 6.62499 9.50892 6.44166C9.37559 6.21666 9.25892 5.99166 9.14226 5.75832C9.1246 5.72049 9.10752 5.68286 9.09096 5.64544C8.96798 5.36767 8.60578 5.28647 8.39098 5.50126L3.61726 10.275C3.50892 10.3833 3.40892 10.5917 3.38392 10.7333L2.93392 13.925C2.85059 14.4917 3.00892 15.025 3.35892 15.3833C3.65892 15.675 4.07559 15.8333 4.52559 15.8333C4.62559 15.8333 4.72559 15.825 4.82559 15.8083L8.02559 15.3583C8.17559 15.3333 8.38392 15.2333 8.48392 15.125L13.2517 10.3572C13.468 10.1409 13.3864 9.76972 13.105 9.64967C13.0734 9.63615 13.0414 9.62238 13.0089 9.60832Z",fill:"#1F1D1A"})]}):!1]}),e.jsx(g,{appear:!0,show:r,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>l(!1),children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Investment Metrics"}),e.jsx("button",{onClick:()=>l(!1),type:"button",children:e.jsx(oe,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal md:text-base",children:[s.investment_details_sync==G.MANUAL?"*":"","Investment Stage"]}),e.jsx("p",{children:s.investment_stage??"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal md:text-base",children:[s.investment_details_sync==G.MANUAL?"*":"","Invested to Date"]}),e.jsx("p",{children:s.invested_to_date??"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal md:text-base",children:[s.investment_details_sync==G.MANUAL?"*":"","Fund managers on Cap Table"]}),e.jsx("p",{children:s.investors_on_cap_table??"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-normal md:text-base",children:[s.investment_details_sync==G.MANUAL?"*":"","Valuation at Last Round"]}),e.jsx("p",{children:s.valuation_at_last_round??"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm font-normal md:text-base",children:"Date of Last Round"}),e.jsxs("p",{children:[" ",s.date_of_last_round?U(s.date_of_last_round).format("DD MMM"):"N/A"]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:s.id?e.jsx(Xs,{update:s,afterEdit:d}):null})]})]})})})})]})})]})}function Ks({note_id:s,afterDelete:d}){const[o,r]=t.useState(!1),{dispatch:l}=t.useContext(ne),{dispatch:c}=t.useContext(ie),[j,p]=t.useState(!1);async function T(){p(!0);try{await new ae().callRawAPI(`/v4/api/records/notes/${s}`,{},"DELETE"),r(!1),d()}catch(u){X(l,u.message),P(c,u.message,5e3,"error")}p(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"h-[2.5625rem] w-[4.375rem] min-w-fit rounded-[.125rem] border-[.0625rem] border-[#1f1d1a] bg-brown-main-bg px-4 py-2 font-iowan text-[14px] font-medium text-[#1f1d1a] sm:w-[80px] md:text-[14px]",onClick:()=>r(!0),children:"Delete"}),e.jsx(g,{appear:!0,show:o,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>r(!1),children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"overflow-y-auto fixed inset-0",children:e.jsx("div",{className:"flex justify-center items-center p-4 min-h-full text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"overflow-hidden p-6 w-full text-base text-left align-middle rounded-md shadow-xl transition-all transform bg-brown-main-bg sm:max-w-md",children:[e.jsxs("div",{className:"flex gap-2 justify-between items-center",children:[e.jsx(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900 font-iowan",children:"Are you sure you want to delete this entry?"}),e.jsx("button",{onClick:()=>r(!1),type:"button",children:e.jsx(oe,{className:"w-6 h-6"})})]}),e.jsx("p",{className:"mt-2",children:"This action cannot be undone."}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mt-6",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>r(!1),children:"Cancel"}),e.jsx(Ee,{loading:j,disabled:j,onClick:T,className:"rounded-lg bg-[#1f1d1a] py-2 text-center font-iowan font-semibold text-white transition-colors duration-100 disabled:bg-opacity-60",children:"Yes, delete"})]})]})})})})]})})]})}function Gs({note:s,afterAsking:d}){const[o,r]=t.useState(!1),[l,c]=t.useState(!1),{dispatch:j}=t.useContext(ne),{dispatch:p}=t.useContext(ie),[T,u]=t.useState(""),[h,b]=t.useState(s==null?void 0:s.content);t.useEffect(()=>{b(s==null?void 0:s.content)},[s]);const F=Le({expandOrShorten:Q().nullable().optional(),rephrase:Rt(),correctGrammar:Rt()}),{register:v,handleSubmit:w,formState:{isSubmitting:N},reset:M,setValue:A}=Ae({resolver:Fe(F),defaultValues:{expandOrShorten:"",rephrase:!1,correctGrammar:!1}}),x=S=>{T===S?(u(""),A("expandOrShorten","")):(u(S),A("expandOrShorten",S))},f=t.useCallback(S=>{S&&S.stopPropagation(),window.innerWidth<768?c(!0):r(!0)},[]),L=t.useCallback(S=>{S&&S.stopPropagation(),r(!1)},[]),y=t.useCallback(S=>{S&&S.stopPropagation(),c(!1)},[]);function k(S){return!S||!S.blocks?"":S.blocks.map(B=>B.type==="header"?`${B.data.text}`:B.type==="list"?B.data.items.map(q=>`• ${q}`).join(`
`):B.type==="paragraph"?B.data.text:"").join(`

`)}async function i(S){var B;try{let q=k(At(h,{blocks:[]}));const E=new ae;if(S.expandOrShorten||S.correctGrammar||S.rephrase){const le=`
        ${S.expandOrShorten?`${S.expandOrShorten} text. 
`:""}
        ${S.rephrase?`Rewrite text. 
`:""}
        ${S.correctGrammar?`Fix grammar. 
`:""}
            ${q}
        `.trim(),ee=[{role:"system",content:"update the text based on the user request"},{role:"user",content:le}],R=await E.callRawAPI("/v3/api/custom/goodbadugly/ai/ask",{temperature:1,prompt:ee},"POST");q=(B=R==null?void 0:R.data)==null?void 0:B.content;const a=JSON.stringify({time:Date.now(),blocks:[{id:Math.floor(Math.random()*999)+1,type:"paragraph",data:{style:"unordered",text:q}}]});await E.callRawAPI(`/v4/api/records/notes/${s.id}`,{content:a},"PUT"),b(a),d(a)}r(!1),c(!1),M({expandOrShorten:"",rephrase:!1,correctGrammar:!1}),u(""),A("expandOrShorten","")}catch(q){X(j,q.message),P(p,q.message,5e3,"error")}}const W=()=>e.jsx("div",{className:"flex flex-col",children:e.jsxs("form",{onSubmit:w(i),className:"flex flex-col",children:[e.jsx("div",{className:"gap--[80px] flex flex-col",children:e.jsxs("div",{className:"flex gap-8 items-center",children:[e.jsxs("div",{className:"flex gap-4 items-center text-lg bold",children:[e.jsx("input",{type:"radio",id:"expand",name:"expandShorten",checked:T==="expand",onChange:()=>x("expand"),className:"h-4 w-4 border-[2px] border-[#1f1d1a] bg-brown-main-bg checked:bg-[#1f1d1a]"}),e.jsx("label",{htmlFor:"expand",children:"Expand"})]}),e.jsxs("div",{className:"flex gap-4 items-center text-lg bold",children:[e.jsx("input",{type:"radio",id:"shorten",name:"expandShorten",checked:T==="shorten",onChange:()=>x("shorten"),className:"h-4 w-4 border-[2px] border-[#1f1d1a] bg-brown-main-bg checked:bg-[#1f1d1a]"}),e.jsx("label",{htmlFor:"shorten",children:"Shorten"})]})]})}),e.jsxs("div",{className:"flex gap-4 items-center mt-4 text-lg bold",children:[e.jsx("input",{type:"checkbox",...v("rephrase"),className:"border-[2px] border-[#1f1d1a] bg-brown-main-bg accent-black"}),e.jsx("label",{children:"Rephrase"})]}),e.jsxs("div",{className:"flex gap-4 items-center mt-4 text-lg bold",children:[e.jsx("input",{type:"checkbox",...v("correctGrammar"),className:"border-[2px] border-[#1f1d1a] bg-brown-main-bg accent-black"}),e.jsx("label",{children:"Correct Grammar"})]}),e.jsx(Ee,{type:"submit",loading:N,disabled:N,className:"mt-6 w-full rounded bg-[#1f1d1a] px-4 py-2 font-iowan font-bold text-white",children:"Process"})]})});return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"h-[2.5625rem] w-[4.375rem] whitespace-nowrap rounded-[.125rem] border-[.0625rem] border-[#1f1d1a] bg-[#1f1d1a] px-2 py-2 font-iowan text-[.875rem] font-medium text-white sm:w-[5rem] sm:px-4 sm:text-[.875rem] md:text-sm",onClick:f,children:"Ask AI"}),e.jsx("div",{className:"hidden md:block",children:e.jsx(g,{appear:!0,show:o,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] hidden md:block",onClose:L,children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 hidden bg-[#1f1d1a] bg-opacity-25 md:block"})}),e.jsx("div",{className:"hidden overflow-y-auto fixed inset-0 md:block",children:e.jsx("div",{className:"hidden justify-center items-center p-4 min-h-full text-center md:flex",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"askai-dialog w-full max-w-full transform overflow-hidden rounded-md border border-[#1f1d1a] bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all sm:w-[400px]",children:[e.jsxs("div",{className:"flex justify-between items-center pb-5",children:[e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsxs("svg",{width:"24",height:"25",viewBox:"0 0 24 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M12.9476 6.89924C12.7636 6.52965 12.2364 6.52965 12.0524 6.89924L10.0822 10.8574C10.0337 10.9548 9.95475 11.0337 9.85736 11.0822L5.89924 13.0524C5.52965 13.2364 5.52965 13.7636 5.89924 13.9476L9.85736 15.9178C9.95475 15.9663 10.0337 16.0452 10.0822 16.1426L12.0524 20.1008C12.2364 20.4704 12.7636 20.4704 12.9476 20.1008L14.9178 16.1426C14.9663 16.0452 15.0452 15.9663 15.1426 15.9178L19.1008 13.9476C19.4704 13.7636 19.4704 13.2364 19.1008 13.0524L15.1426 11.0822C15.0452 11.0337 14.9663 10.9548 14.9178 10.8574L12.9476 6.89924Z",stroke:"#1F1D1A",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M5.5 4.5C5.13084 5.72076 4.70251 6.1348 3.5 6.5C4.6962 6.87751 5.136 7.31356 5.5 8.5C5.85347 7.28434 6.30137 6.84994 7.5 6.5C6.30999 6.14978 5.86661 5.71912 5.5 4.5Z",stroke:"#1F1D1A",strokeWidth:"2",strokeLinejoin:"round"})]}),e.jsx(_.Title,{as:"h3",className:"text-xl font-bold leading-6 text-black font-iowan",children:"Ask AI"})]}),e.jsx("button",{onClick:L,type:"button",children:e.jsx(oe,{className:"w-6 h-6"})})]}),W()]})})})})]})})}),e.jsx("div",{className:"md:hidden",children:e.jsxs(Te,{isOpen:l,onClose:y,children:[e.jsxs("div",{className:"flex justify-between items-center pb-5",children:[e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsxs("svg",{width:"24",height:"25",viewBox:"0 0 24 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M12.9476 6.89924C12.7636 6.52965 12.2364 6.52965 12.0524 6.89924L10.0822 10.8574C10.0337 10.9548 9.95475 11.0337 9.85736 11.0822L5.89924 13.0524C5.52965 13.2364 5.52965 13.7636 5.89924 13.9476L9.85736 15.9178C9.95475 15.9663 10.0337 16.0452 10.0822 16.1426L12.0524 20.1008C12.2364 20.4704 12.7636 20.4704 12.9476 20.1008L14.9178 16.1426C14.9663 16.0452 15.0452 15.9663 15.1426 15.9178L19.1008 13.9476C19.4704 13.7636 19.4704 13.2364 19.1008 13.0524L15.1426 11.0822C15.0452 11.0337 14.9663 10.9548 14.9178 10.8574L12.9476 6.89924Z",stroke:"#1F1D1A",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M5.5 4.5C5.13084 5.72076 4.70251 6.1348 3.5 6.5C4.6962 6.87751 5.136 7.31356 5.5 8.5C5.85347 7.28434 6.30137 6.84994 7.5 6.5C6.30999 6.14978 5.86661 5.71912 5.5 4.5Z",stroke:"#1F1D1A",strokeWidth:"2",strokeLinejoin:"round"})]}),e.jsx("h3",{className:"text-xl font-bold leading-6 text-black font-iowan",children:"Ask AI"})]}),e.jsx("button",{onClick:y,type:"button",children:e.jsx(oe,{className:"w-6 h-6"})})]}),W()]})})]})}function ea({note:s,afterEdit:d}){const[o,r]=t.useState(!1),{dispatch:l}=t.useContext(ne),{dispatch:c}=t.useContext(ie),[j,p]=t.useState(!1),[T,u]=t.useState(null),[h,b]=t.useState(!1),{startRecording:F,stopRecording:v,recordingBlob:w,isRecording:N}=ps();t.useEffect(()=>{w&&(console.log("setting blob"),u(w))},[w]),t.useEffect(()=>{!T||!j||M()},[T,j]);async function M(){r(!0);try{let x=new FormData;const f=new File([T],"audio.wav",{type:"audio/wav"});x.append("file",f),console.log("f",T);const y=await new ae().callTranscribe("/v3/api/custom/goodbadugly/ai/transcribe-audio",x,"POST");console.log(y);const k=At(s.content,{blocks:[]});A(JSON.stringify({time:Date.now(),blocks:[...k.blocks,{id:Math.floor(Math.random()*999)+1,type:"list",data:{style:"unordered",items:[y.data]}}]}))}catch(x){X(l,x.message),P(c,x.message,5e3,"error")}r(!1),p(!1),u(null)}async function A(x){b(!0);try{await new ae().callRawAPI(`/v4/api/records/notes/${s.id}`,{content:x},"PUT"),d()}catch(f){X(l,f.message),P(c,f.message,5e3,"error")}b(!1)}return e.jsx("button",{onClick:async()=>{N?(v(),p(!0)):F()},disabled:o||h,className:`focus:shadow-outline rounded-[.125rem] ${N?"animate-pulse border-red-500":"border-[#1f1d1a]"}  border-[.0625rem]  px-4 py-2`,title:"Transcribe more",children:N?e.jsx("div",{className:"h-6 w-6 rounded-[50%]  bg-red-500",children:e.jsx(bs,{className:"h-6 w-6 text-white"})}):e.jsx(gs,{className:"h-6 w-6 text-[#1f1d1a]",strokeWidth:2})})}function ta({note:s,afterEdit:d}){const{dispatch:o}=t.useContext(ne),{dispatch:r}=t.useContext(ie),[l,c]=t.useState(!1),[j,p]=t.useState(""),[T,u]=t.useState(null);t.useEffect(()=>{p(s.type)},[s]);async function h(b){c(!0);try{await new ae().callRawAPI(`/v4/api/records/notes/${s.id}`,{type:b},"PUT"),d(),P(r,"Saved")}catch(F){X(o,F.message),P(r,F.message,5e3,"error")}c(!1)}return e.jsx(e.Fragment,{children:e.jsx("input",{className:`no-box-shadow w-full border-none font-inter text-[1.25rem] font-[600] leading-[1.5rem] ${j==="Section title"?"bg-brown-main-bg":"bg-transparent"}  p-0 text-xl font-semibold ring-transparent`,value:j,autoFocus:j==="Section title",readOnly:j==="Section title"?!1:l,onChange:b=>{p(b.target.value),T&&clearTimeout(T);const F=setTimeout(()=>h(b.target.value),2e3);u(F)}})})}const sa=t.memo(({note:s,refetch:d,dragHandleProps:o,setEdited:r,isOwner:l=!1,collaborator:c=null,isCollaborator:j=!1})=>{const[p,T]=t.useState(!1),[u,h]=t.useState(s),{note:b,arrayData:F,refetch:v}=js(s.id,s);t.useContext(ne);const{convertDate:w}=Qt(),N=t.useMemo(()=>At(b==null?void 0:b.content,{blocks:[{id:"zbGZFPM-iI",type:"paragraph",data:{text:""}}]}),[b==null?void 0:b.content]),M=t.useCallback(()=>{T(!0),setTimeout(()=>T(!1),2e3)},[]);t.useEffect(()=>{s&&v()},[s==null?void 0:s.id]),t.useEffect(()=>{b&&b!==u&&h(b)},[b]);const A=t.useCallback(async L=>{h(y=>({...y,content:L,update_at:new Date().toISOString()})),await d(),await v(),M()},[d,v]),x=t.useCallback(L=>{const y=Math.abs(U(L).diff(U(),"hours")),k=Math.abs(U(L).diff(U(),"years"));return y<24?w(L,{hour:"2-digit",minute:"2-digit",hour12:!0,timeZoneName:"short",timeZone:"America/Los_Angeles"}).replace(", "," - "):k>0?w(L,{year:"numeric",month:"numeric",day:"numeric",timeZoneName:"short",timeZone:"America/Los_Angeles"}).replace(", "," - "):w(L,{month:"numeric",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!0,timeZoneName:"short",timeZone:"America/Los_Angeles"}).replace(", "," - ")},[w]),f=t.useMemo(()=>`editorjs-container-${b.id}`,[b.id]);return e.jsx("div",{className:`${!(l||j&&(c==null?void 0:c.note_id)==(b==null?void 0:b.id))&&"overlayed bg-[#F2DFCE] p-4"}
        relative flex flex-col justify-between gap-4`,children:e.jsxs("div",{className:"flex flex-col justify-between",children:[e.jsxs("div",{className:"flex flex-col justify-between items-start w-full sm:flex-row sm:items-center",children:[e.jsxs("div",{className:"group mt-5 flex flex-col gap-[.25rem]",children:[e.jsxs("div",{className:"flex items-center gap-[.25rem]",children:[e.jsx("div",{...o,className:"cursor-grab",children:e.jsx(Gt,{})}),e.jsx(ta,{note:b,afterEdit:v})]}),e.jsxs("p",{className:"flex flex-row items-center gap-2 pl-1 font-iowan text-[1rem] font-[700] leading-[1.25rem]",children:[p&&e.jsx(ys,{color:"green",size:20}),e.jsx("span",{className:"",children:"Last Saved"}),x(b.update_at)]})]}),e.jsx(ws,{isOwner:l,note_id:b.id})]}),e.jsxs("div",{className:"relative self-end mt-4 space-y-4 w-full",children:[!(l||j&&(c==null?void 0:c.note_id)==(b==null?void 0:b.id))&&e.jsx("div",{className:"absolute inset-0 left-[-10px] top-[-10px] z-[9999] h-full max-h-full min-h-[110%] w-full min-w-[102%] max-w-full  md:min-h-[107%]"}),e.jsxs("div",{className:"",children:[e.jsx(fs,{data:N,note_id:b.id,editorID:f,afterEdit:v,setUpdated:T,updateSaved:M}),e.jsx("div",{className:"flex justify-start items-center mt-4 md:justify-end",children:e.jsxs("div",{className:"flex gap-2 items-center sm:gap-4",children:[e.jsx(ea,{note:b,afterEdit:v}),e.jsx(Ks,{note_id:b.id,afterDelete:d}),e.jsx(Gs,{note:u,afterAsking:A})]})})]})]})]})})},(s,d)=>{var o,r;return s.note.id===d.note.id&&s.note.content===d.note.content&&s.note.update_at===d.note.update_at&&s.isOwner===d.isOwner&&s.isCollaborator===d.isCollaborator&&((o=s.collaborator)==null?void 0:o.note_id)===((r=d.collaborator)==null?void 0:r.note_id)});function aa(s){return Pe({tag:"svg",attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 18.75h-9m9 0a3 3 0 013 3h-15a3 3 0 013-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 01-.982-3.172M9.497 14.25a7.454 7.454 0 00.981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 007.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 002.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.492a46.32 46.32 0 012.916.52 6.003 6.003 0 01-5.395 4.972m0 0a6.726 6.726 0 01-2.749 1.35m0 0a6.772 6.772 0 01-3.044 0"}}]})(s)}function na(s){return Pe({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"g",attr:{id:"Microphone_On"},child:[{tag:"g",attr:{},child:[{tag:"path",attr:{d:"M11.989,2.065a4.507,4.507,0,0,0-4.5,4.5v5.76a4.5,4.5,0,0,0,9,0V6.565A4.507,4.507,0,0,0,11.989,2.065Zm0,13.76a3.5,3.5,0,0,1-3.5-3.5V6.565a3.5,3.5,0,0,1,6.94-.62h-1.87a.5.5,0,0,0-.5.5.5.5,0,0,0,.5.5h1.93v2h-1.93a.5.5,0,0,0-.5.5.508.508,0,0,0,.5.5h1.93v2h-1.94a.508.508,0,0,0-.5.5.515.515,0,0,0,.5.5h1.88A3.492,3.492,0,0,1,11.989,15.825Z"}},{tag:"path",attr:{d:"M12.489,18.925v2.01h3.5a.5.5,0,0,1,0,1h-8a.5.5,0,0,1,0-1h3.5v-1.99a6.055,6.055,0,0,1-2.74-.88,6.291,6.291,0,0,1-2.97-5.14c-.03-1.04,0-2.09,0-3.13a.5.5,0,0,1,1,0c0,1.04-.03,2.09,0,3.13A5.212,5.212,0,0,0,17.2,12.7c.01-.96,0-1.93,0-2.9a.5.5,0,0,1,1,0,26.322,26.322,0,0,1-.08,3.97A6.235,6.235,0,0,1,12.489,18.925Z"}}]}]}]})(s)}function ra(s){return Pe({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"rect",attr:{x:"2",y:"3",width:"20",height:"14",rx:"2",ry:"2"}},{tag:"line",attr:{x1:"8",y1:"21",x2:"16",y2:"21"}},{tag:"line",attr:{x1:"12",y1:"17",x2:"12",y2:"21"}}]})(s)}function ia(s){return Pe({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M311.133 257.95a15.074 15.074 0 0 1-12.845 17.026l-147.248 20.61-21.33 32.522-82.637 11.57 21.33-32.568-24.547 3.44c-.278 0-.555.12-.843.165a15.218 15.218 0 0 1-2.108.144 15.074 15.074 0 0 1-2.074-30.016c.29 0 .567 0 .855-.078l24.547-3.438-29.45-25.512 82.582-11.547 29.45 25.51 147.26-20.608a15.196 15.196 0 0 1 2.107-.145 15.085 15.085 0 0 1 14.953 12.923zm-36.704-38.546a32.4 32.4 0 0 1 10.847-10.326 23.427 23.427 0 0 1 13.422-3.04 27.875 27.875 0 0 1 13.542 5.047 44.557 44.557 0 0 1 11.924 12.59 66.342 66.342 0 0 1 8.386 19.134 77.48 77.48 0 0 1 2.562 21.995 67.895 67.895 0 0 1-3.494 19.966 46.132 46.132 0 0 1-8.54 15.352 28.163 28.163 0 0 1-12.402 8.552 23.382 23.382 0 0 1-13.765.255 32.012 32.012 0 0 1-12.512-7.122 45.478 45.478 0 0 1-5.734-6.2l-32.278 4.514a131.154 131.154 0 0 0 7.1 15.973 104.566 104.566 0 0 0 18.656 25.512 69.016 69.016 0 0 0 23.893 15.806 48.373 48.373 0 0 0 27.597 2.22 53.43 53.43 0 0 0 26.31-14.876c7.898-7.853 14.42-18.258 19.112-30.514a131.997 131.997 0 0 0 8.32-41.995 153.26 153.26 0 0 0-5.48-46.92 128.758 128.758 0 0 0-18.49-39.932c-7.6-10.726-16.417-18.946-25.78-24.403a52.998 52.998 0 0 0-27.962-7.62 48.573 48.573 0 0 0-26.278 8.718 69.88 69.88 0 0 0-20.165 21.897 107.505 107.505 0 0 0-11.99 29.516A137.144 137.144 0 0 0 237.68 235l31.192-4.37a49.172 49.172 0 0 1 5.557-11.226zm198.305-34.984c-10.926-35.274-27.287-64.757-46.842-87.374-18.557-21.518-39.544-36.26-61.118-44.213-20.155-7.41-40.564-8.74-59.953-4.248-18.058 4.204-35.196 13.466-50.603 27.62-14.42 13.21-26.09 29.626-35.185 47.807a233.224 233.224 0 0 0-19.29 56.57 286.023 286.023 0 0 0-5.856 60.674l22.582-3.16a209.143 209.143 0 0 1 5.047-40.344 166.26 166.26 0 0 1 15.972-42.926c7.365-13.4 16.716-25.124 27.997-34.087 11.89-9.44 24.88-14.986 38.3-16.64 14.165-1.774 28.773.744 42.938 7.51 14.863 7.1 29.084 18.78 41.485 34.774 12.856 16.572 23.515 37.46 30.66 61.917a225.515 225.515 0 0 1 8.74 74.65c-1.254 24.05-6.4 46.422-14.72 65.656-8.042 18.58-18.857 33.887-31.824 44.88-12.313 10.47-26.345 16.915-41.463 18.656a76.226 76.226 0 0 1-41.163-7.1c-12.313-5.722-23.826-14.485-34.03-25.51a162.212 162.212 0 0 1-25.724-37.637 204.584 204.584 0 0 1-14.542-38.578l-22.484 3.106a280.965 280.965 0 0 0 19.966 57.823 228.2 228.2 0 0 0 32.168 50.092c12.99 15.186 27.82 27.83 43.914 36.793 17.18 9.574 36.027 15.064 55.705 14.865 21.263-.21 41.44-7.022 59.52-19.778 19.356-13.654 36.005-33.897 48.617-59.432 13.244-26.82 21.697-58.788 24.048-93.64a300.742 300.742 0 0 0-12.856-108.76z"}}]})(s)}function la(s){return Pe({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M87.49 27.99C58.7 27.99 35.5 51.17 35.5 80c0 28.8 23.2 52 52 52s52-23.2 52-52c0-28.83-23.2-52.01-52.01-52.01zM219.5 54.55c-2.6 4.13-4 9.58-4.5 15.32-20.8-.7-39.2-1.03-58.3-.73.8 6.29 1.1 12.15.4 18 19.8-.25 39.1 0 58.2.77.3 3.12.7 5.96 1.2 8.26-11.9 24.43-25.4 44.13-32.3 70.43 2.3 24.6 5.2 53.2 23.1 77.7 5 19.9 9.1 39.7 14.6 59.6 2.1-25.1 7.6-51.9 21.4-79.2-1.9-9.1-2.1-17.2-1.3-25.7-4.2-8.1-9.8-16.2-19.2-24 12.9-23.8 13.2-46.2 17.6-71.8l19.5-4.78c1.8-2.39 3.3-4.92 4.4-7.56 31 2.54 61.2 6.27 90.6 10.94.6-6.09 2-11.99 4.1-17.56-29.6-4.75-60-8.55-91.2-11.17 0-4.55-.4-9.28-1.1-14.13-17.1-2.57-31.6-6.06-47.2-4.39zm205 2.44c-28.8 0-52 23.18-52 52.01 0 28.8 23.2 52 52 52s52-23.2 52-52c0-28.83-23.2-52.01-52-52.01zm-135 108.11c-7.5 0-14.6 3.9-20.3 11.6-5.8 7.6-9.7 18.8-9.7 31.3 0 6.6 1.1 13.6 3 19.1 8.9-3.1 18.1-7.5 26.7-15 7.9 6.4 16.6 10.9 26.8 15 2.2-5.9 3.5-11.9 3.5-19.1 0-12.5-3.9-23.7-9.7-31.3-5.7-7.7-12.8-11.6-20.3-11.6zm-.3 58.9c-6.8 10.6-8.8 14.7-21.7 12.9 5.3 7.4 12.7 14.1 22 14 10.4-.1 17.2-6.5 21.8-13.8-11.2.8-16.9-3.1-22.1-13.1zm60.8 28.3c2.4 25.1 3.6 39.4 1.5 63.8 2.2 3.2 2.8 19.1 11.1 22.5-12.8 6.5-17.6 24.1-24.6 31-18.2 21.6-31.1 55.3-43.6 86.3 3.7 12.8 8.3 25.5 13.7 38.1H365c-5.6-27.2-13.6-54.7-22.2-82.1 3.4-5.4 3-4 6.5-23 14.9-10.2 47.2-27.3 52.6-49.6-5-19.9-11.7-32.8-23.5-49.9 2.8-24.2-16.2-30.8-28.4-37.1zm-100.3 3.4c-3.5 10.4-5.9 20.7-7.6 30.9l13.9-1.2c-3.2-9.7-4.9-20.1-6.3-29.7zm65.9 3.5c-7.4 6-16.4 9.7-26.1 9.7-7.4 0-14.3-2.1-20.4-5.7.5 2.4.9 4.8 1.5 7.2 1.9 8.5 4.7 16.8 8 22.4 3.2 5.5 6 7.5 8.9 7.7 3.1.2 6.6-1.5 10.9-6.7 4.4-5.2 8.7-13.2 12.2-21.4 1.9-4.5 3.6-9 5-13.2zm17.4 5c-2.8 7.9-5.3 14.6-8.6 21.5l9.8.5c-.2-7.3-.6-14.6-1.2-22zm-69.4 38.7l-23.7 2c-.6 7.7-.9 15.4-1.1 23l94.6-1.5c.7-7.3 1-14.7 1.1-22.1l-20.5-1.1c-7.6 8.6-18.5 15.7-27.7 15.3-10.3-.7-17.8-7.7-22.7-15.6zm65.6 41.5l-90.4 1.5c-.9 9.3 6.9 16.2 12.3 20.3l66.7-.2c7.2-8 8.6-11.2 11.4-21.6zM236 376.8c-14.4 39-29.7 77.9-41.2 117.2h53.1c6.7-12.3 12.8-24.9 18-37.8-.7-31.9-14.5-62.9-29.9-79.4zm70.3 7.3l-41.9.1c7.8 13.1 11.8 28.8 15 45.6l8.3-.8c6.1-15.2 10.6-30.7 18.6-44.9z"}}]})(s)}function oa(s){return Pe({tag:"svg",attr:{viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}},{tag:"path",attr:{d:"M9.5 3h5a1.5 1.5 0 0 1 1.5 1.5a3.5 3.5 0 0 1 -3.5 3.5h-1a3.5 3.5 0 0 1 -3.5 -3.5a1.5 1.5 0 0 1 1.5 -1.5z"}},{tag:"path",attr:{d:"M4 17v-1a8 8 0 1 1 16 0v1a4 4 0 0 1 -4 4h-8a4 4 0 0 1 -4 -4z"}}]})(s)}function ca(s){return Pe({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M459.94 53.25a16.06 16.06 0 00-23.22-.56L424.35 65a8 8 0 000 11.31l11.34 11.32a8 8 0 0011.34 0l12.06-12c6.1-6.09 6.67-16.01.85-22.38zM399.34 90L218.82 270.2a9 9 0 00-2.31 3.93L208.16 299a3.91 3.91 0 004.86 4.86l24.85-8.35a9 9 0 003.93-2.31L422 112.66a9 9 0 000-12.66l-9.95-10a9 9 0 00-12.71 0z"}},{tag:"path",attr:{d:"M386.34 193.66L264.45 315.79A41.08 41.08 0 01247.58 326l-25.9 8.67a35.92 35.92 0 01-44.33-44.33l8.67-25.9a41.08 41.08 0 0110.19-16.87l122.13-121.91a8 8 0 00-5.65-13.66H104a56 56 0 00-56 56v240a56 56 0 0056 56h240a56 56 0 0056-56V199.31a8 8 0 00-13.66-5.65z"}}]})(s)}function da(s){return Pe({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M160 240h147.37l-64-64L266 153.37 368.63 256 266 358.63 243.37 336l64-64H160v148a12 12 0 0012 12h296a12 12 0 0012-12V92a12 12 0 00-12-12H172a12 12 0 00-12 12zm-128 0h128v32H32z"}}]})(s)}function ma({afterAdd:s}){const[d,o]=t.useState(!1),[r,l]=t.useState(!1),{dispatch:c}=t.useContext(ne),{dispatch:j}=t.useContext(ie),[p,T]=t.useState(!1),[u,h]=t.useState(""),{id:b}=Me(),F=t.useCallback(x=>{x&&x.stopPropagation(),window.innerWidth<768?l(!0):o(!0)},[]),v=t.useCallback(x=>{x&&x.stopPropagation(),o(!1)},[]),w=t.useCallback(x=>{x&&x.stopPropagation(),l(!1)},[]);async function N(x){T(!0),o(!1),l(!1),h(x);try{await new ae().callRawAPI("/v4/api/records/notes",{type:x,update_id:b,status:0},"POST"),s(),P(j,"New section added"),h("")}catch(f){X(c,f.message),P(j,f.message,5e3,"error"),h("")}T(!1)}const M=[{id:1,title:"Shout outs",key:"Shout outs",icon:e.jsx(vs,{size:20})},{id:2,title:"Achievements",key:"Achievements",icon:e.jsx(aa,{size:20})},{id:3,title:"Announcements",key:"Announcements",icon:e.jsx(na,{size:20})},{id:4,title:"Marketing strategies",key:"Marketing strategies",icon:e.jsx(ia,{size:20})},{id:5,title:"Product",key:"Product",icon:e.jsx(ra,{size:20})},{id:6,title:"Challenges",key:"Challenges",icon:e.jsx(la,{size:22})},{id:7,title:"Financials",key:"Financials",icon:e.jsx(oa,{size:22})},{id:8,title:"KPIs",key:"KPIs",icon:e.jsx(os,{size:17})},{id:9,title:"Intro",key:"Intro",icon:e.jsx(da,{size:22})},{id:10,title:"What's next?",key:"What's next?",icon:e.jsx(mt,{size:20})},{id:12,title:"TL:DR (too long; didn't read)",key:"TL:DR (too long; didn't read)",icon:e.jsx(mt,{size:20})},{id:13,title:"NSM (North Star Metric)",key:"NSM (North Star Metric)",icon:e.jsx(mt,{size:20})},{id:14,title:"Customer Quote(s)",key:"Customer Quote(s)",icon:e.jsx(mt,{size:20})},{id:11,title:"Custom",key:"Section title",icon:e.jsx(ca,{size:22})}],A=()=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold leading-6 text-gray-900 font-iowan md:text-xl",children:"Add section name"}),e.jsx("button",{onClick:window.innerWidth<768?w:v,type:"button",children:e.jsx(oe,{className:"w-6 h-6"})})]}),e.jsx("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:M.map(x=>e.jsxs("div",{className:`flex h-[44px] w-full cursor-pointer flex-row items-center gap-2 rounded border border-[#1f1d1a] px-2 font-medium ${u===x.key&&"border-[2px] border-blue-400"}`,onClick:()=>N(x.key),children:[e.jsx("span",{children:x.icon}),e.jsx("span",{className:"text-sm truncate md:text-base",children:x.title})]},x.id))})]});return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"mt-6 block w-full rounded-[2px] border-[1px] border-[#1f1d1a] bg-brown-main-bg py-2 font-iowan font-semibold text-[#1f1d1a]",onClick:F,children:"Add section +"}),e.jsx("div",{className:"hidden md:block",children:e.jsx(g,{appear:!0,show:d,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50]",onClose:v,children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"overflow-y-auto fixed inset-0",children:e.jsx("div",{className:"flex justify-center items-center p-4 min-h-full text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsx(_.Panel,{className:"custom-overflow max-h-[90vh] w-fit max-w-fit transform overflow-hidden overflow-y-auto rounded-md bg-brown-main-bg p-6 text-left align-middle text-sm shadow-xl transition-all",children:A()})})})})]})})}),e.jsx("div",{className:"md:hidden",children:e.jsx(Te,{isOpen:r,onClose:w,className:"px-1",children:A()})})]})}function xa({update:s,afterEdit:d,isOwner:o=!1}){const{dispatch:r}=t.useContext(ne),{dispatch:l}=t.useContext(ie),[c,j]=t.useState(!1),[p,T]=t.useState(""),[u,h]=t.useState(30),{id:b}=Me(),[F,v]=t.useState(null),w=t.useRef(null),N=Yt(),M=new URLSearchParams(N.search).get("autofocus")==="true",[A,x]=t.useState(M),f=y=>window.innerWidth<658?y<=20?18:y<=30?17:y<=40?14:y<=50?12:10:y<=20?30:y<=30?26:y<=40?22:y<=50?18:16;t.useEffect(()=>{var y;M&&w.current?(w.current.focus(),w.current.select()):(T(s.name),h(f(((y=s.name)==null?void 0:y.length)||0)))},[s,M]);async function L(y){j(!0);try{await new ae().callRawAPI(`/v4/api/records/updates/${b}`,{name:y},"PUT"),d(),P(l,"Saved"),x(!1)}catch(k){X(r,k.message),P(l,k.message,5e3,"error")}j(!1)}return e.jsxs("div",{className:`flex  flex-col items-start gap-2 md:w-auto ${(p==null?void 0:p.length)<=17?"w-[140px] max-w-[500px]":"w-full min-w-[310px]"}`,children:[e.jsx("input",{ref:w,maxLength:30,placeholder:A?"Edit Title":"",disabled:!o,className:"no-box-shadow focus:shadow-outline  w-full max-w-[500px] appearance-none border-none bg-brown-main-bg p-0 font-bold transition-all duration-200 placeholder:text-gray-500 focus:outline-none",value:p,onChange:y=>{T(y.target.value),h(f(y.target.value.length)),F&&clearTimeout(F);const k=setTimeout(()=>L(y.target.value),5e3);v(k)},readOnly:c,style:{fontSize:`${u}px`}}),A&&e.jsx("div",{className:"rounded-md bg-[yellow] p-2 text-sm italic text-black",children:"Modify the default update title to begin working on it."})]})}const $t=t.lazy(()=>Lt(()=>import("./PrivacyLink-35299c5d.js"),["assets/PrivacyLink-35299c5d.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"]));t.lazy(()=>Lt(()=>Promise.resolve().then(()=>Hs),void 0));t.lazy(()=>Lt(()=>import("./ScheduleSendButton-084382b0.js"),["assets/ScheduleSendButton-084382b0.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"]));const ua={1:"Only logged in users that have been invited as a recipient can view this update (no sharing w/ 3rd parties) on UpdateStack with the following link",2:"Both logged out and logged in users that have the public UpdateStack link below can view and share this update"},ha={0:"Enable Shareable Link",1:"Disable Shareable Link"};function qt({update:s,refetch:d,isOwner:o=!1}){const[r,l]=t.useState(!1),[c,j]=t.useState(!1),[p,T]=t.useState(!1),{dispatch:u}=t.useContext(ne),{dispatch:h}=t.useContext(ie),[b,F]=t.useState(!1),[v,w]=t.useState(s.private_link_open==dt.YES),{id:N}=Me(),[M,A]=t.useState(s.public_link_id),[x,f]=t.useState(s.private_link_access==0?1:s.private_link_access);console.log("update.private_link_access",s.private_link_access);const[L,y]=t.useState(s.public_link_enabled==1),k=()=>s.public_link_id&&[1].includes(x)?v?1:0:s.public_link_id&&[2].includes(x)&&L?1:0,i=()=>{s.public_link_id&&[1].includes(x)?W():s.public_link_id&&[2].includes(x)&&S()},W=()=>{w(!v)},S=()=>{y(!L)};async function B(){F(!0);try{const R=await Vt(h,u,"updates",N,{private_link_access:x,private_link_open:x==1?v?dt.YES:dt.NO:0,public_link_id:[1,2].includes(x)?M:"",public_link_enabled:x==2?L:0});R!=null&&R.error||(d(),P(h,"Saved"),l(!1),j(!1))}catch(R){X(u,R.message),P(h,R.message,5e3,"error")}F(!1)}function q(R){const a=new Date().getTime();f(R),console.log("access",R),A(btoa(`${Dt(15)}_${Xe[R]}_${a}`)),console.log(btoa(`${Dt(15)}_${Xe[R]}_${a}`)),console.log(k(),"dhdhd"),T(!0)}t.useEffect(()=>{f((s==null?void 0:s.private_link_access)==0?1:s==null?void 0:s.private_link_access),y(s==null?void 0:s.public_link_enabled),A(s==null?void 0:s.public_link_id),w((s==null?void 0:s.private_link_open)==dt.YES),s!=null&&s.public_link_id&&T(!0)},[s==null?void 0:s.private_link_access,s==null?void 0:s.public_link_enabled,s==null?void 0:s.public_link_id,s==null?void 0:s.private_link_open]);const E=t.useCallback(R=>{R&&R.stopPropagation(),window.innerWidth<768?j(!0):l(!0)},[]),H=t.useCallback(R=>{R&&R.stopPropagation(),l(!1)},[]),le=t.useCallback(R=>{R&&R.stopPropagation(),j(!1)},[]),ee=()=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mt-6 flex flex-col gap-4 rounded-md border-[1px] border-[#1f1d1a] bg-brown-main-bg p-4 font-medium text-gray-800 sm:flex-row sm:items-center",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("p",{className:"text-lg font-semibold text-[#1f1d1a]",children:[Xe[x]," Access"]}),e.jsx("p",{className:"mt-3 text-sm font-normal md:text-base",children:ua[x]})]}),e.jsx("select",{className:"focus:shadow-outline appearance-none  border bg-brown-main-bg py-2 pl-3 pr-8 leading-tight text-[#1f1d1a] shadow focus:outline-none ",value:x,onChange:R=>q(Number(R.target.value)),children:Object.entries(Xe).filter(([R])=>R!=="0").map(([R,a])=>e.jsx("option",{value:R,children:a},R))})]}),e.jsx($t,{link:`${window.location.origin}/update/view/${N}`}),x===2&&e.jsxs("div",{className:"flex gap-4 items-center mt-4",children:[e.jsx(qe,{enabled:v,setEnabled:w}),e.jsx("p",{className:"text-sm md:text-base",children:"Allow users to share update with third parties by providing their email"})]}),[2].includes(x)&&s.public_link_id?e.jsxs("div",{className:"p-4 mt-6 rounded-md border border-black/60",children:[e.jsx("p",{className:"text-[16px] font-bold",children:"Shareable link"}),e.jsx("p",{className:"mt-2",children:"Anyone with the shareable link can view this update."}),p?e.jsx($t,{className:"bg-[#F5D9D8]",inputProps:{style:{color:"#CE0000"},disabled:!k(),type:k()?"text":"password"},iconFill:"#CE0000",buttonProps:{style:{color:"#CE0000"},disabled:!M||!k()},link:`${window.location.origin}/update/public/view/${N}/${M}`}):null,e.jsxs("div",{className:"flex gap-3 items-center mt-6",children:[e.jsx("button",{className:"rounded-[.125rem] bg-[#1f1d1a] px-4 py-2 font-iowan font-medium text-white",onClick:()=>q(x),children:"Generate New Shareable Link"}),s.public_link_id&&p?e.jsxs("button",{className:"rounded-[.125rem] border border-[#1f1d1a] px-4 py-2 font-iowan font-medium",onClick:i,children:[" ",ha[k()]]}):null]})]}):null,e.jsx("div",{className:"flex justify-end mt-12",children:e.jsx(Ft,{loading:b,disabled:b,onClick:B,className:"disabled:bg-disabledblack w-full rounded bg-[#1f1d1a] px-6  py-2 text-center font-iowan font-semibold text-white transition-colors duration-100 md:w-auto",children:"Save"})})]});return e.jsxs(e.Fragment,{children:[e.jsxs("button",{disabled:!o,className:"flex h-[28px] w-[28px] items-center justify-center gap-2 rounded-[3px] border-[1px] border-[#1f1d1a] bg-brown-main-bg px-0 font-iowan sm:h-[36px] sm:w-auto sm:justify-start md:px-5",onClick:E,children:[e.jsx(cs,{className:"min-h-[14px] min-w-[14px] text-[14px] md:min-h-[20px] md:min-w-[20px] md:text-[20px]"}),e.jsx("span",{className:"hidden sm:block",children:Xe[s.private_link_access]})]}),e.jsx("div",{className:"hidden md:block",children:e.jsx(es,{modalHeader:!0,isOpen:r,modalCloseClick:H,title:"Update Privacy Settings",classes:{modal:"w-full",modalDialog:"!w-[100%] md:!w-[60%]",modalContent:""},children:ee()})}),e.jsx("div",{className:"md:hidden",children:e.jsx(Te,{isOpen:c,onClose:le,title:"Update Privacy Settings",children:ee()})})]})}function fa({code:s,afterLeave:d}){const[o,r]=t.useState(!1);return t.useEffect(()=>{s&&r(!0)},[s]),e.jsx(e.Fragment,{children:e.jsx(g,{appear:!0,show:o,as:t.Fragment,afterLeave:d,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>r(!1),children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",as:"div",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{}),e.jsx(_.Title,{as:"h3",className:"text-center text-xl font-semibold leading-6 text-gray-900",children:"Sending Update"}),e.jsx("button",{onClick:()=>r(!1),type:"button",children:e.jsx(oe,{className:"h-6 w-6"})})]}),s=="NO_FREE_USES_LEFT"?e.jsx("p",{className:"mt-4",children:"You have exceeded your limit of free reports."}):null,s=="TOO_MANY_INVITEES"?e.jsx("p",{className:"mt-4",children:"With the free plan, you're limited to sending reports to a maximum of 5 Fund managers."}):null,s=="UPDATE_LIMIT_REACHED"?e.jsx("p",{className:"mt-4",children:"You have reached your monthly update limit for your current plan."}):null,s=="TRIAL_EXPIRED"?e.jsx("p",{className:"mt-4",children:"Please Upgrade your account to send an update!"}):null,s=="TOO_MANY_INVITEES"?e.jsx("p",{className:"mt-4 text-center",children:"To send to as many Fund managers as you want, please subscribe to our plans"}):s=="UPDATE_LIMIT_REACHED"?e.jsx("p",{className:"mt-4 text-center",children:"To send more updates, please upgrade your plan"}):s=="TRIAL_EXPIRED"?e.jsx("p",{className:"mt-4 text-center",children:"To send updates, please subscribe to our plans"}):e.jsx("p",{className:"mt-4 text-center",children:"To send plans to Fund managers, please subscribe to our plans"}),e.jsx(Ye,{to:"/member/billing",className:"mt-6 block w-full rounded bg-primary-black px-4 py-2 text-center font-bold text-white",children:"See Plans"})]})})})})]})})})}const pa=()=>e.jsx("svg",{width:"101",height:"100",viewBox:"0 0 101 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-6",children:e.jsx("path",{d:"M69.4414 37.3086C69.8784 37.7441 70.2252 38.2615 70.4617 38.8313C70.6983 39.4011 70.8201 40.012 70.8201 40.6289C70.8201 41.2458 70.6983 41.8567 70.4617 42.4265C70.2252 42.9963 69.8784 43.5137 69.4414 43.9492L47.5664 65.8242C47.1309 66.2612 46.6135 66.608 46.0437 66.8445C45.4739 67.0811 44.8631 67.2029 44.2461 67.2029C43.6292 67.2029 43.0183 67.0811 42.4485 66.8445C41.8787 66.608 41.3613 66.2612 40.9258 65.8242L31.5508 56.4492C31.1148 56.0132 30.7689 55.4955 30.5329 54.9258C30.2969 54.3561 30.1755 53.7455 30.1755 53.1289C30.1755 52.5123 30.2969 51.9017 30.5329 51.332C30.7689 50.7623 31.1148 50.2446 31.5508 49.8086C31.9868 49.3726 32.5045 49.0267 33.0742 48.7907C33.6439 48.5547 34.2545 48.4333 34.8711 48.4333C35.4877 48.4333 36.0984 48.5547 36.6681 48.7907C37.2378 49.0267 37.7554 49.3726 38.1914 49.8086L44.25 55.8594L62.8086 37.2969C63.2447 36.8618 63.7623 36.517 64.3318 36.2821C64.9013 36.0472 65.5114 35.9269 66.1274 35.928C66.7435 35.929 67.3532 36.0515 67.9218 36.2884C68.4905 36.5253 69.0069 36.872 69.4414 37.3086ZM92.6875 50C92.6875 58.3439 90.2133 66.5004 85.5776 73.4381C80.942 80.3758 74.3532 85.7831 66.6445 88.9762C58.9357 92.1692 50.4532 93.0047 42.2696 91.3769C34.0861 89.7491 26.569 85.7311 20.669 79.8311C14.7689 73.931 10.751 66.4139 9.12314 58.2304C7.49533 50.0468 8.33078 41.5643 11.5239 33.8555C14.7169 26.1468 20.1242 19.558 27.0619 14.9224C33.9996 10.2868 42.1561 7.8125 50.5 7.8125C61.685 7.82491 72.4084 12.2736 80.3174 20.1826C88.2264 28.0916 92.6751 38.815 92.6875 50ZM83.3125 50C83.3125 43.5103 81.3881 37.1663 77.7826 31.7704C74.1771 26.3744 69.0525 22.1687 63.0568 19.6852C57.0611 17.2017 50.4636 16.5519 44.0986 17.818C37.7336 19.0841 31.887 22.2091 27.2981 26.7981C22.7092 31.387 19.5841 37.2336 18.318 43.5986C17.0519 49.9636 17.7017 56.5611 20.1852 62.5568C22.6687 68.5525 26.8744 73.6771 32.2704 77.2826C37.6664 80.8881 44.0103 82.8125 50.5 82.8125C59.1996 82.8032 67.5402 79.3432 73.6917 73.1917C79.8432 67.0401 83.3032 58.6996 83.3125 50Z",fill:"#9DD321"})});function ba({report_sent_count:s,afterLeave:d,isScheduled:o,scheduledDateTime:r}){const[l,c]=t.useState(!1),j=et();t.useEffect(()=>{(s||o)&&c(!0)},[s,o]);const p=()=>o?e.jsxs("p",{className:"mb-8 text-base",children:["Your update has been successfully scheduled to send on"," ",e.jsx("span",{className:"font-semibold",children:U(r).format("MMM D, YYYY [at] h:mm A")})]}):e.jsxs("p",{className:"mb-8 text-base",children:["Your update has been successfully sent to ",s," ","recipient(s)"]});return e.jsx(e.Fragment,{children:e.jsx(g,{appear:!0,show:l,as:t.Fragment,afterLeave:d,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>c(!1),children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"w-full max-w-lg transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle shadow-xl transition-all",as:"div",children:[e.jsx("div",{className:"absolute right-6 top-6",children:e.jsx("button",{onClick:()=>c(!1),type:"button",children:e.jsx(oe,{className:"h-6 w-6"})})}),e.jsxs("div",{className:"mt-6 text-center",children:[e.jsx(pa,{}),e.jsx(_.Title,{as:"h3",className:"mb-4 font-iowan text-2xl font-semibold",children:o?"Successfully Scheduled!":"Congrats!"}),p(),e.jsx("button",{onClick:()=>{c(!1),j("/member/dashboard")},className:"w-full rounded-sm bg-[#1f1d1a] px-4 py-2 font-iowan text-base font-semibold text-white hover:bg-[#2a2724]",children:"Return to Dashboard"})]})]})})})})]})})})}function ga({reportDate:s,selectedTemplate:d,ccMyself:o,recipientAccess:r,updateGroups:l,className:c,sendUpdateRef:j=null,UpdateCadence:p=null,refetch:T,nextScheduledUpdate:u,setNextScheduledUpdate:h}){var We,Qe,m;const[b,F]=t.useState(!1),[v,w]=t.useState(!1),[N,M]=t.useState(!1),[A,x]=t.useState(!1),{globalState:f,setGlobalState:L}=ut(),[y,k]=t.useState(!1),{dispatch:i}=t.useContext(ne),{dispatch:W}=t.useContext(ie),[S,B]=t.useState(!1),[q,E]=t.useState(""),{id:H}=Me(),[le,ee]=t.useState(null),[R,a]=t.useState(null),[z,J]=t.useState(null),[xe,Re]=t.useState(!1),[fe,Ue]=t.useState(null),[st,at]=t.useState([]),[ft,Ie]=t.useState(""),[ge,nt]=t.useState(null),[rt,it]=t.useState(!1),[je,Ce]=t.useState(!1),[be,ye]=t.useState(!1),ze=et(),{profile:pe}=tt(),{loading:Y,data:D,processRegisteredDate:pt,getSubscription:bt,getCustomerSubscription:gt,getSentUpdates:jt}=Xt(),re=(m=(Qe=(We=D==null?void 0:D.object)==null?void 0:We.plan)==null?void 0:Qe.nickname)==null?void 0:m.toLowerCase(),ve=re!=null&&re.includes("enterprise")?1/0:re!=null&&re.includes("business")?10:re!=null&&re.includes("pro")?5:0;console.log("subscriptionData",D,"limittt",ve,re),t.useEffect(()=>{(async()=>await _e())(),!N&&!A&&(k(!1),Ie(""))},[N,A]),t.useEffect(()=>{pe!=null&&pe.id&&(gt(),bt({filter:[`user_id,eq,${pe==null?void 0:pe.id}`]}),pt(pe==null?void 0:pe.create_at),jt(pe),f!=null&&f.refreshSubscription&&L("refreshSubscription",!1))},[pe==null?void 0:pe.id,f==null?void 0:f.refreshSubscription]),t.useEffect(()=>{if(D){if(D!=null&&D.trial_expired&&!(D!=null&&D.subscription)){Ce(!0);return}(D==null?void 0:D.sentUpdates)>=ve&&ve!==1/0?Ce(!0):Ce(!1)}},[D==null?void 0:D.sentUpdates,ve,D==null?void 0:D.trial_expired,D==null?void 0:D.subscription]);const _e=async()=>{const n=new ae;try{const I=await n.callRawAPI(`/v3/api/custom/goodbadugly/updates/${H}/scheduled-sends`,[],"GET");if(!I.error&&I.data){const C=I.data.map($=>({...$,scheduled_at:U.utc($.scheduled_date).local().format(),status:$.status}));at(C);const O=C.filter($=>$.status===0);if(O.length>0){const $=O.sort((K,se)=>U(K.scheduled_at).valueOf()-U(se.scheduled_at).valueOf());h($[0])}else h(null);if(C.length>0){const $=[...C].sort((K,se)=>U(se.scheduled_at).valueOf()-U(K.scheduled_at).valueOf());nt($[0].scheduled_at)}}}catch(I){console.error("Error fetching scheduled updates:",I)}},wt=()=>{const n=U().add(1,"day"),I=U().day()===1?U().add(7,"days"):U().day(8);return[{label:"Tomorrow Morning",date:n.format("YYYY-MM-DD"),time:"08:00",displayDate:n.format("MMM D"),displayTime:"8:00 AM"},{label:"Tomorrow Afternoon",date:n.format("YYYY-MM-DD"),time:"13:00",displayDate:n.format("MMM D"),displayTime:"1:00 PM"},{label:"Monday Morning",date:I.format("YYYY-MM-DD"),time:"08:00",displayDate:I.format("MMM D"),displayTime:"8:00 AM"}]},De=n=>{Be(n.date)&&(J(n.date),Ue(n.time),k(!0))},Be=n=>{const I=st.filter(C=>C.status===0);for(const C of I){const O=U(C.scheduled_at),$=U(`${n} ${fe||"00:00"}`,"YYYY-MM-DD HH:mm");if(Math.abs(O.diff($,"hours"))<24)return Ie(`Cannot schedule update. An update is already scheduled for ${O.format("MMM D, YYYY [at] h:mm A")}. Please choose a time at least 24 hours apart.`),!1}return Ie(""),!0},Oe=n=>{Be(n)&&(J(n),k(!0))};t.useEffect(()=>{_e(),p&&E(p.toString())},[p]),t.useEffect(()=>{if(z&&fe){const[n,I]=fe.split(":"),C=U(),O=U(`${z} ${n}:${I}`,"YYYY-MM-DD HH:mm");ye(O.isBefore(C))}},[z,fe]);async function $e(n,I){B(!0);try{const C=new ae;if(!s)throw new Error("Report date is required");if(l.length<=0)throw new Error("Please select a recipient");if(D!=null&&D.trial_expired&&!(D!=null&&D.subscription))throw new Error("TRIAL_EXPIRED");if((D==null?void 0:D.sentUpdates)>=ve&&ve!==1/0)throw new Error("UPDATE_LIMIT_REACHED");if(z&&fe){const O=`${z} ${fe}`,$=U(O,"YYYY-MM-DD HH:mm").utc().format("YYYY-MM-DD HH:mm:ss");console.log("Scheduling for UTC time:",$),(await C.callRawAPI(`/v3/api/custom/goodbadugly/updates/${H}/schedule-sends`,{scheduled_dates:[$]},"POST")).error||(M(!1),x(!1),Re(!0),P(W,"Update scheduled successfully",3e3),await _e())}else{const O={date:s,template_id:d||"Custom",cc_myself:o?1:0,recipient_access:r,cadence:n},$=await C.callRawAPI(`/v3/api/custom/goodbadugly/updates/${H}/send-report`,O,"POST");a($.report_sent_count)}F(!1),w(!1),T()}catch(C){X(i,C.message),["TOO_MANY_INVITEES","NO_FREE_USES_LEFT","UPDATE_LIMIT_REACHED","TRIAL_EXPIRED"].includes(C.message)?ee(C.message):P(W,C.message,5e3,"error")}B(!1)}const lt=({selectedDate:n,onDateSelect:I})=>{const[C,O]=t.useState(U()),$=C.daysInMonth(),K=U(C).startOf("month").day(),se=[];let Z=[];for(let te=0;te<K;te++)Z.push(e.jsx("td",{className:"p-2"},`empty-${te}`));for(let te=1;te<=$;te++){const he=U(C).date(te),we=n===he.format("YYYY-MM-DD"),Se=he.isSame(U(),"day"),de=he.isBefore(U(),"day");Z.push(e.jsx("td",{className:"p-1",children:e.jsx("button",{disabled:de,onClick:()=>I(he.format("YYYY-MM-DD")),className:`flex h-8 w-8 items-center justify-center rounded-full transition-colors
              ${de?"text-gray-300 cursor-not-allowed":"hover:bg-gray-100"}
              ${we?"bg-[#1f1d1a] text-white hover:bg-[#1f1d1a]":""}
              ${Se?"border border-[#1f1d1a]":""}`,children:te})},te)),Z.length===7&&(se.push(e.jsx("tr",{children:Z},`week-${se.length}`)),Z=[])}return Z.length>0&&se.push(e.jsx("tr",{children:Z},`week-${se.length}`)),e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("button",{onClick:()=>O(U(C).subtract(1,"month")),className:"p-1 rounded-full hover:bg-gray-100",children:e.jsx(qs,{className:"w-5 h-5"})}),e.jsx("h2",{className:"font-semibold font-iowan",children:C.format("MMMM YYYY")}),e.jsx("button",{onClick:()=>O(U(C).add(1,"month")),className:"p-1 rounded-full font-iowan hover:bg-gray-100",children:e.jsx(Bs,{className:"w-5 h-5"})})]}),e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsx("tr",{children:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map(te=>e.jsx("th",{className:"p-2 text-xs font-medium",children:te},te))})}),e.jsx("tbody",{children:se})]})]})},ke=({selectedTime:n,onTimeSelect:I})=>{const[C,O]=t.useState(n?n.split(":")[0]:""),[$,K]=t.useState(n?n.split(":")[1]:""),[se,Z]=t.useState("AM"),[te,he]=t.useState(""),we=(de,ce)=>{const ue=parseInt(de),me=parseInt(ce);return isNaN(ue)||ue<1||ue>12?(he("Hour must be between 1 and 12"),!1):isNaN(me)||me<0||me>59?(he("Minutes must be between 0 and 59"),!1):(he(""),!0)},Se=(de,ce,ue)=>{if(de===""||ce===""){O(de),K(ce);return}if(de&&ce&&we(de,ce)){let me=parseInt(de);ue==="PM"&&me!==12?me+=12:ue==="AM"&&me===12&&(me=0);const kt=me.toString().padStart(2,"0"),Pt=ce.toString().padStart(2,"0");I(`${kt}:${Pt}`)}};return t.useEffect(()=>{if(n){const[de,ce]=n.split(":"),ue=parseInt(de);let me=ue%12;me===0&&(me=12),O(me.toString()),K(ce),Z(ue>=12?"PM":"AM")}},[n]),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsxs("div",{className:"flex gap-2 justify-start items-center",children:[e.jsxs("div",{className:"flex gap-1 items-center",children:[e.jsx("input",{type:"text",value:C,onChange:de=>{const ce=de.target.value.replace(/\D/g,"");O(ce),Se(ce,$,se)},placeholder:"HH",className:"w-12 rounded border border-[#1f1d1a] bg-transparent px-2 py-1 text-center font-iowan",maxLength:2}),e.jsx("span",{className:"font-iowan",children:":"}),e.jsx("input",{type:"text",value:$,onChange:de=>{const ce=de.target.value.replace(/\D/g,"");K(ce),Se(C,ce,se)},placeholder:"MM",className:"w-12 rounded border border-[#1f1d1a] bg-transparent px-2 py-1 text-center font-iowan",maxLength:2})]}),e.jsxs("div",{className:"flex rounded-full border border-[#1f1d1a] p-1",children:[e.jsx("button",{onClick:()=>{Z("AM"),Se(C,$,"AM")},className:`rounded-full px-3 py-1 text-sm transition-colors ${se==="AM"?"bg-[#1f1d1a] text-white":"text-[#1f1d1a] hover:bg-gray-100"}`,children:"AM"}),e.jsx("button",{onClick:()=>{Z("PM"),Se(C,$,"PM")},className:`rounded-full px-3 py-1 text-sm transition-colors ${se==="PM"?"bg-[#1f1d1a] text-white":"text-[#1f1d1a] hover:bg-gray-100"}`,children:"PM"})]})]}),te&&e.jsx("div",{className:"text-sm text-red-500",children:te})]})},ot=wt(),ct=({showTimeSelect:n,lastScheduledTime:I,quickSelectOptions:C,handleQuickSelect:O,setShowTimeSelect:$,selectedDate:K,Calendar:se,handleDateSelect:Z,dateError:te,TimePicker:he,selectedTime:we,setSelectedTime:Se,sendReport:de,globalDispatch:ce})=>e.jsx("div",{className:"flex flex-col",children:n?e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx(se,{selectedDate:K,onDateSelect:Z}),te&&e.jsx("div",{className:"mt-2 text-sm text-red-500",children:te})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex flex-col gap-2 justify-start p-2 rounded",children:[e.jsx("label",{className:"font-iowan-regular text-[18px]",htmlFor:"time",children:"Time:"}),e.jsx(he,{selectedTime:we,onTimeSelect:Se})]}),K&&we&&e.jsxs(e.Fragment,{children:[be&&e.jsx("div",{className:"mt-2 text-sm text-red-500",children:"Please select a time in the future"}),e.jsx("button",{onClick:()=>{const[ue,me]=we.split(":");if(ue==="00"&&me==="00"){P(ce,"Please select a valid time",3e3,"error");return}if(K<U().format("YYYY-MM-DD")){P(ce,"Please select a date in the future",3e3,"error");return}if(K===U().format("YYYY-MM-DD")){const kt=U();if(U(`${K} ${ue}:${me}`,"YYYY-MM-DD HH:mm").isBefore(kt)){P(ce,"Please select a time in the future",3e3,"error");return}}de()},disabled:be,className:"mt-4 w-full rounded bg-[#1f1d1a] px-4 py-2 text-white hover:bg-[#1f1d1a]/90 disabled:cursor-not-allowed disabled:opacity-50",children:"Save"})]})]})]}):e.jsxs("div",{className:"p-4",children:[I&&e.jsxs("div",{className:"flex justify-between items-center p-3 mb-2 font-semibold border-b border-gray-200",children:[e.jsx("span",{children:"Last scheduled time"}),e.jsx("span",{children:U(I).format("ddd, MMM D, h:mm A")})]}),e.jsx("div",{className:"mb-4",children:C.map((ue,me)=>e.jsxs("div",{onClick:()=>O(ue),className:"flex justify-between items-center p-3 border-b border-gray-200 cursor-pointer hover:bg-gray-100",children:[e.jsx("span",{children:ue.label}),e.jsxs("span",{children:[ue.displayDate,", ",ue.displayTime]})]},me))}),e.jsxs("button",{onClick:()=>$(!0),className:"flex w-full items-center justify-between rounded border border-[#1f1d1a] px-4 py-3 hover:bg-[#1f1d1a]/5",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(zs,{className:"mr-2 w-5 h-5"}),e.jsx("span",{children:"Pick date & time"})]}),e.jsx("svg",{className:"w-5 h-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]})]})}),He=({cadenceDuration:n,setCadenceDuration:I,UpdateCadence:C,sending:O,sendReport:$})=>e.jsxs("div",{className:"flex flex-col p-0",children:[e.jsxs("div",{className:"gap-2 items-center mb-6 md:hidden",children:[e.jsx("h3",{className:"font-iowan text-xl font-bold text-[#1f1d1a]   md:text-lg",children:"Create a scheduled cadence notification for this update template"}),e.jsx(V,{className:"inline relative",children:({open:K})=>e.jsxs(e.Fragment,{children:[e.jsx(V.Button,{className:"ml-2",children:e.jsx(Ne,{className:"w-5 h-5"})}),e.jsx(g,{as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(V.Panel,{className:"absolute left-0 z-10 mt-3 w-screen max-w-[250px] translate-x-[0%] transform px-4 text-sm  sm:max-w-xl md:-translate-x-1/2",children:e.jsxs("div",{className:"relative rounded-lg bg-[#1f1d1a] px-6 py-4 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:[e.jsxs("div",{className:"font-medium",children:[e.jsx(Ne,{className:"inline mr-2 w-5 h-5"})," ","You can create a scheduled cadence notification (ie. weekly, bi-weekly, monthly, quarterly, bi-annually, annually, etc), using this update template. You will be notified 24hrs prior to each update so you can continue to edit prior to sending."]}),e.jsx("div",{className:"absolute left-1/2 top-0 h-3 w-3 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-[#1f1d1a]"})]})})})]})})]}),e.jsxs("div",{className:"flex flex-col gap-4 mt-4 md:flex-row md:items-center",children:[e.jsx("p",{className:"font-medium whitespace-nowrap",children:"Select cadence notification"}),e.jsxs("select",{value:n,onChange:K=>I(K.target.value),className:"focus:shadow-outline h-[40px] w-full appearance-none rounded border border-black bg-transparent py-2 pl-6 pr-8 font-iowan text-[16px] leading-tight text-[#1f1d1a] focus:outline-none",children:[e.jsx("option",{value:"",children:"Select a cadence..."}),e.jsx("option",{value:"7",children:"Weekly"}),e.jsx("option",{value:"14",children:"Bi-weekly"}),e.jsx("option",{value:"30",children:"Monthly"}),e.jsx("option",{value:"120",children:"Quarterly"}),e.jsx("option",{value:"365",children:"Annually"}),e.jsx("option",{value:"730",children:"Bi-annually"})]})]}),C&&e.jsxs("div",{className:"mt-4 text-sm text-gray-600",children:["Current cadence: ",C?`${C} days`:""]}),e.jsx("div",{className:"flex flex-col gap-4 mt-5 md:mt-8 md:flex-row",children:O?e.jsx(Cs,{size:18}):e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"flex h-[40px] w-full items-center justify-center rounded border border-[#1f1d1a] py-3 text-center font-iowan text-[16px] text-[#1f1d1a]",type:"button",onClick:()=>$(n,!1),disabled:O,children:"No, Continue to send without"}),e.jsx("button",{onClick:()=>$(n,!0),disabled:O,className:"flex h-[40px] w-full items-center justify-center rounded border bg-[#1f1d1a] py-3 text-center font-iowan text-[16px] text-brown-main-bg",children:"Select cadence and Send"})]})})]}),yt=t.useCallback(n=>{if(n&&n.stopPropagation(),je)return ze("/member/billing?openManagePlan=true");window.innerWidth<768?w(!0):F(!0)},[je,ze]),Ze=t.useCallback(n=>{n&&n.stopPropagation(),F(!1)},[]),vt=t.useCallback(n=>{n&&n.stopPropagation(),w(!1)},[]),Nt=t.useCallback(n=>{if(n&&n.stopPropagation(),je)return ze("/member/billing?openManagePlan=true");window.innerWidth<768?x(!0):M(!0)},[je,ze]),Ve=t.useCallback(n=>{n&&n.stopPropagation(),M(!1)},[]),Ct=t.useCallback(n=>{n&&n.stopPropagation(),x(!1)},[]),_t=async()=>{if(!u)return;it(!0);const n=new ae;try{await n.callRawAPI(`/v3/api/custom/goodbadugly/scheduled-sends/${u.id}`,{},"DELETE"),P(W,"Scheduled update cancelled successfully",3e3),await _e()}catch(I){X(i,I.message),P(W,"Failed to cancel scheduled update",5e3,"error")}it(!1)};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex relative flex-col justify-center items-center w-full sm:w-auto",children:[e.jsx("div",{className:"absolute top-0",children:e.jsx(Tt,{children:e.jsx(Mt,{display:e.jsx("button",{className:"p-0 m-0 w-full h-0",ref:j}),openOnClick:!0,backgroundColor:"#1f1d1a",children:e.jsx("span",{className:"text-white",children:"Click to Send Update"})})})}),e.jsx(Mt,{display:e.jsx("button",{className:`focus:shadow-outline ml-2 flex h-[2.75rem] !w-[calc(100%_-_8px)] items-center justify-center whitespace-nowrap rounded-[2px] bg-[#1f1d1a] px-6 py-2 font-iowan text-base font-semibold text-white focus:outline-none sm:w-fit md:!w-full ${c} ${je?"opacity-75":""}`,onClick:yt,children:je?D!=null&&D.trial_expired&&!(D!=null&&D.subscription)?"Subscribe to Send":"Upgrade to Send":"Send Update"}),openOnClick:!1,place:"top",tooltipClasses:"!bg-transparent !shadow-none !-top-[75px]",backgroundColor:"transparent",className:"w-full",children:e.jsx("div",{className:"flex flex-col gap-2 p-2",children:u?e.jsx("button",{onClick:_t,disabled:rt,className:"flex h-[3.5rem] items-center justify-center gap-2 rounded-[.25rem] border border-red-500 bg-brown-main-bg p-[1rem] px-4 py-2 transition duration-200 hover:bg-red-50",children:rt?e.jsx(Ns,{size:20,color:"#EF4444"}):e.jsxs(e.Fragment,{children:[e.jsx(oe,{className:"w-5 h-5 text-red-500"}),e.jsxs("span",{className:"text-[12px] font-medium text-red-500",children:["Cancel Scheduled update (",U(u.scheduled_at).format("MMM D, h:mm A"),")"]})]})}):e.jsxs("button",{onClick:Nt,className:`flex h-[3.5rem] items-center justify-center gap-2 rounded-[.25rem] border border-primary-black bg-brown-main-bg p-[1rem] px-4 py-2 transition duration-200 ${je?"opacity-75":""}`,children:[e.jsx(ts,{}),e.jsx("span",{className:"font-medium text-black",children:je?D!=null&&D.trial_expired&&!(D!=null&&D.subscription)?"Subscribe to Schedule":"Upgrade to Schedule":"Schedule send"})]})})})]}),e.jsx("div",{className:"hidden md:block",children:e.jsx(g,{appear:!0,show:N,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50]",onClose:Ve,children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"overflow-y-auto fixed inset-0",children:e.jsx("div",{className:"flex justify-center items-center p-4 min-h-full text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-lg border border-[#1f1d1a] bg-brown-main-bg text-left align-middle shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between border-b border-[#1f1d1a] px-4 py-3",children:[e.jsx(_.Title,{className:"text-lg font-semibold font-iowan",children:y?"Pick date & time":"Schedule Send"}),e.jsx("button",{onClick:Ve,className:"rounded-full p-1 hover:bg-[#1f1d1a]/10",children:e.jsx(oe,{className:"w-5 h-5"})})]}),ct({showTimeSelect:y,lastScheduledTime:ge,quickSelectOptions:ot,handleQuickSelect:De,setShowTimeSelect:k,selectedDate:z,Calendar:lt,handleDateSelect:Oe,dateError:ft,TimePicker:ke,selectedTime:fe,setSelectedTime:Ue,sendReport:$e,globalDispatch:W})]})})})})]})})}),e.jsx("div",{className:"md:hidden",children:e.jsx(Te,{isOpen:A,onClose:Ct,title:y?"Pick date & time":"Schedule Send",children:ct({showTimeSelect:y,lastScheduledTime:ge,quickSelectOptions:ot,handleQuickSelect:De,setShowTimeSelect:k,selectedDate:z,Calendar:lt,handleDateSelect:Oe,dateError:ft,TimePicker:ke,selectedTime:fe,setSelectedTime:Ue,sendReport:$e,globalDispatch:W})})}),e.jsx("div",{className:"hidden md:block",children:e.jsx(g,{appear:!0,show:b,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50]",onClose:Ze,children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"overflow-y-auto fixed inset-0",children:e.jsx("div",{className:"flex justify-center items-center p-4 min-h-full text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"w-[600px]  transform rounded border-[1px] border-[#1f1d1a] bg-brown-main-bg p-2 px-8 pb-7 pt-5 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs(_.Title,{as:"h3",className:" pt-4 font-iowan text-[20px] font-bold text-[#1f1d1a] md:text-xl",children:["Create a scheduled cadence notification for this update template?",e.jsx(V,{className:"inline relative",children:({open:n})=>e.jsxs(e.Fragment,{children:[e.jsx(V.Button,{className:"ml-2",children:e.jsx(Ne,{className:"w-5 h-5"})}),e.jsx(g,{as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(V.Panel,{className:"absolute left-0 z-10 mt-3 w-screen max-w-[250px] -translate-x-1/2 transform px-4  text-sm sm:max-w-xl",children:e.jsxs("div",{className:"relative rounded-lg bg-[#1f1d1a] px-6 py-4 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:[e.jsxs("div",{className:"font-medium",children:[e.jsx(Ne,{className:"inline mr-2 w-5 h-5"})," ","You can create a scheduled cadence notification (ie. weekly, bi-weekly, monthly, quarterly, bi-annually, annually, etc), using this update template. You will be notified 24hrs prior to each update so you can continue to edit prior to sending."]}),e.jsx("div",{className:"absolute left-1/2 top-0 h-3 w-3 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-[#1f1d1a]"})]})})})]})})]}),He({cadenceDuration:q,setCadenceDuration:E,UpdateCadence:p,sending:S,sendReport:$e})]})})})})]})})}),e.jsx("div",{className:"md:hidden",children:e.jsx(Te,{isOpen:v,onClose:vt,title:"",children:He({cadenceDuration:q,setCadenceDuration:E,UpdateCadence:p,sending:S,sendReport:$e})})}),e.jsx(fa,{code:le,afterLeave:()=>ee(null)}),e.jsx(ba,{report_sent_count:R,afterLeave:()=>{a(null),Re(!1)},isScheduled:xe,scheduledDateTime:z&&fe?`${z} ${fe}`:null})]})}function ja({update:s,afterEdit:d}){var F,v,w,N,M,A,x,f,L,y;const[o,r]=t.useState(!1),{dispatch:l}=t.useContext(ne),{dispatch:c}=t.useContext(ie),j=Le({cac:Q().required("This field is required"),marketing_activations:Q().required("This field is required"),churn_rate:Q().required("This field is required"),acv:Q().required("This field is required"),csat:Q().required("This field is required")}),{register:p,handleSubmit:T,formState:{isSubmitting:u,errors:h}}=Ae({resolver:Fe(j),defaultValues:{cac:s.cac,marketing_activations:s.marketing_activations,churn_rate:s.churn_rate,acv:s.acv,csat:s.csat}});async function b(k){try{await new ae().callRawAPI(`/v4/api/records/updates/${s.id}`,{cac:k.cac,marketing_activations:k.marketing_activations,churn_rate:k.churn_rate,acv:k.acv,csat:k.csat},"PUT"),r(!1),d()}catch(i){X(l,i.message),P(c,i.message,5e3,"error")}}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded-md bg-primary-black p-2 font-iowan font-medium text-white",onClick:()=>r(!0),children:"Edit manually"}),e.jsx(g,{appear:!0,show:o,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>r(!1),children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{as:"form",className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",onSubmit:T(b),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Edit update"}),e.jsx("button",{onClick:()=>r(!1),type:"button",children:e.jsx(oe,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Customer Acquisition Cost (CAC)"}),e.jsx("input",{type:"text",...p("cac"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(F=h.cac)!=null&&F.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(v=h.cac)==null?void 0:v.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Activations / Daily active users"}),e.jsx("input",{type:"text",...p("marketing_activations"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(w=h.marketing_activations)!=null&&w.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(N=h.marketing_activations)==null?void 0:N.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Retention / Churn rate (Monthly)"}),e.jsx("input",{type:"text",...p("churn_rate"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(M=h.churn_rate)!=null&&M.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(A=h.churn_rate)==null?void 0:A.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Annual contract value (ACV)"}),e.jsx("input",{type:"text",...p("acv"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(x=h.acv)!=null&&x.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(f=h.acv)==null?void 0:f.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Customer Satisfaction Score (CSAT/NPS)"}),e.jsx("input",{type:"text",...p("csat"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(L=h.csat)!=null&&L.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(y=h.csat)==null?void 0:y.message})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>r(!1),children:"Cancel"}),e.jsx(Ee,{loading:u,disabled:u,type:"submit",className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})]})]})})})})]})})]})}const Je=({children:s,last:d=!1})=>{const[o,r]=t.useState(!1),l=t.useRef(null),c=()=>{l.current&&clearTimeout(l.current),r(!0)},j=()=>{l.current=setTimeout(()=>{r(!1)},100)};return e.jsx(V,{className:"relative inline",children:e.jsxs("div",{onMouseEnter:c,onMouseLeave:j,children:[e.jsx(V.Button,{className:"ml-0",children:e.jsx(Ne,{className:"h-4 w-4 cursor-pointer",pathClasses:"text-[#1f1d1a]",stroke:"white"})}),o&&e.jsx(g,{show:!0,as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(V.Panel,{static:!0,className:`absolute left-0 z-10 mt-3 w-screen max-w-[200px] -translate-x-[35%] transform px-4 text-sm ${d?"top-0":"top-full"}`,onMouseEnter:c,onMouseLeave:j,children:e.jsxs("div",{className:"relative rounded-lg bg-[#1f1d1a] px-4 py-3 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:[e.jsx("div",{className:"font-medium",children:s}),e.jsx("div",{className:"absolute left-1/2 top-0 h-3 w-3 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-[#1f1d1a]"})]})})})]})})};function wa({update:s,refetchUpdate:d,showMetric:o}){const[r,l]=t.useState(!1);t.useContext(ne),t.useContext(ie),t.useState(!1);const[c,j]=t.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsxs("button",{className:"flex w-full items-center justify-between text-[16px] font-medium text-[#1f1d1a] md:text-[18px] ",onClick:()=>l(o),children:["Show marketing metrics"," ",o?e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.5 18.3333H2.5C2.15833 18.3333 1.875 18.05 1.875 17.7083C1.875 17.3667 2.15833 17.0833 2.5 17.0833H17.5C17.8417 17.0833 18.125 17.3667 18.125 17.7083C18.125 18.05 17.8417 18.3333 17.5 18.3333Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M15.8495 2.89999C14.2328 1.28332 12.6495 1.24166 10.9912 2.89999L9.98283 3.90832C9.89949 3.99166 9.86616 4.12499 9.89949 4.24166C10.5328 6.44999 12.2995 8.21666 14.5078 8.84999C14.5412 8.85832 14.5745 8.86666 14.6078 8.86666C14.6995 8.86666 14.7828 8.83332 14.8495 8.76666L15.8495 7.75832C16.6745 6.94166 17.0745 6.14999 17.0745 5.34999C17.0828 4.52499 16.6828 3.72499 15.8495 2.89999Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M13.0089 9.60832C12.7673 9.49166 12.5339 9.37499 12.3089 9.24166C12.1256 9.13332 11.9506 9.01666 11.7756 8.89166C11.6339 8.79999 11.4673 8.66666 11.3089 8.53332C11.2923 8.52499 11.2339 8.47499 11.1673 8.40832C10.8923 8.17499 10.5839 7.87499 10.3089 7.54166C10.2839 7.52499 10.2423 7.46666 10.1839 7.39166C10.1006 7.29166 9.95892 7.12499 9.83392 6.93332C9.73392 6.80832 9.61726 6.62499 9.50892 6.44166C9.37559 6.21666 9.25892 5.99166 9.14226 5.75832C9.1246 5.72049 9.10752 5.68286 9.09096 5.64544C8.96798 5.36767 8.60578 5.28647 8.39098 5.50126L3.61726 10.275C3.50892 10.3833 3.40892 10.5917 3.38392 10.7333L2.93392 13.925C2.85059 14.4917 3.00892 15.025 3.35892 15.3833C3.65892 15.675 4.07559 15.8333 4.52559 15.8333C4.62559 15.8333 4.72559 15.825 4.82559 15.8083L8.02559 15.3583C8.17559 15.3333 8.38392 15.2333 8.48392 15.125L13.2517 10.3572C13.468 10.1409 13.3864 9.76972 13.105 9.64967C13.0734 9.63615 13.0414 9.62238 13.0089 9.60832Z",fill:"#1F1D1A"})]}):!1]}),e.jsx(g,{appear:!0,show:r,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>l(!1),children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Marketing Metrics"}),e.jsx("button",{onClick:()=>l(!1),type:"button",children:e.jsx(oe,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex flex-row items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","Customer Acquisition Cost (CAC)",e.jsx(Je,{children:"The average cost a business incurs to acquire a new customer."})]}),e.jsx("p",{children:s.cac})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex flex-row items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","Activations / Daily active users",e.jsx(Je,{children:"Number of new users who become active and engage with your product daily."})]}),e.jsx("p",{children:s.marketing_activations})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex flex-row items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","Retention / Churn rate (Monthly)",e.jsx(Je,{children:"Percentage of customers who stay (retention) vs those who leave (churn) each month."})]}),e.jsx("p",{children:s.churn_rate})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex flex-row items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","Annual contract value (ACV)",e.jsx(Je,{children:"Average yearly revenue generated from each customer contract."})]}),e.jsx("p",{children:s.acv})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex flex-row items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","Customer Satisfaction Score (CSAT/NPS)",e.jsx(Je,{last:!0,children:"Metrics measuring customer satisfaction and likelihood to recommend your product."})]}),e.jsx("p",{children:s.csat})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:s.id?e.jsx(ja,{update:s,afterEdit:d}):null}),c?e.jsxs("div",{className:"mt-4 border border-black/60 bg-brown-main-bg p-3",children:[e.jsxs("p",{className:"flex items-center gap-3 font-semibold",children:[e.jsx(ht,{className:"h-5 text-yellow-500",strokeWidth:2}),"No integrations setup currently."]}),e.jsxs("p",{className:"mt-1",children:["In order to populate financial information in your updates, you must setup integrations with your current financial services"," ",e.jsx(Ye,{to:"/member/integrations",className:"font-semibold text-primary-black underline",children:"here"})]})]}):null]})]})})})})]})})]})}function ya({update:s,afterEdit:d}){var F,v,w,N,M,A,x,f,L,y;const[o,r]=t.useState(!1),{dispatch:l}=t.useContext(ne),{dispatch:c}=t.useContext(ie),j=Le({headcount:Q().required("This field is required"),turnover:Q().required("This field is required"),retention_hr:Q().required("This field is required"),satisfaction:Q().required("This field is required"),revenue_per_employee:Q().required("This field is required")}),{register:p,handleSubmit:T,formState:{isSubmitting:u,errors:h}}=Ae({resolver:Fe(j),defaultValues:{headcount:s.headcount,turnover:s.turnover,retention_hr:s.retention_hr,satisfaction:s.satisfaction,revenue_per_employee:s.revenue_per_employee}});async function b(k){try{await new ae().callRawAPI(`/v4/api/records/updates/${s.id}`,{headcount:k.headcount,turnover:k.turnover,retention_hr:k.retention_hr,satisfaction:k.satisfaction,revenue_per_employee:k.revenue_per_employee},"PUT"),r(!1),d()}catch(i){X(l,i.message),P(c,i.message,5e3,"error")}}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded-md bg-primary-black p-2 font-iowan font-medium text-white",onClick:()=>r(!0),children:"Edit manually"}),e.jsx(g,{appear:!0,show:o,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>r(!1),children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{as:"form",className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",onSubmit:T(b),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Edit update"}),e.jsx("button",{onClick:()=>r(!1),type:"button",children:e.jsx(oe,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Headcount"}),e.jsx("input",{type:"text",...p("headcount"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(F=h.headcount)!=null&&F.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(v=h.headcount)==null?void 0:v.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Turnover Rate (annual)"}),e.jsx("input",{type:"text",...p("turnover"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(w=h.turnover)!=null&&w.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(N=h.turnover)==null?void 0:N.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Retention Rate (annual)"}),e.jsx("input",{type:"text",...p("retention_hr"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(M=h.retention_hr)!=null&&M.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(A=h.retention_hr)==null?void 0:A.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Employee Satisfaction (employee NPS)"}),e.jsx("input",{type:"text",...p("satisfaction"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(x=h.satisfaction)!=null&&x.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(f=h.satisfaction)==null?void 0:f.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Revenue per Employee"}),e.jsx("input",{type:"text",...p("revenue_per_employee"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(L=h.revenue_per_employee)!=null&&L.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(y=h.revenue_per_employee)==null?void 0:y.message})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>r(!1),children:"Cancel"}),e.jsx(Ee,{loading:u,disabled:u,type:"submit",className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})]})]})})})})]})})]})}function va({update:s,afterEdit:d}){var F,v,w,N,M,A,x,f,L,y;const[o,r]=t.useState(!1),{dispatch:l}=t.useContext(ne),{dispatch:c}=t.useContext(ie),j=Le({new_product_release:Q().required("This field is required"),new_product_sales:Q().required("This field is required"),roi:Q().required("This field is required"),rd:Q().required("This field is required"),on_time_delivery:Q().required("This field is required")}),{register:p,handleSubmit:T,formState:{isSubmitting:u,errors:h}}=Ae({resolver:Fe(j),defaultValues:{nps:s.new_product_sales,npr:s.new_product_release,roi:s.roi,rd:s.rd,on_time_delivery:s.on_time_deliverye}});async function b(k){console.log(k);try{await new ae().callRawAPI(`/v4/api/records/updates/${s.id}`,{nps:k.new_product_release,npr:k.new_product_sales,roi:k.roi,rd:k.rd,on_time_delivery:k.on_time_delivery},"PUT"),r(!1),d()}catch(i){X(l,i.message),P(c,i.message,5e3,"error")}}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"rounded-md bg-primary-black p-2 font-iowan font-medium text-white",onClick:()=>r(!0),children:"Edit manually"}),e.jsx(g,{appear:!0,show:o,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>r(!1),children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{as:"form",className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",onSubmit:T(b),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_.Title,{as:"h3",className:"text-[16px] font-semibold leading-6 text-[#1f1d1a] md:text-xl",children:"Edit update"}),e.jsx("button",{onClick:()=>r(!1),type:"button",children:e.jsx(oe,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"New Product Releases"}),e.jsx("input",{type:"text",...p("new_product_release"),className:`focus:shadow-outline  h-[41.6px] w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 font-Inter    text-sm font-normal leading-tight text-[#1d1f1a] shadow focus:outline-none ${(F=h.new_product_release)!=null&&F.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(v=h.new_product_release)==null?void 0:v.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"New Product Sales"}),e.jsx("input",{type:"text",...p("new_product_sales"),className:`focus:shadow-outline  h-[41.6px] w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 font-Inter    text-sm font-normal leading-tight text-[#1d1f1a] shadow focus:outline-none ${(w=h.new_product_sales)!=null&&w.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(N=h.new_product_sales)==null?void 0:N.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"ROI"}),e.jsx("input",{type:"text",...p("roi"),className:`focus:shadow-outline  h-[41.6px] w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 font-Inter    text-sm font-normal leading-tight text-[#1d1f1a] shadow focus:outline-none ${(M=h.roi)!=null&&M.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(A=h.roi)==null?void 0:A.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"R&D (as a % of sales)"}),e.jsx("input",{type:"text",...p("rd"),className:`focus:shadow-outline  h-[41.6px] w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 font-Inter    text-sm font-normal leading-tight text-[#1d1f1a] shadow focus:outline-none ${(x=h.rd)!=null&&x.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(f=h.rd)==null?void 0:f.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"On-time delivery"}),e.jsx("input",{type:"text",...p("on_time_delivery"),className:`focus:shadow-outline  h-[41.6px] w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 font-Inter    text-sm font-normal leading-tight text-[#1d1f1a] shadow focus:outline-none ${(L=h.on_time_delivery)!=null&&L.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(y=h.on_time_delivery)==null?void 0:y.message})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>r(!1),children:"Cancel"}),e.jsx(Ee,{loading:u,disabled:u,type:"submit",className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Save"})]})]})})})})]})})]})}const Ke=({children:s})=>{const[d,o]=t.useState(!1),r=t.useRef(null),l=()=>{r.current&&clearTimeout(r.current),o(!0)},c=()=>{r.current=setTimeout(()=>{o(!1)},100)};return e.jsx(V,{className:"relative inline",children:e.jsxs("div",{onMouseEnter:l,onMouseLeave:c,children:[e.jsx(V.Button,{className:"ml-0",children:e.jsx(Ne,{className:"mt-[3px] h-4 w-4 cursor-pointer",pathClasses:"text-[#1f1d1a]",stroke:"white"})}),d&&e.jsx(g,{show:!0,as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(V.Panel,{static:!0,className:"absolute left-0 z-10 mt-3 w-screen max-w-[200px] -translate-x-[30%] transform px-4 text-sm",onMouseEnter:l,onMouseLeave:c,children:e.jsxs("div",{className:"relative rounded-lg bg-[#1f1d1a] px-4 py-3 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:[e.jsx("div",{className:"font-medium",children:s}),e.jsx("div",{className:"absolute left-1/2 top-0 h-3 w-3 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-[#1f1d1a]"})]})})})]})})};function Na({update:s,refetchUpdate:d,showMetric:o}){const[r,l]=t.useState(!1);t.useContext(ne),t.useContext(ie),t.useState(!1);const[c,j]=t.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsxs("button",{className:"flex w-full items-center justify-between text-[16px] font-medium text-[#1f1d1a] md:text-[18px] ",onClick:()=>l(o),children:[e.jsx("span",{children:"Show product metrics"})," ",o?e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.5 18.3333H2.5C2.15833 18.3333 1.875 18.05 1.875 17.7083C1.875 17.3667 2.15833 17.0833 2.5 17.0833H17.5C17.8417 17.0833 18.125 17.3667 18.125 17.7083C18.125 18.05 17.8417 18.3333 17.5 18.3333Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M15.8495 2.89999C14.2328 1.28332 12.6495 1.24166 10.9912 2.89999L9.98283 3.90832C9.89949 3.99166 9.86616 4.12499 9.89949 4.24166C10.5328 6.44999 12.2995 8.21666 14.5078 8.84999C14.5412 8.85832 14.5745 8.86666 14.6078 8.86666C14.6995 8.86666 14.7828 8.83332 14.8495 8.76666L15.8495 7.75832C16.6745 6.94166 17.0745 6.14999 17.0745 5.34999C17.0828 4.52499 16.6828 3.72499 15.8495 2.89999Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M13.0089 9.60832C12.7673 9.49166 12.5339 9.37499 12.3089 9.24166C12.1256 9.13332 11.9506 9.01666 11.7756 8.89166C11.6339 8.79999 11.4673 8.66666 11.3089 8.53332C11.2923 8.52499 11.2339 8.47499 11.1673 8.40832C10.8923 8.17499 10.5839 7.87499 10.3089 7.54166C10.2839 7.52499 10.2423 7.46666 10.1839 7.39166C10.1006 7.29166 9.95892 7.12499 9.83392 6.93332C9.73392 6.80832 9.61726 6.62499 9.50892 6.44166C9.37559 6.21666 9.25892 5.99166 9.14226 5.75832C9.1246 5.72049 9.10752 5.68286 9.09096 5.64544C8.96798 5.36767 8.60578 5.28647 8.39098 5.50126L3.61726 10.275C3.50892 10.3833 3.40892 10.5917 3.38392 10.7333L2.93392 13.925C2.85059 14.4917 3.00892 15.025 3.35892 15.3833C3.65892 15.675 4.07559 15.8333 4.52559 15.8333C4.62559 15.8333 4.72559 15.825 4.82559 15.8083L8.02559 15.3583C8.17559 15.3333 8.38392 15.2333 8.48392 15.125L13.2517 10.3572C13.468 10.1409 13.3864 9.76972 13.105 9.64967C13.0734 9.63615 13.0414 9.62238 13.0089 9.60832Z",fill:"#1F1D1A"})]}):!1]}),e.jsx(g,{appear:!0,show:r,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>l(!1),children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Product Metrics"}),e.jsx("button",{onClick:()=>l(!1),type:"button",children:e.jsx(oe,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","New Product Releases",e.jsx(Ke,{children:"Number of new product versions, features, or updates released during this period."})]}),e.jsx("p",{children:s.npr})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","New Product Sales",e.jsx(Ke,{children:"Total number of new product sales or subscriptions acquired in this period."})]}),e.jsx("p",{children:s.nps})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","ROI",e.jsx(Ke,{children:"Return on Investment - Measures the profitability of product investments relative to their costs."})]}),e.jsx("p",{children:s.roi})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","R&D (as a % of sales)",e.jsx(Ke,{children:"Research and Development expenses as a percentage of total sales, indicating investment in innovation."})]}),e.jsx("p",{children:s.rd})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","On-time delivery",e.jsx(Ke,{children:"Percentage of product deliveries or releases that meet scheduled deadlines."})]}),e.jsx("p",{children:s.on_time_delivery})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:s.id?e.jsx(va,{update:s,afterEdit:d}):null}),c?e.jsxs("div",{className:"mt-4 border border-black/60 bg-brown-main-bg p-3",children:[e.jsxs("p",{className:"flex items-center gap-3 font-semibold",children:[e.jsx(ht,{className:"h-5 text-yellow-500",strokeWidth:2}),"No integrations setup currently."]}),e.jsxs("p",{className:"mt-1",children:["In order to populate financial information in your updates, you must setup integrations with your current financial services"," ",e.jsx(Ye,{to:"/member/integrations",className:"font-semibold text-primary-black underline",children:"here"})]})]}):null]})]})})})})]})})]})}const Ge=({children:s})=>{const[d,o]=t.useState(!1),r=t.useRef(null),l=()=>{r.current&&clearTimeout(r.current),o(!0)},c=()=>{r.current=setTimeout(()=>{o(!1)},100)};return e.jsx(V,{className:"relative inline",children:e.jsxs("div",{onMouseEnter:l,onMouseLeave:c,children:[e.jsx(V.Button,{className:"ml-0",children:e.jsx(Ne,{className:"mt-[3px] h-4 w-4 cursor-pointer",pathClasses:"text-[#1f1d1a]",stroke:"white"})}),d&&e.jsx(g,{show:!0,as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(V.Panel,{static:!0,className:"absolute left-0 z-10 mt-3 w-screen max-w-[200px] -translate-x-[30%] transform px-4 text-sm",onMouseEnter:l,onMouseLeave:c,children:e.jsxs("div",{className:"relative rounded-lg bg-[#1f1d1a] px-4 py-3 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:[e.jsx("div",{className:"font-medium",children:s}),e.jsx("div",{className:"absolute left-1/2 top-0 h-3 w-3 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-[#1f1d1a]"})]})})})]})})};function Ca({update:s,refetchUpdate:d,showMetric:o}){const[r,l]=t.useState(!1);t.useContext(ne),t.useContext(ie),t.useState(!1);const[c,j]=t.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsxs("button",{className:"flex w-full items-center justify-between text-[16px] font-medium text-[#1f1d1a] md:text-[18px]  ",onClick:()=>l(o),children:[e.jsx("span",{children:"Show team/HR metrics"})," ",o?e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.5 18.3333H2.5C2.15833 18.3333 1.875 18.05 1.875 17.7083C1.875 17.3667 2.15833 17.0833 2.5 17.0833H17.5C17.8417 17.0833 18.125 17.3667 18.125 17.7083C18.125 18.05 17.8417 18.3333 17.5 18.3333Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M15.8495 2.89999C14.2328 1.28332 12.6495 1.24166 10.9912 2.89999L9.98283 3.90832C9.89949 3.99166 9.86616 4.12499 9.89949 4.24166C10.5328 6.44999 12.2995 8.21666 14.5078 8.84999C14.5412 8.85832 14.5745 8.86666 14.6078 8.86666C14.6995 8.86666 14.7828 8.83332 14.8495 8.76666L15.8495 7.75832C16.6745 6.94166 17.0745 6.14999 17.0745 5.34999C17.0828 4.52499 16.6828 3.72499 15.8495 2.89999Z",fill:"#1F1D1A"}),e.jsx("path",{d:"M13.0089 9.60832C12.7673 9.49166 12.5339 9.37499 12.3089 9.24166C12.1256 9.13332 11.9506 9.01666 11.7756 8.89166C11.6339 8.79999 11.4673 8.66666 11.3089 8.53332C11.2923 8.52499 11.2339 8.47499 11.1673 8.40832C10.8923 8.17499 10.5839 7.87499 10.3089 7.54166C10.2839 7.52499 10.2423 7.46666 10.1839 7.39166C10.1006 7.29166 9.95892 7.12499 9.83392 6.93332C9.73392 6.80832 9.61726 6.62499 9.50892 6.44166C9.37559 6.21666 9.25892 5.99166 9.14226 5.75832C9.1246 5.72049 9.10752 5.68286 9.09096 5.64544C8.96798 5.36767 8.60578 5.28647 8.39098 5.50126L3.61726 10.275C3.50892 10.3833 3.40892 10.5917 3.38392 10.7333L2.93392 13.925C2.85059 14.4917 3.00892 15.025 3.35892 15.3833C3.65892 15.675 4.07559 15.8333 4.52559 15.8333C4.62559 15.8333 4.72559 15.825 4.82559 15.8083L8.02559 15.3583C8.17559 15.3333 8.38392 15.2333 8.48392 15.125L13.2517 10.3572C13.468 10.1409 13.3864 9.76972 13.105 9.64967C13.0734 9.63615 13.0414 9.62238 13.0089 9.60832Z",fill:"#1F1D1A"})]}):!1]}),e.jsx(g,{appear:!0,show:r,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>l(!1),children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Team/HR Metrics"}),e.jsx("button",{onClick:()=>l(!1),type:"button",children:e.jsx(oe,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","Headcount",e.jsx(Ge,{children:"Total number of full-time employees currently working in your organization."})]}),e.jsx("p",{children:s.headcount})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","Turnover Rate (annual)",e.jsx(Ge,{children:"Percentage of employees who left the organization over the past year relative to average total employees."})]}),e.jsx("p",{children:s.turnover})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","Retention Rate (annual)",e.jsx(Ge,{children:"Percentage of employees who remained with the organization over the past year."})]}),e.jsx("p",{children:s.retention_hr})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","Employee Satisfaction",e.jsx(Ge,{children:"Employee Net Promoter Score (eNPS) measuring employee satisfaction and loyalty on a -100 to +100 scale."})]}),e.jsx("p",{children:s.satisfaction})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"flex items-center gap-1 text-sm font-normal md:text-base",children:[s.sync==G.MANUAL?"*":"","Revenue Per Employee",e.jsx(Ge,{children:"Average revenue generated per employee, calculated by dividing total revenue by number of employees."})]}),e.jsx("p",{children:s.revenue_per_employee})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:s.id?e.jsx(ya,{update:s,afterEdit:d}):null}),c?e.jsxs("div",{className:"mt-4 border border-black/60 bg-brown-main-bg p-3",children:[e.jsxs("p",{className:"flex items-center gap-3 font-semibold",children:[e.jsx(ht,{className:"h-5 text-yellow-500",strokeWidth:2}),"No integrations setup currently."]}),e.jsxs("p",{className:"mt-1",children:["In order to populate financial information in your updates, you must setup integrations with your current financial services"," ",e.jsx(Ye,{to:"/member/integrations",className:"font-semibold text-primary-black underline",children:"here"})]})]}):null]})]})})})})]})})]})}const _a=({open:s,setOpen:d,loading:o,changeTemplate:r,setSelectedTemplate:l,defaultTemplate:c})=>{et(),t.useContext(ne),t.useContext(ie),t.useState();const j=()=>{d(!1),l(c)};return e.jsx(g,{appear:!0,show:s,as:t.Fragment,children:e.jsxs(_,{as:"div",className:"relative z-[7000]",onClose:()=>j(),children:[e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"overflow-y-auto fixed inset-0",children:e.jsx("div",{className:"flex justify-center items-center p-4 min-h-full text-center",children:e.jsx(g.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(_.Panel,{className:"w-full max-w-[500px] transform space-y-10 overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-sm shadow-xl transition-all",children:[e.jsxs("div",{className:"flex flex-col justify-between",children:[e.jsxs(_.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:["Are you sure you want to change your update template? ",e.jsx("br",{})]}),e.jsx("p",{className:"mt-2 text-base font-iowan",children:"All contents of existing update will be erased"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mt-6",children:[e.jsx("button",{className:"rounded-[.125rem] border-2 border-gray-400 py-2 text-center",type:"button",onClick:()=>j(),children:"Cancel"}),e.jsx(Ft,{loading:o,disabled:o,onClick:()=>r(),className:"!w-auto !rounded-[.125rem] bg-[#1f1d1a] py-2 text-center font-semibold text-white transition-colors duration-100 disabled:bg-opacity-60",children:"Change"})]})]})})})})]})})},Xn=()=>{var We,Qe;const s=t.useRef(null),d=t.useRef(null),o=Yt(),[r]=Kt(),l=r.get("next");(We=o==null?void 0:o.state)==null||We.templateTitle;const[c,j]=t.useState(!1),[p,T]=t.useState(!1),[u,h]=t.useState(),[b,F]=t.useState([]);et();const{globalDispatch:v,authDispatch:w,authState:N}=ut(),[M,A]=t.useState(null),x=t.useRef(null),{id:f}=Me(),{data:L}=Xt(),y=r.get("new"),k=async()=>{try{await new ae().callRawAPI(`/v3/api/custom/goodbadugly/activities/${i==null?void 0:i.id}/view`,{},"PUT")}catch(m){console.error("Error calling view endpoint:",m)}};t.useEffect(()=>{y=="new"&&k()},[]);const{update:i,loading:{single:W},refetch:S}=xs(f),{notes:B,refetch:q}=us(f),{updateGroups:E,refetch:H}=hs(f);t.useEffect(()=>{if((E==null?void 0:E.length)>0){const m=E.map(n=>n.group_id);localStorage.setItem(`update_${f}_groups`,JSON.stringify(m))}},[E,f]),t.useEffect(()=>{localStorage.removeItem(`update_${f}_groups`)},[f]);const[le,ee]=t.useState(""),[R,a]=t.useState(!1),[z,J]=t.useState(""),[xe,Re]=t.useState(!1),[fe,Ue]=t.useState(!1),[st,at]=t.useState("7"),[ft,Ie]=t.useState(!1);t.useState("text");const[ge,nt]=t.useState({show_financial_metrics:null,show_product_metric:null,show_hr_metric:null,show_marketing_metrics:null,show_investment_metrics:null}),[rt,it]=t.useState(!1),[je,Ce]=t.useState(!1),[be,ye]=t.useState({showPrompt:!1,updateId:f,createdGroupId:null,skipAwareness:!1}),[ze,pe]=t.useState([]),{profile:Y}=tt({isPublic:!1}),{getMyCompanyMembers:D}=Ms(),{getRecipientGroups:pt}=Fs({}),{data:{isCollaborator:bt,collaborator:gt},getUpdateCollaborator:jt}=As({filter:[`update_id,eq,${f}`]}),re=(Y==null?void 0:Y.id)==(i==null?void 0:i.user_id),[ve,_e]=t.useState([]),[wt,De]=t.useState(!1),[Be,Oe]=t.useState(!1);console.log(Be),t.useEffect(()=>{_e(B)},[B]);async function $e(m){try{const n=await St(v,w,{method:"DELETE",endpoint:`/v3/api/custom/goodbadugly/updates/recipient/${m}`});n!=null&&n.error||(P(v,"Group removed successfully",5e3,"success"),H(),q())}catch(n){X(w,n.message),P(v,n.message,5e3,"error")}}const lt=async()=>{var O,$,K,se;const m=await pt({filter:[`goodbadugly_recipient_group.user_id,eq,${Y==null?void 0:Y.id}`]}),n=await D({filter:[`goodbadugly_company_member.company_id,eq,${($=(O=Y==null?void 0:Y.companies)==null?void 0:O[0])==null?void 0:$.id}`]}),I=(K=m==null?void 0:m.data)==null?void 0:K.map(Z=>{var te,he;return{recipient_member:Z==null?void 0:Z.recipient_member,group_name:(te=Z==null?void 0:Z.group)==null?void 0:te.group_name,id:(he=Z==null?void 0:Z.group)==null?void 0:he.id,type:1}}),C=(se=n==null?void 0:n.data)==null?void 0:se.map(Z=>{var te,he,we;return{group_name:[(te=Z==null?void 0:Z.user)==null?void 0:te.first_name,(he=Z==null?void 0:Z.user)==null?void 0:he.last_name].join(" "),id:(we=Z==null?void 0:Z.user)==null?void 0:we.id,type:2}});pe(()=>[...I,...C])};async function ke(m){Ie(!0);try{await new ae().callRawAPI(`/v4/api/records/updates/${f}`,{[m]:i[m]?0:1},"PUT"),S(),P(v,"Updated"),nt({...ge,[m]:!(ge!=null&&ge[m])})}catch(n){X(w,n.message),P(v,n.message,5e3,"error")}Ie(!1)}async function ot(){var m,n;{T(!0);try{const I=new ns,C=await Vt(v,w,"updates",f,{template_name:u==null?void 0:u.title,user_id:N.user,mrr:0,arr:0,cash:0,burnrate:0,public_link_enabled:0,private_link_open:1,date:U().format("MMM D, YYYY"),company_id:N.company.id});if(!(C!=null&&C.error)){const O=await Bt(v,w,"notes",{filter:[`update_id,eq,${f}`]}),$=(m=O==null?void 0:O.data)==null?void 0:m.map(se=>{Ht(v,w,"notes",se.id)});await Promise.all($);const K=(n=u==null?void 0:u.template)==null?void 0:n.map(se=>Zt(v,w,"notes",{update_id:f,type:se,status:0}));await Promise.all(K),S(),q(),j(!1),T(!1),P(v,"Template updated")}}catch(I){T(!1),X(w,I.message),P(v,I.message,5e3,"error")}}}const ct=m=>{const n=JSON.parse(m==null?void 0:m.target.selectedOptions[0].dataset.template);J(m==null?void 0:m.target.value),j(!0),h(n)};t.useEffect(()=>{J(i==null?void 0:i.template_name),ss(v,i,"update")},[i]),t.useEffect(()=>{ee(i.date),at(i.recipient_access||"7"),nt({show_financial_metrics:i.show_financial_metrics===1,show_hr_metric:i.show_hr_metric===1,show_product_metric:i.show_product_metric===1,show_marketing_metrics:i.show_marketing_metrics===1,show_investment_metrics:i.show_investment_metrics===1})},[i]),t.useEffect(()=>{v({type:"SETPATH",payload:{path:"updates"}})},[]),t.useEffect(()=>{Y!=null&&Y.id&&lt()},[Y==null?void 0:Y.id]),t.useEffect(()=>{Y!=null&&Y.id&&f&&jt({filter:[`update_id,eq,${f}`],join:[]})},[Y==null?void 0:Y.id,f]),t.useEffect(()=>{Y!=null&&Y.id&&(Y==null?void 0:Y.awareness)<3&&!(be!=null&&be.skipAwareness)&&ye(m=>({...m,showPrompt:!0}))},[Y==null?void 0:Y.id]),t.useEffect(()=>{l&&ye(m=>({...m,next:l}))},[l]);const He=E.length===4?4:3,yt=E.slice(0,He),Ze=E.slice(He),vt=()=>{const m=document.querySelectorAll(".section-collaborators");m.length>0&&(m[0].scrollIntoView({behavior:"smooth"}),m.forEach(n=>{n.click()}))},Nt=async m=>{if(!m.destination)return;const n=Array.from(ve),[I]=n.splice(m.source.index,1);n.splice(m.destination.index,0,I),_e(n);try{await new ae().callRawAPI("/v3/api/custom/goodbadugly/notes/reorder",{notes:n.map((O,$)=>({id:O.id,order:$})),update_id:i.id},"POST"),q()}catch(C){_e(B),X(w,C.message),P(v,C.message,5e3,"error")}},Ve=()=>{if(!(i!=null&&i.sent_at)||!(i!=null&&i.cadence))return!1;const m=new Date(i.sent_at),n=new Date(m);return n.setDate(m.getDate()+i.cadence),new Date<n},Ct=()=>{if(!(i!=null&&i.sent_at)||!(i!=null&&i.cadence))return null;const m=new Date(i.sent_at),n=new Date(m);return n.setDate(m.getDate()+i.cadence),n.toDateString()},_t=async()=>{try{await new ae().callRawAPI(`/v4/api/records/updates/${f}`,{cadence:null},"PUT"),S(),Oe(!1),P(v,"Cadence stopped successfully")}catch(m){X(w,m.message),P(v,m.message,5e3,"error")}};return e.jsxs("div",{className:`relative h-full w-full ${W&&"overflow-hidden"}`,children:[W&&e.jsx("div",{className:"absolute z-[9999] h-full w-full bg-black/20",children:e.jsx("div",{className:"flex h-full items-center justify-center",children:e.jsx(Wt,{loading:W,color:"#1f1d1a"})})}),e.jsx("div",{className:"flex w-full flex-col border-2 border-black p-4 md:hidden",children:e.jsxs("div",{className:"flex w-full flex-wrap items-center justify-between",children:[e.jsx("span",{className:"font-Inter text-xl font-bold",children:((Qe=N.company)==null?void 0:Qe.name)||"Company Name"}),e.jsxs("div",{className:"flex gap-2",children:[Ve()&&e.jsx("button",{className:"  flex h-[28px]  w-[28px] items-center justify-center gap-2  rounded border border-[#1f1d1a] bg-transparent text-[#1f1d1a] sm:h-[36px] sm:w-[36px]",title:"Active Scheduled Update Cadence",onClick:()=>De(!0),children:e.jsx(zt,{className:"h-4 w-4 sm:h-5 sm:w-5"})}),e.jsx("div",{className:"",children:e.jsx(qt,{update:i,refetch:S,isOwner:re})}),e.jsxs("button",{disabled:!re,className:"flex h-[2.25rem] w-fit min-w-fit items-center justify-center gap-2 bg-transparent px-2 font-iowan text-[1rem] font-[700] leading-[1.25rem] text-[#1f1d1a]",onClick:()=>ye(m=>({...m,showPrompt:!0})),children:[e.jsx(Et,{className:"h-4 w-4"})," Help"]})]}),e.jsx("div",{className:`${L!=null&&L.subscription?"":"hidden"}`,children:e.jsx(Tt,{children:e.jsx(Ps,{})})})]})}),e.jsxs("div",{className:"mx-auto rounded px-5 py-5 pt-8 shadow-md sm:px-8 xl:px-8",children:[e.jsxs("div",{className:"flex w-full grid-cols-2 flex-wrap  items-center justify-between gap-3 border-b-[2px] border-[#1F1D1A1A]/10 pb-[12px] sm:border-b-0 sm:pb-0 md:grid md:grid-cols-2",children:[e.jsx("div",{className:"",children:e.jsx(xa,{update:i,afterEdit:S,isOwner:re})}),e.jsxs("div",{className:"flex justify-end gap-2 md:justify-end",children:[e.jsx("div",{className:"md:hidden",children:e.jsx(Ds,{nextScheduledUpdate:M})}),Ve()&&e.jsx("button",{className:" hidden h-[28px] w-[28px]  items-center justify-center gap-2 rounded  border border-[#1f1d1a] bg-transparent text-[#1f1d1a] sm:h-[36px] sm:w-[36px] md:flex",title:"Active Scheduled Update Cadence",onClick:()=>De(!0),children:e.jsx(zt,{className:"h-4 w-4 sm:h-5 sm:w-5"})}),e.jsx("div",{className:"hidden md:block",children:e.jsx(qt,{update:i,refetch:S,isOwner:re})}),e.jsxs("button",{disabled:!re,className:" hidden h-[2.25rem] w-fit min-w-fit items-center justify-center gap-2 bg-transparent px-2 font-iowan text-[1rem] font-[700] leading-[1.25rem] text-[#1f1d1a] md:flex",onClick:()=>ye(m=>({...m,showPrompt:!0})),children:[e.jsx(Et,{className:"h-4 w-4"})," Help"]})]})]}),e.jsx("hr",{className:"hidden border-[1px] border-transparent  md:my-10  md:block md:border-[#1F1D1A1A] "}),e.jsxs("div",{className:"mt-6 flex w-full flex-col",children:[e.jsxs("div",{className:"flex flex-col gap-5 md:flex-row",children:[e.jsxs("div",{className:"flex w-full flex-wrap items-center gap-3 md:justify-start",children:[e.jsx("div",{className:"flex max-w-[390px] grow-[1] flex-row flex-wrap items-center gap-6 sm:max-w-fit md:w-auto md:flex-row",children:e.jsxs("select",{disabled:!re,value:z,onChange:ct,className:"focus:shadow-outline font h-[36px] w-full appearance-none rounded-[3px] border-[1px] border-[#1f1d1a] bg-brown-main-bg py-1 pl-4 pr-5 font-iowan leading-tight text-[#1f1d1a] focus:outline-none sm:w-[172px] md:w-auto ",children:[e.jsx("option",{value:"",children:"Select Template"}),Ut.map(m=>e.jsx("option",{"data-template":JSON.stringify(m),value:m.title,children:m.title},m.id))]})}),e.jsx(Vs,{afterRestore:q,template:i==null?void 0:i.template_name,isOwner:re})]}),e.jsx(Jt,{isOwner:re,createGroupModalRef:s,groups:ze,updateGroups:E,refetch:q,refetchUpdateGroups:H,setIsOpen:it,setSelectedGroupIDs:F,ref:x,isOpen:rt,selectedGroupIDs:b,update:i})]}),e.jsx("div",{className:"flex w-full sm:justify-end",children:e.jsxs("div",{className:"mt-2 flex w-full min-w-full max-w-full flex-wrap gap-2 sm:justify-end",children:[yt.map((m,n)=>e.jsx(V,{className:`relative  ${n!==0?"z-[${8 - index - 1}]}":"z-[8]"}`,children:({open:I})=>e.jsxs(e.Fragment,{children:[e.jsxs(V.Button,{as:"div",onMouseEnter:()=>a(m.id),onMouseLeave:()=>a(!1),className:"line-clamp-1 inline-flex cursor-pointer items-center gap-1 overflow-hidden text-ellipsis whitespace-nowrap rounded-[50px] border border-[#1f1d1a] bg-[#BCBBBA] px-1 py-[2px] text-[10px] hover:text-gray-900 md:line-clamp-2 md:gap-2 md:whitespace-normal md:px-[6px] md:py-[4px] md:text-[12px]",children:[e.jsx("span",{className:"max-w-[60px] truncate md:max-w-none",children:m.group_name}),re?e.jsx("button",{type:"button",onClick:C=>{$e(m.id)},className:"ml-0.5 focus:outline-none md:ml-1",children:e.jsx(Es,{className:"h-2 w-2 md:h-3 md:w-3",strokeWidth:2})}):null]}),e.jsx(g,{as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",show:m.id===R,onMouseEnter:()=>a(m.id),onMouseLeave:()=>a(!1),children:e.jsx(V.Panel,{className:`absolute ${n===0||n===2?"-translate-x-[15%] md:-translate-x-1/2":"left-0"}  mt-2 w-fit -translate-x-1/2 transform whitespace-nowrap px-2 z-[${8-n-1}]`,children:e.jsx("div",{className:"overflow-hidden  rounded-lg bg-[#1f1d1a] p-[3px] text-white shadow-lg ring-1 ring-[#1f1d1a]/5 md:p-[6px]",children:e.jsxs("div",{className:"text-[8px] font-medium md:text-[12px]",children:[m.members.length==0?e.jsx("span",{children:"No members"}):null,m.members.map((C,O,$)=>e.jsxs("span",{className:"mr-2",children:[C.first_name," ",C.last_name," ",O==$.length-1?"":"•"]},C.id))]})})})})]})},m.id)),Ze.length>0&&E.length>4&&e.jsx(V,{className:"relative z-[4]",children:({open:m})=>e.jsxs(e.Fragment,{children:[e.jsx(V.Button,{as:"div",onMouseEnter:()=>Ce(!0),onMouseLeave:()=>Ce(!1),className:"line-clamp-2 inline-flex cursor-pointer items-center gap-2 overflow-x-hidden whitespace-nowrap rounded-[50px] border border-[#1f1d1a] bg-[#BCBBBA] px-[3px] py-[2px] text-[10px] hover:text-gray-900 md:px-[6px] md:py-1 md:text-[12px]",children:e.jsxs("span",{className:"max-w-[60px] md:max-w-none",children:["+",Ze.length," more"]})}),e.jsx(g,{show:je,as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",children:e.jsx(V.Panel,{static:!0,className:"absolute left-0 z-[4] mt-2 w-fit -translate-x-1/2 transform whitespace-nowrap px-2",onMouseEnter:()=>Ce(!0),onMouseLeave:()=>Ce(!1),children:e.jsx("div",{className:"overflow-hidden rounded-lg bg-[#1f1d1a] p-[3px]  text-white shadow-lg ring-1 ring-[#1f1d1a]/5 md:p-[6px]",children:e.jsx("div",{className:"text-[8px] font-medium md:text-[12px]",children:Ze.map((n,I)=>e.jsx("div",{className:"flex items-center justify-between gap-2 py-1",children:e.jsx("span",{children:n.group_name})},n.id))})})})})]})})]})})]}),e.jsx("hr",{className:"my-2 mt-4 border-[1px]  border-[#1f1d1a]/10 sm:my-5 md:my-10 md:border-[#1F1D1A1A]"}),e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"mt-5 items-start justify-between sm:mt-12",children:[e.jsxs("div",{className:"mb-5 flex w-full items-center justify-between",children:[" ",e.jsx("h3",{className:"text-[1.125rem] font-semibold sm:text-[1.25rem]",children:"Edit Update Metrics"}),e.jsxs("div",{onClick:()=>{Re(!xe)},className:"flex cursor-pointer items-center gap-2",children:[e.jsx("p",{className:"font-iowan underline underline-offset-2",children:xe?"Close":"Open"}),e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12.3536 14.6464L16.1464 10.8536C16.4614 10.5386 16.2383 10 15.7929 10L8.20711 10C7.76165 10 7.53857 10.5386 7.85355 10.8536L11.6464 14.6464C11.8417 14.8417 12.1583 14.8417 12.3536 14.6464Z",fill:"black"})})]})]}),xe?e.jsxs("div",{className:"flex w-full flex-col justify-between md:flex-row md:gap-5",children:[e.jsxs("div",{className:"w-full  md:w-[45%]",children:[" ",e.jsxs("div",{className:"flex h-[50px] w-full items-center justify-between gap-4 md:justify-start",children:[e.jsx(qe,{enabled:i.show_product_metric==1,setEnabled:()=>ke("show_product_metric")}),e.jsx(Na,{update:i,refetchUpdate:S,showMetric:ge.show_product_metric})]}),e.jsxs("div",{className:"flex h-[40px] w-full items-center justify-between gap-4 md:justify-start",children:[e.jsx(qe,{enabled:i.show_financial_metrics==1,setEnabled:()=>ke("show_financial_metrics")}),e.jsx(Qs,{update:i,refetchUpdate:S,showMetric:ge.show_financial_metrics})]}),e.jsxs("div",{className:"flex h-[40px] w-full items-center justify-between gap-4 md:justify-start",children:[e.jsx(qe,{enabled:i.show_hr_metric==1,setEnabled:()=>ke("show_hr_metric")}),e.jsx(Ca,{update:i,refetchUpdate:S,showMetric:ge.show_hr_metric})]})]}),e.jsxs("div",{className:"w-full md:w-[45%]",children:[e.jsxs("div",{className:"flex h-[40px] w-full items-center justify-between gap-4 md:justify-start",children:[e.jsx(qe,{enabled:i.show_marketing_metrics==1,setEnabled:()=>ke("show_marketing_metrics")}),e.jsx(wa,{update:i,refetchUpdate:S,showMetric:ge.show_marketing_metrics})]}),e.jsxs("div",{className:"flex h-[40px] w-full items-center justify-between gap-4 md:justify-start",children:[e.jsx(qe,{enabled:i.show_investment_metrics==1,setEnabled:()=>ke("show_investment_metrics")}),e.jsx(Js,{update:i,refetchUpdate:S,showMetric:ge.show_investment_metrics})]})]})]}):e.jsx("div",{className:"border-2 border-[#1F1D1A]"}),e.jsx("div",{className:"",children:N.company.id?e.jsx("div",{className:"mt-6 flex flex-col items-end"}):null})]}),e.jsx(_s,{onDragEnd:Nt,children:e.jsx(ks,{droppableId:"sections",children:m=>e.jsxs("div",{className:"space-y-12",...m.droppableProps,ref:m.innerRef,children:[ve.map((n,I)=>e.jsx(Ss,{draggableId:n.id.toString(),index:I,children:(C,O)=>e.jsx("div",{ref:C.innerRef,...C.draggableProps,style:{...C.draggableProps.style,opacity:O.isDragging?.8:1},children:e.jsx(sa,{note:n,refetch:q,dragHandleProps:C.dragHandleProps,update:i,isOwner:re,collaborator:gt,isCollaborator:bt})})},n.id)),m.placeholder]})})}),re?e.jsxs(e.Fragment,{children:[e.jsx(ma,{afterAdd:q}),e.jsx(Zs,{investors:E.flatMap(m=>m.members).filter((m,n,I)=>I.findIndex(C=>C.id===m.id)===n)}),e.jsxs("div",{className:"mt-8 flex flex-col items-start justify-between md:flex-row md:items-center",children:[e.jsxs("label",{className:"flex items-center gap-3 text-lg font-medium capitalize",children:[e.jsx("input",{type:"checkbox",checked:fe,className:"bg-brown-main-bg",onChange:()=>Ue(m=>!m)})," ","CC myself"]}),e.jsxs("div",{className:"mt-4 flex w-fit flex-wrap items-center justify-between gap-4 md:mt-0 md:justify-normal",children:[e.jsxs("p",{className:"whitespace-nowrap font-iowan md:w-auto",children:["Give recipients access for"," "]}),e.jsxs("select",{value:st,onChange:m=>at(m.target.value),className:"focus:shadow-outline font w-auto appearance-none rounded border border-[#1f1d1a] bg-brown-main-bg py-2 pl-6 pr-8 font-iowan leading-tight text-[#1f1d1a] focus:outline-none md:w-auto ",children:[e.jsx("option",{value:"1",children:"1 day"}),e.jsx("option",{value:"7",children:"7 days"}),e.jsx("option",{value:"14",children:"2 weeks"}),e.jsx("option",{value:"30",children:"1 month"}),e.jsx("option",{value:"90",children:"3 months"})]}),e.jsxs("div",{className:"flex w-full justify-between sm:w-auto sm:justify-normal md:gap-2",children:[e.jsx(Ye,{to:`/${as[Y==null?void 0:Y.role]}/update/preview/${f}?mode=preview`,className:"focus:shadow-outline font t flex !w-full items-center justify-center whitespace-nowrap rounded-[2px] bg-[#1f1d1a] px-4 py-2 text-center font-iowan text-base  font-bold text-white focus:outline-none sm:text-base md:w-auto",children:"Preview Update"}),e.jsx(ga,{nextScheduledUpdate:M,setNextScheduledUpdate:A,refetch:S,UpdateCadence:i==null?void 0:i.cadence,updateGroups:E,reportDate:le,recipientAccess:st,ccMyself:fe,selectedTemplate:z,className:"send-update-button !w-full md:!w-auto",sendUpdateRef:d})]})]})]})]}):null]}),e.jsx(_a,{open:c,setOpen:j,changeTemplate:ot,loading:p,setSelectedTemplate:J,defaultTemplate:(i==null?void 0:i.template_name)||""}),e.jsx(Ts,{data:be,next:be==null?void 0:be.next,title:e.jsx(e.Fragment,{children:"Getting Started With Updates?"}),isOpen:be==null?void 0:be.showPrompt,createGroupModalRef:s,onClose:(m=!1)=>ye(n=>({...n,showPrompt:!1,skipAwareness:m})),updateNext:m=>{ye(n=>({...n,next:m}))},onNext:m=>{var n;ye(I=>({...I,next:m,showPrompt:!1})),m==5&&(console.log("recipientSelectRef?.current",x==null?void 0:x.current),(n=x==null?void 0:x.current)==null||n.click()),m==6&&vt(),m==7&&setTimeout(()=>{var I,C;(I=document.querySelector(".send-update-button"))==null||I.scrollIntoView({behavior:"smooth"}),d!=null&&d.current&&((C=d==null?void 0:d.current)==null||C.click())},250)}}),wt&&e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"w-full max-w-xl transform rounded-md border-[1px] border-[#1f1d1a] bg-brown-main-bg p-8 text-left align-middle text-base shadow-xl transition-all",children:[e.jsx("h2",{className:"text-lg font-bold",children:"Scheduled Updates Cadence"}),e.jsxs("p",{children:["Current Cadence: ",i.cadence," days"]}),e.jsxs("p",{children:["Next Send Date: ",Ct()]}),e.jsx("button",{className:"mt-4 rounded bg-red-500 px-4 py-2 text-white",onClick:()=>{De(!1),Oe(!0)},children:"Stop Cadence"}),e.jsx("button",{className:"ml-2 mt-4 rounded bg-gray-500 px-4 py-2 text-white",onClick:()=>De(!1),children:"Close"})]})}),e.jsx(Tt,{children:e.jsx(Ls,{title:"Stop Cadence",mode:"manual",action:"Stop",multiple:!1,onSuccess:_t,inputConfirmation:!1,onClose:()=>Oe(!1),customMessage:e.jsx(e.Fragment,{children:"Are you sure you want to stop the cadence?"}),isOpen:Be})})]})]})};export{Xn as default};
