import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as n,b as se}from"./vendor-4cdf2bd1.js";import{M as ae,G as re,A as ne,f as ie,t as w,g as m,s as h}from"./index-f2ad9142.js";import{o as oe}from"./yup-0917e80c.js";import{u as ce}from"./react-hook-form-a383372b.js";import{c as le,a as g}from"./yup-342a5df4.js";import{P as de}from"./index-9dceff66.js";import{B as pe,a as ue}from"./index.esm-54e24cf9.js";import{A as me,a as he}from"./index.esm-25e0e799.js";import{R as ge}from"./index.esm-3e7472af.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-icons-36ae72b7.js";let S=new ae;const v=[{header:"Customer",accessor:"userEmail"},{header:"Plan",accessor:"planName"},{header:"Starts",accessor:"currentPeriodStart",type:"timestamp"},{header:"Ends",accessor:"currentPeriodEnd",type:"timestamp"},{header:"type",accessor:"planType",mapping:{recurring:"Recurring",life_time:"Lifetime"}},{header:"Usage Type",accessor:"isMetered",mapping:{0:"Upfront",1:"Metered"}},{header:"Price",accessor:"planAmount",type:"currency"},{header:"Has Trial",accessor:"trialDays"},{header:"Status",accessor:"status"},{header:"Action",accessor:""}],He=()=>{const{dispatch:l}=n.useContext(re),{dispatch:x}=n.useContext(ne);n.useState("");const[N,O]=n.useState([]),[o,C]=n.useState(10),[P,$]=n.useState(0),[xe,L]=n.useState(0),[d,q]=n.useState(0),[z,B]=n.useState(!1),[I,M]=n.useState(!1),[k,U]=n.useState(!1),[R,_]=n.useState(!1),[i,f]=n.useState([]),[E,y]=n.useState([]),[G,H]=n.useState(""),[T,K]=n.useState("eq"),[V,A]=n.useState(!1);se();const J=le({customer_email:g(),plan_name:g(),sub_status:g(),plan_type:g()}),{register:fe,handleSubmit:Q,formState:{errors:ye}}=ce({resolver:oe(J)}),D=(t,a,s)=>{const r=a==="eq"&&isNaN(s)?`"${s}"`:s,p=`${t},${a},${r}`;y(b=>[...b.filter(u=>!u.includes(t)),p]),H(s)};function W(t){(async function(){C(t),await c(1,t)})()}function X(){(async function(){await c(d-1>1?d-1:1,o)})()}function Y(){(async function(){await c(d+1<=P?d+1:1,o)})()}async function c(t,a,s){A(!0);try{const r=await S.getStripeSubscriptions({page:t,limit:a},`filter=${s.toString()}`),{list:p,total:b,limit:F,num_pages:u,page:j}=r;O(p),C(+F),$(+u),q(+j),L(+b),B(+j>1),M(+j+1<=+u)}catch(r){console.log("ERROR",r),w(x,r.message)}A(!1)}const Z=t=>{const a=m(t.customer_email),s=m(t.plan_name),r=m(t.sub_status),p=m(t.plan_type);c(1,o,{customer_email:a,plan_name:s,sub_status:r,plan_type:p})},ee=async t=>{console.log(t);try{const a=await S.adminCancelStripeSubscription(t,{});h(l,a.message,3e3),c(1,o)}catch(a){console.log("ERROR",a),h(l,a.message),w(x,a.message)}},te=async(t,a)=>{console.log(t);try{const s=await S.adminCreateUsageCharge(t,a);h(l,s.message,3e3),c(1,o)}catch(s){console.log("ERROR",s),h(l,s.message),w(x,s.message)}};return n.useEffect(()=>{l({type:"SETPATH",payload:{path:"subscriptions"}});const a=setTimeout(async()=>{await c(1,o,E)},700);return()=>{clearTimeout(a)}},[G,E,T]),console.log("data in subscriptions",N),e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex items-center justify-between py-3",children:e.jsxs("form",{className:"relative rounded bg-brown-main-bg",onSubmit:Q(Z),children:[e.jsxs("div",{className:"flex items-center gap-4 text-gray-700",children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-[#0003] px-3 py-1",onClick:()=>U(!k),children:[e.jsx(pe,{}),e.jsx("span",{children:"Filters"}),i.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:i.length>0?i.length:null})]}),e.jsxs("div",{className:" flex cursor-pointer items-center justify-between gap-3 rounded-md border border-[#0003] px-2 py-1 focus-within:border-gray-400",children:[e.jsx(ue,{className:"text-xl text-gray-200"}),e.jsx("input",{type:"text",placeholder:"search",className:"border-none p-0 placeholder:text-left focus:outline-none",style:{boxShadow:"0 0 transparent"},onInput:t=>{var a;return D("name","cs",(a=t.target)==null?void 0:a.value)}}),e.jsx(me,{className:"text-lg text-gray-200"})]})]}),k&&e.jsxs("div",{className:"top-fill filter-form-holder absolute left-0  z-20 mt-4 min-w-[70%] rounded-md border border-[#0003] bg-brown-main-bg p-5 shadow-xl",children:[i==null?void 0:i.map((t,a)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:"mb-3 w-1/3 rounded-md border border-black/60 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"w-[30%] appearance-none border-none outline-0",onChange:s=>{K(s.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value",className:"mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>D(t,T,s.target.value)}),e.jsx(ge,{className:"cursor-pointer text-xl",onClick:()=>{f(s=>s.filter(r=>r!==t)),y(s=>s.filter(r=>!r.includes(t)))}})]},a)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-brown-main-bg px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{_(!R)},children:[e.jsx(he,{}),"Add filter"]}),R&&e.jsx("div",{className:"absolute top-11 z-10 bg-brown-main-bg px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:v.slice(0,-1).map(t=>e.jsx("li",{className:`${i.includes(t.header)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{i.includes(t.header)||f(a=>[...a,t.header]),_(!1)},children:t.header},t.header))})}),i.length>0&&e.jsx("div",{onClick:()=>{f([]),y([])},className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]})]})]})}),V?e.jsx(ie,{}):e.jsx("div",{className:"overflow-x-auto shadow",children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10",children:[e.jsx("thead",{children:e.jsx("tr",{children:v.map((t,a)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},a))})}),e.jsx("tbody",{className:"font-iowan-regular divide-y divide-[#1f1d1a]/10",children:N.map((t,a)=>e.jsx("tr",{className:"  md:h-[60px]",children:v.map((s,r)=>{if(s.accessor=="")return e.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:[t.status!=="canceled"?e.jsx("button",{onClick:()=>ee(t.subId),type:"button",className:"mx-1 inline-block rounded-full bg-[#1f1d1a] px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-red-700 hover:shadow-lg focus:bg-red-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-red-800 active:shadow-lg",children:"Cancel"}):"",t.isMetered===1?e.jsx("button",{onClick:()=>te(t.subId,1),type:"button",className:"mx-1 inline-block rounded-full bg-[#1f1d1a] px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-red-700 hover:shadow-lg focus:bg-red-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-red-800 active:shadow-lg",children:"Create Charge"}):""]},r);if(s.mapping)return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.mapping[t[s.accessor]]},r);if(t.planType==="recurring"&&s.type==="timestamp")return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:new Date(t[s.accessor]*1e3).toLocaleDateString("en-US",{dateStyle:"medium"})},r);if(t.planType==="lifetime"&&s.type==="timestamp"){if(s.accessor==="currentPeriodStart")return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:new Date(t.createdAt*1e3).toLocaleDateString("en-US",{dateStyle:"medium"})},r);if(s.accessor==="currentPeriodEnd")return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:"Infinity"},r)}else if(s.type=="currency")return e.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:["$",+(t[s.accessor]??0)]},r);return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[s.accessor]},r)})},a))})]})}),e.jsx(de,{currentPage:d,pageCount:P,pageSize:o,canPreviousPage:z,canNextPage:I,updatePageSize:W,previousPage:X,nextPage:Y})]})};export{He as default};
