import{j as d}from"./@nextui-org/listbox-0f38ca19.js";import{u as V,I as W,bS as X}from"./index-f2ad9142.js";import{u as Y}from"./useSubscription-dc563085.js";import{r as p}from"./vendor-4cdf2bd1.js";import{A as l}from"./index-afef2e72.js";import P from"./EditPaymentMethodModal-512dfdd0.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const D={free:"#1f1d1a",pro:"#fff0e5",business:"#fff0e5",enterprise:"#fff0e5"},o={free:0,"pro monthly":1,"business monthly":2,"enterprise monthly":3,"pro yearly":4,"business yearly":5,"enterprise yearly":6,pro:1,business:2,enterprise:3},we=({plan:g,name:c,currency:F,onSuccess:M,currentPlan:r,focusYearly:_,allPlans:S=[]})=>{var k,v,N,B,I;const{loading:u,data:t,handleSubscription:H,handleUpgradeSubscription:L,updateData:z,getCardData:A}=Y(),[e,h]=p.useState({modal:null,showModal:!1,currentPrice:null,selectedPrice:null,action:"subscribe",pendingAction:null}),[y,w]=p.useState(""),[E,m]=p.useState(""),{profile:C}=V(),x=(s,n)=>{h(i=>({...i,modal:s,showModal:n})),!n&&s==="subscribe"&&(w(""),m(""))},R=async()=>{var s,n;if(m(""),["subscribe"].includes(e==null?void 0:e.action))try{const i=y.trim()||null;await H((s=e==null?void 0:e.selectedPrice)==null?void 0:s.id,i),x("subscribe",!1),w(""),m("")}catch(i){console.log(i),i.message&&i.message.toLowerCase().includes("coupon")?m("Invalid coupon code. Please check and try again."):m("An error occurred. Please try again.");return}if(["upgrade","downgrade"].includes(e==null?void 0:e.action))try{const i=y.trim()||null;await L((n=e==null?void 0:e.selectedPrice)==null?void 0:n.id,i),x("subscribe",!1),w(""),m("")}catch(i){console.log(i),i.message&&i.message.toLowerCase().includes("coupon")?m("Invalid coupon code. Please check and try again."):m("An error occurred. Please try again.");return}},Z=()=>{var i,j;const s=o==null?void 0:o[(i=e==null?void 0:e.currentPrice)==null?void 0:i.name],n=o==null?void 0:o[(j=e==null?void 0:e.selectedPrice)==null?void 0:j.name];return s>n?"downgrade":"upgrade"},q=()=>{var s;if(r!=null&&r.price_id){if(String(r==null?void 0:r.price_id)===String((s=e==null?void 0:e.selectedPrice)==null?void 0:s.id))return"Current Plan";{if(c==="free")return"Free Plan";const n=Z();return`${X(n)} Plan`}}else return["free"].includes(c)?"Current Plan":"Subscribe"},G=()=>{if(!(t!=null&&t.card)||t!=null&&t.card&&(t!=null&&t.cardExpired)){h(s=>({...s,pendingAction:"subscribe",modal:"add_card",showModal:!0})),console.log("Setting pendingAction to subscribe");return}x("subscribe",!0)};return p.useEffect(()=>{(t==null?void 0:t.subscriptionStatus)==="success"&&M&&(z({subscriptionStatus:null}),M())},[t==null?void 0:t.subscriptionStatus]),p.useEffect(()=>{C!=null&&C.id&&A()},[C==null?void 0:C.id]),p.useEffect(()=>{const s=S==null?void 0:S.flatMap(i=>i==null?void 0:i.stripe_price),n=s==null?void 0:s.find(i=>String(i==null?void 0:i.id)===String(r==null?void 0:r.price_id));console.log("SubscribeButton Debug:",{currentPlanPriceId:r==null?void 0:r.price_id,foundCurrentPrice:n,allPricesCount:s==null?void 0:s.length,planName:c}),h(i=>({...i,currentPrice:n}))},[r==null?void 0:r.price_id,S,c]),p.useEffect(()=>{var O,T,U,$;if(!_){const f=(O=g==null?void 0:g.stripe_price)==null?void 0:O.find(b=>(b==null?void 0:b.name)==`${c} monthly`||(b==null?void 0:b.name)==c),J=o==null?void 0:o[(T=e==null?void 0:e.currentPrice)==null?void 0:T.name],K=o==null?void 0:o[f==null?void 0:f.name],Q=r!=null&&r.price_id?J>K?"downgrade":"upgrade":"subscribe";return h(b=>({...b,action:Q,selectedPrice:f}))}const s=(U=g==null?void 0:g.stripe_price)==null?void 0:U.find(f=>(f==null?void 0:f.name)==`${c} yearly`),n=o==null?void 0:o[($=e==null?void 0:e.currentPrice)==null?void 0:$.name],i=o==null?void 0:o[s==null?void 0:s.name],j=r!=null&&r.price_id?n>i?"downgrade":"upgrade":"subscribe";h(f=>({...f,action:j,selectedPrice:s}))},[(k=e==null?void 0:e.selectedPrice)==null?void 0:k.id,(v=e==null?void 0:e.currentPrice)==null?void 0:v.id,_]),d.jsxs("div",{children:[d.jsxs(W,{type:"button",loading:u==null?void 0:u.subscribe,disabled:["free"].includes(c)||(u==null?void 0:u.subscribe)||(u==null?void 0:u.card)||String(r==null?void 0:r.price_id)===String((N=e==null?void 0:e.selectedPrice)==null?void 0:N.id),onClick:G,className:`!flex !h-[3.5rem] !w-full !items-center !justify-center !gap-2 !rounded-[.125rem] !border !border-primary-black !p-3 font-iowan !text-[1rem] !font-[700] !leading-[1.2431rem] !tracking-wide  ${["free"].includes(c)?"!bg-[#fff0e5] !text-primary-black":"!bg-primary-black !text-white"}`,children:[q(),u.subscribe||["free"].includes(c)?null:d.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"min-h-6 min-w-6",children:d.jsx("path",{d:"M14.707 5.63585L20.364 11.2929C20.5515 11.4804 20.6568 11.7347 20.6568 11.9999C20.6568 12.265 20.5515 12.5193 20.364 12.7069L14.707 18.3639C14.5184 18.546 14.2658 18.6468 14.0036 18.6445C13.7414 18.6423 13.4906 18.5371 13.3052 18.3517C13.1198 18.1663 13.0146 17.9155 13.0123 17.6533C13.01 17.3911 13.1108 17.1385 13.293 16.9499L17.243 12.9999H4C3.73478 12.9999 3.48043 12.8945 3.29289 12.707C3.10536 12.5194 3 12.2651 3 11.9999C3 11.7346 3.10536 11.4803 3.29289 11.2927C3.48043 11.1052 3.73478 10.9999 4 10.9999H17.243L13.293 7.04985C13.1975 6.95761 13.1213 6.84726 13.0689 6.72526C13.0165 6.60325 12.9889 6.47204 12.9877 6.33926C12.9866 6.20648 13.0119 6.0748 13.0622 5.9519C13.1125 5.829 13.1867 5.71735 13.2806 5.62346C13.3745 5.52957 13.4861 5.45531 13.609 5.40503C13.7319 5.35475 13.8636 5.32945 13.9964 5.3306C14.1292 5.33176 14.2604 5.35934 14.3824 5.41175C14.5044 5.46416 14.6148 5.54034 14.707 5.63585Z",fill:D[c]})})]}),d.jsx(P,{isOpen:(e==null?void 0:e.showModal)&&["add_card"].includes(e==null?void 0:e.modal),onClose:()=>x(null,!1),onSuccess:()=>{console.log("EditPaymentMethodModal onSuccess called, pendingAction:",e==null?void 0:e.pendingAction),(e==null?void 0:e.pendingAction)==="subscribe"?(console.log("Proceeding to subscribe modal after card added"),setTimeout(()=>{h(s=>({...s,pendingAction:null,modal:"subscribe",showModal:!0}))},300)):x(null,!1),A()},title:"Add a Card"}),d.jsx(l,{title:e==null?void 0:e.action,mode:"manual",action:e==null?void 0:e.action,multiple:!1,onSuccess:()=>{R()},inputConfirmation:!1,onClose:()=>{x("subscribe",!1),w(""),m("")},customMessage:d.jsxs("div",{className:"space-y-5",children:[d.jsxs("div",{children:["Plan: ",(B=e==null?void 0:e.selectedPrice)==null?void 0:B.name]}),d.jsx("div",{className:"flex gap-5 justify-between items-center",children:d.jsxs("span",{children:["Price ",F,(I=e==null?void 0:e.selectedPrice)==null?void 0:I.amount,y.trim()&&d.jsxs("span",{className:"ml-2 text-sm text-green-600",children:["(Coupon: ",y.trim(),")"]})]})}),d.jsxs("div",{className:"space-y-2",children:[d.jsx("label",{className:"block text-sm font-medium text-black font-iowan",children:"Coupon Code (Optional)"}),d.jsx("input",{type:"text",value:y,onChange:s=>{w(s.target.value),m("")},placeholder:"Enter coupon code (e.g., vip-access-100)",className:"px-3 py-2 w-full bg-transparent rounded-md border border-black shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"}),E&&d.jsx("p",{className:"text-sm text-red-600",children:E})]})]}),isOpen:(e==null?void 0:e.showModal)&&["subscribe"].includes(e==null?void 0:e.modal)})]})};export{we as default};
