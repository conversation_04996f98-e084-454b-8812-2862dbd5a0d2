import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{o as M}from"./yup-0917e80c.js";import{A as O,G as U,ay as V,M as q,s as C,t as G}from"./index-f2ad9142.js";import{r as i,u as S}from"./vendor-4cdf2bd1.js";import{u as B}from"./react-hook-form-a383372b.js";import{c as K,a as n}from"./yup-342a5df4.js";import{S as Y,a as H,b as J}from"./SelectCity-952884e6.js";import{u as Q}from"./useFundProfile-6ed8f0b3.js";import{b as W}from"./index.esm-54e24cf9.js";import{InteractiveButton2 as X}from"./InteractiveButton-060359e0.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./countries-912e22d5.js";import"./ChevronDownIcon-8b7ce98c.js";import"./react-icons-36ae72b7.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";function Ae(){var h,y,g,w,j,N,_;const{dispatch:P,state:t}=i.useContext(O),{dispatch:u}=i.useContext(U),{loading:Z,profileData:k,refetch:ee}=Q(),l=i.useRef(null),z=S();i.useEffect(()=>{console.log("djhjdjhdhdh"),window.scrollTo(0,0),l.current&&l.current.scrollIntoView({behavior:"smooth"})},[z.pathname,l]);const E=K({first_name:n().required("This field is required"),last_name:n().required("This field is required"),country:n(),state:n(),city:n()}),{register:p,handleSubmit:L,setError:te,reset:R,watch:A,setValue:r,formState:{errors:s,defaultValues:d,dirtyFields:D,isSubmitting:b,isDirty:F},control:f}=B({resolver:M(E),defaultValues:{first_name:t==null?void 0:t.profile.first_name,last_name:t==null?void 0:t.profile.last_name,country:t==null?void 0:t.profile.country,state:t==null?void 0:t.profile.state,city:t==null?void 0:t.profile.city}});console.log(t.company.id);async function I(o){console.log(o);try{const a=new q;let v="";if(D.logo&&o.logo instanceof FileList&&o.logo.length>0){const $=await a.upload(o.logo[0]);v=a.baseUrl()+$.url}await a.callRawAPI("/v3/api/custom/goodbadugly/fundmanager/preference",{first_name:o.first_name,last_name:o.last_name,photo:v,country:o.country,state:o.state,city:o.city},"POST"),C(u,"Changes saved")}catch(a){G(P,a.message),C(u,a.message,5e3,"error")}}const[m,c,x]=A(["photo","country","state"]);i.useEffect(()=>{c===""&&(r("state",""),r("city","")),x===""&&r("city","")},[c,x]);const T=i.useMemo(()=>m instanceof FileList&&m.length>0?URL.createObjectURL(m[0]):null,[m]);return e.jsxs("div",{ref:l,className:"gap-6 items-start p-5 pt-8 mt-10 w-full max-w-7xl sm:px-8 md:flex",children:[e.jsxs("div",{className:"flex flex-col items-center rounded-md border border-[#1f1d1a] px-16 py-8",children:[e.jsx("img",{src:T||(d==null?void 0:d.photo)||"/default.png",alt:"profile",className:"h-20 min-h-20 w-20 min-w-20 rounded-[50%] object-cover sm:h-40 sm:min-h-40 sm:w-40 sm:min-w-40"}),e.jsxs("label",{className:"md:max-w-auto mt-4 flex max-w-[200px] cursor-pointer flex-row items-center justify-center gap-3 rounded-lg border border-[#1f1d1a] px-3 py-2 text-center text-lg font-medium capitalize capitalize",children:[e.jsx("input",{type:"file",...p("photo"),className:"hidden"}),e.jsx(W,{className:"min-h-5 min-w-5"}),e.jsxs("span",{className:"text-sm whitespace-nowrap font-iowan-regular",children:[" ","Upload Profile Picture"]})]})]}),e.jsxs("form",{className:"mb-20 mt-8 max-w-[500px] flex-grow md:mt-0",onSubmit:L(I),children:[e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block cursor-pointer font-iowan text-base font-bold capitalize capitalize text-[#1f1d1a]",children:"First name"}),e.jsx("input",{type:"text",autoComplete:"off",...p("first_name"),className:`w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(h=s.first_name)!=null&&h.message?"border-red-500":""}`}),e.jsx("p",{className:"italic text-red-500 text-field-error",children:(y=s.first_name)==null?void 0:y.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block cursor-pointer font-iowan text-base font-bold capitalize capitalize text-[#1f1d1a]",children:"Last name"}),e.jsx("input",{type:"text",autoComplete:"off",...p("last_name"),className:`w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(g=s.last_name)!=null&&g.message?"border-red-500":""}`}),e.jsx("p",{className:"italic text-red-500 text-field-error",children:(w=s.last_name)==null?void 0:w.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block cursor-pointer font-iowan text-base font-bold capitalize capitalize text-[#1f1d1a]",children:"Title"}),e.jsx("input",{type:"text",autoComplete:"off",readOnly:!0,value:V[t==null?void 0:t.profile.role],className:`w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(j=s.role)!=null&&j.message?"border-red-500":""}`})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block cursor-pointer font-iowan text-base font-bold capitalize capitalize text-[#1f1d1a]",children:"Fund"}),e.jsx("input",{readOnly:!0,type:"text",autoComplete:"off",value:k.display_name,className:`w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(N=s.company_name)!=null&&N.message?"border-red-500":""}`}),e.jsx("p",{className:"italic text-red-500 text-field-error",children:(_=s.company_name)==null?void 0:_.message})]}),e.jsx(Y,{control:f,name:"country",setValue:o=>r("country",o)}),e.jsx(H,{control:f,name:"state",setValue:o=>r("state",o),country:c}),e.jsx(J,{control:f,name:"city",setValue:o=>r("city",o),country:c,state:x}),e.jsxs("div",{className:"flex gap-4 justify-end items-center mt-6",children:[" ",F?e.jsx("button",{className:"h-[40px] rounded-md border border-[#1f1d1a] px-4 py-2 font-iowan text-[10px] font-medium sm:text-[12px]    xl:text-base",type:"button",onClick:()=>R(),children:"Discard Changes"}):null,e.jsx(X,{loading:b,disabled:b,type:"submit",className:`whitespace-nowr disabled:bg-disabledblack h-[40px]  w-[115px] min-w-fit rounded-md bg-primary-black px-6 py-2 text-center text-[10px] font-semibold font-semibold text-white transition-colors \r
duration-100 sm:!text-[12px] md:!w-[146px]  xl:!text-base`,children:"Save changes"})]})]})]})}export{Ae as default};
