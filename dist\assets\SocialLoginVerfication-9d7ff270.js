import{j as p}from"./@nextui-org/listbox-0f38ca19.js";import{h as y,R as w,r as s,b as x,u as L}from"./vendor-4cdf2bd1.js";import{A as v,G as E,b as A,L as I,s as i,M as k,c as G}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const H=()=>{const[m]=y(),{dispatch:g}=w.useContext(v),{dispatch:a}=s.useContext(E),{tdk:u,sdk:R}=A();let n=m.get("data");const e=x(),f=L(),[h,b]=s.useState(!0);return s.useEffect(()=>{(async()=>{var l,c;try{if(!n){i(a,"Invalid login data",6e3,"error"),e("/member/login");return}const o=JSON.parse(n);if(console.log("Social login data:",o),g({type:"LOGIN",payload:o}),!o.role){i(a,"User already registered with a Gmail account",6e3,"warning"),setTimeout(()=>{e("/member/login")},1e3);return}localStorage.setItem("role",o.role),localStorage.setItem("token",o.token);const d=new k,r=await d.callRawAPI(`/v3/api/custom/goodbadugly/get-profile/${o.user_id}`,{},"GET"),t=r==null?void 0:r.data;if(console.log("User profile:",t),t!=null&&t.oauth&&(t==null?void 0:t.step)==1)try{console.log("Updating OAuth user step from 1 to 3"),await d.callRawAPI("/v3/api/custom/goodbadugly/users/send-google-welcome",[],"GET"),await u.update("user",t.id,{step:3}),console.log("Successfully updated OAuth user step")}catch(S){console.error("Error updating OAuth user step:",S)}t!=null&&t.is_onboarded?e(`/${G(o.role,f)}/dashboard`):e("/member/get-started")}catch(o){console.error("Error in social login verification:",o),i(a,((c=(l=o==null?void 0:o.response)==null?void 0:l.data)==null?void 0:c.message)||"Error during login verification",6e3,"error"),e("/member/login")}finally{b(!1)}})()},[]),h?p.jsx(I,{brand:!0}):p.jsx("div",{children:"Redirecting..."})};export{H as default};
