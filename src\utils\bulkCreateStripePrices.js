/**
 * Bulk Stripe Prices Creation Script
 * 
 * This script creates multiple Stripe prices at once using the existing API endpoints.
 * Run this in the browser console while logged into the admin panel.
 * 
 * IMPORTANT: Update the productMapping object with the actual product IDs 
 * returned from the bulk product creation script.
 * 
 * Usage:
 * 1. Update the productMapping with your actual product IDs
 * 2. Copy this entire script
 * 3. Paste it in the browser console
 * 4. Call: await bulkCreateStripePrices()
 */

const bulkCreateStripePrices = async () => {
  // ⚠️  UPDATE THESE IDs WITH THE ACTUAL IDs FROM YOUR CREATED PRODUCTS
  const productMapping = {
    'free': null,        // Free plan doesn't need prices
    'pro': 5,           // Replace with actual ID from product creation
    'business': 6,      // Replace with actual ID from product creation  
    'enterprise': 7     // Replace with actual ID from product creation
  };

  // Define the prices to create
  const prices = [
    // Monthly plans
    {
      product_id: productMapping.pro,
      name: "pro monthly",
      amount: 29.99,
      type: "recurring",
      interval: "month",
      interval_count: 1,
      usage_type: "licenced"
    },
    {
      product_id: productMapping.business,
      name: "business monthly",
      amount: 49.99,
      type: "recurring", 
      interval: "month",
      interval_count: 1,
      usage_type: "licenced"
    },
    {
      product_id: productMapping.enterprise,
      name: "enterprise monthly",
      amount: 89.99,
      type: "recurring",
      interval: "month", 
      interval_count: 1,
      usage_type: "licenced"
    },
    // Yearly plans (with discount)
    {
      product_id: productMapping.pro,
      name: "pro yearly",
      amount: 287.99, // ~20% discount
      type: "recurring",
      interval: "year",
      interval_count: 1,
      usage_type: "licenced"
    },
    {
      product_id: productMapping.business,
      name: "business yearly", 
      amount: 479.99, // ~20% discount
      type: "recurring",
      interval: "year",
      interval_count: 1,
      usage_type: "licenced"
    },
    {
      product_id: productMapping.enterprise,
      name: "enterprise yearly",
      amount: 863.99, // ~20% discount
      type: "recurring",
      interval: "year",
      interval_count: 1,
      usage_type: "licenced"
    }
  ];

  console.log('🚀 Starting bulk price creation...');
  console.log(`💰 Creating ${prices.length} prices`);
  
  // Validate product IDs
  const missingProducts = prices.filter(price => !price.product_id);
  if (missingProducts.length > 0) {
    console.error('❌ Missing product IDs! Please update the productMapping object.');
    console.error('Missing IDs for:', missingProducts.map(p => p.name));
    return { success: false, error: 'Missing product IDs in productMapping' };
  }
  
  const createdPrices = [];
  const errors = [];
  
  // Get auth token and project encoding
  const token = localStorage.getItem('token') || localStorage.getItem('admin_token');
  const projectId = 'goodbadugly';
  const secret = 'i3k2c2k8kjp9ook0m14mhrtcwpgdd8g';
  const base64Encode = btoa(projectId + ':' + secret);
  
  if (!token) {
    console.error('❌ No authentication token found. Please log in first.');
    return { success: false, error: 'No authentication token found' };
  }
  
  for (let i = 0; i < prices.length; i++) {
    const price = prices[i];
    
    try {
      console.log(`\n💵 Creating price ${i + 1}/${prices.length}: "${price.name}" ($${price.amount})`);
      
      const response = await fetch('https://api.updatestack.com/v2/api/lambda/stripe/price', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-project': base64Encode,
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(price)
      });
      
      const result = await response.json();
      
      if (result.error) {
        console.error(`❌ Error creating "${price.name}":`, result.message);
        errors.push({
          price: price.name,
          error: result.message,
          validation: result.validation
        });
      } else {
        console.log(`✅ Successfully created "${price.name}" with ID: ${result.model}`);
        createdPrices.push({
          name: price.name,
          id: result.model,
          amount: price.amount,
          interval: price.interval,
          product_id: price.product_id,
          stripe_id: result.stripe_id || null
        });
      }
      
      // Add a small delay to avoid rate limiting
      if (i < prices.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
    } catch (error) {
      console.error(`❌ Network error creating "${price.name}":`, error.message);
      errors.push({
        price: price.name,
        error: error.message
      });
    }
  }
  
  // Summary
  console.log('\n📊 BULK CREATION SUMMARY');
  console.log('========================');
  console.log(`✅ Successfully created: ${createdPrices.length} prices`);
  console.log(`❌ Failed: ${errors.length} prices`);
  
  if (createdPrices.length > 0) {
    console.log('\n🎉 Created Prices:');
    createdPrices.forEach(price => {
      console.log(`  • ${price.name} - $${price.amount}/${price.interval} (ID: ${price.id})`);
    });
  }
  
  if (errors.length > 0) {
    console.log('\n⚠️  Errors:');
    errors.forEach(error => {
      console.log(`  • ${error.price}: ${error.error}`);
    });
  }
  
  console.log('\n🎯 All done! Your Stripe products and prices are ready.');
  
  return {
    success: errors.length === 0,
    created: createdPrices,
    errors: errors,
    summary: {
      total: prices.length,
      successful: createdPrices.length,
      failed: errors.length
    }
  };
};

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { bulkCreateStripePrices };
}

console.log('📋 Bulk Stripe Prices Creation Script Loaded');
console.log('💻 Run: await bulkCreateStripePrices()');
console.log('⚠️  Remember to update productMapping with actual product IDs first!');
