import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{f as a,$ as o}from"./index-f2ad9142.js";import"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const R=({onSort:s,columns:t,actions:p,actionPostion:m,areAllRowsSelected:l,handleSelectAll:d})=>r.jsx(r.Fragment,{children:r.jsxs("tr",{className:"flex !h-[2.25rem] !max-h-[2.25rem] !min-h-[2.25rem] overflow-hidden",children:[t!=null&&t.length?null:r.jsx("th",{scope:"col",className:"!w-full !min-w-full !max-w-full ",children:r.jsx(a,{count:1,counts:[1],className:"!m-0 !h-full !p-0"})}),t.map((e,i)=>{if((e==null?void 0:e.accessor)!==""&&(e!=null&&e.selected_column))return r.jsx("th",{scope:"col",className:`flex !w-[6.25rem] !min-w-[6.25rem] max-w-[auto] shrink-0 grow cursor-pointer justify-start px-[.75rem] py-[.5rem] text-left text-sm font-[400] capitalize leading-[1.5rem] tracking-wider text-sub-500 ${e.isSorted?"cursor-pointer":""} `,onClick:e.isSorted?()=>s(i):void 0,children:r.jsxs("div",{className:"flex !w-auto !min-w-fit max-w-[auto] shrink-0  items-center justify-start gap-2",children:[e.header,r.jsx("span",{className:"shrink-0",children:e.isSorted?r.jsx(o,{className:`h-2 w-2 ${e.isSortedDesc?"rotate-180":""}`}):""})]})},i)})]})});export{R as default};
