import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import"./vendor-4cdf2bd1.js";import{S as f}from"./react-loading-skeleton-f57eafae.js";import"./@nextui-org/theme-345a09ed.js";const j=({className:l="",count:o=5,counts:e=[2,1,3,1,1],circle:a=!1})=>t.jsx("div",{className:`flex overflow-hidden flex-col gap-5 p-4 w-full max-h-screen h-fit min-h-fit ${l}`,children:Array.from({length:o}).map((m,r)=>t.jsx(f,{count:e[r]??1,height:e[r]&&e[r]>1||r+1===o?25:80,circle:a},`${m}${r}`))});export{j as default};
