import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{a as N,u as k,L as a,l as v,I as U}from"./index-f2ad9142.js";import{M as S}from"./index-713720be.js";import{u as C}from"./useSubscription-dc563085.js";import{r as n,b as I}from"./vendor-4cdf2bd1.js";import{M as b}from"./index-49e40c51.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const P={pro:5,"pro yearly":5,business:10,"business yearly":10,enterprise:1/0},X=()=>{const{setGlobalState:c,globalState:s}=N(),{profile:r}=k(),[h,o]=n.useState(!1),[j,l]=n.useState(!1);I();const{loading:L,data:e,processRegisteredDate:y,getSubscription:g,getCustomerSubscription:d,getSentUpdates:w}=C();return n.useEffect(()=>{r!=null&&r.id&&(d(),g({filter:[`user_id,eq,${r==null?void 0:r.id}`]}),y(r==null?void 0:r.create_at),w(r),s!=null&&s.refreshSubscription&&c("refreshSubscription",!1))},[r==null?void 0:r.id,s==null?void 0:s.refreshSubscription]),n.useEffect(()=>{var m,p,f,x;if(e){if(e!=null&&e.trial_expired&&!(e!=null&&e.subscription)){o(!0);return}const i=(x=(f=(p=(m=e==null?void 0:e.object)==null?void 0:m.plan)==null?void 0:p.nickname)==null?void 0:f.split(" ")[0])==null?void 0:x.trim(),u=P[i]||(i!=null&&i.includes("enterprise")?1/0:0);(e==null?void 0:e.sentUpdates)>=u&&u!==1/0?o(!0):o(!1)}},[e]),t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"flex h-full w-full items-center justify-center",children:[" ",t.jsxs("div",{className:"flex flex-col items-center justify-center gap-[1.5rem] text-center",children:[t.jsxs("div",{className:"flex w-full flex-col items-center justify-center text-center",children:[t.jsx("div",{className:"no-updates-icon",children:t.jsx(a,{children:t.jsx(v,{fill:"black",className:"!h-[3.75rem] !w-[3.75rem]"})})}),t.jsx("br",{}),t.jsx("div",{className:"font-iowan text-[20px] font-[700] md:text-[1.5rem] md:leading-[1.865rem]  ",children:"You Have No Updates"}),t.jsx("br",{}),t.jsx("p",{className:"font-inter text-[1rem] font-[400] leading-[1.5rem]",children:"Keep your team/stakeholders up to date with"}),t.jsx("p",{className:"font-inter text-[1rem] font-[400] leading-[1.5rem]",children:"company updates"})]}),t.jsx(a,{children:h?t.jsx(S,{display:t.jsx("button",{onClick:()=>{l(!0)},className:"flex h-[2.75rem] w-fit items-center justify-center rounded-[.0625rem] bg-[#1f1d1a] px-5 py-2 font-iowan tracking-wide text-white opacity-50",children:e!=null&&e.trial_expired&&!(e!=null&&e.subscription)?"Subscribe":"Upgrade Plan"}),openOnClick:!1,backgroundColor:"#1f1d1a",place:"top",children:t.jsx("div",{className:"px-1 py-3 text-white sm:p-3",children:t.jsx("p",{className:"text-[12px]",children:e!=null&&e.trial_expired&&!(e!=null&&e.subscription)?"Please Upgrade your account to create an update!":"You have reached your monthly update limit for your current plan."})})}):t.jsx(U,{type:"button",className:"flex h-[2.75rem] w-fit items-center justify-center rounded-[.0625rem] bg-[#1f1d1a] px-5 py-2 font-iowan tracking-wide text-white",color:"black",onClick:()=>{c("triggerUpdate",!0)},children:"Create Update"})})]})]}),t.jsx(a,{children:t.jsx(b,{isOpen:j,onClose:()=>{l(!1)},currentPlan:e==null?void 0:e.subscription,onSuccess:()=>{d()}})})]})};export{X as default};
