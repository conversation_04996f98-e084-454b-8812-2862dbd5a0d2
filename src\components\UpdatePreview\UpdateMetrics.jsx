import moment from "moment";
import React, { useState, useRef } from "react";
import { IoMdArrowDropdown } from "react-icons/io";
import { Popover, Transition } from "@headlessui/react";
import { Fragment } from "react";
import { InformationCircleIcon } from "@heroicons/react/24/solid";

export const InfoPopover = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const timeoutRef = useRef(null);

  const handleMouseEnter = () => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    setIsOpen(true);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setIsOpen(false);
    }, 100);
  };

  return (
    <Popover className="inline relative">
      <div onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
        <Popover.Button className={"ml-0"}>
          <InformationCircleIcon
            className={`w-4 h-4 cursor-pointer mt-[3px]`}
            pathClasses={`text-[#1f1d1a]`}
            stroke={"white"}
          />
        </Popover.Button>
        {isOpen && (
          <Transition
            show={true}
            as={Fragment}
            enter="transition ease-out duration-200"
            enterFrom="opacity-0 -translate-y-1"
            enterTo="opacity-100 translate-y-0"
            leave="transition ease-in duration-150"
            leaveFrom="opacity-100 translate-y-0"
            leaveTo="opacity-0 -translate-y-1"
          >
            <Popover.Panel
              static
              className="absolute left-0 z-10 mt-3 w-screen max-w-[200px] -translate-x-[30%] transform px-4 text-sm"
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
            >
              <div className="relative rounded-lg bg-[#1f1d1a] px-4 py-3 text-white shadow-lg ring-1 ring-[#1f1d1a]/5">
                <div className="font-medium">{children}</div>
                <div className="absolute left-1/2 top-0 h-3 w-3 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-[#1f1d1a]"></div>
              </div>
            </Popover.Panel>
          </Transition>
        )}
      </div>
    </Popover>
  );
};

const tooltips = {
  show_financial_metrics: {
    mrr: "Monthly Recurring Revenue - The predictable revenue generated each month from subscriptions and recurring payments.",
    arr: "Annual Recurring Revenue - The yearly value of all recurring revenue normalized for a 12-month period.",
    cash: "Available cash and cash equivalents that can be quickly accessed for business operations and investments.",
    burnrate:
      "The rate at which a company spends its cash reserves on operating expenses over time.",
    runway:
      "Number of months the company can continue operating at current burn rate with available cash.",
  },
  show_investment_metrics: {
    investment_stage:
      "Current stage of investment funding (e.g., Seed, Series A, Series B, etc.).",
    invested_to_date:
      "Total amount of capital raised from investors since company inception.",
    investors_on_cap_table:
      "Number of investors who own equity in the company.",
    valuation_at_last_round:
      "Company's valuation determined at the most recent funding round.",
    date_of_last_round:
      "Date when the most recent funding round was completed.",
  },
  show_hr_metric: {
    headcount:
      "Total number of full-time employees currently working in your organization.",
    turnover:
      "Percentage of employees who left the organization over the past year relative to average total employees.",
    retention_hr:
      "Percentage of employees who remained with the organization over the past year.",
    satisfaction:
      "Employee Net Promoter Score (eNPS) measuring employee satisfaction and loyalty on a -100 to +100 scale.",
    revenue_per_employee:
      "Average revenue generated per employee, calculated by dividing total revenue by number of employees.",
  },
  show_product_metric: {
    npr: "Number of new product versions, features, or updates released during this period.",
    nps: "Total number of new product sales or subscriptions acquired in this period.",
    roi: "Return on Investment - Measures the profitability of product investments relative to their costs.",
    rd: "Research and Development expenses as a percentage of total sales, indicating investment in innovation.",
    on_time_delivery:
      "Percentage of product deliveries or releases that meet scheduled deadlines.",
  },
  show_marketing_metrics: {
    cac: "Customer Acquisition Cost - Average cost to acquire a new customer, including marketing and sales expenses.",
    marketing_activations:
      "Number of daily active users and new user activations from marketing efforts.",
    churn_rate:
      "Monthly rate at which customers stop using your product or service.",
    acv: "Average Contract Value - Average revenue per customer contract over a year.",
    csat: "Customer Satisfaction Score - Measures customer satisfaction through surveys and feedback.",
  },
};

const matrices = [
  {
    slug: "show_financial_metrics",
    name: "Financial Overview",
    sync: "last_sync",
  },
  {
    slug: "show_investment_metrics",
    name: "Investment Overview",
    sync: "last_sync",
  },
  { slug: "show_hr_metric", name: "Team/HR Overview", sync: "last_sync" },
  { slug: "show_product_metric", name: "Product Overview", sync: "last_sync" },
  {
    slug: "show_marketing_metrics",
    name: "Marketing Overview",
    sync: "last_sync",
  },
];
const matriceProperties = {
  // last_sync
  show_financial_metrics: [
    {
      property: "MRR",
      key: "mrr",
    },
    {
      property: "ARR",
      key: "arr",
    },
    {
      property: "Cash",
      key: "cash",
    },
    {
      property: "Burnrate",
      key: "burnrate",
    },
    {
      property: "Runway (months)",
      key: "burnrate",
    },
  ],
  // investment_details_last_sync
  show_investment_metrics: [
    {
      property: "Investment Stage",
      key: "investment_stage",
    },
    {
      property: "Invested to Date",
      key: "invested_to_date",
    },
    {
      property: "Investors On Cap Table",
      key: "investors_on_cap_table",
    },
    {
      property: "Valuation At Last Round",
      key: "valuation_at_last_round",
    },
    {
      property: "Date Of Last Round",
      key: "date_of_last_round",
    },
  ],
  show_hr_metric: [
    {
      property: "Headcount",
      key: "headcount",
    },
    {
      property: "Turnover rate (annual)",
      key: "turnover",
    },
    {
      property: "Retention rate (annual)",
      key: "retention_hr",
    },
    {
      property: "Employee satisfaction (employee NPS)",
      key: "satisfaction",
    },
    {
      property: "Revenue per employee",
      key: "revenue_per_employee",
    },
  ],
  show_product_metric: [
    {
      property: "New Product Releases",
      key: "npr",
    },
    {
      property: "New Product Sales",
      key: "nps",
    },
    {
      property: "ROI",
      key: "roi",
    },
    {
      property: "R&D (as a % of sales) ",
      key: "rd",
    },
    {
      property: "On-time delivery",
      key: "on_time_delivery",
    },
  ],
  show_marketing_metrics: [
    {
      property: "Customer Acquisition Cost (CAC)",
      key: "cac",
    },
    {
      property: "Activations / Daily active users",
      key: "marketing_activations",
    },
    {
      property: "Retention / Churn rate (Monthly)",
      key: "churn_rate",
    },
    {
      property: "Annual contract value (ACV)",
      key: "acv",
    },
    {
      property: "Customer Satisfaction Score (CSAT/NPS)",
      key: "csat",
    },
  ],
};
const UpdateMetrics = ({ update = null }) => {
  const [overviewDropdown, setOverviewDropdown] = useState({
    show_financial_metrics: false,
    show_investment_metrics: false,
    show_hr_metric: false,
    show_product_metric: false,
    show_marketing_metrics: false,
  });

  // Helper function to check if a value is empty or zero
  const isEmptyOrZero = (value) => {
    return (
      value === null ||
      value === undefined ||
      value === "" ||
      value === 0 ||
      value === "0"
    );
  };

  // Helper function to check if all metrics in a section are empty
  const areAllMetricsEmpty = (slug) => {
    return matriceProperties[slug]?.every((item) =>
      isEmptyOrZero(update?.[item.key])
    );
  };

  // Check if all enabled sections are empty
  const areAllSectionsEmpty = matrices.every(
    (matrice) => !update?.[matrice?.slug] || areAllMetricsEmpty(matrice.slug)
  );

  if (areAllSectionsEmpty) {
    return <div className="hidden"></div>;
  }

  {
    /* <div className="my-2 flex w-full items-center justify-center px-4 text-[1rem]">
                  <span className="font-semibold">Last sync:</span>{" "}
                  <span className="block sm:ml-auto">
                    {update?.last_sync
                      ? moment(update?.last_sync).format("DD MMM hh:mm a")
                      : "N/A"}
                  </span>
                </div> */
  }
  // 48+24=72
  return (
    <div className="mt-[35px] grid grid-cols-1 border-b-2 border-b-[#1f1d1a] md:grid-cols-2 md:pb-[2.5rem] ">
      {matrices?.map((matrice, matriceKey) => {
        if (update?.[matrice?.slug] == 1 && !areAllMetricsEmpty(matrice.slug)) {
          return (
            <div
              key={matriceKey}
              className={` flex min-h-[6rem] flex-col justify-start rounded-sm pb-[1.5rem] ${
                matriceKey % 2 != 0 ? "mr-5 md:border-r" : ""
              } md:border-r-gray-900 md:pr-4`}
            >
              <div className="flex h-full min-h-full flex-col justify-between border-b  md:border-b-[#1F1D1A1A]">
                <div>
                  <div className="flex flex-row justify-between items-center">
                    <h4 className="mb-3 whitespace-nowrap font-Inter text-[1.25rem] font-[600] leading-[1.5125rem] md:mb-0">
                      {matrice?.name}
                    </h4>
                    <div
                      className="flex flex-row gap-3 items-center cursor-pointer"
                      onClick={() => {
                        setOverviewDropdown((prev) => ({
                          ...prev,
                          [matrice?.slug]: !prev?.[matrice?.slug],
                        }));
                      }}
                    >
                      <span className="font-bold underline font-iowan">
                        {overviewDropdown?.[matrice?.slug] ? "Close" : "Expand"}
                      </span>
                      <IoMdArrowDropdown />
                    </div>
                  </div>
                  <div className="flex w-full items-center justify-start gap-2 text-[1rem]">
                    <span className="font-iowan text-[1rem] font-[400] leading-[1.25rem] text-[#57514d]">
                      Last sync:
                    </span>{" "}
                    <span className="font-iowan text-[1rem] font-[400] leading-[1.25rem] text-[#57514d]">
                      {update?.[matrice?.sync]
                        ? moment(update?.[matrice?.sync]).format(
                            "DD MMM hh:mm a z"
                          )
                        : "N/A"}
                    </span>
                  </div>
                </div>

                {overviewDropdown[matrice?.slug] ? (
                  <ul className="mt-4">
                    {matriceProperties?.[matrice?.slug]?.map(
                      (item, itemKey) => {
                        return (
                          <li
                            key={itemKey}
                            className="flex flex-row items-center justify-between gap-3 border-b border-b-[#1f1d1a]/20 py-3 sm:gap-0"
                          >
                            <span className="flex gap-1 items-center font-medium font-iowan-regular">
                              {item?.property}
                              <InfoPopover>
                                {tooltips[matrice?.slug]?.[item?.key]}
                              </InfoPopover>
                            </span>{" "}
                            <span className="block sm:ml-auto">
                              {update?.[item?.key]}
                            </span>
                          </li>
                        );
                      }
                    )}
                  </ul>
                ) : null}
              </div>
            </div>
          );
        }
      })}
    </div>
  );
};

export default UpdateMetrics;
