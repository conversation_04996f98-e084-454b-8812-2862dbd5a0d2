import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{A as je,G as Le,M as de,s as ye,t as Me,b as He,a as qe,O as Fe,L as E,i as Oe,y as Be,z as Ge}from"./index-f2ad9142.js";import{R as Ve,C as Ye,a as ve,b as ze,c as Ke,d as Je,e as Qe,f as We,g as Xe,h as Ze}from"./index-d20ea84b.js";import{r as c,u as De,b as et,h as tt,i as nt}from"./vendor-4cdf2bd1.js";import"./index-b8adfdf8.js";import{h as be}from"./moment-a9aaa855.js";import{u as st}from"./useRecentEngagements-913e39ba.js";import"./yup-342a5df4.js";import{_ as ot}from"./MoonLoader-6f2b5db4.js";import{C as at}from"./ChevronDownIcon-8b7ce98c.js";import{H as D,t as rt}from"./@headlessui/react-cdd9213e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const it=[{id:1,reason:"I don’t need to send updates to my current team any longer"},{id:2,reason:"I’ve changed positions/companies and no longer need to send updates"},{id:3,reason:"I’ve decided to send updates with another service"},{id:4,reason:"The product isn’t worth it for me, too few features"},{id:5,reason:"It’s too expensive"},{id:6,reason:"Product too complicated"},{id:7,reason:"Forgot to cancel when free trial ended"},{id:8,reason:"I always cancel auto-renew subscriptions"},{id:10,reason:"I only intended to use during free trial"},{id:11,reason:"It’s easier sending updates manually via word/google doc, notion, or via email"},{id:2,reason:"other"}],dt=({setSteps:U,setModal:Y,setReason:ce,reason:q,suggestion:z,setSuggestion:K})=>{const{dispatch:_,state:ee}=c.useContext(je),{dispatch:F}=c.useContext(Le);c.useState(!1),c.useState([]);const[te,O]=c.useState(!1);c.useState([]);const[R,ne]=c.useState(""),[N,L]=c.useState({reason:"",password:R}),se=Object.keys(q).length===0;async function M(v){if(v.preventDefault(),se)L({...N,reason:"This field is required"});else if(R==="")L({...N,password:"This field is required"});else{O(!0);try{const J=await new de().callRawAPI("/v3/api/custom/goodbadugly/confirm-password",{password:R},"POST");O(!1),ye(F,J.message),console.log(J),U(2)}catch(b){O(!1),Me(_,b.message),ye(F,b.message,5e3,"error")}}}return e.jsxs("div",{className:"mt-9 flex h-[inherit] flex-col justify-between space-y-10",children:[e.jsxs("div",{className:"flex flex-row justify-between gap-3",children:[e.jsxs("span",{className:"text-[16px] font-[500] leading-7",children:["What has made you consider ",e.jsx("br",{})," cancelling?"," ",e.jsx("span",{className:"text-[19px] text-red-600",children:"*"})]}),e.jsxs("div",{className:"w-full max-w-[600px]",children:[e.jsxs(D,{value:q,onChange:ce,as:"div",className:"relative text-left ",children:[e.jsxs(D.Button,{className:"relative inline-flex  w-full rounded-md border px-4 py-2 text-lg font-medium focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75",children:[q.id?q.reason:"Select reason",e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center border-l border-black/60 p-2",children:e.jsx(at,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})})]}),e.jsx(rt,{as:c.Fragment,enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:e.jsx(D.Options,{className:"absolute right-0 mt-2 w-full origin-top divide-y divide-gray-100 rounded-md bg-brown-main-bg shadow-lg ring-1 ring-[#1f1d1a]/5 focus:outline-none",children:e.jsx("div",{className:"",children:it.map(v=>e.jsx(D.Option,{value:v,children:({active:b})=>e.jsx("button",{className:`${b?"bg-brown-main-bg":""} w-full px-4 py-2 text-left text-lg font-medium`,children:v.reason})},v.id))})})})]}),se&&e.jsx("p",{className:"text-field-error mt-1 text-end text-[15px] italic text-red-500",children:N.reason})]})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-[16px] font-[500] leading-7",children:"What should we improve?"}),e.jsx("textarea",{name:"",className:"rounded",id:"",cols:"30",rows:"8",placeholder:"Enter suggestion",value:z,onChange:v=>K(v.target.value)})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("div",{className:"text-[16px] font-[500] leading-7",children:["Password reconfirmation",e.jsx("span",{className:"text-[19px] text-red-600",children:"*"})]}),e.jsx("input",{type:"password",className:"rounded",placeholder:"********",onChange:v=>ne(v.target.value)}),e.jsxs("span",{className:"mt-1 text-gray-500",children:["This is required for account owner verification and prevention of unauthorized account deletion."," "]}),R===""&&e.jsx("p",{className:"text-field-error text-[15px] italic text-red-500",children:N.password})]}),e.jsxs("div",{className:"mb-5 flex flex-row items-center justify-between",children:[e.jsxs("button",{className:"flex flex-row items-center gap-1 rounded bg-red-500 p-3 text-[17px] font-[500] text-white",onClick:v=>M(v),children:[e.jsx(ot,{loading:te,size:15,color:"#ffffff"}),"Continue"]}),e.jsx("span",{className:"cursor-pointer text-[17px] text-sky-600 underline",onClick:()=>Y(!1),children:"← Nevermind, keep my account"})]})]})},Ct=({isPublic:U=!1})=>{var we;const{sdk:Y,tdk:ce}=He(),{engagements:q,refetch:z}=st(),{state:K}=c.useContext(je);Y.getProjectId();const _=De(),ee=et(),[F,te]=c.useState(!0),[O]=tt(),R=O.get("new");c.useEffect(()=>{const o=_.pathname.includes("/public/view/");b("isPublicView",o),console.log("Setting public view state:",o,_.pathname)},[_.pathname]);const ne=async()=>{try{await new de().callRawAPI(`/v3/api/custom/goodbadugly/activities/${r==null?void 0:r.id}/view`,{},"PUT")}catch(o){console.error("Error calling view endpoint:",o)}};c.useEffect(()=>{R=="new"&&ne()},[]),c.useEffect(()=>()=>{z()},[]);const{globalDispatch:N,authState:L,authDispatch:se,custom:M,getSingle:v,setGlobalState:b,getMany:J,globalState:H}=qe();localStorage.getItem("token");const[le,_e]=c.useState([]),[B,oe]=c.useState(!0),[r,Se]=c.useState(null),[Q,Ue]=c.useState(),[ue,Ne]=c.useState(""),[pe,Ee]=c.useState(null),[Ie,me]=c.useState(""),{update_id:k,public_link_id:fe}=nt(),[Te,ge]=c.useState(null),[s,W]=c.useState({newUpdates:[],currentUpdate:null,updateNames:[],index:0,previousUpdate:null,nextUpdate:null}),he=c.useRef(null);async function ke(o){const n={private:async()=>{var a;return await v("companies",(a=r==null?void 0:r.companies)==null?void 0:a.id,{method:"GET"})},public:async()=>{var a;return await M({endpoint:`/v3/api/custom/goodbadugly/user/company/${(a=r==null?void 0:r.companies)==null?void 0:a.id}`,method:"GET"})}};try{const a=n==null?void 0:n[o];if(!a)return;const l=await a(),t=(l==null?void 0:l.data)??{};Ee(()=>({...t,socials:t!=null&&t.socials?Object.entries(JSON.parse(t==null?void 0:t.socials)).map(([d,p])=>{if(!Oe(p))return{key:d,value:p}}).filter(Boolean):[]}))}catch(a){showToast(a.message,5e3,"error"),tokenExpireError(a.message)}finally{}}async function Ae(o){var a,l,t,d,p,f,x,h,g,m,j,A,C,T,G;oe(!0);const n={private:async()=>{var i;const S=await v("updates",Number(k),{join:["companies|company_id","notes","update_questions","update_comments","update_reaction","update_comment_replies","update_question_answers"],method:"GET"}),w={update_id:k};return await Y.callRawAPI("/v3/api/custom/goodbadugly/mentions/mark-all-seen",w,"PUT"),b("mentionsChange",!(H!=null&&H.mentionsChange)),(i=S==null?void 0:S.data)!=null&&i.notes&&Array.isArray(S.data.notes)&&S.data.notes.sort((I,P)=>{const V=I.order!==void 0&&I.order!==null?I.order:Number.MAX_SAFE_INTEGER,$=P.order!==void 0&&P.order!==null?P.order:Number.MAX_SAFE_INTEGER;return V-$}),S},public:async()=>await M({endpoint:`/v3/api/custom/goodbadugly/public/updates/${k}/${fe}`,method:"GET"})};try{let S=function(y){return y==null?void 0:y.replace(/<[^>]*>/g,"")};const w=n==null?void 0:n[o];if(!w)return;const i=await w();Se(i==null?void 0:i.data),me((a=i==null?void 0:i.data)==null?void 0:a.update_question_answers),_e((l=i==null?void 0:i.data)==null?void 0:l.notes);const I=(d=(t=i==null?void 0:i.data)==null?void 0:t.notes)==null?void 0:d.map(y=>{const{blocks:Z}=Be(y==null?void 0:y.content,{blocks:[]});if(Z.length==0)return null;const Re=Ge(Z);return{title:y==null?void 0:y.type,item:Re}}),P=I==null?void 0:I.map(y=>S(y==null?void 0:y.item)).join(`
`),V=[{role:"user",content:` Financial overview. MRR: ${(p=i==null?void 0:i.data)==null?void 0:p.mrr}, ARR: ${(f=i==null?void 0:i.data)==null?void 0:f.arr}, Cash: ${(x=i==null?void 0:i.data)==null?void 0:x.cash}, Burnrate: ${(h=i==null?void 0:i.data)==null?void 0:h.burnrate}, Runway (months): ${(g=i==null?void 0:i.data)==null?void 0:g.burnrate}.: investment overview. Investment stage: ${(m=i==null?void 0:i.data)==null?void 0:m.investment_stage}, Invested to date: ${(j=i==null?void 0:i.data)==null?void 0:j.invested_to_date}, Fund managers on Cap Table: ${(A=i==null?void 0:i.data)==null?void 0:A.investors_on_cap_table}, Valuation of last round: ${(C=i==null?void 0:i.data)==null?void 0:C.valuation_at_last_round}, Date of last round: ${((T=i==null?void 0:i.data)==null?void 0:T.date_of_last_round)&&be((G=i==null?void 0:i.data)==null?void 0:G.date_of_last_round).format("MM/DD/YYYY")}.  notes: ${P}.
          summerize the above report in less than 450 words. ignore all undefined values and don't say it in your response`}],$=await M({endpoint:"/v3/api/custom/goodbadugly/ai/ask",payload:{prompt:V},method:"POST"});Ue($==null?void 0:$.data),oe(!1)}catch(S){Ne(S.message)}oe(!1)}c.useEffect(()=>{const o=n=>{(n.key==="F12"||n.ctrlKey&&n.shiftKey&&n.key==="I"||n.ctrlKey&&n.shiftKey&&n.key==="J")&&n.preventDefault()};return window.addEventListener("keydown",o),()=>{window.removeEventListener("keydown",o)}},[]),c.useEffect(()=>{Ae(U?"public":"private")},[U,k]),c.useEffect(()=>{if(s.newUpdates.length>0){const o=s.newUpdates.findIndex(n=>n==Number(k));if(o!==-1){const n=o<s.newUpdates.length-1?s.newUpdates[o+1]:null,a=o>0?s.newUpdates[o-1]:null,l=n?s.updateNames.find(d=>d.update_id===n):null,t=a?s.updateNames.find(d=>d.update_id===a):null;W(d=>({...d,currentUpdate:Number(k),index:o,previousUpdate:l,nextUpdate:t}))}}},[s.newUpdates,s.updateNames,r==null?void 0:r.id]),c.useEffect(()=>{ge({id:"overview",index:0}),N({type:"SETPATH",payload:{path:"overview",sectionIndex:0,hasDuplicates:!1}})},[]);const X=c.useCallback(o=>{const n=o.target,a=n.querySelectorAll("[id]"),l=n.scrollTop,t=n.clientHeight,d=n.scrollHeight,p=l+t,f=d-p<10,x=l<10;if(console.log("--- Scroll Event Debug ---"),console.log("Scroll Position:",l),console.log("Container Height:",t),console.log("Scroll Bottom:",p),console.log("Is at bottom:",f),console.log("Is at top:",x),x){const g=Array.from(a).find(m=>m.id==="overview");if(g){ae(g);return}}if(f){const g=Array.from(a).find(m=>m.id==="summary");if(g){ae(g);return}}let h=null;for(const g of a){g.getBoundingClientRect();const m=g.offsetTop-n.offsetTop,j=m<=l+50;j&&(h=g),console.log("Section Debug:",{id:g.id,noteId:g.getAttribute("data-note-id"),top:m,isInView:j})}h&&ae(h)},[Te,N,le]),ae=c.useCallback(o=>{const n=o.getAttribute("data-note-id"),a=o.id;console.log("Most Visible Section:",{id:a,noteId:n}),ge(a),b("noteId",n),N({type:"SETPATH",payload:{path:a,noteId:n}})},[N,b]);c.useEffect(()=>{const o=he.current;return o&&(console.log("Attaching scroll listener to container"),o.addEventListener("scroll",X,{passive:!0}),X({target:o})),()=>{o&&o.removeEventListener("scroll",X)}},[X]),c.useEffect(()=>{var n;if(U)return;const o=(n=r==null?void 0:r.notes)==null?void 0:n.map(a=>{var p,f,x;const l=((p=r==null?void 0:r.update_comments)==null?void 0:p.filter(h=>h.note_id===a.id))||[],t=((f=r==null?void 0:r.update_comment_replies)==null?void 0:f.filter(h=>h.note_id===a.id))||[],d=((x=r==null?void 0:r.update_reaction)==null?void 0:x.filter(h=>h.note_id===a.id))||[];return{...a,counts:{comments:l.length,replies:t.length,reactions:d.length}}});b("updateSideNotes",o),b("currentUpdate",r==null?void 0:r.name),b("updateQuestions",r==null?void 0:r.update_questions)},[r==null?void 0:r.notes]),c.useEffect(()=>{if(_!=null&&_.hash&&!B){const[o,n]=_.hash.split("?"),a=o.substring(1),l=decodeURIComponent(a),t=new URLSearchParams(n||""),d=t.get("from");N({type:"SETPATH",payload:{path:d==="engagement"?"engagements":l}});const f=t.get("engagement_id"),x=t.get("engagement_type");d==="engagement"&&f&&x&&Ce();const h=10;let g=0;const m=()=>{if(l.includes("comment:")){const[A,C]=l.split(":"),T=document.getElementById(C);if(T)return T.scrollIntoView({behavior:"smooth",block:"center"}),!0}else{const A=document.getElementById(l);if(A)return A.scrollIntoView({behavior:"smooth",block:"start"}),!0}return!1},j=setInterval(()=>{g++,(m()||g>=h)&&clearInterval(j)},200);return()=>clearInterval(j)}else N({type:"SETPATH",payload:{path:"updates"}})},[_==null?void 0:_.hash,B]),c.useEffect(()=>{var o;(o=r==null?void 0:r.companies)!=null&&o.id&&ke(fe?"public":"private")},[(we=r==null?void 0:r.companies)==null?void 0:we.id]);const Ce=async(o,n)=>{try{const a=await M({endpoint:"/v3/api/custom/goodbadugly/member/mark-engagement-update-viewed",method:"POST",payload:{update_id:Number(k)}},void 0,!1,null);a!=null&&a.error&&console.error("Error marking engagement as viewed:",a.message)}catch(a){console.error("Error marking engagement as viewed:",a)}},Pe=async()=>{var o;try{const n=await J("update_member",{filter:[`goodbadugly_update_member.user_id,eq,${localStorage.getItem("user")}`],join:["goodbadugly_updates|update_id,id"],fields:["goodbadugly_updates.name"],order:"goodbadugly_update_member.id,desc"});if(!(n!=null&&n.error)){const a=(o=n==null?void 0:n.data)==null?void 0:o.map(t=>({update_id:t==null?void 0:t.update_id,name:t==null?void 0:t.name,date_received:t==null?void 0:t.date_receieve,first_name:t==null?void 0:t.first_name,last_name:t==null?void 0:t.last_name,company_name:t==null?void 0:t.company_name})).filter(Boolean),l=a.map(t=>t.update_id).filter((t,d,p)=>p.indexOf(t)===d);W(t=>({...t,newUpdates:l,updateNames:a}))}}catch(n){console.log(n)}};let u=c.useRef(null);const re=async()=>new Promise(o=>{if(u!=null&&u.current)try{typeof u.current.stop=="function"&&u.current.stop(),typeof u.current.pause=="function"&&u.current.pause(),u.current.currentTime=0,u.current.src="",u.current.onended=null,u.current.oncanplaythrough=null,u.current.onerror=null,u.current=null}catch(n){console.error("Error stopping audio:",n)}setTimeout(o,200)}),xe=async()=>{var o,n,a,l,t;if((s==null?void 0:s.index)>0){await re();const d=(s==null?void 0:s.index)-1,p=(o=s==null?void 0:s.newUpdates)==null?void 0:o[d],f=(n=s==null?void 0:s.newUpdates)==null?void 0:n[d-1],x=(a=s==null?void 0:s.updateNames)==null?void 0:a.find(m=>(m==null?void 0:m.update_id)===f),h=(l=s==null?void 0:s.newUpdates)==null?void 0:l[d+1],g=(t=s==null?void 0:s.updateNames)==null?void 0:t.find(m=>(m==null?void 0:m.update_id)===h);W(m=>({...m,index:d,currentUpdate:p,previousUpdate:x,nextUpdate:g})),ee(`/member/update/private/view/${p}`)}},ie=async(o=!1)=>{var n,a,l,t,d,p;if((s==null?void 0:s.index)<((n=s==null?void 0:s.newUpdates)==null?void 0:n.length)-1){await re();const f=(s==null?void 0:s.index)+1,x=(a=s==null?void 0:s.newUpdates)==null?void 0:a[f],h=(l=s==null?void 0:s.newUpdates)==null?void 0:l[f+1],g=(t=s==null?void 0:s.updateNames)==null?void 0:t.find(w=>(w==null?void 0:w.update_id)===h),m=(d=s==null?void 0:s.newUpdates)==null?void 0:d[f-1],j=(p=s==null?void 0:s.updateNames)==null?void 0:p.find(w=>(w==null?void 0:w.update_id)===m);if(W(w=>({...w,index:f,currentUpdate:x,previousUpdate:g,nextUpdate:j})),o&&j){await new Promise(Z=>setTimeout(Z,300));const{first_name:w,last_name:i,company_name:I,name:P,date_received:V}=j;console.log(j,"nextUpdate",I);const $=be(V).format("MMMM Do, YYYY [at] h:mm A"),y=`Your next update is from ${w} ${i} from ${I||`${dt}'s Company`} titled ${P} sent to you on ${$}`;await $e(y)}const C=new URLSearchParams(window.location.search).get("listen_option"),T=new URLSearchParams;o&&T.set("autoplay","true"),C&&T.set("listen_option",C);const G=T.toString(),S=`/member/update/private/view/${x}${G?`?${G}`:""}`;ee(S)}},$e=async o=>{var n;try{await re();const a=new de;if(H!=null&&H.isPublicView){console.log("Skipping Polly synthesis - public view");return}let l=()=>{};try{const t=await a.callRawAPI("/v3/api/custom/goodbadugly/integrations/polly/synthesize",{text:o},"POST");if(t&&!t.error&&((n=t.data)!=null&&n.audioUrl)){await new Promise(f=>setTimeout(f,100));const d=new Audio(t.data.audioUrl);l=()=>{d&&(d.pause(),d.currentTime=0,d.src="",d.onended=null,d.oncanplaythrough=null,d.onerror=null,d.remove())};const p={current:d};u.current=d,d.onerror=()=>{l(),p.current=null,u.current=null},await new Promise((f,x)=>{d.oncanplaythrough=f,d.onerror=x,d.load()}),await d.play(),d.onended=()=>{l(),p.current=null,u.current=null}}else throw new Error((t==null?void 0:t.error)||"Failed to generate audio")}catch(t){throw l(),t}}catch(a){console.error("Failed to speak message:",a)}};if(c.useEffect(()=>{K.user&&Pe()},[K.user]),!U&&!(L!=null&&L.isAuthenticated)&&!B){const n=`${window.location.origin}/member/login`,a=encodeURIComponent("/member/updates?availability=available"),l=`${n}?redirect_uri=${a}`;return window.location.href=l,e.jsx("div",{className:"flex overflow-y-auto justify-center items-center h-screen -scroll-mt-6 scroll-smooth bg-brown-main-bg",children:e.jsx("h2",{children:"Redirecting to Login ...."})})}return ue?e.jsx("div",{className:"flex justify-center items-center w-full h-full text-7xl text-gray-700 min-full max-full",children:ue}):e.jsxs("div",{ref:he,className:`min-full max-full relative h-full w-full -scroll-mt-6 space-y-[2rem] scroll-smooth bg-brown-main-bg px-4 py-[1.0375rem] pb-[270px] md:px-[3.125rem] md:pb-[1.9375rem] md:pt-[2.252rem] lg:pb-[100px] ${B?"overflow-y-hidden":"overflow-y-auto"}`,children:[B?e.jsx(Fe,{loading:!0}):null,U?e.jsx(E,{children:e.jsx(Ve,{})}):null,r&&pe&&e.jsxs("div",{className:"mx-auto w-full",children:[e.jsx(E,{children:e.jsx(Ye,{companyDetails:pe,update:r})}),e.jsx("hr",{className:"my-6 hidden border-[1px] border-[#1f1d1a] md:block"}),e.jsxs("div",{className:"flex flex-col gap-5 justify-between items-start pt-5 border-t-2 border-black md:flex-row md:items-center md:border-t-0 md:pt-0",children:[e.jsx("div",{className:"block w-full md:hidden",children:!U&&e.jsx(E,{children:e.jsx(ve,{update:r,localData:s,previousUpdate:xe,nextUpdate:ie})})}),e.jsx(E,{children:e.jsx(ze,{update:r})}),e.jsx("div",{className:"hidden w-full md:block md:w-auto",children:!U&&e.jsx(E,{children:e.jsx(ve,{update:r,localData:s,previousUpdate:xe,nextUpdate:ie})})})]}),e.jsxs("div",{className:"mt-[1.5rem] flex w-full  flex-row items-start gap-5 md:mt-8 md:items-center md:justify-between",children:[e.jsx(E,{children:e.jsx(Ke,{update:r})}),e.jsxs("div",{className:"flex w-full flex-col items-center gap-[1.5625rem]  md:w-[75%] md:flex-row md:justify-between",children:[e.jsx(E,{children:e.jsx(Je,{data:r,showControls:F,setShowControls:te,summary:Q,nextUpdate:ie,localData:s,activeAudioRef:u,onBeforePlay:()=>{u!=null&&u.current&&(typeof u.current.stop=="function"&&u.current.stop(),typeof u.current.pause=="function"&&u.current.pause(),u.current=null)}})}),e.jsx(E,{children:e.jsx(Qe,{update:r})})]})]}),e.jsx("hr",{className:"mt-5 border-[.0625rem] border-black/40 md:block"}),e.jsxs("section",{className:"mb-10 mt-[0.5rem] flex flex-col items-center gap-[2.5rem] md:mb-0 md:mt-[0] md:flex-row md:items-start md:gap-4",children:[U?e.jsx(E,{children:e.jsx(We,{update:r})}):null,e.jsxs("div",{className:`min-h-[40rem] max-w-[100%] flex-grow border-0 ${U?"border-l-[#1f1d1a] pl-[3.25rem] md:border-l-[.125rem]":""}`,children:[e.jsx(E,{children:e.jsx(Xe,{update:r})}),e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsx(E,{children:e.jsx(Ze,{update:r,showControls:F,refetch:z,updateData:le.map(o=>({...o,"data-note-id":o.id})),questionAnswerState:Ie,setQuestionAnswerState:me})}),e.jsxs("div",{id:"summary",className:"mt-[16px] bg-[#F2DFCE] p-3 lg:p-5",children:[e.jsx("span",{className:"text-[18px] font-bold",children:"Summary"}),e.jsx("p",{children:e.jsx("p",{className:"font-regular mt-2 font-iowan text-[16px] font-medium leading-7",dangerouslySetInnerHTML:{__html:Q==null?void 0:Q.content}})})]})]})]})]})]})]})};export{Ct as default};
