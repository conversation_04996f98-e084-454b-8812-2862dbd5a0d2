import{A as p,G as d,M as f,t as h,s as g}from"./index-f2ad9142.js";import{r as t}from"./vendor-4cdf2bd1.js";function w(s){const[c,a]=t.useState(!1),[n,u]=t.useState([]),{dispatch:i}=t.useContext(p),{dispatch:l}=t.useContext(d);async function o(){a(!0);try{const r=await new f().callRawAPI(`/v3/api/goodbadugly/customer/view-insights?page=1&limit=1000&update_id=${s}`);u(r.model),console.log(r)}catch(e){h(i,e.message),g(l,e.message,5e3,"error")}a(!1)}return t.useEffect(()=>{s&&o()},[s]),{loading:c,updateRequests:n,refetch:o}}export{w as u};
