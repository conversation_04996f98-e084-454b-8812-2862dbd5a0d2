import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{InteractiveButton2 as b}from"./InteractiveButton-060359e0.js";import{a as g}from"./index-f2ad9142.js";import{o as w}from"./yup-0917e80c.js";import{a as j}from"./index.esm-3e7472af.js";import{u as S}from"./react-hook-form-a383372b.js";import{r as y}from"./vendor-4cdf2bd1.js";import{c as v,a as N}from"./yup-342a5df4.js";import{L as m,t as E}from"./@headlessui/react-cdd9213e.js";function C({update_id:i}){const{showToast:a,tokenExpireError:n,custom:l}=g(),d=v({email:N().required("This field is required").test("is-valid-emails","Some emails are invalid",s=>s?s.split(",").map(r=>r.trim()).every(r=>/^\S+@\S+\.\S+$/.test(r)):!1)}),{reset:T,control:$,register:c,setValue:F,handleSubmit:p,formState:{isSubmitting:h,errors:u}}=S({resolver:w(d)});async function x(s){console.log(s,i);try{const t=[];for(const o of s.email.split(",").map(f=>f.trim()))/^\S+@\S+\.\S+$/.test(o)?t.push(o):a(`${o} is not a valid email`,5e3,"error");const r=await l({endpoint:`/v3/api/custom/goodbadugly/updates/${i}/send-update`,method:"POST",payload:{emails:t}});console.log(r),r!=null&&r.error||a(r.message)}catch(t){console.log(t),a(t.message,5e3,"error"),n(t.message)}}return e.jsx(m,{className:"relative h-full",children:({open:s})=>{var t;return e.jsxs(e.Fragment,{children:[e.jsxs(m.Button,{className:`
              group flex  h-[32px] !min-h-full items-center !border-[#1f1d1a] px-[1rem] py-2 font-iowan text-sm font-medium focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 sm:text-base md:rounded-[.125rem] md:border `,children:[e.jsx(j,{className:"hidden w-4 h-4 md:block md:h-6 md:w-6"}),e.jsx("span",{className:"hidden md:block",children:"Share"}),e.jsx("img",{src:"/assets/share-button.svg",alt:"share",className:"block min-h-5 min-w-5 md:hidden md:h-6 md:w-6"})]}),e.jsx(E,{as:y.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 translate-y-1",children:e.jsx(m.Panel,{className:"absolute right-[-200px] z-10 mt-3 w-[250px] transform rounded-[.125rem] border border-[#1f1d1a] bg-brown-main-bg p-4 md:right-0 md:w-screen md:max-w-md",children:e.jsxs("form",{className:"space-y-4",onSubmit:p(x),children:[e.jsx("span",{className:"mb-1 font-Inter text-base font-[700] leading-[1.5125rem] md:text-[1.25rem]",children:"Share this update"}),e.jsx("p",{className:"mb-1 font-Inter text-sm font-[400] leading-[1.5rem] md:text-[1rem]",children:"To share with multiple people, separate each email with a comma"}),e.jsxs("div",{className:"flex items-center justify-between gap-[1.5rem] text-sm md:text-base",children:[e.jsxs("div",{className:"grow",children:[e.jsx("input",{...c("email"),type:"text",className:"h-[2.5rem] w-full !rounded-[.125rem] border border-[#1f1d1a] bg-transparent",placeholder:"Enter Email"}),e.jsx("p",{className:"italic text-red-500 text-field-error bg-brown-main-bg",children:(t=u.email)==null?void 0:t.message})]}),e.jsx(b,{loading:h,type:"submit",className:"h-[2.5rem] w-full max-w-[6.25rem] !rounded-[.125rem] bg-[#1f1d1a] text-[1.0625rem] text-white",children:"Share"})]})]})})})]})}})}export{C as S};
