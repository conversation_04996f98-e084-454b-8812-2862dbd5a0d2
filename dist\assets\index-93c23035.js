import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as o,b as T,h as B}from"./vendor-4cdf2bd1.js";import{M as $,A as R,G as V}from"./index-f2ad9142.js";import{u as G}from"./react-hook-form-a383372b.js";import{o as W}from"./yup-0917e80c.js";import{c as H,a as I}from"./yup-342a5df4.js";import{A as K}from"./AddButton-51d1b2cd.js";import{E as O}from"./ExportButton-eb4cf1f9.js";import{u as q,A as J}from"./AddCompanyModal-e0dc0776.js";import"./InteractiveButton-060359e0.js";import{L as x}from"./index-b8adfdf8.js";import{d as Q}from"./index.esm-be5e1c14.js";import{P as U}from"./index-9dceff66.js";import{R as X}from"./tableWrapperDashboard-6c11f374.js";import{C as Y}from"./ClipboardDocumentIcon-f03b0627.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-b50d6e2a.js";import"./XMarkIcon-cfb26fe7.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";import"./react-icons-36ae72b7.js";import"./index.esm-7add6cfb.js";let f=new $;const Z=[{header:"Logo",accessor:"logo"},{header:"Name",accessor:"name"},{header:"Action",accessor:""}],$e=()=>{var l,c;const{dispatch:_,state:h}=o.useContext(R),{dispatch:u}=o.useContext(V),d=T(),[s,m]=B(),{companies:t,loading:r,refetch:b,currentPage:g,pageCount:j,pageSize:w,updatePageSize:N,previousPage:y,nextPage:v,canPreviousPage:C,canNextPage:S}=q(h.user);console.log(t);const[ee,P]=o.useState({}),[k,n]=o.useState(!1);console.log("pdpdoododoodddddddddddddddddddddddddddd");const A=H({name:I()}),{register:E,handleSubmit:L,setError:ae,reset:M,formState:{errors:i}}=G({resolver:W(A),defaultValues:async()=>({name:s.get("name")??""})});async function z(){f.setTable("companies"),await f.exportCSV()}o.useEffect(()=>{u({type:"SETPATH",payload:{path:"companies"}})},[]);const D=()=>{n(!0)};function F(a){s.set("name",a.name),s.set("page",1),m(s)}return e.jsxs(e.Fragment,{children:[r?e.jsx(x,{}):e.jsxs(e.Fragment,{children:[e.jsxs("button",{className:"mx-5 my-5 mt-5 flex h-[34px] w-[83px] items-center justify-center gap-2 rounded-[6px] border-[2px] border-[#1f1d1a] bg-[#1f1d1a] px-3 text-white shadow-md sm:my-10 sm:h-[34px] sm:w-[83px] md:mx-8",onClick:()=>d(-1),children:[e.jsx(Q,{className:"h-3 w-3"})," ",e.jsx("span",{className:"text-sm",children:" Back"})]}),e.jsxs("div",{className:"rounded bg-brown-main-bg p-5 md:px-8",children:[e.jsxs("div",{className:"item-center mb-3 flex w-full justify-between ",children:[e.jsx("h4",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"Search"}),e.jsx("div",{className:"flex"})]}),e.jsxs("form",{onSubmit:L(F),className:"flex flex-col gap-4",children:[e.jsx("div",{className:"flex flex-wrap",children:e.jsxs("div",{className:"w-full sm:w-auto",children:[e.jsx("label",{className:"mb-2 block  text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Name"}),e.jsx("input",{type:"text",...E("name"),className:`focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm text-sm font-normal font-normal leading-tight text-[#1f1d1a] shadow   focus:outline-none sm:w-[180px] ${(l=i.name)!=null&&l.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(c=i.name)==null?void 0:c.message})]})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{type:"submit",disabled:r,className:"font-iowan-regular  rounded-md bg-primary-black/80 px-4 py-1 font-semibold text-white hover:bg-primary-black",children:"Search"}),e.jsx("button",{type:"button",onClick:()=>{M({name:"",members:""}),s.set("name",""),s.set("page",1),m(s)},disabled:r,className:"rounded-md px-4 py-1 font-semibold text-[#1f1d1a]",children:"Clear"})]})]}),e.jsxs("div",{className:"mt-10 overflow-x-auto rounded bg-brown-main-bg p-5 px-0 md:mt-8",children:[e.jsxs("div",{className:"mb-3 flex w-full items-center justify-between text-center",children:[e.jsx("h4",{className:"text-left text-[16px] font-[600] sm:text-[20px]",children:"Companies"}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(K,{onClick:D,className:"py-2 font-medium   sm:px-2"}),e.jsx(O,{onClick:z,className:"sm:px-1"})]})]}),e.jsx("div",{className:`${r?"":"custom-overflow overflow-x-auto"} `,children:r?e.jsx("div",{className:"flex max-h-fit min-h-fit min-w-fit max-w-full items-center justify-center  py-5",children:e.jsx(x,{size:50})}):e.jsx(e.Fragment,{children:e.jsx(X,{children:e.jsxs("table",{className:"min-w-full divide-y divide-[#1f1d1a]/10",children:[e.jsx("thead",{className:"",children:e.jsx("tr",{children:Z.map((a,p)=>e.jsx("th",{scope:"col",className:"font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3",children:a.header},p))})}),e.jsx("tbody",{className:"font-iowan-regular  divide-y divide-[#1f1d1a]/10",children:t.map((a,p)=>e.jsxs("tr",{children:[e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("img",{src:a.logo,className:"h-[50px] w-[80px] rounded",alt:""})}),e.jsx("td",{className:"whitespace-nowrap px-3 md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:a.name}),e.jsx("td",{className:"flex items-center justify-start gap-2 whitespace-nowrap px-6 py-4",children:e.jsx("p",{className:"cursor-pointer font-medium text-[#292829fd] underline hover:underline disabled:cursor-not-allowed disabled:text-gray-400",onClick:()=>{d("/fundmanager/view-companies/"+a.id,{state:a})},children:e.jsx("span",{children:"View"})})})]},a.id))})]})})})}),(t==null?void 0:t.length)==0?e.jsxs("div",{className:"mb-[20px] mt-24 flex flex-col items-center",children:[e.jsx(Y,{className:"h-8 w-8 text-gray-700",strokeWidth:2}),e.jsx("p",{className:"mt-4 text-center text-base font-medium",children:"No Company Added"})]}):null]}),t.length>0&&e.jsx(U,{currentPage:g,pageCount:j,pageSize:w,canPreviousPage:C,canNextPage:S,updatePageSize:N,previousPage:y,nextPage:v,dataLoading:r})]})]}),e.jsx(J,{isOpen:k,closeModal:()=>n(!1),afterCreate:()=>{b(),P({})}})]})};export{$e as default};
