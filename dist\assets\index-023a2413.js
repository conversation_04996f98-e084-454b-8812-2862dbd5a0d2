import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{o as A}from"./yup-0917e80c.js";import{A as D,G as M,ay as U,M as V,s as I,t as G}from"./index-f2ad9142.js";import{r as f}from"./vendor-4cdf2bd1.js";import{a as F,u as Q}from"./react-hook-form-a383372b.js";import{c as W,a as w}from"./yup-342a5df4.js";import{c as N}from"./countries-912e22d5.js";import{C as $}from"./ChevronDownIcon-8b7ce98c.js";import{W as m,t as z}from"./@headlessui/react-cdd9213e.js";import{b as H}from"./index.esm-54e24cf9.js";import{InteractiveButton2 as Y}from"./InteractiveButton-060359e0.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-icons-36ae72b7.js";import"./index-dc002f62.js";import"./react-spinners-b860a5a3.js";function K({control:b,name:i,setValue:p}){var n,l;const[x,d]=f.useState(""),{field:u,formState:g,fieldState:h}=F({control:b,name:i}),y=x===""?N:N.filter(o=>o.name.toLowerCase().replace(/\s+/g,"").includes(x.toLowerCase().replace(/\s+/g,"")));return e.jsx(e.Fragment,{children:e.jsx(m,{value:u.value,onChange:p,children:e.jsxs("div",{className:"relative mt-6 sm:mt-8",children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"Country"}),e.jsxs("div",{children:[e.jsxs("div",{className:"relative w-full cursor-default rounded-md  text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300",children:[e.jsx(m.Input,{className:`focus:shadow-outline h-[41.6px] w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a]    focus:outline-none ${(n=h.error)!=null&&n.message?"border-red-500":""}`,placeholder:"Country",displayValue:o=>{const t=N.find(a=>a.name==o)??{};return`${t.emoji??""}   ${t.name??""}`},onChange:o=>{const t=o.target.value.replace(/\p{Emoji}/gu,"").trim();d(t),t===""&&p("")},onBlur:u.onBlur,ref:u.ref,name:u.name,autoComplete:"off",defaultValue:"oooo"}),e.jsx(m.Button,{className:"absolute inset-y-0 right-0 flex items-center border-l border-black/60 p-2",children:e.jsx($,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(l=h.error)==null?void 0:l.message})]}),e.jsx(z,{as:f.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx(m.Options,{className:"absolute z-10 mt-1 h-fit w-full rounded-md border bg-brown-main-bg py-1 text-base shadow-lg ring-1 ring-[#1f1d1a]/5 focus:outline-none",children:e.jsx("div",{className:"max-h-96 overflow-auto",children:y.length===0&&x!==""?e.jsx("div",{className:"relative cursor-default select-none bg-brown-main-bg px-4 py-2 text-gray-700",children:"Nothing found."}):y.map(o=>e.jsx(m.Option,{className:({active:t})=>`relative flex cursor-default select-none items-center gap-4 py-2 pl-10 pr-4 ${t?"bg-primary-black text-white":"text-gray-900"}`,value:o.name,children:({selected:t,active:a})=>e.jsxs(e.Fragment,{children:[e.jsx("span",{children:o.emoji}),e.jsx("span",{className:"block truncate font-medium",children:o.name})]})},o.id))})})})]})})})}function J({control:b,name:i,setValue:p,country:x}){var o,t;const[d,u]=f.useState(""),{field:g,formState:h,fieldState:y}=F({control:b,name:i}),n=f.useMemo(()=>{const a=N.find(r=>r.name==x);return(a==null?void 0:a.states)??[]},[x]),l=d===""?n:n.filter(a=>a.name.toLowerCase().replace(/\s+/g,"").includes(d.toLowerCase().replace(/\s+/g,"")));return e.jsx(e.Fragment,{children:e.jsx(m,{value:g.value,onChange:p,children:e.jsxs("div",{className:"relative mt-6 sm:mt-8",children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"State"}),e.jsxs("div",{children:[e.jsxs("div",{className:"relative w-full cursor-default rounded-md  text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300",children:[e.jsx(m.Input,{className:`focus:shadow-outline h-[41.6px] w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a]    focus:outline-none ${(o=y.error)!=null&&o.message?"border-red-500":""}`,placeholder:"State",displayValue:a=>(n.find(c=>c.name==a)??{}).name,onChange:a=>{u(a.target.value),a.target.value===""&&p("")},onBlur:g.onBlur,ref:g.ref,name:g.name,autoComplete:"off"}),e.jsx(m.Button,{className:"absolute inset-y-0 right-0 flex items-center border-l border-black/60 p-2",children:e.jsx($,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(t=y.error)==null?void 0:t.message})]}),e.jsx(z,{as:f.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx(m.Options,{className:"absolute z-10 mt-1 h-fit w-full rounded-md border bg-brown-main-bg py-1 text-base shadow-lg ring-1 ring-[#1f1d1a]/5 focus:outline-none",children:e.jsx("div",{className:"max-h-96 overflow-auto",children:l.length===0&&d!==""?e.jsx("div",{className:"relative cursor-default select-none bg-brown-main-bg px-4 py-2 text-gray-700",children:"Nothing found."}):l.map(a=>e.jsx(m.Option,{className:({active:r})=>`relative flex cursor-default select-none items-center gap-4 py-2 pl-10 pr-4 ${r?"bg-primary-black text-white":"text-gray-900"}`,value:a.name,children:({selected:r,active:c})=>e.jsx(e.Fragment,{children:e.jsx("span",{className:"block truncate font-medium",children:a.name})})},a.id))})})})]})})})}function X({control:b,name:i,setValue:p,country:x,state:d}){var t,a;const[u,g]=f.useState(""),{field:h,formState:y,fieldState:n}=F({control:b,name:i}),l=f.useMemo(()=>{const r=N.find(j=>j.name==x),c=r==null?void 0:r.states.find(j=>j.name==d);return(c==null?void 0:c.cities)??[]},[x,d]),o=u===""?l:l.filter(r=>r.name.toLowerCase().replace(/\s+/g,"").includes(u.toLowerCase().replace(/\s+/g,"")));return e.jsx(e.Fragment,{children:e.jsx(m,{value:h.value,onChange:p,children:e.jsxs("div",{className:"relative mt-6 sm:mt-8",children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"City"}),e.jsxs("div",{children:[e.jsxs("div",{className:"relative w-full cursor-default rounded-md  text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300",children:[e.jsx(m.Input,{className:`focus:shadow-outline h-[41.6px] w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a]    focus:outline-none ${(t=n.error)!=null&&t.message?"border-red-500":""}`,placeholder:"City",displayValue:r=>(l.find(j=>j.name==r)??{}).name,onChange:r=>{g(r.target.value),r.target.value===""&&p("")},onBlur:h.onBlur,ref:h.ref,name:h.name,autoComplete:"off"}),e.jsx(m.Button,{className:"absolute inset-y-0 right-0 flex items-center border-l border-black/60 p-2",children:e.jsx($,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})})]}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(a=n.error)==null?void 0:a.message})]}),e.jsx(z,{as:f.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx(m.Options,{className:"absolute z-10 mt-1 h-fit w-full rounded-md border bg-brown-main-bg py-1 text-base shadow-lg ring-1 ring-[#1f1d1a]/5 focus:outline-none",children:e.jsx("div",{className:"max-h-96 overflow-auto",children:o.length===0&&u!==""?e.jsx("div",{className:"relative cursor-default select-none bg-brown-main-bg px-4 py-2 text-gray-700",children:"Nothing found."}):o.map(r=>e.jsx(m.Option,{className:({active:c})=>`relative flex cursor-default select-none items-center gap-4 py-2 pl-10 pr-4 ${c?"bg-primary-black text-white":"text-gray-900"}`,value:r.name,children:({selected:c,active:j})=>e.jsx(e.Fragment,{children:e.jsx("span",{className:"block truncate font-medium",children:r.name})})},r.id))})})})]})})})}function ke(){var P,E,L,T,q,B;const{dispatch:b,state:i}=f.useContext(D),{dispatch:p}=f.useContext(M),x=W({first_name:w().required("This field is required"),last_name:w().required("This field is required"),role:w().required("This field is required"),company_name:w().required("This field is required"),country:w(),state:w(),city:w()}),{register:d,handleSubmit:u,setError:g,reset:h,watch:y,setValue:n,formState:{errors:l,defaultValues:o,dirtyFields:t,isSubmitting:a,isDirty:r},control:c}=Q({resolver:A(x),defaultValues:{first_name:i.profile.first_name,last_name:i.profile.last_name,role:U[i.profile.role],company_name:i.company.name,photo:i.profile.photo,country:i.profile.country,state:i.profile.state,city:i.profile.city}});async function j(s){try{const v=new V;let k="";if(t.photo||t.first_name||t.last_name){if(t.photo&&s.photo instanceof FileList&&s.photo.length>0){const R=await v.upload(s.photo[0]);k=v.baseUrl()+R.url}await v.callRawAPI(`/v4/api/records/user/${i.user}`,{first_name:s.first_name,last_name:s.last_name,...t.photo&&{photo:k}},"PUT"),b({type:"REFETCH_PROFILE"})}t.company_name&&(await v.callRawAPI(`/v4/api/records/companies/${i.company.id}`,{name:s.company_name},"PUT"),b({type:"REFETCH_COMPANY"})),t.country||t.state||t.city,await v.callRawAPI("/v4/api/records/profile",{updateCondition:{user_id:i.user},country:s.country,state:s.state,city:s.city,contact_link:s.contact_link},"PUT"),h({...o,first_name:s.first_name,last_name:s.last_name,...t.photo&&{photo:k},company_name:s.company_name,country:s.country,state:s.state,city:s.city},{keepDefaultValues:!1}),I(p,"Changes saved")}catch(v){G(b,v.message),I(p,v.message,5e3,"error")}}const[C,_,S]=y(["photo","country","state"]);f.useEffect(()=>{_===""&&(n("state",""),n("city","")),S===""&&n("city","")},[_,S]);const O=f.useMemo(()=>C instanceof FileList&&C.length>0?URL.createObjectURL(C[0]):null,[C]);return e.jsxs("div",{className:"w-full max-w-7xl items-start gap-6 p-6 pt-8 sm:px-5 md:flex",children:[e.jsxs("div",{className:" flex flex-col items-center rounded-md border border-[#1f1d1a] px-16 py-8",children:[e.jsx("img",{src:O||(o==null?void 0:o.photo)||"/default.png",alt:"profile",className:"h-40 w-40 rounded-[50%] object-cover"}),e.jsxs("label",{className:"md:max-w-auto mt-4 flex max-w-[200px] cursor-pointer flex-row items-center justify-center gap-3 rounded-lg border border-[#1f1d1a] px-3 py-2 text-center text-lg font-medium capitalize capitalize",children:[e.jsx("input",{type:"file",...d("photo"),className:"hidden"}),e.jsx(H,{className:"min-h-5 min-w-5"}),e.jsxs("span",{className:"font-iowan-regular  whitespace-nowrap text-sm",children:[" ","Upload Profile Picture"]})]})]}),e.jsxs("form",{className:"mb-20 mt-8 max-w-[500px] flex-grow md:mt-0",onSubmit:u(j),children:[e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"First name"}),e.jsx("input",{type:"text",autoComplete:"off",...d("first_name"),className:`w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(P=l.first_name)!=null&&P.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(E=l.first_name)==null?void 0:E.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"Last name"}),e.jsx("input",{type:"text",autoComplete:"off",...d("last_name"),className:`w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(L=l.last_name)!=null&&L.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(T=l.last_name)==null?void 0:T.message})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"mb-2 block font-iowan text-base font-semibold capitalize capitalize text-[#1f1d1a]",children:"Title"}),e.jsx("input",{type:"text",autoComplete:"off",readOnly:!0,...d("role"),className:`w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(q=l.role)!=null&&q.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(B=l.role)==null?void 0:B.message})]}),e.jsx(K,{control:c,name:"country",setValue:s=>n("country",s)}),e.jsx(J,{control:c,name:"state",setValue:s=>n("state",s),country:_}),e.jsx(X,{control:c,name:"city",setValue:s=>n("city",s),country:_,state:S}),e.jsxs("div",{className:"mt-6 flex items-center justify-end gap-4",children:[" ",r?e.jsx("button",{className:"h-[40px] rounded-md border border-[#1f1d1a] px-4 py-2 font-iowan text-[10px] font-medium sm:text-[12px]    xl:text-base",type:"button",onClick:()=>h(),children:"Discard Changes"}):null,e.jsx(Y,{loading:a,disabled:a,type:"submit",className:`whitespace-nowr disabled:bg-disabledblack h-[40px]  w-[115px] min-w-fit rounded-md bg-primary-black px-6 py-2 text-center text-[10px] font-semibold font-semibold text-white transition-colors \r
duration-100 sm:!text-[12px] md:!w-[146px]  xl:!text-base`,children:"Save changes"})]})]})]})}export{ke as default};
