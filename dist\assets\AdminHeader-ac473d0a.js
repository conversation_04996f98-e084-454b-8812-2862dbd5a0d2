import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{b as d,j as f}from"./vendor-4cdf2bd1.js";import{a as h,ba as u,bl as i,L as o,bg as j,bk as b}from"./index-f2ad9142.js";import{H as g}from"./index-87e46e89.js";import{o as t}from"./@headlessui/react-cdd9213e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const H=()=>{var a;const{globalState:l,gobalDispatch:c,authState:s,authDispatch:p}=h(),n=d(),x=[{to:`/${s==null?void 0:s.role}/dashboard`,text:"Dashboard",icon:e.jsx(u,{fillOpacity:`${l.path==="dashboard"?"1":"0.6"}`}),value:"dashboard"},{to:`/${s==null?void 0:s.role}/plans`,text:"Plans",icon:e.jsx(i,{fillOpacity:l.path==="plans"?"1":"0.6"}),value:"plans"},{to:`/${s==null?void 0:s.role}/pricing`,text:"Plan Prices",icon:e.jsx(i,{fillOpacity:l.path==="pricing"?"1":"0.6"}),value:"pricing"},{to:`/${s==null?void 0:s.role}/coupons`,text:"Coupons",icon:e.jsx(i,{fillOpacity:l.path==="coupons"?"1":"0.6"}),value:"coupons"},{to:`/${s==null?void 0:s.role}/email`,text:"Emails",icon:e.jsx(i,{fillOpacity:l.path==="emails"?"1":"0.6"}),value:"email"},{to:`/${s==null?void 0:s.role}/users`,text:"Users",icon:e.jsx(i,{fillOpacity:l.path==="users"?"1":"0.6"}),value:"users"}].filter(r=>r);let m=r=>{c({type:"OPEN_SIDEBAR",payload:{isOpen:r}})};return e.jsxs("div",{className:`relative z-[10] flex flex-col bg-[#1F1D1A] px-5 text-[#A8A8A8] transition-all ${l.isOpen?"absolute left-0 top-0 h-full w-[16.25rem] sm:min-w-[16.25rem] sm:max-w-[260px] lg:relative":"relative h-full w-[5.25rem] bg-[#1f1d1a] text-white sm:min-w-[5rem] sm:max-w-[5.25rem]"} `,children:[e.jsxs("div",{className:"relative flex h-screen max-h-full min-h-full min-w-full max-w-full flex-col overflow-y-clip   pt-[1.5rem] ",children:[e.jsx("div",{className:`text-[#393939] ${l.isOpen?"flex w-full":"flex items-center justify-center"} `,children:e.jsx(o,{children:e.jsx(g,{})})}),e.jsx("div",{className:`mt-6 flex h-fit w-auto flex-1   ${l.isOpen?"justify-start":"justify-center"}`,children:e.jsx("div",{className:"w-full",children:e.jsx("ul",{className:"flex flex-col gap-[.75rem]   text-sm ",children:x.map(r=>e.jsx("li",{className:`flex h-[2.5rem] w-full  list-none items-center rounded-[.25rem]  font-inter text-[.875rem] font-[600] leading-[1.0588rem] hover:bg-[#F7F5D714] ${l.path==r.value?"bg-[#F7F5D714]":""} `,children:e.jsxs(f,{to:r.to,className:`flex w-full items-center  gap-3 whitespace-nowrap px-[.8456rem] ${l.isOpen?" w-full justify-start ":"w-[2.5rem]  justify-center"}`,children:[e.jsx("span",{children:r.icon}),l.isOpen&&e.jsx("span",{children:r.text})]})},r.value))})})}),e.jsx("div",{className:"flex justify-end pb-5",children:e.jsxs(t,{as:"div",className:"relative w-full space-y-3 px-2 text-[#262626]",children:[e.jsxs(t.Button,{className:"flex w-full items-center justify-center gap-[5px] border-t border-t-white/10 pb-1 pt-4 sm:gap-6",children:[e.jsx("img",{className:`${l.isOpen?"h-10 w-10":"h-5 w-5 xl:h-6 xl:w-6"} rounded-[50%] object-cover`,src:((a=s.profile)==null?void 0:a.photo)??"/default.png",alt:""}),l.isOpen?e.jsx(e.Fragment,{children:e.jsxs("div",{className:"text-left text-white",children:[e.jsxs("p",{className:"w-32 truncate text-sm font-medium",children:[s.profile.first_name," ",s.profile.last_name]}),e.jsx("p",{className:"mt-1 w-[150px] truncate text-xs",children:s.profile.email})]})}):null]}),e.jsx(t.Items,{className:` absolute ${l.isOpen?"-bottom-1 -right-full md:-right-[170px]":"-bottom-1 -right-[170px]"}  ] mb-8 w-[160px] origin-top-right divide-y divide-gray-100 rounded-md bg-brown-main-bg font-bold text-[#1f1d1a] shadow-lg ring-1 ring-[#1f1d1a] ring-opacity-5 focus:outline-none`,children:e.jsxs("div",{className:"px-1 py-1",children:[e.jsx(t.Item,{children:({active:r})=>e.jsxs("button",{className:`group flex w-full items-center px-3 py-3 text-sm ${r?"border-b border-b-black/20":"border-b border-b-transparent"}`,onClick:()=>{n(`/${s==null?void 0:s.role}/profile`)},children:[e.jsx(o,{children:e.jsx(j,{className:"mr-2 h-5 w-5"})}),"Account"]})}),e.jsx(t.Item,{children:({active:r})=>e.jsxs("button",{className:`group flex w-full items-center px-3 py-3 text-sm text-[#CE0000] ${r?"border-b border-b-black/20":"border-b border-b-transparent"}`,onClick:()=>{p({type:"LOGOUT"}),n(`/${s==null?void 0:s.role}/login`)},children:[e.jsx(o,{children:e.jsx(b,{className:"mr-2 h-5 w-5"})}),"Logout"]})})]})})]})})]}),e.jsx("div",{className:" absolute -right-3 top-[1.5rem] z-[5] cursor-pointer",children:e.jsx("div",{onClick:()=>m(!l.isOpen),children:e.jsx("span",{children:e.jsxs("svg",{width:"25",height:"25",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:`${l.isOpen?"hidden":""}`,children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"19",height:"19",rx:"9.5",fill:"#1F1D1A"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"19",height:"19",rx:"9.5",stroke:"#FFF0E5"}),e.jsx("path",{d:"M8.66602 6.66667L11.9993 10L8.66602 13.3333",stroke:"#FFF0E5","stroke-width":"1.33333","stroke-linecap":"round","stroke-linejoin":"round"})]})})})})]})};export{H as AdminHeader,H as default};
