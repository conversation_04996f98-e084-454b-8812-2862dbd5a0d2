import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{A as s,G as r,u as a,L as n,a3 as i,I as o}from"./index-f2ad9142.js";import{r as t,b as l}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const E=()=>(t.useContext(s),t.useContext(r),l(),t.useState(!1),t.useState({}),t.useState(!0),t.useState(!1),t.useState(!1),t.useState(!1),t.useState({startHerePrompt:!0,skipAwareness:!1}),a({isPublic:!1}),e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex h-full w-full items-center justify-center",children:[" ",e.jsxs("div",{className:"flex flex-col items-center justify-center gap-[1.5rem] text-center",children:[e.jsxs("div",{className:"flex w-full flex-col items-center justify-center text-center",children:[e.jsx("div",{className:"no-updates-icon",children:e.jsx(n,{children:e.jsx(i,{fill:"black",className:"!h-[3.75rem] !w-[3.75rem]"})})}),e.jsx("br",{}),e.jsx("div",{className:"font-iowan text-[20px] font-[700] leading-[1.865rem] md:text-[1.5rem] ",children:"You Have No Engagement"}),e.jsx("br",{}),e.jsxs("p",{className:"font-inter text-[1rem] font-[400] leading-[1.5rem]",children:["Recent engagement from your updates will be"," "]}),e.jsx("p",{className:"font-inter text-[1rem] font-[400] leading-[1.5rem]",children:"shown here"})]}),e.jsx(o,{type:"button",className:"flex h-[2.75rem] w-fit items-center justify-center rounded-[.0625rem] bg-brown-main-bg ",color:"black",onClick:()=>{}})]})]})}));export{E as default};
