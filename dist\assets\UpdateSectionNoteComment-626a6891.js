import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{r as n,u as v}from"./vendor-4cdf2bd1.js";import"./moment-a9aaa855.js";import{a as M,u as A,L as o,E as L,d as O,br as U}from"./index-f2ad9142.js";import{d as D,e as S,f as P}from"./UpdateSection-24e02fd2.js";import{u as R}from"./useComments-7e9daaa3.js";import{_ as u}from"./qr-scanner-cf010ec4.js";import{a as T,D as k}from"./index-45396f34.js";import{A as c}from"./index-afef2e72.js";import{a as z}from"./lucide-react-0b94883e.js";const I=n.lazy(()=>u(()=>import("./UpdateSectionNoteCommentReplies-59e1f1e4.js"),["assets/UpdateSectionNoteCommentReplies-59e1f1e4.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/useComments-7e9daaa3.js","assets/AddButton-51d1b2cd.js","assets/index-afef2e72.js","assets/MkdInput-d37679e9.js","assets/react-toggle-6478c5c4.js","assets/@uppy/dashboard-51133bb7.js","assets/@fullcalendar/core-085b11ae.js","assets/core-b9802b0d.css","assets/@uppy/core-a4ba4b97.js","assets/@uppy/aws-s3-a6b02742.js","assets/@craftjs/core-a2cdaeb4.js","assets/@uppy/compressor-4bcbc734.js","assets/MkdInput-5e6afe8d.css","assets/useUpdateCollaborator-1187c43b.js","assets/react-popper-9a65a9b6.js","assets/@popperjs/core-f3391c26.js","assets/UpdateSection-24e02fd2.js","assets/index-45396f34.js","assets/lucide-react-0b94883e.js"])),se=n.lazy(()=>u(()=>import("./UpdateSectionNoteCommentReply-5e3f772d.js"),["assets/UpdateSectionNoteCommentReply-5e3f772d.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/moment-a9aaa855.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/UpdateSectionNoteCommentReplyContent-afa45df8.js","assets/useUpdateCollaborator-1187c43b.js","assets/index-45396f34.js","assets/index-afef2e72.js","assets/UpdateSection-24e02fd2.js","assets/useComments-7e9daaa3.js","assets/lucide-react-0b94883e.js"]));n.lazy(()=>u(()=>import("./UpdateSectionNoteCommentReplyActivities-8706f2fe.js"),["assets/UpdateSectionNoteCommentReplyActivities-8706f2fe.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-d20ea84b.js","assets/qr-scanner-cf010ec4.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-31067895.js","assets/index-45396f34.js","assets/lucide-react-0b94883e.js"]));n.lazy(()=>u(()=>import("./UpdateSectionNoteCommentReplyContent-afa45df8.js"),["assets/UpdateSectionNoteCommentReplyContent-afa45df8.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/useUpdateCollaborator-1187c43b.js"]));const re=n.lazy(()=>u(()=>import("./UpdateSectionNoteCommentReplyUser-e623e15c.js"),["assets/UpdateSectionNoteCommentReplyUser-e623e15c.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-713720be.js"])),V=({note:d=null,update:i=null,comment:e=null,onSuccess:p=null,loadComments:g=null})=>{var C;const{showToast:_,tokenExpireError:F,custom:K,setLoading:f,RequestItems:a}=M(),h=v(),{profile:j}=A(),[l,m]=n.useState({html:null,data:null,modal:null,showReply:!0,showModal:!1,edit_comment:null,comment:"",errors:{comment:{message:""}}}),{loading:w,comments:W,getComments:B,customDeleteNoteComment:E}=R(),y=[{icon:r.jsx(o,{children:r.jsx(z,{className:"h-[1.125rem] w-[1.125rem]"})}),name:"Add Reply",onClick:()=>{if(h.search.includes("mode=preview")){_("you are in preview mode");return}m(s=>({...s,showModal:!0,add_reply:!0,modal:"add_reply"}))},loading:!1},...(j==null?void 0:j.id)==((C=e==null?void 0:e.user)==null?void 0:C.id)?[{icon:r.jsx(o,{children:r.jsx(L,{className:"h-[1.125rem] w-[1.125rem]"})}),name:"Edit",onClick:()=>{if(h.search.includes("mode=preview")){_("you are in preview mode");return}m(s=>({...s,edit_comment:!0,modal:"edit_comment",comment:e==null?void 0:e.comment}))},loading:!1},{icon:r.jsx(o,{children:r.jsx(O,{fill:"#1F1D1A",className:"h-[1.125rem] w-[1.125rem]"})}),name:"Delete",onClick:()=>{if(h.search.includes("mode=preview")){_("you are in preview mode");return}m(s=>({...s,showModal:!0,modal:"delete_comment"}))},loading:w==null?void 0:w.delete}]:[]],t=(s,x)=>{m(N=>({...N,[s]:x}))};async function b(){try{const s=await E({noteId:d==null?void 0:d.id,updateId:i==null?void 0:i.id,commentId:e==null?void 0:e.id});s!=null&&s.error||(t("modal",null),t("showModal",!1),f(a==null?void 0:a.deleteModel,!1),p&&p())}catch(s){console.error("error >> ",s)}finally{f(a==null?void 0:a.deleteModel,!1)}}return n.useEffect(()=>{e!=null&&e.id&&t("comment",e==null?void 0:e.comment)},[e==null?void 0:e.id]),r.jsxs(n.Fragment,{children:[r.jsxs("div",{className:"mb-[1.8rem] flex flex-col gap-[1rem] ",children:[r.jsxs("div",{className:"mb-[0.8rem] flex flex-col ",children:[r.jsxs("div",{className:"flex justify-between items-start",children:[r.jsx(o,{children:r.jsx(D,{comment:e,update:i,note:d})}),r.jsx(o,{children:r.jsx(T,{className:"!rounded-[.125rem] !bg-brown-main-bg",childrenWrapperClass:"!bg-brown-main-bg !rounded-[.125rem] !px-[1rem] !min-w-[10rem] !w-[10rem]",icon:r.jsx(o,{children:r.jsx(U,{stroke:"#1F1D1A",className:"!rotate-90"})}),children:y==null?void 0:y.map((s,x)=>r.jsx(o,{children:r.jsx(k,{icon:s==null?void 0:s.icon,name:s==null?void 0:s.name,onClick:s==null?void 0:s.onClick,loading:s==null?void 0:s.loading,disabled:s==null?void 0:s.loading,className:"!h-[2.75rem] !min-h-[2.75rem]"},x)},x))})})]}),r.jsx(o,{children:r.jsx(S,{setExternalData:m,loadComments:g,externalData:l,comment:e,update:i,note:d})}),r.jsx(o,{children:r.jsx(P,{note:d,update:i,comment:e,onSuccess:p,toggleRplies:t,showReply:l==null?void 0:l.showReply})})]}),r.jsx(o,{children:r.jsx(I,{setExternalData:m,externalData:l,comment:e,update:i,note:d})})]}),r.jsx(o,{children:r.jsx(c,{isOpen:(l==null?void 0:l.showModal)&&(l==null?void 0:l.modal)==="delete_comment",onClose:()=>t("showModal",!1),customMessage:"Are you sure you want to delete this comment?",table:"update_comments",title:"Delete Comment",action:"delete",mode:"manual",data:{id:e==null?void 0:e.id},inputConfirmation:!1,onSuccess:s=>{f(a==null?void 0:a.deleteModel,!0),b()}})}),r.jsx(o,{children:r.jsx(c,{isOpen:(l==null?void 0:l.showModal)&&(l==null?void 0:l.modal)==="edit_comment",onClose:()=>{t("showModal",!1),t("modal",null)},title:"Edit Comment",input:"comment",mode:"input_update",inputType:"textarea",initialValue:e==null?void 0:e.comment,customMessage:r.jsx(r.Fragment,{}),table:"update_comments",action:"update",data:{id:e==null?void 0:e.id,note_id:d==null?void 0:d.id,update_id:i==null?void 0:i.id},onSuccess:s=>{t("showModal",!1),t("modal",null),p&&p()}})})]})},le=Object.freeze(Object.defineProperty({__proto__:null,default:V},Symbol.toStringTag,{value:"Module"}));export{se as U,re as a,le as b};
