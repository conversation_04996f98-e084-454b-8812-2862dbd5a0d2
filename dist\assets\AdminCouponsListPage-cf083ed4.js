import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{b as k,r as a,R as I}from"./vendor-4cdf2bd1.js";import{u as S,A,G as D,M,f as g,d as E,s as r,t as f}from"./index-f2ad9142.js";import{M as P}from"./index-d526f96e.js";import{A as F}from"./index-afef2e72.js";import{A as _}from"./AddButton-51d1b2cd.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const te=()=>{S(),k();const{dispatch:l}=a.useContext(A),{dispatch:o}=a.useContext(D),[d,y]=a.useState([]),[n,h]=a.useState(!1),[j,i]=a.useState(!1),[w,p]=a.useState(!1),[m,N]=a.useState(null),[s,c]=a.useState({couponId:"",name:"",percent_off:"",duration:"forever"}),u=new M,x=async()=>{h(!0);try{const t=await u.getStripeCoupons();console.log("Coupons API response:",t),t.error?r(o,t.message||"Failed to fetch coupons",5e3,"error"):y(t.coupons||[])}catch(t){console.error("Error fetching coupons:",t),r(o,t.message||"Failed to fetch coupons",5e3,"error"),f(l,t.message)}finally{h(!1)}},b=async()=>{try{const t=await u.createStripeCoupon({couponId:"vip-access-100",name:"VIP Access - 100% Off",percent_off:100,duration:"forever"});t.error?r(o,t.message,5e3,"error"):(r(o,"VIP Coupon created successfully!",5e3,"success"),x())}catch(t){console.error("Error creating VIP coupon:",t),r(o,t.message,5e3,"error"),f(l,t.message)}},C=async()=>{if(!s.couponId||!s.name||!s.percent_off){r(o,"Please fill in all required fields",5e3,"error");return}try{const t=await u.createStripeCoupon({couponId:s.couponId,name:s.name,percent_off:parseInt(s.percent_off),duration:s.duration});t.error?r(o,t.message,5e3,"error"):(r(o,"Coupon created successfully!",5e3,"success"),i(!1),c({couponId:"",name:"",percent_off:"",duration:"forever"}),x())}catch(t){console.error("Error creating coupon:",t),r(o,t.message,5e3,"error"),f(l,t.message)}};return I.useEffect(()=>{o({type:"SETPATH",payload:{path:"coupons"}}),x()},[]),n?e.jsx("div",{className:"mx-auto rounded p-5 shadow-md",children:e.jsx("div",{className:"min-h-screen",children:e.jsx(g,{})})}):e.jsxs(a.Fragment,{children:[e.jsxs("div",{className:"px-8",children:[e.jsx("div",{className:"items-center py-3 pt-10",children:e.jsxs("div",{className:"mb-6 flex w-full items-center justify-between gap-5",children:[e.jsx("h4",{className:"text-[1rem] font-semibold md:text-xl",children:"Coupon Management"}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:b,disabled:n,className:"rounded bg-green-600 px-4 py-2 text-white hover:bg-green-700 disabled:opacity-50",children:n?"Creating...":"Create VIP Coupon (100% Off)"}),e.jsx(_,{onClick:()=>i(!0)})]})]})}),n?e.jsx(g,{}):e.jsxs("div",{className:"overflow-x-auto shadow",children:[e.jsxs("table",{className:"min-w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Coupon ID"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Name"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Discount"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Duration"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Times Redeemed"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Status"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Actions"})]})}),e.jsx("tbody",{className:"font-iowan-regular divide-y divide-[#1f1d1a]/10",children:d.length>0?d.map((t,v)=>e.jsxs("tr",{className:"md:h-[60px]",children:[e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm font-medium",children:t.id}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm",children:t.name}),e.jsxs("td",{className:"whitespace-nowrap px-6 py-4 text-sm",children:[t.percent_off,"% off"]}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm",children:t.duration}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm",children:t.times_redeemed||0}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm",children:t.valid?e.jsx("span",{className:"rounded-md bg-[#D1FAE5] px-3 py-1 text-[#065F46]",children:"Active"}):e.jsx("span",{className:"rounded-md bg-[#F4F4F4] px-3 py-1 text-[#393939]",children:"Inactive"})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4 text-sm font-medium",children:e.jsx("button",{onClick:()=>{N(t),p(!0)},className:"text-red-600 hover:text-red-900",children:e.jsx(E,{})})})]},t.id||v)):e.jsx("tr",{children:e.jsx("td",{colSpan:"7",className:"px-6 py-4 text-center text-gray-500",children:"No coupons found. Create your first coupon!"})})})]}),!n&&d.length===0&&e.jsx("p",{className:"px-10 py-3 text-xl capitalize",children:"No coupons found"})]})]}),e.jsx(P,{isModalActive:j,showHeader:!0,title:"Create Custom Coupon",closeModalFn:()=>i(!1),customMinWidthInTw:"md:w-[25%] w-full !bg-brown-main-bg",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Coupon ID *"}),e.jsx("input",{type:"text",value:s.couponId,onChange:t=>c({...s,couponId:t.target.value}),className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"e.g., SAVE50"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Coupon Name *"}),e.jsx("input",{type:"text",value:s.name,onChange:t=>c({...s,name:t.target.value}),className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"e.g., 50% Off Special"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Percent Off *"}),e.jsx("input",{type:"number",min:"1",max:"100",value:s.percent_off,onChange:t=>c({...s,percent_off:t.target.value}),className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"50"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Duration"}),e.jsxs("select",{value:s.duration,onChange:t=>c({...s,duration:t.target.value}),className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",children:[e.jsx("option",{value:"forever",children:"Forever"}),e.jsx("option",{value:"once",children:"Once"}),e.jsx("option",{value:"repeating",children:"Repeating"})]})]}),e.jsxs("div",{className:"flex justify-end space-x-3",children:[e.jsx("button",{onClick:()=>i(!1),className:"rounded-md bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-300",children:"Cancel"}),e.jsx("button",{onClick:C,className:"rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700",children:"Create Coupon"})]})]})}),e.jsx(F,{isOpen:w,onClose:()=>p(!1),onConfirm:()=>{p(!1),r(o,"Delete functionality not implemented yet",3e3,"info")},title:"Delete Coupon",message:`Are you sure you want to delete the coupon "${m==null?void 0:m.name}"?`,confirmText:"Delete",cancelText:"Cancel"})]})};export{te as default};
