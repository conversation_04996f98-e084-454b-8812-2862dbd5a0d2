import { useCallback, useContext, useState } from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";

/**
 * Custom hook for handling mentions functionality
 * @returns {Object} Mentions related functions and state
 */
export const useMentions = () => {
  const [loading, setLoading] = useState({
    list: false,
    count: false,
    markSeen: false,
    markAllSeen: false,
  });
  const [mentions, setMentions] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);

  const { dispatch: authDispatch } = useContext(AuthContext);
  const { dispatch: globalDispatch } = useContext(GlobalContext);

  /**
   * Fetch all mentions for the current user
   */
  const fetchMentions = useCallback(async () => {
    setLoading((prev) => ({ ...prev, list: true }));
    try {
      const sdk = new MkdSDK();
      const result = await sdk.callRawAPI(
        "/v3/api/custom/goodbadugly/mentions",
        undefined,
        "GET"
      );

      if (!result.error && result.data) {
        setMentions(result.data);
      }
    } catch (error) {
      console.error("Error fetching mentions:", error);
      const message = error?.response?.data?.message ?? error.message;
      showToast(globalDispatch, message, 5000, "error");
      tokenExpireError(authDispatch, message);
    } finally {
      setLoading((prev) => ({ ...prev, list: false }));
    }
  }, [authDispatch, globalDispatch]);

  /**
   * Get count of unread mentions
   */
  const fetchUnreadCount = useCallback(async () => {
    setLoading((prev) => ({ ...prev, count: true }));
    try {
      const sdk = new MkdSDK();
      const result = await sdk.callRawAPI(
        "/v3/api/custom/goodbadugly/mentions/count",
        undefined,
        "GET"
      );

      if (!result.error) {
        setUnreadCount(result.count || 0);
      }
    } catch (error) {
      console.error("Error fetching unread mentions count:", error);
      const message = error?.response?.data?.message ?? error.message;
      tokenExpireError(authDispatch, message);
    } finally {
      setLoading((prev) => ({ ...prev, count: false }));
    }
  }, [authDispatch]);

  /**
   * Mark a single mention as seen
   * @param {number} mentionId - The ID of the mention to mark as seen
   */
  const markMentionAsSeen = useCallback(
    async (mentionId) => {
      setLoading((prev) => ({ ...prev, markSeen: true }));
      try {
        const sdk = new MkdSDK();
        const result = await sdk.callRawAPI(
          `/v3/api/custom/goodbadugly/mentions/${mentionId}/mark-seen`,
          {},
          "PUT"
        );

        if (!result.error) {
          // Update local state to reflect the change
          setMentions((prevMentions) =>
            prevMentions.map((mention) =>
              mention.id === mentionId ? { ...mention, seen: 1 } : mention
            )
          );

          // Update unread count
          setUnreadCount((prevCount) => Math.max(0, prevCount - 1));

          return true;
        }
        return false;
      } catch (error) {
        console.error("Error marking mention as seen:", error);
        const message = error?.response?.data?.message ?? error.message;
        showToast(globalDispatch, message, 5000, "error");
        tokenExpireError(authDispatch, message);
        return false;
      } finally {
        setLoading((prev) => ({ ...prev, markSeen: false }));
      }
    },
    [authDispatch, globalDispatch]
  );

  /**
   * Mark all mentions as seen, optionally for a specific update
   * @param {number} [updateId] - Optional update ID to only mark mentions from that update
   */
  const markAllMentionsAsSeen = useCallback(
    async (updateId = null) => {
      setLoading((prev) => ({ ...prev, markAllSeen: true }));
      try {
        const sdk = new MkdSDK();
        const payload = updateId ? { update_id: updateId } : {};

        const result = await sdk.callRawAPI(
          "/v3/api/custom/goodbadugly/mentions/mark-all-seen",
          payload,
          "PUT"
        );

        if (!result.error) {
          if (updateId) {
            // Only mark mentions from this update as seen
            setMentions((prevMentions) =>
              prevMentions.map((mention) =>
                mention.update_id === updateId
                  ? { ...mention, seen: 1 }
                  : mention
              )
            );

            // Update unread count (count how many were marked as seen)
            const markedCount = mentions.filter(
              (m) => m.update_id === updateId && m.seen === 0
            ).length;
            setUnreadCount((prevCount) => Math.max(0, prevCount - markedCount));
          } else {
            // Mark all mentions as seen
            setMentions((prevMentions) =>
              prevMentions.map((mention) => ({ ...mention, seen: 1 }))
            );
            setUnreadCount(0);
          }

          return true;
        }
        return false;
      } catch (error) {
        console.error("Error marking all mentions as seen:", error);
        const message = error?.response?.data?.message ?? error.message;
        showToast(globalDispatch, message, 5000, "error");
        tokenExpireError(authDispatch, message);
        return false;
      } finally {
        setLoading((prev) => ({ ...prev, markAllSeen: false }));
      }
    },
    [authDispatch, globalDispatch, mentions]
  );

  return {
    loading,
    mentions,
    unreadCount,
    fetchMentions,
    fetchUnreadCount,
    markMentionAsSeen,
    markAllMentionsAsSeen,
  };
};
