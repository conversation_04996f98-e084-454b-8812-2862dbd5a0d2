import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{a as je,u as ye,bD as ke,G as ve,L as j,I as Ne,o as de,s as v,v as Ce,M as z,T as Me}from"./index-f2ad9142.js";import{r as u,b as Pe}from"./vendor-4cdf2bd1.js";import{h as G}from"./moment-a9aaa855.js";import{u as _e}from"./useUpdate-3cd97540.js";import{M as H}from"./index-713720be.js";import{A as Se,G as Ue}from"./index-4342bf32.js";import{R as ce,b as Ie,U as Ae}from"./index-4e4ee51a.js";import{u as Le}from"./useCompanyMember-0033d2de.js";import{u as Ye}from"./useSubscription-dc563085.js";import{M as S}from"./index-dc002f62.js";import{D as Te}from"./DocumentTextIcon-54b5e200.js";import{D as $e}from"./DocumentIcon-22c47322.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const ft=({onUpdateSuccess:B})=>{var J,Q,X,Z,D,ee,te,se,ne,re,ie,le,ae;const b=u.useRef(null),U=u.useRef(null),[L,W]=u.useState(!1),[me,M]=u.useState(!1),[F,I]=u.useState(!1),[w,Y]=u.useState(!1),[l,P]=u.useState(null),{authDispatch:ue,authState:A,globalState:h,setGlobalState:T,showToast:Re}=je(),{triggerUpdate:K}=h;u.useState({startHerePrompt:!0,skipAwareness:!1});const g=Pe(),{profile:s,updateProfile:Ee}=ye({isPublic:!1}),{loading:Oe,data:t,processRegisteredDate:fe,getSubscription:$,getCustomerSubscription:pe,getSentUpdates:R}=Ye(),{companyMember:{myMembers:N},loading:E,getMyCompanyMembers:xe}=Le({filter:[`company_id,eq,${(Q=(J=s==null?void 0:s.companies)==null?void 0:J[0])==null?void 0:Q.id}`]}),{loading:C,updates:d,getUpdates:he}=_e(),r=((X=h==null?void 0:h[ke.createModel])==null?void 0:X.loading)||!1;u.useEffect(()=>{!r&&l&&P(null)},[r]);const{dispatch:y}=u.useContext(ve),o=h==null?void 0:h.subscriptionData,O={pro:5,"pro yearly":5,business:10,"business yearly":10,enterprise:1/0};u.useEffect(()=>{var n,a,i,m;s!=null&&s.id&&(xe({filter:[`company_id,eq,${((a=(n=s==null?void 0:s.companies)==null?void 0:n[0])==null?void 0:a.id)||""}`]}),pe(),$({filter:[`user_id,eq,${s==null?void 0:s.id}`]}),fe(s==null?void 0:s.create_at),R(s),h!=null&&h.refreshSubscription&&T("refreshSubscription",!1),he({filter:[`company_id,eq,${((m=(i=s==null?void 0:s.companies)==null?void 0:i[0])==null?void 0:m.id)||""}`]}))},[s==null?void 0:s.id,h==null?void 0:h.refreshSubscription]);const[p,q]=u.useState(!1);u.useEffect(()=>{(async()=>{var a,i,m,x;if(t){if(t!=null&&t.trial_expired&&!(t!=null&&t.subscription)){Y(!0),q(!1);return}const f=(x=(m=(i=(a=t==null?void 0:t.object)==null?void 0:a.plan)==null?void 0:i.nickname)==null?void 0:m.split(" ")[0])==null?void 0:x.trim(),c=O[f]||(f!=null&&f.includes("enterprise")?1/0:0);(t==null?void 0:t.sentUpdates)>=c&&c!==1/0?Y(!0):Y(!1),q(!1)}})()},[t]),u.useEffect(()=>{s!=null&&s.id&&(q(!0),$({filter:[`user_id,eq,${s==null?void 0:s.id}`]}),R(s))},[s==null?void 0:s.id]),u.useEffect(()=>{var n;K&&(s!=null&&s.id)&&((n=b==null?void 0:b.current)==null||n.click(),T("triggerUpdate",!1))},[s==null?void 0:s.id,K]),u.useEffect(()=>{!r&&l===null&&(s!=null&&s.id)&&($({filter:[`user_id,eq,${s==null?void 0:s.id}`]}),R(s))},[r,l,s==null?void 0:s.id]),u.useEffect(()=>{var n,a,i;!E&&!(N!=null&&N.length)?(n=U==null?void 0:U.current)==null||n.click():!(C!=null&&C.list)&&!((a=d==null?void 0:d.list)!=null&&a.length)&&((i=b==null?void 0:b.current)==null||i.click())},[E,C==null?void 0:C.list]),console.log("UpdateAIButton render");async function ge(){var i,m,x,f;if(l!==null||r||p||w)return;if(t!=null&&t.trial_expired&&!(t!=null&&t.subscription)){v(y,"Please upgrade your account to create an update!",5e3,"error"),g("/member/billing?openManagePlan=true");return}const n=(f=(x=(m=(i=t==null?void 0:t.object)==null?void 0:i.plan)==null?void 0:m.nickname)==null?void 0:x.split(" ")[0])==null?void 0:f.trim(),a=O[n]||(n!=null&&n.includes("enterprise")?1/0:0);if((t==null?void 0:t.sentUpdates)>=a&&a!==1/0){v(y,"You have reached your monthly update limit for your current plan.",5e3,"error"),g("/member/billing?openManagePlan=true");return}try{P("blank");const c=await Ce(y,ue,"updates",{name:"Update Title",user_id:A.user,mrr:0,arr:0,cash:0,burnrate:0,date:G().format("MMM D, YYYY"),public_link_enabled:0,private_link_open:1,company_id:A.company.id},!1);await new z().callRawAPI("/v3/api/custom/goodbadugly/activities/draft",{update_id:c.data},"POST"),c!=null&&c.error||g(`/member/edit-updates/${c.data}?autofocus=true`),M(!1)}catch(c){console.error(c),v(y,c.message,5e3,"error"),P(null)}}async function V(){var i,m,x,f;if(l!==null||r||p||w)return;if(t!=null&&t.trial_expired&&!(t!=null&&t.subscription)){v(y,"Please upgrade your account to create an update!",5e3,"error"),g("/member/billing?openManagePlan=true");return}const n=(f=(x=(m=(i=t==null?void 0:t.object)==null?void 0:i.plan)==null?void 0:m.nickname)==null?void 0:x.split(" ")[0])==null?void 0:f.trim(),a=O[n]||(n!=null&&n.includes("enterprise")?1/0:0);if((t==null?void 0:t.sentUpdates)>=a&&a!==1/0){v(y,"You have reached your monthly update limit for your current plan.",5e3,"error"),g("/member/billing?openManagePlan=true");return}try{P("recent");const k=await new Me().create("updates",{name:`Update ${G().format("MMM D, YYYY")}`,user_id:A.user,mrr:0,arr:0,cash:0,burnrate:0,date:G().format("MMM D, YYYY"),public_link_enabled:0,private_link_open:1,company_id:A.company.id}),_=new z;await _.callRawAPI("/v3/api/custom/goodbadugly/activities/draft",{update_id:k.data},"POST");const oe=(await _.callRawAPI(`/v4/api/records/updates?filter=id,lt,${k.data}&page=1,1&order=id,desc`,void 0,"GET")).list[0];oe&&await be(oe.id,k.data),M(!1),g(`/member/edit-updates/${k.data}?autofocus=true`)}catch(c){console.error(c),v(y,c.message,5e3,"error"),P(null)}}async function be(n,a){const i=new z,{list:m}=await i.callRawAPI(`/v4/api/records/notes?filter=update_id,eq,${n}&order=id,asc`,void 0,"GET");for(let x=0;x<m.length;x++){const f=m[x];await i.callRawAPI("/v4/api/records/notes",{update_id:a,content:f.content,type:f.type,status:0},"POST")}}const we=()=>{var n,a,i,m,x,f,c,k,_;if(!(o!=null&&o.subscription)){v(y,"You need to upgrade your plan to Enterprise or Business to use AI",5e3,"warning"),g("/member/billing?openManagePlan=true");return}if((i=(a=(n=o==null?void 0:o.object)==null?void 0:n.plan)==null?void 0:a.nickname)!=null&&i.toLowerCase().includes("pro")){v(y,"You need to upgrade your plan to Enterprise or Business to use AI",5e3,"warning"),g("/member/billing?openManagePlan=true");return}if((f=(x=(m=o==null?void 0:o.object)==null?void 0:m.plan)==null?void 0:x.nickname)!=null&&f.toLowerCase().includes("business")||(_=(k=(c=o==null?void 0:o.object)==null?void 0:c.plan)==null?void 0:k.nickname)!=null&&_.toLowerCase().includes("enterprise")){I(!0);return}g("/member/billing?openManagePlan=true")};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-[18px] hidden w-full flex-col flex-wrap items-center justify-between gap-5 md:flex md:flex-row",children:[e.jsxs("div",{className:"flex flex-row flex-wrap gap-2 justify-start items-center md:flex-nowrap md:gap-5",children:[!E&&!(N!=null&&N.length)?e.jsx(j,{children:e.jsx(H,{display:e.jsx(j,{children:e.jsx(Ne,{buttonRef:U,type:"button",className:"add-member-button my-4 flex h-[2.75rem] w-fit items-center justify-center whitespace-nowrap rounded-[.0625rem] bg-[#1f1d1a] px-2 py-2 !text-[1rem] tracking-wide text-white outline-none focus:outline-none ",color:"black",onClick:()=>{},children:"Add Team Members"})}),onPopoverStateChange:n=>{var a,i;n||(a=d==null?void 0:d.list)!=null&&a.length||(i=b==null?void 0:b.current)==null||i.click()},className:"",tooltipClasses:"h-fit max-h-fit min-h-fit !border-[.125rem] !border-primary-black",openOnClick:!0,place:"bottom-start",classNameArrow:"!border-b !border-r !border-primary-black",children:e.jsx(j,{children:e.jsx(Se,{})})})}):null,e.jsx(H,{display:e.jsx(j,{children:e.jsx("button",{ref:b,type:"button",disabled:r||l!==null||p,className:`my-4 flex h-[2.75rem] w-fit items-center justify-center whitespace-nowrap rounded-[.0625rem] bg-[#1f1d1a] !px-6 py-2 font-iowan !text-[1rem] tracking-wide text-white outline-none focus:outline-none ${w&&!p&&!r?"opacity-90":""}`,onClick:()=>{if(w){g("/member/billing?openManagePlan=true");return}},children:r||p?e.jsx("div",{className:"flex gap-2 items-center",children:e.jsx("span",{children:"Create Update"})}):(w&&t!=null&&t.trial_expired&&!(t!=null&&t.subscription),"Create Update")})}),tooltipClasses:"h-fit max-h-fit min-h-fit !border-[.125rem] !border-primary-black",openOnClick:!w&&!r&&l===null&&!p,place:"bottom-start",classNameArrow:"!border-b !border-r !border-primary-black",onPopoverStateChange:n=>{n||T("triggerUpdate",!1)},children:p?e.jsx("div",{className:"p-6 h-fit max-h-fit min-h-fit",children:e.jsxs("div",{className:"flex justify-center items-center",children:[e.jsx(S,{size:20,color:"#000000",loading:!0,type:"beat"}),e.jsx("span",{className:"ml-2",children:"Checking update limits..."})]})}):w?e.jsxs("div",{className:"row-span-12 grid h-full max-h-full min-h-full w-full grid-cols-1 grid-rows-12 items-center justify-center p-5 md:w-[250px]",children:[e.jsx("div",{className:"text-center row-span-8",children:e.jsx("p",{children:t!=null&&t.trial_expired&&!(t!=null&&t.subscription)?"Please Upgrade your account to create an update!":"You have reached your monthly update limit for your current plan."})}),e.jsx("div",{className:"row-span-4 w-full",children:e.jsx("button",{className:"flex w-full flex-col items-center justify-center gap-5 rounded-[.125rem] bg-primary-black px-4 py-2 font-iowan text-[1rem] font-[700] leading-5 text-white",onClick:()=>{g("/member/billing?openManagePlan=true")},children:t!=null&&t.trial_expired&&!(t!=null&&t.subscription)?"Subscribe":"Upgrade Plan"})})]}):e.jsx("div",{className:"h-fit max-h-fit min-h-fit",children:e.jsx(Ue,{showModifyRecentOption:((Z=d==null?void 0:d.list)==null?void 0:Z.length)>0,onModifyRecent:V,limitReached:w,limitChecking:p,subscriptionData:t})})}),e.jsx(j,{children:e.jsx(ce,{onSuccess:B})})]}),e.jsx(j,{children:e.jsx(Ie,{})})]}),e.jsx("div",{className:"fixed right-4 bottom-4 z-50 md:hidden",children:e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsx("button",{onClick:we,className:"flex h-[60px] min-h-[60px] w-[60px] min-w-[60px] items-center justify-center rounded-full bg-[#1f1d1a]",children:e.jsx(j,{children:e.jsx("img",{src:"/updateai.svg",alt:"",className:"object-cover w-8 h-8"})})}),e.jsxs("div",{className:"relative",children:[e.jsx("button",{onClick:()=>W(!L),className:"flex h-[60px] min-h-[60px] w-[60px] min-w-[60px] items-center justify-center rounded-full bg-[#1f1d1a]",children:L?e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M18 6L6 18M6 6L18 18",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}):e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12 5V19M5 12H19",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),L&&e.jsxs("div",{className:"absolute bottom-full right-0 mb-2 flex min-w-[132px] flex-col gap-2",children:[w?e.jsx(H,{display:e.jsx("button",{onClick:()=>{g("/member/billing?openManagePlan=true")},className:`flex h-[40px] w-[140px] items-center justify-center rounded-[4px] bg-[#1f1d1a] font-iowan text-base font-bold text-white ${r?"":"opacity-50"}`,children:t!=null&&t.trial_expired&&!(t!=null&&t.subscription)?"Subscribe":"Upgrade Plan"}),openOnClick:!1,backgroundColor:"#1f1d1a",place:"top",children:e.jsx("div",{className:"p-3 text-white",children:e.jsx("p",{className:"text-sm",children:t!=null&&t.trial_expired&&!(t!=null&&t.subscription)?"Please Upgrade your account to create an update!":"You have reached your monthly update limit for your current plan."})})}):e.jsx("button",{onClick:()=>{!r&&l===null&&!p&&!w&&(W(!1),M(!0))},disabled:r||l!==null||p,className:`flex h-[40px] w-[140px] items-center justify-center rounded-[4px] bg-[#1f1d1a] font-iowan text-base font-bold text-white ${r||l!==null||p?"cursor-not-allowed":""}`,children:r||p?e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsx("span",{children:"Create Update"}),e.jsx(S,{size:10,color:"#ffffff",loading:!0,type:"beat",className:"!ml-2"})]}):"Create Update"}),e.jsx(j,{children:e.jsx(ce,{onSuccess:B})})]})]})]})}),(o==null?void 0:o.subscription)&&(((te=(ee=(D=o==null?void 0:o.object)==null?void 0:D.plan)==null?void 0:ee.nickname)==null?void 0:te.toLowerCase().includes("business"))||((re=(ne=(se=o==null?void 0:o.object)==null?void 0:se.plan)==null?void 0:ne.nickname)==null?void 0:re.toLowerCase().includes("enterprise")))&&e.jsx(j,{children:e.jsx(de,{clickOutToClose:!0,modalHeader:!1,panelHeader:!0,modalHeaderClassName:"!bg-black text-brown-main-bg",title:e.jsx(j,{children:e.jsxs("div",{className:"flex gap-2 justify-start items-center",children:[e.jsx("img",{src:"/updateai.svg",alt:"",className:"object-cover w-8 h-8"}),e.jsxs("span",{className:"flex justify-start items-center",children:[e.jsx("b",{children:"Update"}),e.jsx("span",{className:"!font-thin",children:"AI"})]})]})}),isOpen:F,modalCloseClick:()=>I(!1),classes:{modalDialog:"!bg-black min-h-fit max-h-fit w-full !max-h-[500px] md:!w-fit !max-w-full min-w-full md:!min-w-[595px] !min-h-[550px] md:!max-w-[595px] overflow-y-auto !gap-0 !m-0 !mt-auto !rounded-t-2xl",modalContent:"!bg-black !text-brown-main-bg !z-10 !mt-0 overflow-y-auto max-h-[500px] !pt-0",modal:"h-full items-end",modal:"!p-0",panelClassName:"!bg-[#565452]"},children:F?e.jsx("div",{children:e.jsx(Ae,{onClose:()=>I(!1),onSuccess:()=>{I(!1)}})}):null})}),e.jsx(de,{panelHeader:!0,clickOutToClose:!r&&!l,isOpen:me,modalCloseClick:()=>{!r&&!l&&M(!1)},classes:{modalDialog:"!bg-brown-main-bg h-fit min-h-fit max-h-fit w-full !max-w-full !min-w-full !rounded-t-xl !m-0 !mt-auto",modalContent:"!bg-brown-main-bg !z-10 !mt-0 overflow-y-auto !pt-0 !rounded-b-0",modal:"h-full items-end !p-0"},children:e.jsxs("div",{className:"h-fit max-h-fit min-h-fit",children:[p?e.jsxs("div",{className:"flex justify-center items-center p-8",children:[e.jsx(S,{size:20,color:"#000000",loading:!0,type:"beat"}),e.jsx("span",{className:"ml-2",children:"Checking update limits..."})]}):e.jsx("div",{className:`flex w-full items-start gap-4 py-4 ${((ie=d==null?void 0:d.list)==null?void 0:ie.length)>0?"hidden":""}`,children:e.jsxs("div",{children:[e.jsx("div",{className:"flex gap-2 items-center",children:e.jsx("span",{className:"text-left font-iowan text-[18px] font-semibold md:text-[20px]",children:((le=d==null?void 0:d.list)==null?void 0:le.length)>0?"":"Start Here"})}),e.jsx("p",{className:"mt-1 text-[14px] font-medium md:text-[14px]",children:"Create an update by selecting:"})]})}),!p&&((ae=d==null?void 0:d.list)==null?void 0:ae.length)>0&&e.jsxs("button",{disabled:l!==null||r,onClick:()=>{l===null&&!r&&V()},className:"mt-6 flex h-fit max-h-fit min-h-fit w-full items-start justify-start gap-4 border-b border-b-[#1f1d1a]/10 bg-brown-main-bg px-1 py-4 hover:bg-brown-main-bg/50",children:[e.jsx("div",{className:"flex items-center justify-center gap-2 rounded-[.625rem] border border-[#1f1d1a] p-2",children:e.jsx("img",{src:"/assets/edit-2.svg",alt:"",className:"w-8 h-8"})}),e.jsxs("div",{className:"flex-grow",children:[e.jsx("p",{className:"text-left text-[14px] font-semibold md:text-xl",children:"Modify with Recent"}),e.jsx("p",{className:"md: mt-1 text-[12px] font-medium",children:"Start with most recent sent update"})]}),(l==="recent"||r)&&e.jsx("div",{className:"flex items-center",children:e.jsx(S,{size:10,color:"#000000",loading:!0,type:"beat",className:"ml-2"})})]}),!p&&e.jsxs(e.Fragment,{children:[e.jsxs("button",{disabled:l!==null||r,onClick:()=>{l===null&&!r&&(M(!1),g("/member/select-template"))},className:`flex w-full items-start gap-4 border-b border-b-[#1f1d1a]/10 px-1 py-4 hover:bg-brown-main-bg/50 ${l!==null||r?"cursor-not-allowed":""}`,children:[e.jsx("div",{className:"flex items-center justify-center gap-2 rounded-[.625rem] border border-[#1f1d1a] p-2",children:e.jsx(Te,{className:"w-8 h-8 text-primary-black",strokeWidth:2})}),e.jsxs("div",{className:"flex-grow",children:[e.jsx("p",{className:"text-left text-[14px] font-semibold md:text-xl",children:"New Template Update"}),e.jsx("p",{className:"md: mt-1 text-[12px] font-medium",children:"Start an update using an existing update templates"})]})]}),e.jsxs("button",{disabled:l!==null||r,onClick:()=>{l===null&&!r&&ge()},className:"flex h-fit max-h-fit min-h-fit w-full items-start justify-start gap-4 border-b border-b-[#1f1d1a]/10 bg-brown-main-bg px-1 py-4 hover:bg-brown-main-bg/50",children:[e.jsx("div",{className:"flex items-center justify-center gap-2 rounded-[.625rem] border border-[#1f1d1a] p-2",children:e.jsx($e,{className:"w-8 h-8 text-primary-black",strokeWidth:2})}),e.jsxs("div",{className:"flex-grow",children:[e.jsx("p",{className:"text-left text-[14px] font-semibold md:text-xl",children:"New Blank Update"}),e.jsx("p",{className:"md: mt-1 text-[12px] font-medium",children:"Start an update from scratch"})]}),(l==="blank"||r&&l==="blank")&&e.jsx("div",{className:"flex items-center",children:e.jsx(S,{size:10,color:"#000000",loading:!0,type:"beat",className:"ml-2"})})]})]})]})})]})};export{ft as default};
