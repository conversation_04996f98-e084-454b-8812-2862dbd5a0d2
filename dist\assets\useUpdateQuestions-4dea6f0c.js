import{A as l,G as p,M as h,t as d,s as x}from"./index-f2ad9142.js";import{r as t}from"./vendor-4cdf2bd1.js";function k(s){const[r,o]=t.useState(!1),[n,i]=t.useState([]),{dispatch:c}=t.useContext(l),{dispatch:u}=t.useContext(p);async function a(){o(!0);try{const f=await new h().callRawAPI(`/v4/api/records/update_questions?filter=update_id,eq,${s}&join=user|investor_id`);i(f.list)}catch(e){d(c,e.message),x(u,e.message,5e3,"error")}o(!1)}return t.useEffect(()=>{s&&a()},[s]),{loading:r,questions:n,refetch:a}}export{k as u};
