import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{o as ke}from"./yup-0917e80c.js";import{b as Ne,a as Ce,u as Ie,au as Ae,I as q,a6 as Le,L as ie,d as Pe,av as Me,M as Re}from"./index-f2ad9142.js";import{r,h as Oe,R as $e,b as Fe}from"./vendor-4cdf2bd1.js";import{u as Ve}from"./react-hook-form-a383372b.js";import{c as ze,a as Q}from"./yup-342a5df4.js";import{M as qe}from"./index-d07d87ac.js";import{M as U}from"./MkdInput-d37679e9.js";import{A as Qe}from"./AddButton-51d1b2cd.js";import{getCorrectOperator as D,getCorrectValueTypeFormat as Ue}from"./MkdListTableV2-db78e8c5.js";import{_ as v}from"./qr-scanner-cf010ec4.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import{C as De}from"./ClipboardDocumentIcon-f03b0627.js";const Be=r.lazy(()=>v(()=>import("./TeamModal-77686f18.js"),["assets/TeamModal-77686f18.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/yup-0917e80c.js","assets/@hookform/resolvers-b50d6e2a.js","assets/react-hook-form-a383372b.js","assets/yup-342a5df4.js","assets/index-d07d87ac.js","assets/MkdInput-d37679e9.js","assets/react-toggle-6478c5c4.js","assets/@uppy/dashboard-51133bb7.js","assets/@fullcalendar/core-085b11ae.js","assets/core-b9802b0d.css","assets/@uppy/core-a4ba4b97.js","assets/@uppy/aws-s3-a6b02742.js","assets/@craftjs/core-a2cdaeb4.js","assets/@uppy/compressor-4bcbc734.js","assets/MkdInput-5e6afe8d.css","assets/AddButton-51d1b2cd.js","assets/MkdListTableV2-db78e8c5.js","assets/index-9dceff66.js","assets/ExportButton-eb4cf1f9.js","assets/index.esm-54e24cf9.js","assets/react-icons-36ae72b7.js","assets/lucide-react-0b94883e.js","assets/MkdListTableV2-e3b0c442.css","assets/ClipboardDocumentIcon-f03b0627.js"])),ps=r.lazy(()=>v(()=>import("./RemoveTeamMember-238d9818.js"),["assets/RemoveTeamMember-238d9818.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-8c774937.js","assets/qr-scanner-cf010ec4.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/useCompanyMember-0033d2de.js"])),fs=r.lazy(()=>v(()=>import("./AcceptTeamRequest-aeee98e5.js"),["assets/AcceptTeamRequest-aeee98e5.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-8c774937.js","assets/qr-scanner-cf010ec4.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"])),gs=r.lazy(()=>v(()=>import("./RejectTeamRequest-27bac885.js"),["assets/RejectTeamRequest-27bac885.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-8c774937.js","assets/qr-scanner-cf010ec4.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"])),hs=r.lazy(()=>v(()=>import("./ResendTeamRequest-8ecb02ce.js"),["assets/ResendTeamRequest-8ecb02ce.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-8c774937.js","assets/qr-scanner-cf010ec4.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/useCompanyMember-0033d2de.js"])),Ke=j=>{let S=[];return new Set(j.map(u=>u==null?void 0:u.accessor)).forEach(u=>{const c=j.filter(l=>l.accessor===u);if((c==null?void 0:c.length)>0){const l=c.filter(d=>d==null?void 0:d.value);if(l.length>1)l.forEach(d=>{const{accessor:y,operator:m,value:k}=d,B=`goodbadugly_user.${y},${m==="cs"||m==="eq"?D("o"+m,k):D(m,k)},${k}`;S.push(B)});else if(l.length===1){const{accessor:d,operator:y,value:m}=l[0];S.push(`goodbadugly_user.${d},${D(y,m)},${Ue(m,y)}`)}}}),S},Te=()=>{const j=[{header:"First Name",accessor:"first_name",join:"user",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"teams"},{header:"Last Name",accessor:"last_name",join:"user",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"teams"},{header:"Email",accessor:"email",join:"user",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"font-iowan"},{header:"Last Login",accessor:"last_login_at",join:"user",isSorted:!0,isSortedDesc:!1,mappingExist:!1,date:!0,mappings:{},selected_column:!0},{size:"reduce",header:"Invitations",accessor:"member_status",isSorted:!0,isSortedDesc:!1,mappingExist:!0,mappingType2:!0,type:"version2",mappings:{0:{text:"Outgoing",bg:"#F6A13C",color:"black",type:"version2"},1:{text:"Incoming",bg:"#9DD321",color:"black",type:"version2"}},selected_column:!0},{size:"reduce",header:"Status",accessor:"member_status",isSorted:!0,isSortedDesc:!1,mappingExist:!0,mappings:{1:{text:"Active",bg:"#9DD321",color:"black"},0:{text:"pending",bg:"#F6A13C",color:"black"},2:{text:"Request",bg:"#F6A13C",color:"black"}},selected_column:!0},{header:"Action",accessor:""}],{sdk:S,tdk:E,operations:u}=Ne(),c=r.useRef(null),l=r.useRef(null),d=r.useRef(null),{authState:y,showToast:m,tokenExpireError:k,getSingle:B,globalState:P,setGlobalState:le}=Ce();r.useState(""),r.useState(!1);const[N,Ge]=Oe();r.useState("");const[K,T]=r.useState(!1),[p,ce]=r.useState({modal:null,showModal:!1,seletedItems:[]}),G=btoa("user:email"),H=btoa("user:first_name"),J=btoa("user:last_name"),C=N.get("trigger"),I=N.get("update_id"),[f,de]=$e.useState([{accessor:"email",operator:"cs",value:"",uid:G},{accessor:"last_name",operator:"cs",value:"",uid:J},{accessor:"first_name",operator:"cs",value:"",uid:H}]);r.useState(parseInt(N.get("page")||"1"));const[h,M]=r.useState({page:1,data:[],limit:0,pages:0,total:0,use:!0,reload:!1,canNextPage:!1,canPreviousPage:!1});r.useState(parseInt(N.get("limit")||"30"));const ue=r.useMemo(()=>JSON.stringify(f),[f]),W=Fe(),{profile:o,updateProfile:He}=Ie({isPublic:!1}),me=ze({last_name:Q().nullable(),first_name:Q().nullable(),email:Q().nullable().email("Invalid email")}),[X,pe]=r.useState([]),{register:R,handleSubmit:fe,setError:Je,reset:ge,watch:he,formState:{errors:O,isSubmitting:We}}=Ve({resolver:ke(me),defaultValues:{email:"",last_name:"",first_name:""}}),{email:Y,first_name:Z,last_name:ee}=he(),$=r.useCallback((t,n,s)=>{de(i=>i.map(_=>(_==null?void 0:_.uid)===s?{..._,[t]:n}:_))},[f,ue]),_e=r.useCallback(async(t,n,s)=>{var se,te,re,oe;d.current&&d.current.abort();const i=new AbortController;d.current=i;const _=i.signal,A=[...Ke(f),...s==null?void 0:s.filterConditions,`company_id,eq,${(te=(se=o==null?void 0:o.companies)==null?void 0:se[0])==null?void 0:te.id}`];try{const[x,ve,je]=await Promise.all([E.getPaginate("company_member",{size:n,page:t,join:["user|member_id"],...s!=null&&s.order?{order:s==null?void 0:s.order,direction:s==null?void 0:s.direction}:null,filter:A!=null&&A.length?A:void 0},_),E.getList("company_invites",{filter:[`user_id,eq,${o==null?void 0:o.id}`],join:["user|inviter_id"],order:"id,desc"}),E.getList("company_invites",{filter:[`company_id,eq,${(oe=(re=o==null?void 0:o.companies)==null?void 0:re[0])==null?void 0:oe.id}`],join:["user|user_id"],order:"id,desc"})]);let{list:ae,total:Ye,limit:F,num_pages:Ze,page:V}=x;pe(ae);const Se=ve.list||[],Ee=je.list||[],b=new Map;ae.filter(a=>a.member_status===1).forEach(a=>{b.set(a.member_id,{...a,is_active:!0})}),Se.forEach(a=>{if(a.user_id===(o==null?void 0:o.id)){const w=a.inviter_id;b.has(w)||b.set(w,{id:`invite-${a.id}`,member_id:w,user:a.user,invite_id:a.id,invited_at:a.invited_at,invite_status:a.status,is_incoming:!0,member_status:1,company_id:a.company_id})}}),Ee.forEach(a=>{var w,ne;if(a.company_id===((ne=(w=o==null?void 0:o.companies)==null?void 0:w[0])==null?void 0:ne.id)&&a.user_id!==(o==null?void 0:o.id)){const z=a.user_id;b.has(z)||b.set(z,{id:`invite-${a.id}`,member_id:z,user:a.user,invite_id:a.id,invited_at:a.invited_at,invite_status:a.status,is_incoming:!1,member_status:0,company_id:a.company_id})}});const L=Array.from(b.values());M(a=>({...a,data:L,page:V,limit:F,total:L.length,pages:Math.ceil(L.length/F),canPreviousPage:V>1,canNextPage:V*F<L.length}))}catch(x){x.name==="AbortError"?console.log("Fetch aborted"):console.error("ERROR",x)}finally{M(x=>({...x,reload:!0})),d.current===i&&(console.info("abortControllerRef.current null"),d.current=null)}},[f,o]),xe=t=>{var n;(n=c==null?void 0:c.current)==null||n.click()},{fetchInvitations:be}=Ae(),g=(t,n,s=[])=>{console.log(s,"ids"),ce(i=>({...i,seletedItems:s,showModal:n,modal:n?t:null}))},we=()=>{var t;(t=c==null?void 0:c.current)==null||t.click(),be(!1),g(null,!1),le("memberChange",!(P!=null&&P.memberChange)),["add"].includes(C)&&I&&W(`/member/edit-updates/${I}`)};r.useEffect(()=>{var t;l!=null&&l.current&&(h!=null&&h.reload)&&(M(n=>({...n,reload:!1})),(t=l==null?void 0:l.current)==null||t.click())},[h==null?void 0:h.reload,l==null?void 0:l.current]),r.useEffect(()=>{$("value",Y,G)},[Y]),r.useEffect(()=>{$("value",Z,H)},[Z]),r.useEffect(()=>{$("value",ee,J)},[ee]),r.useEffect(()=>{["add"].includes(C)&&g("add",!0)},[C]),r.useEffect(()=>{var n;(f==null?void 0:f.every(s=>(s==null?void 0:s.value)==""))&&K&&((n=c==null?void 0:c.current)==null||n.click(),T(!1))},[K,f]);const ye=async()=>{const t=new Re;try{await t.exportCSVTeam()}catch(n){console.error("Export error:",n),m("Failed to export data",5e3,"error")}};return e.jsxs(r.Fragment,{children:[e.jsxs("div",{className:"  grid h-full max-h-full min-h-full w-full grid-rows-[auto_auto_auto_1fr] space-y-[1rem] rounded  bg-brown-main-bg p-5 px-5 md:px-8",children:[e.jsxs("div",{className:"my-[16px] flex w-full items-center justify-between gap-5",children:[e.jsx("h4",{className:" font-iowan text-[18px] font-semibold md:text-[24px]",children:"Team Members"}),e.jsxs("div",{className:"flex gap-5 justify-start items-center w-fit",children:[e.jsxs(q,{type:"button",className:"flex !h-[2.25rem] w-fit items-center justify-center whitespace-nowrap !rounded-[0.125rem] !border border-black bg-transparent !py-[0.5rem] px-2 !text-[1rem] tracking-wide text-black outline-none focus:outline-none md:px-5",color:"black",onClick:()=>{ye()},children:[e.jsx(Le,{})," Export"]}),e.jsx(q,{type:"button",className:"flex !h-[2.25rem] w-fit items-center justify-center whitespace-nowrap !rounded-[0.125rem] !border bg-[#1f1d1a] !py-[0.5rem] px-2 !text-[1rem] tracking-wide text-white outline-none focus:outline-none md:px-5",color:"black",onClick:()=>{g("add",!0)},children:"Add New"})]})]}),e.jsx("div",{className:"flex gap-5 items-center w-full",children:e.jsxs("div",{className:"flex w-full flex-wrap items-end gap-2 md:w-[75%] md:flex-nowrap",children:[e.jsx("div",{children:e.jsx(U,{type:"text",errors:O,register:R,name:"first_name",label:"First Name",placeholder:"Search",className:"!h-[2.25rem] !rounded-[0.125rem] !border !py-[0.5rem]"})}),e.jsx("div",{children:e.jsx(U,{type:"text",errors:O,register:R,name:"last_name",label:"Last Name",placeholder:"Search",className:"!h-[2.25rem] !rounded-[0.125rem] !border !py-[0.5rem]"})}),e.jsx("div",{children:e.jsx(U,{type:"text",errors:O,register:R,name:"email",label:"Email",placeholder:"Search",className:"!h-[2.25rem] !rounded-[0.125rem] !border !py-[0.5rem]"})}),e.jsx(q,{type:"submit",className:"flex !h-[2.25rem] w-fit items-center justify-center whitespace-nowrap !rounded-[0.125rem] !border bg-[#1f1d1a]  !py-[0.5rem] px-2 !text-[1rem] tracking-wide text-white outline-none focus:outline-none md:px-5",color:"black",disabled:!(o!=null&&o.id),onClick:fe(xe,t=>{console.log("error >>",t)}),children:"Search"}),e.jsx(Qe,{onClick:()=>{ge(),T(!0)},showPlus:!1,className:"!w-fit !min-w-fit !max-w-fit !border-0 !bg-transparent !p-0 !font-inter !text-[1rem] !font-[600] !leading-[1.21rem] !tracking-wide !text-black !underline !shadow-none ",children:"Clear"})]})}),e.jsx("div",{className:"h-[.125rem] w-full border-[.125rem] border-black bg-black "}),o!=null&&o.id?e.jsx(qe,{showSearch:!1,useDefaultColumns:!0,defaultColumns:[...j],noDataComponent:{use:!0,component:e.jsx(ie,{children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx(De,{className:"w-8 h-8 text-gray-700",strokeWidth:2}),e.jsx("p",{className:"mt-4 text-base font-medium text-center",children:"No Team Members added yet"})]})})},onUpdateCurrentTableData:t=>{t(h)},externalData:{...h,fetch:_e},hasFilter:!1,tableRole:"admin",table:"order",actionId:"member_id",actions:{view:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},remove:{show:!0,action:t=>{var s;const n=(s=X.find(i=>(i==null?void 0:i.member_id)==t))==null?void 0:s.id;g("delete",!0,[n,t])},multiple:!0,children:"Remove",icon:e.jsx(Pe,{fill:"#292D32"}),locations:["dropdown"],bind:{column:["member_status"],action:"hide",logic:"or",operator:[u.EQUAL],ifValue:[2]}},resend:{show:!0,action:t=>{var s;const n=(s=X.find(i=>(i==null?void 0:i.member_id)==t))==null?void 0:s.id;g("resend",!0,[n,t])},multiple:!0,children:e.jsx("span",{className:"ml-[5px]",children:"Resend"}),showChildren:!0,icon:e.jsx(Me,{fill:"#292D32"}),locations:["dropdown"],bind:{column:["member_status","is_incoming"],action:"hide",logic:"or",operator:[u.NOT_EQUAL,u.EQUAL],ifValue:[0,!0]}},accept:{show:!0,action:t=>g("accept",!0,t),multiple:!0,children:({row:t,actionId:n})=>{const s=t.invite_id;return e.jsxs("div",{className:"flex gap-2 w-full",children:[e.jsx("button",{onClick:i=>{i.stopPropagation(),s&&g("reject",!0,[s,n])},className:"flex-1 rounded-sm border border-[#1f1d1a] px-4 py-1.5 text-sm font-medium hover:bg-gray-50 disabled:opacity-50",children:"Reject"}),e.jsx("button",{onClick:i=>{i.stopPropagation(),s&&g("accept",!0,[s,t.member_id])},className:"flex-1 rounded-sm bg-[#1f1d1a] px-4 py-1.5 text-sm font-medium text-white hover:bg-[#2a2724] disabled:opacity-50",children:"Accept"})]})},showChildren:!0,locations:["buttons"],bind:{column:["member_status","is_incoming"],action:"hide",logic:"or",operator:[u.NOT_EQUAL,u.NOT_EQUAL],ifValue:[2,!0]}},view_all:{show:!1,type:"static",action:()=>{},children:e.jsx(e.Fragment,{children:"View All"}),className:"!gap-2 !bg-transparent !text-black !underline !shadow-none !border-0"},add:{show:!1,multiple:!0,children:"+ Add"},export:{show:!1,action:null,multiple:!0}},defaultPageSize:20,showPagination:!0,refreshRef:c,updateRef:l,maxHeight:"md:grid-rows-[inherit] grid-rows-[inherit]",actionPostion:["dropdown","buttons"]}):null]}),e.jsx(ie,{children:e.jsx(Be,{modal:p==null?void 0:p.modal,isOpen:p==null?void 0:p.showModal,onClose:()=>{g(null,!1),["add"].includes(C)&&I&&W(`/member/edit-updates/${I}`)},ids:p==null?void 0:p.seletedItems,onSuccess:we})})]})},_s=Object.freeze(Object.defineProperty({__proto__:null,default:Te},Symbol.toStringTag,{value:"Module"}));export{fs as A,hs as R,_s as T,gs as a,ps as b};
