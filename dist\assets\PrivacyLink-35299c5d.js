import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{G as l,s as d,D as c}from"./index-f2ad9142.js";import{r as o,i as u}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const G=({link:r,update:x,iconFill:i,className:s,inputProps:n={},buttonProps:p={}})=>{o.useState(!1);const{dispatch:m}=o.useContext(l),a=o.useId();return u(),t.jsx(t.Fragment,{children:t.jsxs("div",{className:`mt-6 grid grid-cols-[1fr_auto] grid-rows-1 items-center gap-2 rounded-md bg-[#F2DFCE] px-4 py-3 md:gap-12 ${s}`,children:[t.jsx("div",{children:t.jsx("input",{value:r,...n,id:`private-link-input_${a}`,onFocus:e=>e.target.select(),className:"no-box-shadow custom-overflow2 w-full select-none overflow-x-auto border-none bg-transparent px-2 py-0 text-sm font-medium text-[#1f1d1a] ring-transparent selection:bg-green-200 md:text-lg",readOnly:!0,"aria-readonly":!0})}),t.jsxs("button",{type:"button",...p,className:"!flex w-fit min-w-fit max-w-fit items-center gap-2 font-bold text-[#1f1d1a] underline",onClick:async()=>{var e;await navigator.clipboard.writeText(r),(e=document.getElementById(`private-link-input_${a}`))==null||e.select(),d(m,"Copied")},children:[t.jsx(c,{...i?{fill:i}:null})," ",t.jsx("span",{children:"Copy"})]})]})})};export{G as default};
