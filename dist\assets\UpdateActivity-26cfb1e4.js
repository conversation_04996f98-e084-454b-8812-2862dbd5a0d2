import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{a as oe,M as ie,u as ae,L as P,A as ce,O as me,f as fe,C as v}from"./index-f2ad9142.js";import{M as de}from"./index-d07d87ac.js";import{R as Z,r as S,b as xe,L as ge}from"./vendor-4cdf2bd1.js";import{R as J}from"./index-64a9a9df.js";import{U as he,b as ee}from"./index-2a0f7dff.js";import{a as se}from"./index-590fd997.js";import re from"./MkdPopover-4a64f030.js";import{M as ne}from"./index-713720be.js";import{u as we}from"./useDate-c1da5729.js";import"./moment-a9aaa855.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-tooltip-a338585f.js";import"./@mantine/core-38f49ae4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const be=(N={filter:[]})=>{const{authDispatch:k,globalDispatch:n,custom:h}=oe(),[m,j]=Z.useState({list:[],single:null}),[d,C]=Z.useState({list:!1,single:!1,update:!1,delete:!1,create:!1}),c=Z.useCallback((F={filter:[]})=>(async()=>{C(p=>({...p,list:!0}));try{const x=await new ie().callRawAPI("/v3/api/custom/goodbadugly/new-activities",{},"GET");return x!=null&&x.error?null:(j($=>({...$,list:(x==null?void 0:x.data)||[]})),{data:(x==null?void 0:x.data)||[],pagination:(x==null?void 0:x.pagination)||{}})}catch(p){return console.error("Error fetching activities:",p),null}finally{C(p=>({...p,list:!1}))}})(),[k,n]);return{loading:d,activities:m,getActivities:c}},pe=({isOpen:N,onClose:k,update:n,onAccept:h,onDecline:m,loading:j=!1})=>{var p,x,$,V,q;ae({isPublic:!1});const{convertDate:d}=we();if(!N)return null;const C=i=>{var M,Q,B;return(M=i==null?void 0:i.type)!=null&&M.includes("Collaboration Request")?"Collaboration Request":(Q=i==null?void 0:i.type)!=null&&Q.includes("Update Request")?"Update Request":(B=i==null?void 0:i.type)!=null&&B.includes("Update (In)")?"Update (In)":"Request"},c=i=>(i==null?void 0:i.status)===0||(i==null?void 0:i.status)===6?"Pending":(i==null?void 0:i.status)===1?"Accepted":(i==null?void 0:i.status)===2?"Rejected":(i==null?void 0:i.status)===7?"Received":"",F=i=>({0:"#F6A13C",1:"#9DD321",2:"#BCBBBA",6:"#F6A13C",7:"#CAB8FF"})[i]||"#BCBBBA";return r.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:r.jsxs("div",{className:"relative w-full max-w-md rounded-md bg-brown-main-bg p-6 shadow-xl",children:[r.jsx("button",{onClick:k,className:"absolute right-4 top-4 text-gray-500 hover:text-gray-700",children:r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),r.jsxs("div",{className:"flex flex-col items-center",children:[r.jsx(P,{children:r.jsx(ne,{display:r.jsx("p",{className:"w-full truncate text-center font-iowan text-[1.125rem] font-[700] capitalize leading-5 text-[#1f1d1a]",children:n==null?void 0:n.title}),openOnClick:!1,show:!!(n!=null&&n.company_name),backgroundColor:"#1f1d1a",place:"top",children:n!=null&&n.company_name?r.jsxs("p",{className:"mt-1 text-sm text-white",children:["(",n==null?void 0:n.company_name,")"]}):null})}),r.jsx("p",{className:"font-iowan-regular mt-3 text-[14px] font-normal text-[#1f1d1a]",children:d((n==null?void 0:n.activity_time)??(n==null?void 0:n.sent_at)??(n==null?void 0:n.date),{formatMatcher:"best fit",year:"numeric",month:"numeric",day:"numeric",timeZoneName:"short",timeZone:"America/Los_Angeles"}).replace(", "," - ")}),r.jsxs("div",{className:"mt-4 flex flex-col items-center justify-center gap-1",children:[r.jsx("span",{style:{backgroundColor:F(n==null?void 0:n.status)},className:"flex h-[1.5rem] w-full max-w-fit items-center justify-center whitespace-nowrap rounded-[100px] border-[.0625rem] border-[#1f1d1a] px-[10px] font-iowan text-xs",children:c(n)}),r.jsx(P,{children:r.jsx(ne,{display:r.jsx("p",{className:"font-iowan-regular text-black",children:C(n)}),openOnClick:!1,backgroundColor:"#1f1d1a",place:"top",children:r.jsx("p",{className:"mt-1 text-sm text-white",children:c(n)})})})]}),r.jsxs("div",{className:"mt-6 w-full",children:[r.jsx("p",{className:"mb-2 text-sm font-medium text-gray-700",children:"From:"}),r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"flex h-8 w-8 items-center justify-center overflow-hidden rounded-full bg-gray-200",children:(p=n==null?void 0:n.from)!=null&&p.photo?r.jsx("img",{src:n.from.photo,alt:`${n.from.first_name} ${n.from.last_name}`,className:"h-full w-full object-cover"}):r.jsx("span",{className:"text-sm font-medium",children:(($=(x=n==null?void 0:n.from)==null?void 0:x.first_name)==null?void 0:$[0])||""})}),r.jsxs("span",{className:"font-medium",children:[(V=n==null?void 0:n.from)==null?void 0:V.first_name," ",(q=n==null?void 0:n.from)==null?void 0:q.last_name]})]})]}),r.jsx("div",{className:"mt-6 w-full",children:r.jsx(he,{onDecline:()=>m(n),onAccept:()=>h(n),update:n,loading:j})})]})]})})},b={UPDATE_IN:"Update (In)",UPDATE_OUT:"Update (Out)",REQUEST_IN:"Update Request (In)",REQUEST_OUT:"Update Request (Out)",COLLABORATOR_IN:"Collaboration Request (In)",COLLABORATOR_OUT:"Collaboration Request (Out)"},le=[{header:"Title",accessor:"name",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,type:"dashboard"},{header:"Type",accessor:"type",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Status",accessor:"update_status",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Date",accessor:"date_created",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"To/From",accessor:"user_data",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0}],te={0:9,1:10,2:7,3:0,4:1,5:2,6:5,9:-2,7:8},ue=N=>{const k=new Date,n=new Date(N),h=k-n,m=Math.floor(h/(1e3*60)),j=Math.floor(h/(1e3*60*60)),d=Math.floor(h/(1e3*60*60*24));return m<60?`${m} ${m===1?"minute":"minutes"} ago`:j<24?`${j} ${j===1?"hour":"hours"} ago`:`${d} ${d===1?"day":"days"} ago`},Xe=({refreshRef:N})=>{var B,G;const[k,n]=S.useState(!1),h=S.useRef(null),[m,j]=S.useState({page:1,data:[],limit:0,pages:0,total:0,use:!0,reload:!1,canNextPage:!1,canPreviousPage:!1}),[d,C]=S.useState({updateSeenLoading:!1,showModal:!1,selectedUpdate:null,modalLoading:!1}),c=xe(),{getActivities:F}=be();oe(),S.useContext(ce),console.log(d==null?void 0:d.updateSeenLoading);const p=e=>{C(f=>({...f,showModal:!0,selectedUpdate:e}))},x=()=>{C(e=>({...e,showModal:!1,selectedUpdate:null,modalLoading:!1}))},$=async e=>{var f,s,t,a;C(o=>({...o,modalLoading:!0})),(f=e==null?void 0:e.type)!=null&&f.includes(b.COLLABORATOR_IN)?c(`/member/updates?view=team_updates&availability=available&update_id=${(s=e==null?void 0:e.update)==null?void 0:s.id}`):(t=e==null?void 0:e.type)!=null&&t.includes(b.REQUEST_IN)&&c(`/member/updates?view=my_updates&availability=available&update_id=${(a=e==null?void 0:e.update)==null?void 0:a.id}`),x()},V=async e=>{var f,s,t,a;C(o=>({...o,modalLoading:!0})),(f=e==null?void 0:e.type)!=null&&f.includes(b.COLLABORATOR_IN)?c(`/member/updates?view=team_updates&availability=available&update_id=${(s=e==null?void 0:e.update)==null?void 0:s.id}&action=decline`):(t=e==null?void 0:e.type)!=null&&t.includes(b.REQUEST_IN)&&c(`/member/updates?view=my_updates&availability=available&update_id=${(a=e==null?void 0:e.update)==null?void 0:a.id}&action=decline`),x()},q=async e=>{var f,s,t,a,o,l,T,_,O,R,A,I,U,y,u,L,E,w,g,D,z,H,Y,K,W,X;if(console.log(e,"upup"),(f=e==null?void 0:e.type)!=null&&f.includes(b.UPDATE_IN)&&e.status===6){c(`/member/update/private/view/${(s=e==null?void 0:e.update)==null?void 0:s.id}?new=${e==null?void 0:e.id}`);return}if((t=e==null?void 0:e.type)!=null&&t.includes(b.UPDATE_OUT))if(e.status==6){c(`/member/edit-updates/${(a=e==null?void 0:e.update)==null?void 0:a.id}?new=${e==null?void 0:e.id}`);return}else if(e.status==3){console.log("update","hipp"),c(`/member/edit-updates/${(o=e==null?void 0:e.update)==null?void 0:o.id}`);return}else if(e.status==8){console.log("update","hipp"),c(`/member/edit-updates/${(l=e==null?void 0:e.update)==null?void 0:l.id}`);return}else{c(`/member/update/private/view/${(T=e==null?void 0:e.update)==null?void 0:T.id}`);return}else if(((e==null?void 0:e.status)==1||(e==null?void 0:e.status)==7)&&((_=e==null?void 0:e.type)!=null&&_.includes(b.REQUEST_OUT)||(O=e==null?void 0:e.type)!=null&&O.includes(b.REQUEST_IN)))if(console.log(e==null?void 0:e.isChild,"child",!((R=e.update)!=null&&R.sent_at)),!((A=e.update)!=null&&A.sent_at)&&((I=e==null?void 0:e.type)!=null&&I.includes(b.REQUEST_IN))){c(`/member/edit-updates/${(U=e==null?void 0:e.update)==null?void 0:U.id}`);return}else if((y=e.update)!=null&&y.sent_at){c(`/member/update/private/view/${(u=e==null?void 0:e.update)==null?void 0:u.id}`);return}else return;else if((L=e==null?void 0:e.type)!=null&&L.includes(b.REQUEST_IN)){if(e.status===0||e.status===6){p(e);return}if(e.status===1||e.status===7||e.status===8){c(`/member/edit-updates/${(E=e==null?void 0:e.update)==null?void 0:E.id}`);return}return}else if((w=e==null?void 0:e.type)!=null&&w.includes(b.COLLABORATOR_IN)){if(e.status===0||e.status===6){p(e);return}if(e.status===1||e.status===7){c(`/member/edit-updates/${(g=e==null?void 0:e.update)==null?void 0:g.id}`);return}return}if((D=e==null?void 0:e.type)!=null&&D.includes(b.COLLABORATOR_OUT))return c(`/member/edit-updates/${(z=e==null?void 0:e.update)==null?void 0:z.id}`);if((H=e==null?void 0:e.type)!=null&&H.includes(b.REQUEST_OUT)){if(e.status===0){c(`/member/updates?view=team_updates&availability=available&update_id=${(Y=e==null?void 0:e.update)==null?void 0:Y.id}`);return}if(e.status===1||e.status===7){c(`/member/update/private/view/${(K=e==null?void 0:e.update)==null?void 0:K.id}`);return}return c(`/member/update/private/view/${(W=e==null?void 0:e.update)==null?void 0:W.id}`)}return c(`/member/update/private/view/${(X=e==null?void 0:e.update)==null?void 0:X.id}`)},i=e=>{const f=(s,t=!1)=>{const a={...s},o=(s==null?void 0:s.type)||"";return a.mobile_data={...s,user_data:typeof o=="string"&&o.includes("(In)")?[s.from]:typeof o=="string"&&o.includes("(Out)")?s.to:s.recipients||[],recipient_data:typeof o=="string"&&o.includes("(In)")?[s.from]:typeof o=="string"&&o.includes("(Out)")?s.to:s.recipients||[]},a.isChild=t,le.forEach(l=>{var T,_,O,R,A,I,U,y;switch(l==null?void 0:l.accessor){case"type":const u=t&&typeof o=="string"&&o.includes("Request (In)")?"Update (Out)":t&&typeof o=="string"&&o.includes("Request (Out)")?"Update (In)":o||"-",L=typeof u=="string"&&u.includes("(In)"),E=typeof u=="string"&&u.includes("(Out)"),w=typeof u=="string"?u.replace("(In)","(Incoming)").replace("(Out)","(Outgoing)"):"-";a[l==null?void 0:l.accessor]=r.jsx(re,{display:r.jsxs("span",{className:"flex w-fit flex-nowrap items-center justify-normal gap-[.3125rem] p-[.3125rem] capitalize",children:[r.jsx("span",{className:"text[13px] min-w-[max-content] max-w-[9.375rem] truncate whitespace-nowrap break-words font-inter font-[400] leading-[1.125rem] text-black",children:w.replace("(Incoming)","").replace("(Outgoing)","")}),L?r.jsx("img",{src:"/assets/incoming.svg",alt:"incoming"}):E?r.jsx("img",{src:"/assets/outgoing.svg",alt:"outgoing"}):null]}),place:"left",backgroundColor:"#000",openOnClick:!1,children:r.jsx("div",{className:"flex max-w-[9.375rem] items-center gap-2",children:r.jsx("span",{className:"w-full whitespace-normal break-words font-inter text-[.875rem] font-[400] leading-[1.125rem] text-white",children:w})})});break;case"name":a[l==null?void 0:l.accessor]=s.title?r.jsxs("div",{className:"flex min-w-[300px] items-center gap-2",children:[t&&r.jsx("svg",{className:"mr-2",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{d:"M13.8538 11.3538L10.8538 14.3538C10.7838 14.4238 10.6947 14.4714 10.5977 14.4908C10.5006 14.5101 10.4 14.5002 10.3086 14.4623C10.2172 14.4244 10.1391 14.3603 10.0841 14.278C10.0292 14.1957 9.99992 14.0989 10 14V11.5H4.5C4.36739 11.5 4.24021 11.4473 4.14645 11.3536C4.05268 11.2598 4 11.1326 4 11V2C4 1.86739 4.05268 1.74021 4.14645 1.64645C4.24021 1.55268 4.36739 1.5 4.5 1.5C4.63261 1.5 4.75979 1.55268 4.85355 1.64645C4.94732 1.74021 5 1.86739 5 2V10.5H10V8C9.99992 7.90105 10.0292 7.80431 10.0841 7.72201C10.1391 7.63971 10.2172 7.57556 10.3086 7.53769C10.4 7.49981 10.5006 7.48991 10.5977 7.50924C10.6947 7.52856 10.7838 7.57624 10.8538 7.64625L13.8538 10.6462C13.9002 10.6927 13.9371 10.7478 13.9623 10.8085C13.9874 10.8692 14.0004 10.9343 14.0004 11C14.0004 11.0657 13.9874 11.1308 13.9623 11.1915C13.9371 11.2522 13.9002 11.3073 13.8538 11.3538Z",fill:"black"})}),r.jsx("button",{onClick:()=>q(s),className:"font-medium line-clamp-1 text-ellipsis font-iowan hover:underline",children:s.title})]}):"-";break;case"date_created":a[l==null?void 0:l.accessor]=r.jsxs("span",{className:"font-iowan-regular flex items-center gap-2 text-[14px]",children:[r.jsx(v,{}),new Date(s.date).toLocaleDateString("en-US",{month:"2-digit",day:"2-digit",year:"2-digit"})]});break;case"update_status":let g;t?(console.log(s,"child"),(T=s.type)!=null&&T.includes("Request (Out)")?g=(_=s==null?void 0:s.update)!=null&&_.sent_at?8:9:(O=s.type)!=null&&O.includes("Request (In)")&&(s.status==8?g=11:g=(R=s==null?void 0:s.update)!=null&&R.sent_at?1:0)):(A=s.type)!=null&&A.includes("(In)")&&s.status==1||(I=s.type)!=null&&I.includes("(In)")&&s.status==8?g=8:(U=s.type)!=null&&U.includes("(Out)")&&s.status==8?g=11:g=te[s.status]||0;const D=s.status==8?(y=s==null?void 0:s.metadata)==null?void 0:y.scheduled_date:null;a[l==null?void 0:l.accessor]=r.jsx(ee,{status:g,scheduledDate:D});break;case"user_data":console.log(s.type.includes("Collaborator (Out)"),s==null?void 0:s.status);const z=s.isChild?Array.isArray(s.recipients)?s.recipients:[]:s.type.includes("Collaborator (In)")||s.type.includes("Request (In)")||s.type.includes("Update (In")?[s.from]:s.type.includes("Collaborator (Out)")&&(s==null?void 0:s.status)==9||s.type.includes("Request (Out)")?s.to:Array.isArray(s.recipients)?s.recipients.map(H=>JSON.parse(H)):[];a[l==null?void 0:l.accessor]=r.jsx("div",{children:r.jsx(P,{children:r.jsx(J,{members:z,title:r.jsx(r.Fragment,{})})})});break;default:a[l==null?void 0:l.accessor]=s[l==null?void 0:l.accessor]}}),a};return e==null?void 0:e.reduce((s,t)=>{if(s.push(f(t)),typeof(t==null?void 0:t.type)=="string"&&t.type.includes("Update Request")&&(t.status===1||t.status===7||t.status===8)){const a={...t,title:t.title,sent_at:t.sent_at,type:t.type};s.push(f(a,!0))}return s},[])},M=async()=>{console.log("fetchData"),n(!0);try{const e=await F();if(console.log("result",e),e!=null&&e.data){const f=i(e.data);j(s=>({...s,data:f,reload:!0,total:e.pagination.total,page:e.pagination.page,limit:e.pagination.limit,pages:e.pagination.pages}))}}catch(e){console.log("error",e)}finally{n(!1)}};S.useEffect(()=>{var e;h!=null&&h.current&&(m!=null&&m.reload)&&((e=h==null?void 0:h.current)==null||e.click(),j(f=>({...f,reload:!1})))},[m==null?void 0:m.reload]),S.useEffect(()=>{N&&(console.log("refreshRef","success"),N.current=()=>{h!=null&&h.current&&(console.log("refreshRef2","success"),M())})},[N]);const Q=(e,f)=>{var O,R,A,I,U,y,u,L,E;console.log("activity",e);const s=(e==null?void 0:e.mobile_data)||e,t=typeof s.type=="string"?s.type:"",a=w=>w?Array.isArray(w)?w.map(g=>{if(typeof g=="string")try{return JSON.parse(g)}catch(D){return console.error("Error parsing recipient:",D),g}return g}):[]:[];let o=[];e.isChild?o=Array.isArray(s.recipients)?s.recipients:[]:t.includes("Collaborator (In)")||t.includes("Request (In)")?o=[s.from]:t.includes("Collaborator (Out)")&&(e==null?void 0:e.status)==9||t.includes("Request (Out)")?o=a(s.to):s.recipient_data?o=a(s.recipient_data):s.recipients&&(o=a(s.recipients)),console.log("Parsed recipientData:",o),console.log("Activity type:",e.type);let l,T=typeof s.type=="string"?s.type:String(s.type||"");e.isChild?(O=s.type)!=null&&O.includes("Request (Out)")?l=(R=s==null?void 0:s.update)!=null&&R.sent_at?8:9:(A=s.type)!=null&&A.includes("Request (In)")&&(s.status==8?l=11:l=(I=s==null?void 0:s.update)!=null&&I.sent_at?1:0):T.includes("(In)")&&s.status==1||(U=s.type)!=null&&U.includes("(In)")&&s.status==8?l=8:(y=s.type)!=null&&y.includes("(Out)")&&s.status==8?l=11:l=te[e.status]||0,console.log("status",e.type,l,(u=e==null?void 0:e.update)==null?void 0:u.sent_at);const _=s.status==8?(L=s==null?void 0:s.metadata)==null?void 0:L.scheduled_date:null;return _&&console.log("Scheduled date:",_),r.jsxs("div",{className:`flex flex-col gap-2 rounded-sm px-5 py-4 md:px-0 ${f%2!==0?"border-y border-y-[#E6DCD2] bg-[#F9EADF]":"bg-brown-main-bg"}`,children:[r.jsxs("div",{className:"flex justify-between items-center",children:[r.jsxs("div",{className:"flex items-center",children:[e.isChild&&r.jsx("svg",{className:"mr-2",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{d:"M13.8538 11.3538L10.8538 14.3538C10.7838 14.4238 10.6947 14.4714 10.5977 14.4908C10.5006 14.5101 10.4 14.5002 10.3086 14.4623C10.2172 14.4244 10.1391 14.3603 10.0841 14.278C10.0292 14.1957 9.99992 14.0989 10 14V11.5H4.5C4.36739 11.5 4.24021 11.4473 4.14645 11.3536C4.05268 11.2598 4 11.1326 4 11V2C4 1.86739 4.05268 1.74021 4.14645 1.64645C4.24021 1.55268 4.36739 1.5 4.5 1.5C4.63261 1.5 4.75979 1.55268 4.85355 1.64645C4.94732 1.74021 5 1.86739 5 2V10.5H10V8C9.99992 7.90105 10.0292 7.80431 10.0841 7.72201C10.1391 7.63971 10.2172 7.57556 10.3086 7.53769C10.4 7.49981 10.5006 7.48991 10.5977 7.50924C10.6947 7.52856 10.7838 7.57624 10.8538 7.64625L13.8538 10.6462C13.9002 10.6927 13.9371 10.7478 13.9623 10.8085C13.9874 10.8692 14.0004 10.9343 14.0004 11C14.0004 11.0657 13.9874 11.1308 13.9623 11.1915C13.9371 11.2522 13.9002 11.3073 13.8538 11.3538Z",fill:"black"})}),r.jsx("p",{onClick:()=>q(s),className:"line-clamp-1 text-ellipsis font-iowan text-[16px] font-medium hover:underline",children:s.title&&s.title.length>15?`${s.title.substring(0,15)}...`:s.title||"Untitled"})]}),r.jsx(ee,{status:l,scheduledDate:s.status==8?(E=s==null?void 0:s.metadata)==null?void 0:E.scheduled_date:null})]}),r.jsxs("div",{className:"flex justify-between items-center text-sm",children:[r.jsxs("span",{className:"font-iowan-regular flex items-center gap-2 text-[14px]",children:[r.jsx(v,{className:"w-4 h-4"}),s.date?new Date(s.date).toLocaleDateString("en-US",{month:"2-digit",day:"2-digit",year:"2-digit"}):"N/A"]}),r.jsx("span",{className:"text-[#1f1d1a]",children:s.date?ue(s.date):"N/A"})]}),r.jsxs("div",{className:"mt-5 grid grid-cols-[1fr_1fr_60px] items-start justify-between",children:[r.jsxs("div",{className:"flex flex-col gap-2 items-start",children:[r.jsx("span",{className:"font-iowan text-[12px]",children:"Update Type"}),s.type?r.jsx(re,{display:r.jsxs("span",{className:"flex w-fit flex-nowrap items-center justify-normal gap-[.3125rem]  capitalize",children:[r.jsx("span",{className:"font-iowan-regular min-w-[max-content] max-w-[9.375rem] truncate whitespace-nowrap break-words text-[11px] font-[400] leading-[1.125rem] text-black",children:s.type.replace("(In)","").replace("(Out)","")}),s.type.includes("(In)")?r.jsx("img",{src:"/assets/incoming.svg",alt:"incoming",className:"w-4 h-4"}):s.type.includes("(Out)")?r.jsx("img",{src:"/assets/outgoing.svg",alt:"outgoing",className:"w-4 h-4"}):null]}),place:"left",backgroundColor:"#000",openOnClick:!1,children:r.jsx("div",{className:"flex max-w-[9.375rem] items-center gap-2",children:r.jsx("span",{className:"w-full whitespace-normal break-words font-inter text-[.875rem] font-[400] leading-[1.125rem] text-white",children:s.type.replace("(In)","(Incoming)").replace("(Out)","(Outgoing)")})})}):r.jsx("span",{className:"font-iowan text-[14px]",children:"Unknown"})]}),r.jsxs("div",{className:`flex flex-col items-start gap-2 ${typeof s.type=="string"&&s.type.includes("Collaboration")?"-mr-3":""}`,children:[r.jsx("span",{className:"font-iowan text-[12px]",children:"Contributor(s)"}),r.jsx(J,{members:Array.isArray(s.contributors)?s.contributors.map(w=>{if(typeof w=="string")try{return JSON.parse(w)}catch(g){return console.error("Error parsing contributor:",g),w}return w}):[],title:r.jsx(r.Fragment,{})})]}),r.jsxs("div",{className:"flex flex-col gap-2 items-start",children:[r.jsx("span",{className:"font-iowan text-[12px]",children:"To/From"}),(o==null?void 0:o.length)>0?r.jsx(J,{members:o,title:r.jsx(r.Fragment,{})}):"-"]})]})]},`${s.id}-${e.isChild?"child":"parent"}`)};return r.jsxs(S.Fragment,{children:[r.jsx(pe,{isOpen:d.showModal,onClose:x,update:d.selectedUpdate,onAccept:$,onDecline:V,loading:d.modalLoading}),r.jsxs("div",{className:"flex flex-col",children:[r.jsxs("div",{className:"flex justify-between items-center px-5 mb-4 md:mt-10 md:hidden md:px-0",children:[r.jsx("h3",{className:"text-lg font-semibold font-inter",children:"Most Recent Activity"}),r.jsx(ge,{to:"/member/updates?availability=available",className:"text-sm font-medium underline",children:"View All"})]}),d!=null&&d.updateSeenLoading?r.jsx(me,{loading:d==null?void 0:d.updateSeenLoading}):null,r.jsx("div",{className:"block md:hidden",children:k?r.jsx(fe,{count:7,counts:[2,2,2,2,2,2]}):((B=m==null?void 0:m.data)==null?void 0:B.length)>0?r.jsx("div",{className:"grid overflow-y-auto overflow-x-hidden gap-4 md:hidden",children:(G=m==null?void 0:m.data)==null?void 0:G.map((e,f)=>Q(e,f))}):r.jsx(P,{children:r.jsx(se,{})})}),r.jsx("div",{className:"mt-[18px] hidden h-full md:block",children:r.jsx(de,{showSearch:!1,useDefaultColumns:!0,defaultColumns:le,noDataComponent:{use:!0,component:r.jsx(P,{children:r.jsx(se,{})})},onUpdateCurrentTableData:e=>{e(m)},externalData:{use:!0,fetch:M},hasFilter:!1,tableRole:"member",table:"order",actionId:"id",tableTitle:"Most Recent Activity",actions:{view:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},export:{show:!1,action:null,multiple:!0},delete:{show:!1,action:null,multiple:!0},orders:{show:!0,type:"static",action:()=>{c("/member/updates?availability=available")},children:r.jsx(r.Fragment,{children:"View All"}),className:"!bg-transparent !text-black !underline !shadow-none !border-0 !p-0 !m-0 !w-fit !min-w-fit !max-w-fit"},add:{show:!1,multiple:!0,children:"+ Add"}},defaultPageSize:10,showPagination:!1,updateRef:h,showScrollbar:!1,maxHeight:"md:grid-rows-[auto_1fr_auto] grid-rows-[auto_25rem_auto]",isActivityTable:!0})})]})]})};export{Xe as default};
