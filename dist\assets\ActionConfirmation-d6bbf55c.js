import{j as o}from"./@nextui-org/listbox-0f38ca19.js";import{R as y}from"./vendor-4cdf2bd1.js";import{u as ee}from"./react-hook-form-a383372b.js";import{o as re}from"./yup-0917e80c.js";import{c as te,a as C}from"./yup-342a5df4.js";import{M as ie,G as se,A as ne,I as oe,s as P,v as g,w as j,x as $,m as v,bD as q}from"./index-f2ad9142.js";import{M as me}from"./MkdInput-d37679e9.js";import{A as ae}from"./AddButton-51d1b2cd.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const Fe=({data:i={id:null},options:l={endpoint:null,method:"GET"},onSuccess:t,onClose:E,multiple:O=!1,action:d="",mode:u="create",customMessage:D="",table:m="",input:s="input",initialValue:R="",inputType:T="text",disableCancel:b=!1,inputConfirmation:w=!0})=>{var _,A,k;new ie;const I=te({...["input","input_create","input_update"].includes(u)?{[s]:C().required()}:{confirm:C().required().oneOf([d],`Confirmation must be "${d}"`)}}).required(),{state:{createModel:c,updateModel:f,deleteModel:p},dispatch:n}=y.useContext(se),{state:le,dispatch:a}=y.useContext(ne),{register:G,handleSubmit:z,setValue:N,watch:B,formState:{errors:F}}=ee({resolver:re(I),defaultValues:{[s]:R}}),x=B(),K=async()=>{if(!["create","update","delete","custom"].includes(u))return P(n,"Mode must be create, update, delete or custom",5e3,"error");if(!Array.isArray(i))return P(n,"Data must be an list",5e3,"error");const r=i==null?void 0:i.map(h=>U(u,h));(await Promise.all(r)).some(h=>!h.error)&&t&&t()},U=(r,e)=>{var h;if(["create"].includes(r))return g(n,a,m,e,!1);if(["update"].includes(r))return j(n,a,m,e==null?void 0:e.id,e,!1);if(["delete"].includes(r))return $(n,a,m,e.id,null,!1);if(["custom"].includes(r))return v(n,a,{endpoint:l==null?void 0:l.endpoint,method:"POST",payload:e},(h=q)==null?void 0:h.createModel,!1)},V=async r=>{const e=await j(n,a,m,r==null?void 0:r.id,r);!(e!=null&&e.error)&&t&&t()},H=async r=>{const e=await j(n,a,m,r==null?void 0:r.id,{[s]:x[s]});!(e!=null&&e.error)&&t&&t({[s]:x[s],id:e==null?void 0:e.data})},J=async r=>{const e=await g(n,a,m,{...r,[s]:x[s]});!(e!=null&&e.error)&&t&&t({[s]:x[s],id:e==null?void 0:e.data})},L=async r=>{if(d==="move")return Z(r);const e=await g(n,a,m,r);!(e!=null&&e.error)&&t&&t()},Q=async r=>{const e=await $(n,a,m,r.id);!(e!=null&&e.error)&&t&&t()},W=async r=>{const e=await v(n,a,{endpoint:l==null?void 0:l.endpoint,method:l==null?void 0:l.method,payload:r},q.createModel);e&&(e!=null&&e.hasOwnProperty("error"))&&!(e!=null&&e.error)&&t&&t(e)},X=()=>{const r=x[s];t&&t({[s]:r})},Y=r=>{t&&t(r)},Z=async r=>{const e=await v(n,a,{endpoint:"/v3/api/custom/qualitysign/inventory/move",method:"POST",payload:r},q.createModel);!(e!=null&&e.error)&&t&&t(e)},M={create:L,input_create:J,input_update:H,update:V,delete:Q,custom:W,manual:Y,input:X},S=async()=>{if(O)K();else{const r=M[u];return r(i)}};return y.useEffect(()=>{w||N("confirm",d)},[w]),y.useEffect(()=>{["input_update"].includes(u)&&N(s,R)},[u]),o.jsx("div",{className:"mx-auto flex h-fit flex-col items-center justify-start rounded pb-5 !font-inter leading-snug tracking-wide md:pb-0",children:o.jsx("form",{className:"flex flex-col w-full h-fit text-start",onSubmit:z(S,r=>{console.log("ERROR >>",r)}),children:o.jsxs("div",{className:"space-y-5",children:[o.jsx("div",{className:"my-2",children:D?o.jsx("div",{children:D}):o.jsxs("div",{children:["Are you sure you want to ",d," ",i!=null&&i.id&&((_=i==null?void 0:i.id)!=null&&_.length)&&((A=i==null?void 0:i.id)==null?void 0:A.length)>1||(i==null?void 0:i.length)>1?"these":"this"," ",(k=m==null?void 0:m.split("_"))==null?void 0:k.join(" "),"?"]})}),o.jsx("div",{className:`!mb-10 ${w?"":"hidden"}`,children:o.jsx(me,{type:T,page:"items",rows:"5",name:["input","input_create","input_update"].includes(u)?s:"confirm",errors:F,register:G,label:o.jsxs("div",{className:"font-bold text-black",children:["Type"," ",["input","input_create","input_update"].includes(u)?"":`'${d}'`," ","below"]}),className:"resize-none grow"})}),o.jsxs("div",{className:"flex gap-5 mt-5 w-full grow",children:[b?null:o.jsx(ae,{type:"button",onClick:()=>E(),disabled:(c==null?void 0:c.loading)||(f==null?void 0:f.loading)||(p==null?void 0:p.loading),showPlus:!1,className:"grow self-end !border-gray-200 !bg-transparent font-iowan text-[14px] font-bold !text-primary-black",children:"Cancel"}),o.jsx(oe,{type:"submit",loading:(c==null?void 0:c.loading)||(f==null?void 0:f.loading)||(p==null?void 0:p.loading),disabled:(c==null?void 0:c.loading)||(f==null?void 0:f.loading)||(p==null?void 0:p.loading),className:`self-end rounded px-4 py-2 font-iowan font-bold capitalize text-white ${b?"!grow":"!w-1/2"}`,children:d})]})]})})})};export{Fe as ActionConfirmation,Fe as default};
