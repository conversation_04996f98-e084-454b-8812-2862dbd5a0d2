import{j as o}from"./@nextui-org/listbox-0f38ca19.js";import{bE as l}from"./index-f2ad9142.js";import{r as n}from"./vendor-4cdf2bd1.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const e=({className:t,step:r,onClick:c,currentStep:i,stepKey:m,stepSize:a})=>o.jsxs(o.Fragment,{children:[o.jsxs("div",{className:"relative flex flex-col items-center justify-start gap-2",children:[o.jsx("div",{className:` flex h-[2.5rem] w-[2.5rem] items-center justify-center  rounded-full border  border-black/30  transition-all duration-200 ${(r==null?void 0:r.step)==i?"bg-[#F2DFCE] text-[##1F1D1A]":i>(r==null?void 0:r.step)?"bg-black text-white ":"bg-transparent"} ${t}`,children:i>(r==null?void 0:r.step)?o.jsx(l,{}):r==null?void 0:r.step}),o.jsx("span",{className:"font-inter text-[1rem] font-[400] leading-[1.21rem]",children:r==null?void 0:r.name})]}),m>=0&&m+1<a?o.jsx("hr",{className:" mt-[-20px] border border-[#1F1D1A]"}):null]}),$=n.memo(e);export{$ as default};
