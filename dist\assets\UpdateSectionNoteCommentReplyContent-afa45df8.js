import{j as p}from"./@nextui-org/listbox-0f38ca19.js";import{r as d,R}from"./vendor-4cdf2bd1.js";import{a as C,M as j,T as E}from"./index-f2ad9142.js";import{u as k}from"./useUpdateCollaborator-1187c43b.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const T=s=>s&&typeof s=="object"&&typeof s.id=="number"&&typeof s.reply=="string",N=(s,r=[])=>{if(!s||typeof s!="string")return"";const h=/@[A-Za-z0-9]+(?:\s+[A-Za-z0-9]+)?(?=\s|$)/g,l=s.split(h),o=s.match(h)||[];return l.map((n,t)=>{if(o[t]){const c=o[t].trim(),g=c.substring(1),f=r.some(u=>`${u.first_name} ${u.last_name}`.trim()===g);return p.jsxs(R.Fragment,{children:[p.jsx("span",{className:`mr-[4px] rounded-sm ${f?"bg-[#1f1d1a]/5":""} px-[1px] py-0.5 font-medium`,children:c}),l[t+1]]},t)}return n})},I=({reply:s=null,update:r=null,note:h=null,comment:l=null,externalData:o=null,setExternalData:n=null,loadReplies:t=null})=>{const{data:c,fetchUpdateContributors:g}=k(),f=d.useRef(null),u=d.useRef(null),{showToast:y}=C();new j,d.useEffect(()=>{r!=null&&r.id&&g(r==null?void 0:r.id)},[r==null?void 0:r.id]);const b=()=>{const i=f.current;i&&(i.style.height="auto",i.style.height=`${i.scrollHeight}px`)};d.useEffect(()=>{o!=null&&o.edit_reply&&f.current&&(f.current.focus(),b())},[o==null?void 0:o.edit_reply]),d.useEffect(()=>{const i=e=>{o!=null&&o.edit_reply&&u.current&&!u.current.contains(e.target)&&n(m=>({...m,edit_reply:!1,reply:s==null?void 0:s.reply}))};return document.addEventListener("mousedown",i),()=>document.removeEventListener("mousedown",i)},[o==null?void 0:o.edit_reply,s==null?void 0:s.reply]);const _=i=>{const e=i.target.value;b(),n&&n(m=>({...m,reply:e}))},w=async()=>{var i;if(!((i=o==null?void 0:o.reply)!=null&&i.trim())){y("Reply cannot be empty",5e3,"error");return}try{const m=await new E().update("update_comment_replies",s==null?void 0:s.id,{reply:o.reply});m!=null&&m.error||(y("Reply updated successfully",5e3,"success"),t&&t(),n&&n(v=>({...v,edit_reply:!1})))}catch(e){console.error("Error updating reply:",e),y(e.message||"Error updating reply",5e3,"error")}};return!T(s)&&!(o!=null&&o.edit_reply)?null:o!=null&&o.edit_reply?p.jsx("div",{ref:u,className:"relative mt-2",children:p.jsx("textarea",{ref:f,className:"mt-3 h-auto min-h-[32px] w-full resize-none appearance-none overflow-hidden rounded-sm border-x-0 border-b border-t-0 border-[#1f1d1a] bg-brown-main-bg p-[12px_16px_12px_16px] px-0 text-sm font-normal leading-tight text-[#1f1d1a] placeholder:text-base placeholder:text-[#79716C] focus:border-x-0 focus:border-t-0 focus:shadow-none focus:outline-none focus:outline-0 focus:ring-0",rows:"1",name:"reply",placeholder:"Edit your reply...",onChange:_,value:(o==null?void 0:o.reply)||"",onKeyDown:i=>{i.key==="Enter"&&!i.shiftKey?(i.preventDefault(),w()):i.key==="Escape"?n(e=>({...e,edit_reply:!1,reply:s==null?void 0:s.reply})):i.key==="Enter"&&i.shiftKey&&setTimeout(b,0)}})}):p.jsx("div",{className:"mt-2 text-sm",children:N(s==null?void 0:s.reply,(c==null?void 0:c.updateContributors)||[])})};export{I as UpdateSectionNoteCommentReplyContent};
