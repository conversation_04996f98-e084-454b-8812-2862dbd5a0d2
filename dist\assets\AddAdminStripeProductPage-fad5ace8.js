import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{R as n,b as E}from"./vendor-4cdf2bd1.js";import{u as S}from"./react-hook-form-a383372b.js";import{o as A}from"./yup-0917e80c.js";import{c as C,a as g}from"./yup-342a5df4.js";import{M as D,A as L,G as $,L as F,I as P,h as b,s as f,t as R}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const se=({onSuccess:l=null})=>{var c,d,p,x;let y=new D;const{dispatch:j}=n.useContext(L),{dispatch:o,state:{addStripeProduct:s}}=n.useContext($);E();const w=C({name:g().required(),description:g().nullable()}).required(),{register:m,handleSubmit:N,setError:v,formState:{errors:a}}=S({resolver:A(w)}),k=async u=>{try{b(o,!0,"addStripeProduct");const e=await y.addStripeProduct({name:u.name,description:u.description});if(!(e!=null&&e.error))f(o,e==null?void 0:e.message,5e3,"success"),l&&l();else if(e.validation){const h=Object.keys(e.validation);for(let r=0;r<h.length;r++){const i=h[r];console.log(i),v(i,{type:"manual",message:e.validation[i]})}}}catch(e){console.log("Error",e),f(o,e.message,5e3,"error"),R(j,e.message)}finally{b(o,!1,"addStripeProduct")}};return n.useEffect(()=>{o({type:"SETPATH",payload:{path:"plans"}})},[]),t.jsx("div",{className:"p-5 mx-auto h-full shadow-md  bg-brown-main-bg",children:t.jsxs("form",{className:"w-full max-w-lg",onSubmit:N(k),children:[t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"name",children:"Name"}),t.jsx("input",{type:"text",placeholder:"Name",...m("name"),className:`"shadow focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 focus:outline-none ${(c=a.name)!=null&&c.message?"border-red-500":""}`}),t.jsx("p",{className:"text-xs italic text-red-500",children:(d=a.name)==null?void 0:d.message})]}),t.jsxs("div",{className:"mb-5",children:[t.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"description",children:"Description"}),t.jsx("input",{type:"text",placeholder:"Description",...m("description"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(p=a.description)!=null&&p.message?"border-red-500":""}`}),t.jsx("p",{className:"text-xs italic text-red-500",children:(x=a.description)==null?void 0:x.message})]}),t.jsx(F,{children:t.jsx(P,{type:"submit",className:"!text-white",color:"white",loading:s==null?void 0:s.loading,disabled:s==null?void 0:s.loading,children:"Submit"})})]})})};export{se as default};
