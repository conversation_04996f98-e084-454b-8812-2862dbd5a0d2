import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{o as V}from"./yup-0917e80c.js";import{A as T,G as R,I as P,M as S,t as k,s as p,_ as L,ay as J}from"./index-f2ad9142.js";import{u as Q}from"./useCompanyMembers-afd93b3b.js";import{r as s}from"./vendor-4cdf2bd1.js";import{u as Z}from"./react-hook-form-a383372b.js";import{c as ee,a as ae}from"./yup-342a5df4.js";import{X as z}from"./XMarkIcon-cfb26fe7.js";import{t as g,S as w}from"./@headlessui/react-cdd9213e.js";import{C as te}from"./ClockIcon-a30de2d8.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";function se({title:r,titleId:c,...n},o){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":c},n),r?s.createElement("title",{id:c},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))}const oe=s.forwardRef(se),ne=oe;function le({member:r,afterRemove:c}){const[n,o]=s.useState(!1),{dispatch:d}=s.useContext(T),{dispatch:j}=s.useContext(R),[m,h]=s.useState(!1);async function u(){h(!0);try{await new S().callRawAPI(`/v4/api/records/startup_member/${r.id}`,{},"DELETE"),o(!1),c()}catch(l){k(d,l.message),p(j,l.message,5e3,"error")}h(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>o(!0),children:e.jsx(ne,{className:"h-6 w-6",strokeWidth:2})}),e.jsx(g,{appear:!0,show:n,as:s.Fragment,children:e.jsxs(w,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>o(!1),children:[e.jsx(g.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(g.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(w.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(w.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Are you sure"}),e.jsx("button",{onClick:()=>o(!1),type:"button",children:e.jsx(z,{className:"h-6 w-6"})})]}),e.jsx("p",{className:"mt-2",children:"Are you sure you want to remove this member from the company"}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>o(!1),children:"Cancel"}),e.jsx(P,{loading:m,disabled:m,onClick:u,className:"rounded-lg bg-[#1f1d1a] py-2 text-center font-iowan font-semibold text-white transition-colors duration-100 disabled:bg-opacity-60",children:"Yes, I'm sure"})]})]})})})})]})})]})}function ie({member:r,refetch:c}){const[n,o]=s.useState(!1),{dispatch:d,state:j}=s.useContext(T),{dispatch:m}=s.useContext(R),[h,u]=s.useState(!1);async function l(){u(!0);try{const v=await new S().callRawAPI("/v3/api/custom/goodbadugly/users/make-owner",{company_id:j.company.id,email:r.email},"POST");console.log(v),o(!1),c()}catch(y){k(d,y.message),p(m,y.message,5e3,"error")}u(!1)}return e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"mt-2 w-fit cursor-pointer rounded bg-green-600 px-1 text-[10px] text-white",onClick:()=>o(!0),children:"Make Account Owner"}),e.jsx(g,{appear:!0,show:n,as:s.Fragment,children:e.jsxs(w,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>o(!1),children:[e.jsx(g.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(g.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(w.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(w.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Are you sure"}),e.jsx("button",{onClick:()=>o(!1),type:"button",children:e.jsx(z,{className:"h-6 w-6"})})]}),e.jsx("p",{className:"mt-2",children:"Are you sure you want to make this account an owner?"}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>o(!1),children:"Cancel"}),e.jsx(P,{loading:h,disabled:h,onClick:l,className:"disabled:bg-disabledblack rounded-lg bg-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Yes, I'm sure"})]})]})})})})]})})]})}const $=new S;function _e(){var A,_,E,D,O;const{dispatch:r,state:c}=s.useContext(T),{dispatch:n}=s.useContext(R),{members:o,refetch:d,setMembers:j}=Q(c.company.id),[m,h]=s.useState(""),[u,l]=s.useState(!1),y=ee({email:ae().required("This field is required")}),[v,B]=useSearchParams(),[I,G]=s.useState(""),{register:Y,handleSubmit:W,setError:re,reset:F,formState:{errors:C,isSubmitting:ce}}=Z({resolver:V(y),defaultValues:{email:""}});function q(a){a.preventDefault(),console.log(v);const i=I.trim().toLowerCase(),t=o==null?void 0:o.filter(f=>{var N,b;return(b=(N=f==null?void 0:f.user)==null?void 0:N.email)==null?void 0:b.toLowerCase().includes(i)});j(t)}async function U(a){var i;console.log("manager"),console.log(),l(!0);try{await $.callRawAPI("/v3/api/custom/goodbadugly/users/make-manager",{company_id:c.company.id,email:a.email},"POST"),l(!1),F(),p(n,"Invite sent"),d()}catch(t){l(!1),k(r,t.message),console.log(t),p(n,(t==null?void 0:t.message)||((i=t==null?void 0:t.mailResult)==null?void 0:i.message)||"Something went wrong",5e3,"error")}}async function X(a){var i;l(!0),console.log("other");try{await $.callRawAPI(`/v3/api/custom/goodbadugly/member/${c.company.id}/invite-to-team`,{email:a.email,role:m},"POST"),l(!1),F(),p(n,"Invite sent"),d()}catch(t){l(!1),console.log(t),k(r,t.message),p(n,(t==null?void 0:t.message)||((i=t==null?void 0:t.mailResult)==null?void 0:i.message)||"Something went wrong",5e3,"error")}}const M=a=>{m==="manager"?U(a):X(a)};async function H(a,i){try{await new S().callRawAPI(`/v4/api/records/startup_member/${a}`,{member_role:i},"PUT"),d(),p(n,"Saved")}catch(t){k(r,t.message),p(n,t.message,5e3,"error")}}return e.jsxs("div",{className:"max-w-5xl overflow-x-auto px-8 py-8 xl:max-w-[1100px]",children:[e.jsx("h2",{className:"text-3xl",children:"Manage team"}),e.jsxs("form",{className:"flex flex-row items-center mt-10",onSubmit:a=>q(a),children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block  text-lg font-bold capitalize capitalize text-[#1f1d1a]",children:"Search"}),e.jsx("div",{children:e.jsx("input",{type:"text",placeholder:"Team members email",onChange:a=>G(a.target.value),className:`focus:shadow-outline w-[100px] w-[250px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] shadow   focus:outline-none sm:w-[180px] ${(A=C.group_name)!=null&&A.message?"border-red-500":""}`})}),e.jsx("p",{className:"italic text-red-500 text-field-error",children:(_=C.group_name)==null?void 0:_.message})]}),e.jsxs("div",{className:"flex gap-4 ml-3",children:[e.jsx("button",{type:"submit",disabled:u||I==="",className:"px-4 py-1 font-semibold text-white font-iowan-regularrounded-md bg-primary-black/80 hover:bg-primary-black",children:"Search"}),e.jsx("button",{type:"button",disabled:u,className:"rounded-md px-4 py-1 font-semibold text-[#1f1d1a]",onClick:()=>{v.delete("search"),d(),B(v)},children:"Clear"})]})]}),e.jsxs("form",{onSubmit:W(M),children:[e.jsxs("div",{className:"flex gap-4 mt-8",children:[e.jsx("input",{type:"text",autoComplete:"off",...Y("email"),className:`focus:shadow-outline  w-full max-w-[500px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 text-sm font-normal   leading-tight text-[#1d1f1a] shadow focus:outline-none ${(E=C.email)!=null&&E.message?"border-red-500":""}`,placeholder:"Add email address"}),e.jsxs("select",{value:m,onChange:a=>h(a.target.value),className:"focus:shadow-outline w-full max-w-[200px] appearance-none rounded border py-2 pl-6 pr-8 leading-tight text-[#1f1d1a] shadow focus:outline-none ",children:[e.jsx("option",{value:"",children:"Select Role"}),e.jsx("option",{value:"investor",children:"Fund Manager"}),e.jsx("option",{value:"collaborator",children:"Collaborator"}),e.jsx("option",{value:"stakeholder",children:"Stakeholder"}),e.jsx("option",{value:"manager",children:"Manager/Founder"})]}),e.jsx(P,{loading:u,disabled:u,type:"submit",className:"px-5 py-2 text-lg font-semibold text-center text-white whitespace-nowrap rounded-lg transition-colors duration-100 disabled:bg-disabledblack bg-primary-black/90",children:"Send Invite"})]}),e.jsx("p",{className:"italic text-red-500 text-field-error",children:(D=C.email)==null?void 0:D.message})]}),e.jsx("div",{className:"mt-12 w-full  max-w-[1100px] space-y-8",children:(O=o==null?void 0:o.toReversed())==null?void 0:O.map(a=>{var i,t,f,N,b;return e.jsxs("div",{className:"flex justify-between items-center w-full",children:[e.jsxs("div",{className:"flex gap-3 items-center",children:[e.jsx("img",{src:((i=a==null?void 0:a.user)==null?void 0:i.photo)||"/default.png",alt:"",className:"h-14 w-14 rounded-[50%] object-cover"}),e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsxs("p",{className:"text-[16px] font-medium md:text-xl",children:[(a==null?void 0:a.member_status)==L.PENDING?(t=a==null?void 0:a.user)==null?void 0:t.email:`${(f=a==null?void 0:a.user)==null?void 0:f.first_name} ${(N=a==null?void 0:a.user)==null?void 0:N.last_name}`," "]})," "]}),a.member_status==L.PENDING?e.jsxs("div",{className:"flex gap-3 items-center mt-1 font-medium text-gray-500",children:[e.jsx(te,{className:"w-5 h-5",strokeWidth:2})," ",e.jsx("p",{children:"Invitation Pending"})," ",e.jsx("button",{className:"text-sm text-primary-black",onClick:()=>{var x;return M({email:(x=a==null?void 0:a.user)==null?void 0:x.email})},children:"Resend invite"})]}):e.jsxs("p",{className:"mt-1 font-medium text-gray-500",children:[(b=a==null?void 0:a.user)==null?void 0:b.email,(a==null?void 0:a.member_role)==="manager"&&e.jsx(ie,{member:a,refetch:d})]})]})]}),e.jsxs("div",{className:"flex gap-6 items-center text-xl font-medium",children:[Object.entries(J).map(([x,K])=>{if(x!="member")return e.jsxs("label",{className:"flex gap-2 items-center",children:[e.jsx("input",{type:"radio",checked:(a==null?void 0:a.member_role)==x,value:x,onChange:()=>H(a.id,x)}),K]},x)}),e.jsx(le,{member:a,afterRemove:d})]})]},a==null?void 0:a.id)})})]})}export{_e as default};
