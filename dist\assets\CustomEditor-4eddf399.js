import{j as n}from"./@nextui-org/listbox-0f38ca19.js";import{r as o}from"./vendor-4cdf2bd1.js";import{T as b}from"./@editorjs/editorjs-3bc58744.js";import{n as x}from"./@editorjs/paragraph-9d333c59.js";import{c as y}from"./@editorjs/header-da8c369a.js";import{d as T}from"./@editorjs/list-86b325f6.js";import{I as j}from"./@editorjs/link-7a38da73.js";import{n as k}from"./@editorjs/delimiter-89018da8.js";import{f as E}from"./@editorjs/checklist-1b2b7ac3.js";import{A as w,G as W,a7 as v,M as C,t as O,s as S}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const D={paragraph:{class:x,inlineToolbar:!0,config:{placeholder:"Write something"}},checkList:{class:E,inlineToolbar:!0,config:{placeholder:"Write something"}},list:{class:T,inlineToolbar:!0,config:{placeholder:"Write something"}},header:{class:y,inlineToolbar:!0,config:{placeholder:"Write something"}},delimiter:{class:k,inlineToolbar:!0,config:{placeholder:"Write something"}},link:{class:j,inlineToolbar:!0,config:{placeholder:"Write something"}}},sr=o.memo(({data:i,editing:s=!0,editorID:a,note_id:c,afterEdit:l,setUpdated:R,updateSaved:U,report:m=!1})=>{const r=o.useRef(),{dispatch:p}=o.useContext(w),{dispatch:d}=o.useContext(W),[A,u]=o.useState(!1);async function f(e){try{const h=await new C().callRawAPI(`/v4/api/records/notes/${c}`,{content:JSON.stringify(e)},"PUT");m&&l()}catch(t){O(p,t.message),S(d,t.message,5e3,"error")}}return o.useEffect(()=>{if(!r.current){const e=new b({holder:a,minHeight:30,style:{nonce:v()},readOnly:!s,tools:D,data:i,async onChange(t,h){const g=await t.saver.save();f(g)},onReady:()=>{r.current=e}})}return()=>{r.current&&r.current.destroy&&r.current.destroy()}},[]),o.useEffect(()=>{var e,t;r.current&&((t=(e=r==null?void 0:r.current)==null?void 0:e.readOnly)==null||t.toggle(!s))},[s]),o.useEffect(()=>{var e,t;r.current&&((t=(e=r==null?void 0:r.current)==null?void 0:e.blocks)==null||t.render(i))},[i,r==null?void 0:r.current]),n.jsxs(n.Fragment,{children:[n.jsx("style",{children:`
      
      .editorjs-container-transparent {
        background-color: #fff0e5 !important;
      }
      .editorjs-container {
        background-color: #fff0e5 !important;
      }
      `}),n.jsx("div",{"data-placeholder":"Write something",className:`bg-brown-main-bg ${s?"editorjs-container border-0":"editorjs-container-transparent"} rounded   px-3 py-2`,id:a,onBlur:()=>u(!0)})]})});export{D as EDITOR_JS_TOOLS,sr as default};
