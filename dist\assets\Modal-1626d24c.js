import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r}from"./vendor-4cdf2bd1.js";import{b as f}from"./index.esm-6fcccbfe.js";import{n as x}from"./index-f2ad9142.js";import"./index-23a711b5.js";import"./moment-a9aaa855.js";import{t as s,S as n}from"./@headlessui/react-cdd9213e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-icons-36ae72b7.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const u=({children:d,title:l,onClose:m,modalHeader:c,classes:t={modal:"h-full",modalDialog:"h-[90%]",modalContent:""},page:h="",isOpen:a,disableCancel:p=!1,clickOutToClose:b=!1})=>(r.useRef(null),r.useEffect(()=>{const i=document.querySelectorAll("body, .scrollable-container");return a?i.forEach(o=>{o.style.overflow="hidden"}):i.forEach(o=>{o.style.overflow="auto"}),()=>{i.forEach(o=>{o.style.overflow="auto"})}},[a]),console.log("snsn"),e.jsx(s,{appear:!0,show:a,as:r.Fragment,children:e.jsx(n,{as:"div",className:"relative z-[50] border border-red-600",onClose:()=>{console.log("onClose"),m()},children:e.jsx("div",{className:"overflow-y-auto fixed inset-0",children:e.jsx("div",{className:"flex justify-center items-center p-4 min-h-full text-center",children:e.jsx(s.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(n.Panel,{className:`relative w-full transform overflow-hidden rounded-md bg-brown-main-bg  text-left align-middle text-base shadow-xl transition-all  ${t==null?void 0:t.modalDialog}`,children:[c&&e.jsx(n.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:e.jsxs("div",{className:"sticky inset-x-0 top-0 !z-50 m-auto flex w-full justify-between border-b bg-brown-main-bg px-5 py-4",children:[e.jsx("div",{className:"text-center font-inter text-[1.125rem] font-bold capitalize leading-[1.5rem] tracking-[-1.5%]",children:["string"].includes(typeof l)?x(l,{casetype:"capitalize",separator:" "}):l}),p?null:e.jsx("button",{type:"button",className:"cursor-pointer modal-close",onClick:m,children:e.jsx(f,{className:"text-xl"})})]})}),e.jsx("div",{className:`-z-10 mt-4 px-5 ${t==null?void 0:t.modalContent}`,children:d})]})})})})})})),B=r.memo(u);export{B as default};
