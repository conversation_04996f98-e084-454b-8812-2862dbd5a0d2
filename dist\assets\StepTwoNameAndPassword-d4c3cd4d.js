import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{b as E,r as a}from"./vendor-4cdf2bd1.js";import{c as k,a as p}from"./yup-342a5df4.js";import{u as q}from"./react-hook-form-a383372b.js";import{M as P}from"./index-bf8349b8.js";import{a as T,u as I,ax as M,I as L}from"./index-f2ad9142.js";import{o as C}from"./yup-0917e80c.js";import{M as x}from"./MkdInput-d37679e9.js";import{u as F}from"./useLocalStorage-46cb237c.js";import{l as O}from"./logo5-2e16f0f2.js";import{STEPS as z}from"./SignUpStepOneEmail-8cbdc5ad.js";import{M as A}from"./index-5d49e854.js";import"./@nextui-org/theme-345a09ed.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";const je=({updateStep:u=null})=>{const{globalState:{completeRegistration:o},custom:f,showToast:h}=T();E();const[m,b]=a.useState("member"),[i,w]=a.useState(3),{localStorageData:l,setLocalStorage:g}=F(["step"]),{profile:s,updateProfile:j}=I({isPublic:!0}),N=k({first_name:p().required("This field is required"),last_name:p().required("This field is required"),password:p().required("This field is required")}),{register:n,handleSubmit:y,setError:S,watch:R,setValue:c,formState:{errors:d,isSubmitting:U}}=q({resolver:C(N),defaultValues:{first_name:"",last_name:"",password:""}}),v=async r=>{try{const t=await f({endpoint:"/v3/api/custom/goodbadugly/users/complete-registration",method:"POST",payload:{first_name:r==null?void 0:r.first_name,last_name:r==null?void 0:r.last_name,password:r==null?void 0:r.password,role:m}},"completeRegistration");t!=null&&t.error||(j({step:i}),g("step",i),u&&u(i))}catch(t){console.error("Error",t),h(t.message,5e3,"error"),S("password",{type:"manual",message:t.message})}};return a.useEffect(()=>{c("password","")},[]),a.useEffect(()=>{var r;s!=null&&s.id&&(c("first_name",s==null?void 0:s.first_name),c("last_name",s==null?void 0:s.last_name),(r=s==null?void 0:s.companies)!=null&&r.length&&(b(M.STAKEHOLDER),w(4)))},[s==null?void 0:s.id]),console.log("step two role >>",m),e.jsxs("div",{className:"w-full md:w-[60%] md:px-6",children:[e.jsx("div",{className:"sticky right-0 top-0 z-[9] flex h-[4.5rem] w-full flex-row items-center justify-between bg-[#1f1d1a] px-8 md:hidden",children:e.jsx("img",{src:O,alt:"logo",className:"h-10 w-[180px]"})}),e.jsxs("div",{className:"flex w-full flex-col px-4 py-8 md:px-0",children:[e.jsxs("div",{className:" w-full space-y-[40px] md:space-y-[6.25rem] ",children:[e.jsx(P,{steps:z[m],currentStep:l==null?void 0:l.step,onClick:()=>{},className:""}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-iowan text-[1.5rem] font-[700] sm:text-[2.5rem]",children:"Account Info"}),e.jsx("p",{className:"mb-3 font-normal text-black",children:"Update your account Info"})]})]}),e.jsxs("form",{className:"space-y-8",onSubmit:y(v),children:[e.jsxs("div",{className:"mt-4 grid grid-cols-2 gap-4",children:[e.jsx("div",{className:"",children:e.jsx(x,{type:"text",name:"first_name",label:"First Name",errors:d,register:n,placeholder:"Enter First Name",required:!0,className:"!rounded-[.125rem] !border !border-black placeholder:text-[12px]"})}),e.jsx("div",{className:"",children:e.jsx(x,{type:"text",name:"last_name",label:"Last Name",errors:d,register:n,placeholder:"Enter Last Name",required:!0,className:"!rounded-[.125rem] !border !border-black placeholder:text-[12px]"})})]}),e.jsx("div",{className:"",children:e.jsx(A,{type:"password",name:"password",label:"Password",errors:d,register:n,placeholder:"Enter Password",className:"!rounded-[.125rem] !border !border-black placeholder:text-[12px]",required:!0})}),e.jsx(L,{type:"submit",className:"my-4 flex h-[2.75rem] w-full items-center justify-center rounded-sm bg-[#1f1d1a] py-2 tracking-wide text-white outline-none focus:outline-none",loading:o==null?void 0:o.loading,disabled:o==null?void 0:o.loading,children:e.jsx("span",{className:"capitalize",children:"Continue"})})]})]})]})};export{je as default};
