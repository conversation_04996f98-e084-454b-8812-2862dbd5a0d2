import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{R as I,r as d}from"./vendor-4cdf2bd1.js";import{u as de}from"./react-hook-form-a383372b.js";import{o as me}from"./yup-0917e80c.js";import{c as ce,a as pe}from"./yup-342a5df4.js";import{M as xe,G as ae,I as T,t as ie,s as M}from"./index-f2ad9142.js";import fe from"./ModalPrompt-6520dab5.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";let E=new xe;const Ie=()=>{var J,Q,X,Y,V;const $=ce({email:pe().email().required()}).required(),{dispatch:p}=I.useContext(ae),[j,A]=d.useState(""),[m,_]=I.useState({}),[z,R]=d.useState(!1),[U,l]=d.useState(!1),[N,x]=d.useState(!1),[q,h]=d.useState(!1),[w,v]=d.useState("");d.useState("");const[f,u]=d.useState(!1),[y,C]=d.useState("Profile"),[t,D]=d.useState({}),{dispatch:le}=I.useContext(ae),{register:F,handleSubmit:k,setError:S,setValue:H,formState:{errors:L}}=de({resolver:me($)}),oe=(s,i,g=!1)=>{let n=m;console.log(i),g?n[s]?n[s]=[...n[s],{file:i.files[0],tempFile:{url:URL.createObjectURL(i.files[0]),name:i.files[0].name,type:i.files[0].type}}]:n[s]=[{file:i.files[0],tempFile:{url:URL.createObjectURL(i.files[0]),name:i.files[0].name,type:i.files[0].type}}]:n[s]={file:i.files[0],tempURL:URL.createObjectURL(i.files[0])},_({...n})};async function B(){try{const s=await E.getProfile();D(s),H("email",s==null?void 0:s.email),H("first_name",s==null?void 0:s.first_name),H("last_name",s==null?void 0:s.last_name),A(s==null?void 0:s.email),v(s==null?void 0:s.photo)}catch(s){console.log("Error",s),ie(p,s.response.data.message?s.response.data.message:s.message)}}const O=async s=>{var i,g,n,ee,se,te;D(s);try{if(u(!0),m&&m.photo&&((i=m.photo)!=null&&i.file)){let o=new FormData;o.append("file",(g=m.photo)==null?void 0:g.file);let r=await E.uploadImage(o);console.log("uploadResult"),console.log(r),s.photo=r.url,M(p,"Profile Photo Updated",1e3)}const a=await E.updateProfile({first_name:s.first_name||(t==null?void 0:t.first_name),last_name:s.last_name||(t==null?void 0:t.last_name),photo:s.photo||w});if(!a.error)M(p,"Profile Updated",4e3),b();else{if(a.validation){const o=Object.keys(a.validation);for(let r=0;r<o.length;r++){const c=o[r];S(c,{type:"manual",message:a.validation[c]})}}b()}if(j!==s.email){const o=await E.updateEmail(s.email);if(!o.error)M(p,"Email Updated",1e3);else if(o.validation){const r=Object.keys(o.validation);for(let c=0;c<r.length;c++){const P=r[c];S(P,{type:"manual",message:o.validation[P]})}}b()}if(s.password.length>0){const o=await E.updatePassword(s.password);if(!o.error)M(p,"Password Updated",2e3);else if(o.validation){const r=Object.keys(o.validation);for(let c=0;c<r.length;c++){const P=r[c];S(P,{type:"manual",message:o.validation[P]})}}}await B(),u(!1)}catch(a){u(!1),console.log("Error",a),S("email",{type:"manual",message:(ee=(n=a==null?void 0:a.response)==null?void 0:n.data)!=null&&ee.message?(te=(se=a==null?void 0:a.response)==null?void 0:se.data)==null?void 0:te.message:a==null?void 0:a.message}),ie(p,a.response.data.message?a.response.data.message:a.message)}};I.useEffect(()=>{le({type:"SETPATH",payload:{path:"profile"}}),B()},[]);const ne=()=>{R(!0)},G=()=>{l(!0)},W=()=>{x(!0)},K=()=>{h(!0)},b=()=>{R(!1),l(!1),x(!1),h(!1)},re=async()=>{try{u(!0);const s=await E.updateProfile({first_name:t==null?void 0:t.first_name,last_name:t==null?void 0:t.last_name,photo:""});if(!s.error)M(p,"Profile Picture Deleted",1e3);else if(s.validation){const i=Object.keys(s.validation);for(let g=0;g<i.length;g++){const n=i[g];S(n,{type:"manual",message:s.validation[n]})}}await B(),u(!1),b()}catch(s){u(!1),console.log("Error",s)}};return e.jsxs("div",{className:"mt-6 w-10/12 rounded-md border",children:[e.jsx("div",{className:"flex items-center border-b border-b-[#E0E0E0] px-8 py-3 text-[#8D8D8D]",children:e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsx("div",{className:`cursor-pointer rounded-lg px-3 py-1 ${y==="Profile"?"bg-[#f4f4f4] text-[#1f1d1a]":""} `,onClick:()=>C("Profile"),children:"Profile"}),e.jsx("div",{className:`cursor-pointer rounded-lg px-3 py-1 ${y==="Security"?"bg-[#f4f4f4] text-[#1f1d1a]":""} `,onClick:()=>C("Security"),children:"Security"})]})}),e.jsxs("main",{children:[y==="Profile"&&e.jsx("div",{className:"rounded bg-brown-main-bg",children:e.jsxs("form",{onSubmit:k(O),children:[e.jsxs("div",{className:"mb-8 flex items-center border-b border-b-[#E0E0E0] px-10",children:[e.jsxs("div",{className:"relative mb-4 h-[100px] w-fit py-5",children:[m&&((J=m.photo)!=null&&J.tempURL)||w?e.jsx("div",{className:"flex h-[80px] w-[80px] items-center rounded-2xl",children:e.jsx("img",{className:"h-[80px] w-[80px] rounded-2xl object-cover",src:((Q=m.photo)==null?void 0:Q.tempURL)||w,alt:""})}):null,w||(X=m.photo)!=null&&X.file?null:e.jsx("div",{className:"flex h-[80px] w-full items-center justify-center bg-slate-300",children:"Select a picture"}),e.jsx("input",{className:"focus:shadow-outline absolute left-0 top-0 h-full w-[100px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] opacity-0   shadow focus:outline-none sm:w-[180px]",id:"photo",type:"file",placeholder:"Photo",name:"photo",onChange:s=>oe("photo",s.target)}),e.jsx("p",{className:"text-xs italic text-red-500",children:(Y=L.photo)==null?void 0:Y.message})]}),e.jsx("div",{className:"ml-6	mr-4	text-sm font-semibold text-indigo-600",children:e.jsx(T,{type:"submit",loading:f,disabled:f,children:"Update"})}),e.jsx("div",{className:"ml-3	cursor-pointer	text-sm font-semibold text-gray-600",onClick:ne,children:"Remove"})]}),e.jsxs("div",{className:"mx-10 max-w-lg",children:[e.jsx("p",{className:"mb-3	text-base	font-medium text-gray-900",children:"Personal Details"}),e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-x-20",children:[e.jsx("p",{className:"text-base	font-medium	text-gray-600",children:"First Name"}),e.jsx("p",{className:"text-base	font-medium	text-gray-900",children:t==null?void 0:t.first_name})]}),e.jsx("p",{className:"cursor-pointer	text-base	font-semibold text-indigo-600",onClick:G,children:"Edit"})]}),e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-x-20",children:[e.jsx("p",{className:"text-base	font-medium	text-gray-600",children:"Last Name"}),e.jsx("p",{className:"text-base	font-medium	text-gray-900",children:t==null?void 0:t.last_name})]}),e.jsx("p",{className:"cursor-pointer	text-base	font-semibold text-indigo-600",onClick:W,children:"Edit"})]}),e.jsxs("div",{className:"mb-6 flex items-center justify-between text-left",children:[e.jsxs("div",{className:"flex items-center gap-x-2",children:[e.jsx("p",{className:"mr-28	text-base	font-medium text-gray-600",children:"Email"}),e.jsx("p",{className:"text-base	font-medium	text-gray-900",children:j})]}),e.jsx("p",{className:"cursor-pointer	text-base	font-semibold text-indigo-600",onClick:K,children:"Edit"})]})]})]})}),y==="Security"&&e.jsx("div",{className:"rounded bg-brown-main-bg px-10 py-6",children:e.jsx("form",{onSubmit:k(O),className:"max-w-lg",children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold capitalize capitalize text-gray-700",children:"Password"}),e.jsx("input",{...F("password"),name:"password",className:"focus:shadow-outline w-[100px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] shadow   focus:outline-none sm:w-[180px]",id:"password",type:"password",placeholder:"******************"}),e.jsx("p",{className:"text-xs italic text-red-500",children:(V=L.password)==null?void 0:V.message})]}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(T,{className:"focus:shadow-outline rounded bg-indigo-600 px-4 py-2 font-bold text-white hover:bg-indigo-600 focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:f,disabled:f,children:"Update"})})]})})}),z&&e.jsx(fe,{actionHandler:re,closeModalFunction:b,title:"Are you sure ? ",message:"Are you sure you want to delete profile picture ? ",acceptText:"DELETE",rejectText:"CANCEL"}),U&&e.jsx(Z,{title:"Edit information",label:"First Name",buttonName:"Save and close",isOpen:G,onClose:b,handleSubmit:k,onSubmit:O,register:F,id:"first_name",submitLoading:f,errors:L}),N&&e.jsx(Z,{title:"Edit information",label:"Last Name",buttonName:"Save and close",isOpen:W,onClose:b,handleSubmit:k,onSubmit:O,register:F,id:"last_name",submitLoading:f,errors:L}),q&&e.jsx(Z,{title:"Change Email",label:"Email",buttonName:"Submit",isOpen:K,onClose:b,handleSubmit:k,onSubmit:O,register:F,id:"email",submitLoading:f,errors:L,defaultValues:t})]})]})},Z=$=>{var y,C;const{title:p,label:j,buttonName:A,isOpen:m,onClose:_,handleSubmit:z,onSubmit:R,register:U,id:l,submitLoading:N,errors:x,defaultValues:q}=$,[h,w]=d.useState(!1),[v,f]=d.useState({email:""}),u=t=>D=>{t==="email"&&f({...v,[t]:D.target.value})};return e.jsx("div",{className:"fixed inset-0 z-10 overflow-y-auto",children:e.jsx("div",{className:`fixed inset-0 z-10 overflow-y-auto ${m?"block":"hidden"} `,children:e.jsxs("div",{className:"flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 transition-opacity",children:e.jsx("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),e.jsx("span",{className:"hidden sm:inline-block sm:h-screen sm:align-middle","aria-hidden":"true",children:"​"}),e.jsxs("div",{className:"inline-block transform overflow-hidden rounded-lg bg-brown-main-bg px-4 pb-4 pt-5 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-md sm:p-6 sm:align-middle",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-lg font-semibold leading-6 text-gray-900",children:p}),e.jsx("button",{className:"text-gray-500 hover:text-gray-700 focus:outline-none",onClick:_,children:e.jsx("svg",{className:"h-6 w-6",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("form",{onSubmit:z(R),className:"max-w-lg",children:[h===!0&&e.jsxs("div",{className:"mt-3 flex",children:[e.jsx("div",{className:"mr-2",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.0003 1.66663C5.39795 1.66663 1.66699 5.39759 1.66699 9.99996C1.66699 14.6023 5.39795 18.3333 10.0003 18.3333C14.6027 18.3333 18.3337 14.6023 18.3337 9.99996C18.3337 5.39759 14.6027 1.66663 10.0003 1.66663ZM8.33366 9.16663C8.33366 8.82145 8.61348 8.54163 8.95866 8.54163H10.0003C10.3455 8.54163 10.6253 8.82145 10.6253 9.16663L10.6253 13.5416C10.6253 13.8868 10.3455 14.1666 10.0003 14.1666C9.65515 14.1666 9.37533 13.8868 9.37533 13.5416L9.37532 9.79163H8.95866C8.61348 9.79163 8.33366 9.5118 8.33366 9.16663ZM10.0003 6.04163C9.65515 6.04163 9.37533 6.32145 9.37533 6.66663C9.37533 7.0118 9.65515 7.29163 10.0003 7.29163C10.3455 7.29163 10.6253 7.0118 10.6253 6.66663C10.6253 6.32145 10.3455 6.04163 10.0003 6.04163Z",fill:"#4F46E5"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-1	text-sm	font-medium text-gray-600",children:"We've send an email to:"}),e.jsx("p",{className:"mb-2	text-sm	font-semibold text-gray-900",children:v==null?void 0:v.email}),e.jsx("p",{className:"mb-2	text-sm	font-medium text-gray-600",children:"In order to complete the email update click the confirmation link."}),e.jsx("p",{className:"mb-2	text-sm	font-medium text-gray-600",children:"(the link expires in 24 hours)"})]})]}),h===!1&&(l==="first_name"||l==="last_name")&&e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{htmlFor:"firstName",className:"mb-1 block text-sm font-medium text-gray-700",children:j}),e.jsx("input",{className:"focus:shadow-outline w-[100px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] shadow   focus:outline-none sm:w-[180px]",id:l,type:"text",placeholder:`Enter ${j} `,name:l,...U(l)}),e.jsx("p",{className:"text-xs italic text-red-500",children:(y=x==null?void 0:x.id)==null?void 0:y.message})]}),h===!1&&l==="email"&&e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{htmlFor:"firstName",className:"mb-1 block text-sm font-medium text-gray-700",children:j}),e.jsx("input",{className:"focus:shadow-outline w-[100px] appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] shadow   focus:outline-none sm:w-[180px]",id:l,type:"text",placeholder:`Enter ${j}`,name:l,...U(l),onChange:u("email")}),e.jsx("p",{className:"text-xs italic text-red-500",children:(C=x==null?void 0:x.id)==null?void 0:C.message})]}),e.jsxs("div",{className:"mt-4 flex justify-between",children:[e.jsx("button",{className:"mr-2 w-full rounded-md border border-black/60 px-4 py-2 text-gray-700	",onClick:_,children:"Cancel"}),(l==="first_name"||l==="last_name"||h===!0)&&e.jsx(T,{className:"focus:shadow-outline w-full rounded-md bg-indigo-500 px-4 py-2 font-bold text-white hover:bg-indigo-600 focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:N,disabled:N,children:A}),l==="email"&&!h&&e.jsx(T,{className:"focus:shadow-outline w-full rounded-md bg-indigo-500 px-4 py-2 font-bold text-white hover:bg-indigo-600 focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:N,disabled:N,onClick:()=>w(!0),children:"Submit"})]})]})]})]})})})};export{Z as EditInfoModal,Ie as default};
