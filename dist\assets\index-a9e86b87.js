import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as t,i as v}from"./vendor-4cdf2bd1.js";import{h as S}from"./moment-a9aaa855.js";import{A as _,G as N,I as k,M as T,s as g,t as A,U as C,y as F,z as M}from"./index-f2ad9142.js";import{u as D}from"./useNotes-19835bdc.js";import{u as P}from"./useUpdate-3cd97540.js";import{u as E}from"./useUpdateRequests-c5628c0a.js";import{u as I}from"./useUpdateQuestions-4dea6f0c.js";import{X as L}from"./XMarkIcon-cfb26fe7.js";import{t as p,S as u,L as f}from"./@headlessui/react-cdd9213e.js";import{R as w}from"./tableWrapper-ca490fb1.js";import{u as U}from"./useDate-c1da5729.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./index.esm-7add6cfb.js";import"./react-icons-36ae72b7.js";function Y({update:l,updateRequest:n,updateQuestion:x}){const[i,r]=t.useState(!1),{dispatch:h}=t.useContext(_),{dispatch:o}=t.useContext(N),[d,m]=t.useState(!1);async function s(){m(!0);try{await new T().callRawAPI("/v3/api/custom/goodbadugly/member/send-question-reminder",{update_question_id:x.id,update_request_id:n.id},"POST"),r(!1),g(o,"Reminder sent")}catch(a){A(h,a.message),g(o,a.message,5e3,"error")}m(!1)}return console.log(n),e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"text-primary-black underline",onClick:()=>r(!0),children:"Send reminder"}),e.jsx(p,{appear:!0,show:i,as:t.Fragment,children:e.jsxs(u,{as:"div",className:"relative z-[50] sm:z-[50]",onClose:()=>r(!1),children:[e.jsx(p.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(p.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(u.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(u.Title,{as:"h3",className:"text-lg font-semibold leading-6 text-gray-900",children:"Send reminder"}),e.jsx("button",{onClick:()=>r(!1),type:"button",children:e.jsx(L,{className:"h-6 w-6"})})]}),e.jsxs("p",{className:"mt-2",children:["Send email reminder to"," ",e.jsx("b",{className:"font-medium",children:(n==null?void 0:n.name)||"{{user}}"}),", to respond to this question?"," "]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#1f1d1a] py-2 text-center font-iowan",type:"button",onClick:()=>r(!1),children:"Cancel"}),e.jsx(k,{loading:d,disabled:d,onClick:s,className:"disabled:bg-disabledblack rounded-lg bg-primary-black py-2 text-center font-semibold text-white transition-colors duration-100",children:"Yes send"})]})]})})})})]})})]})}const R=[{header:"Name"},{header:"Opened"},{header:"Viewed"},{header:"Shared"},{header:"Last viewed at"}],z=[{header:"Question"},{header:"Response"},{header:"Action"}];function xe(){const{id:l}=v(),{update:n}=P(l),{notes:x}=D(l),{updateRequests:i}=E(l),{questions:r}=I(l),{dispatch:h}=t.useContext(N),[o,d]=t.useState(!1),{convertDate:m}=U();return t.useEffect(()=>{h({type:"SETPATH",payload:{path:"updates"}})},[]),console.log(i),e.jsx("div",{className:"flex flex-wrap-reverse gap-4 justify-between items-end p-5 pt-8 sm:px-8",children:e.jsxs("div",{className:"flex-grow w-full max-w-6xl",children:[e.jsxs("h1",{className:"text-[32px] font-bold",children:[n.name," Insights"]}),e.jsxs("div",{className:"flex gap-4 items-center mt-6",children:[e.jsx("p",{className:"font-medium",children:"Report Date:"}),e.jsx("p",{children:S(n.date).format("MMM DD, YYYY")})]}),e.jsxs("div",{className:"flex gap-4 items-center mt-6",children:[e.jsx("p",{className:"font-medium",children:"Report Status:"}),e.jsx("p",{children:C[n.status]})]}),e.jsxs("div",{className:"mt-12",children:[e.jsx("p",{className:"mb-2 text-[16px] font-semibold sm:text-xl ",children:"People who viewed it"}),e.jsx(w,{children:e.jsxs("table",{className:"mt-4 min-w-full divide-y divide-[#1f1d1a]/10",children:[e.jsx("thead",{children:e.jsx("tr",{children:R.map((s,a)=>e.jsx("th",{scope:"col",className:"font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3",children:s.header},a))})}),e.jsx("tbody",{className:"font-iowan-regular  divide-y divide-[#1f1d1a]/10",children:i==null?void 0:i.map((s,a)=>e.jsxs("tr",{className:"  md:h-[60px]",children:[e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:s.name}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:s.view_count>0?"Yes":"No"}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:s.view_count}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:e.jsx(f,{className:"relative",children:({open:c})=>{var j;return e.jsxs(e.Fragment,{children:[e.jsx(f.Button,{as:"div",className:"  max-w-[300px] cursor-pointer font-bold underline",onMouseEnter:()=>d(a),onMouseLeave:()=>d(!1),children:s.shares}),e.jsx(p,{as:t.Fragment,enter:"transition ease-out duration-200",enterFrom:"opacity-0 -translate-y-1",enterTo:"opacity-100 translate-y-0",leave:"transition ease-in duration-150",leaveFrom:"opacity-100 translate-y-0",leaveTo:"opacity-0 -translate-y-1",show:a===o,onMouseEnter:()=>d(a),onMouseLeave:()=>d(!1),className:" absolute z-[999999999999999999] w-[300px]",children:e.jsx(f.Panel,{className:"px-4",children:e.jsx("div",{className:"  rounded-lg bg-[#1f1d1a] p-4 px-4 text-white shadow-lg ring-1 ring-[#1f1d1a]/5",children:e.jsx("div",{className:"flex flex-col  gap-2 text-[14px] font-medium",children:(j=s==null?void 0:s.shared_emails)==null?void 0:j.map((b,y)=>e.jsx("p",{className:"text-[13px] text-gray-300",children:b},y))})})})})]})}},s.id)}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:s.view_count==0?"N/A":m(s.last_viewed_at,{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!0,timeZoneName:"short",timeZone:"America/Los_Angeles"}).replace(", "," - ")})]},a))})]})})]}),e.jsxs("div",{className:"mt-12",children:[e.jsx("p",{className:"mb-2 text-[16px] font-semibold sm:text-xl ",children:"Asks"}),e.jsx(w,{children:e.jsxs("table",{className:"mt-4 min-w-full divide-y divide-[#1f1d1a]/10 ",children:[e.jsx("thead",{children:e.jsx("tr",{children:z.map((s,a)=>e.jsx("th",{scope:"col",className:"font  whitespace-nowrap border-b-[#1f1d1a]/10  px-4 text-left font-[700] md:border-0 md:border-b-[3px] md:border-dashed md:px-6 md:py-3",children:s.header},a))})}),e.jsx("tbody",{className:"font-iowan-regular  divide-y divide-[#1f1d1a]/10",children:r.map((s,a)=>e.jsxs("tr",{className:"  md:h-[60px]",children:[e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:s.question}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:s.reply_text}),e.jsx("td",{className:"px-3 whitespace-nowrap md:max-w-lg md:whitespace-normal md:px-6 md:py-6",children:s.reply_text?"":e.jsx(Y,{updateRequest:i.find(c=>c.investor_id==s.investor_id),update:n,updateQuestion:s})})]},a))})]})}),r.length==0?e.jsx("p",{className:"my-8 text-center",children:"No questions was asked"}):""]}),x.map(s=>{const{blocks:a}=F(s.content,{blocks:[]}),c=M(a);return e.jsxs("div",{className:"flex gap-6 items-start mt-6",children:[e.jsxs("p",{className:"font-medium",children:[s.type,":"]}),s.content?e.jsx("p",{className:"max-w-lg",dangerouslySetInnerHTML:{__html:c}}):"N/A"]})})]})})}export{xe as default};
