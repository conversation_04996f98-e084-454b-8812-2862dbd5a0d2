import{A as l,G as p,M as d,t as h,s as f}from"./index-f2ad9142.js";import{r as t}from"./vendor-4cdf2bd1.js";function b(){const[c,e]=t.useState(!1),[r,n]=t.useState([]),{dispatch:u}=t.useContext(l),{dispatch:i}=t.useContext(p),a=t.useCallback(async()=>{e(!0);try{const o=await new d().callRawAPI("/v3/api/goodbadugly/customer/recent-subscription/");console.log(o),n(o.model)}catch(s){h(u,s.message),f(i,s.message,5e3,"error")}e(!1)},[]);return t.useEffect(()=>{a()},[]),{loading:c,data:r,refetch:a}}export{b as u};
