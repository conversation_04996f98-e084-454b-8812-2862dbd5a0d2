import{j as r}from"./@nextui-org/listbox-0f38ca19.js";import{r as p,b as U,h as z}from"./vendor-4cdf2bd1.js";import{u as H}from"./react-hook-form-a383372b.js";import{o as K}from"./yup-0917e80c.js";import{c as Y,a as S,d as J}from"./yup-342a5df4.js";import{S as Q}from"./CreateGroupModal-d6bb962a.js";import{S as W}from"./SelectGroupType-2b6e1a07.js";import{u as X}from"./useRecipientGroups-30bdbf7a.js";import{b as Z,a as M,L as g,I as h}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./MkdCustomInput-aaf80542.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./InteractiveButton-060359e0.js";import"./index-dc002f62.js";import"./qr-scanner-cf010ec4.js";import"./react-spinners-b860a5a3.js";import"./XMarkIcon-cfb26fe7.js";import"./@headlessui/react-cdd9213e.js";import"./index-b8adfdf8.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const Ae=()=>{const{sdk:o}=Z(),{globalDispatch:P,authDispatch:ee,authState:c,globalState:{fundManagersToAdd:t,subscriptionData:x},setGlobalState:_,tokenExpireError:b,showToast:d,create:k,custom:E}=M(),[w,R]=p.useState(""),{groups:G,refetch:re,loading:te}=X(c==null?void 0:c.user),[j,I]=p.useState({members:[]}),T=Y({group_id:S().required("This field is required"),members:J().min(1,"You must add at least one member").of(S())}),l=U(),[V]=z(),y=V.get("updateId");let n=G.find(e=>(e==null?void 0:e.group_name)==w)||null;const{register:se,handleSubmit:L,setError:N,setValue:u,formState:{errors:m,isSubmitting:f,defaultValues:ie},control:v,clearErrors:C,watch:D}=H({resolver:K(T),defaultValues:{group_id:"",members:[]}}),[$,q]=D(["members","group_id"]);async function F(){try{o.setTable("recipient_group");const e=await o.callRawAPI(`/v4/api/records/recipient_group/${n.id}`,void 0,"GET");o.setTable("recipient_member");const{list:i}=await o.callRawAPI(`/v4/api/records/recipient_member?filter=recipient_group_id,eq,${n.id}`);return{group_id:e.model.group_id,members:i.map(s=>String(s.user_id))}}catch(e){return b(e.message),{group_id:"",members:[]}}}const B=async(e,i)=>{try{const s=await k("update_group",{update_id:e,group_id:i},!1);s!=null&&s.error||l(`/member/edit-updates/${e}?next=3`)}catch(s){b(s.message),N("group_id",{type:"manual",message:s.message})}},O=async e=>{var i;try{const s=await E({endpoint:"/v3/api/custom/goodbadugly/recipients/add-fund-manager",method:"POST",payload:{group_id:Number(e==null?void 0:e.group_id),fund_managers:(i=e==null?void 0:e.members)==null?void 0:i.map(a=>Number(a))}});s!=null&&s.error||(d(s==null?void 0:s.message),l("/member/recipient_group"))}catch(s){d(s==null?void 0:s.message)}finally{}},A=async e=>{if(console.log(e),t&&(t!=null&&t.length)){await O(e);return}try{if(n){o.setTable("recipient_group"),await o.callRestAPI({id:n.id,group_id:e.group_id,members:e.members.join(","),user_id:c.user},"PUT");const i=e.members.filter(a=>!j.members.includes(a)),s=j.members.filter(a=>!e.members.includes(a));console.log(i,s),o.setTable("recipient_member"),await Promise.all(i.map(a=>o.callRestAPI({user_id:a.id,recipient_group_id:n.id},"POST"))),await Promise.all(s.map(a=>o.callRawAPI("/v4/api/records/recipient_member",{user_id:a.id,recipient_group_id:n.id},"DELETE"))),d("Added"),l("/member/recipient_group")}else{o.setTable("recipient_group");const i=await o.callRestAPI({group_id:e.group_id,members:e.members.join(","),user_id:c.user},"POST");o.setTable("recipient_member"),await Promise.all(e.members.map(s=>o.callRestAPI({recipient_group_id:i.data,user_id:s},"POST"))),d("Added"),y?B(y,i.data):l("/member/recipient_group")}}catch(i){b(i.message),N("group_id",{type:"manual",message:i.message})}};return p.useEffect(()=>{P({type:"SETPATH",payload:{path:"recipient_group"}})},[]),p.useEffect(()=>{C(["members","group_id"])},[$,q]),p.useEffect(()=>{(async function(){if(n){const e=await F();I(e),u("members",e.members)}else u("members",[])})()},[n,w]),p.useEffect(()=>(t&&(t!=null&&t.length)&&u("members",[...t==null?void 0:t.map(e=>e==null?void 0:e.id)]),()=>{_("fundManagersToAdd",null)}),[t==null?void 0:t.length]),r.jsxs("div",{className:"p-5 pt-8 min-h-screen sm:px-8",children:[r.jsxs("button",{className:"mb-7 flex h-[2.25rem] w-[5.1875rem] items-center justify-center gap-2 ",onClick:()=>l(-1),children:[r.jsx("div",{className:"flex min-h-[32px] min-w-[32px] items-center justify-center  gap-2 rounded border border-[#1f1d1a] ",children:r.jsxs("svg",{className:"min-h-5 min-w-5",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[r.jsx("path",{d:"M17.5 10.0001L2.5 10.0001",stroke:"black","stroke-width":"1.66667","stroke-linecap":"round","stroke-linejoin":"round"}),r.jsx("path",{d:"M8.33203 15.8335L2.4987 10.0002L8.33203 4.16683",stroke:"black","stroke-width":"1.66667","stroke-linecap":"round","stroke-linejoin":"round"})]})}),r.jsx("span",{className:"font-inter font-[400]",children:" Back"})]}),r.jsxs("div",{className:"flex flex-col gap-5 items-start h-fit",children:[r.jsxs("div",{children:[r.jsxs("h4",{className:"mb-2 font-iowan text-[16px] font-[600] sm:text-[20px]",children:["Create Recipient Group ",r.jsx("br",{}),r.jsx("span",{className:"font-inter text-[12px] font-normal md:text-[14px]",children:"Add existing users to existing or new recipient group"})]}),r.jsxs("form",{className:"mt-5 w-full max-w-[500px]",onSubmit:L(A,e=>{console.error(e)}),children:[r.jsx(W,{control:v,name:"group_id",errors:m,setGroupName:R,setValue:e=>u("group_id",e),allowedRoles:["investor","stakeholder"]}),m&&(m==null?void 0:m.group_id)&&r.jsx("p",{className:"text-field-error absolute inset-x-0 top-[90%] m-auto mt-2 text-[.8rem] italic text-red-500",children:"Group is Required"}),t&&(t!=null&&t.length)?r.jsxs("div",{className:"flex gap-5 justify-start items-center font-iowan-regular grow",children:[r.jsx("b",{className:"text-[3.125rem]",children:t==null?void 0:t.length})," ",r.jsx("span",{children:"Fund Managers Selected"})]}):r.jsx(Q,{control:v,name:"members",setValue:e=>u("members",e)}),m&&(m==null?void 0:m.members)&&r.jsx("p",{className:"text-field-error absolute inset-x-0 top-[90%] m-auto mt-2 text-[.8rem] italic text-red-500",children:"Members is Required"}),r.jsxs("div",{className:"flex gap-5 justify-start items-center",children:[r.jsx(g,{children:r.jsx(h,{type:"submit",loading:f,disabled:f,className:"focus:shadow-outline mt-4 w-[88px] rounded bg-primary-black px-4 py-2 font-iowan font-bold text-white focus:outline-none",children:"Submit"})}),t&&(t!=null&&t.length)?r.jsx(g,{children:r.jsx(h,{type:"button",disabled:f,onClick:()=>{_("fundManagersToAdd",null)},className:"px-4 py-2 mt-4 font-bold text-white rounded focus:shadow-outline bg-primary-black font-iowan focus:outline-none",children:"Remove Fund Managers"})}):null]})]})]}),r.jsx("div",{className:"my-6 flex h-[.125rem] max-h-[.125rem] min-h-[.125rem]  w-full grow flex-row items-center gap-4  border  border-[#1f1d1a]/10 "}),r.jsxs("div",{className:"mb-[30px] flex h-fit w-full flex-col space-y-5 font-iowan",children:[r.jsxs("h4",{className:"mb-2 font-iowan text-[16px] font-[600] sm:text-[20px]",children:["Add Fund Managers to a Recipient Group ",r.jsx("br",{}),!(x!=null&&x.subscription)&&r.jsxs("span",{className:"font-inter text-[12px] font-normal md:text-[14px]",children:[r.jsx("span",{className:"font-semibold underline cursor-pointer tex-sm underline-offset-2",onClick:()=>{l("/member/billing")},children:"Upgrade"})," to a Pro, Business, or Enterprise plan to access more features."]})]}),r.jsx("div",{children:r.jsx(g,{children:r.jsx(h,{onClick:()=>l("/member/search-fund-managers"),className:"!rounded-[.125rem] !px-4 !py-2 font-iowan text-white",children:"Search fund managers"})})})]})]})]})};export{Ae as default};
