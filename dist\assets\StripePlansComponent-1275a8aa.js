import{j as a}from"./@nextui-org/listbox-0f38ca19.js";import{R as g,r as f}from"./vendor-4cdf2bd1.js";import{M as N,A as E,G as I,I as O,s as o,t as l}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const J=()=>{const c=new N,{dispatch:n,state:h}=g.useContext(E),{dispatch:s}=g.useContext(I),[S,C]=f.useState([]),[u,k]=f.useState({}),[b,m]=f.useState(!1),v=async e=>{try{const i=await c.cancelStripeSubscription(e);if(i.error){console.error(i.message),o(s,i.message,7500,"error");return}o(s,i.message,1e4,"error"),d(),x()}catch(i){console.error(i),o(s,i.message,7500,"error"),l(n,i.code)}},j=async(e,{planId:i,planStripeId:_,planType:p})=>{var y,w;if(m(!0),e==="change")try{if(p==="lifetime"){o(s,"You have to cancel your recurring subscription first",1e4);return}const r=await c.changeStripeSubscription({userId:h.user,activeSubscriptionId:u.subId,newPlanId:i});if(r.error){console.error(r.message),o(s,r.message,7500);return}o(s,r.message,1e4),setTimeout(()=>{d()},3e3)}catch(r){console.error(r),o(s,r.message,7500),l(n,r.code)}else if(e==="checkout"){if(p==="recurring")try{const r=await c.createStripeSubscription({planId:i});r.error&&(console.error(r.message),o(s,r.message,7500)),o(s,r.message,1e4),setTimeout(()=>{d()},3e3)}catch(r){m(!1),console.error("Error",r),o(s,r.message,7500,"error"),l(n,r.code)}else if(p==="lifetime"){let r={success_url:`${c.fe_baseurl}/user/checkout?success=true&session_id={CHECKOUT_SESSION_ID}`,cancel_url:`${c.fe_baseurl}/user/checkout?success=false&session_id={CHECKOUT_SESSION_ID}`,mode:"payment",payment_method_types:["card"],line_items:[{price:_,quantity:1}],payment_intent_data:{metadata:{app_price_id:i,is_lifetime_subscription:"true"}}};try{const t=await c.initCheckoutSession(r);if(t.error){o(s,t.message||"Something went wrong",7500,"error");return}(y=t==null?void 0:t.model)!=null&&y.url&&(location.href=t.model.url)}catch(t){m(!1),console.error(t),o(s,t.message,7500,"error"),l(n,t.code)}}else if(p==="one_time"){let r={success_url:`${c.fe_baseurl}/user/checkout?success=true&session_id={CHECKOUT_SESSION_ID}`,cancel_url:`${c.fe_baseurl}/user/checkout?success=false&session_id={CHECKOUT_SESSION_ID}`,mode:"payment",payment_method_types:["card"],line_items:[{price:_,quantity:1}],payment_intent_data:{metadata:{app_price_id:i,is_lifetime_subscription:"false"}}};try{const t=await c.initCheckoutSession(r);if(t.error){o(s,t.message||"Something went wrong");return}(w=t==null?void 0:t.model)!=null&&w.url&&(location.href=t.model.url)}catch(t){m(!1),console.error(t),o(s,t.message,7500,"error"),l(n,t.code)}}}m(!1)},x=async()=>{try{const e=await c.getStripePrices({limit:"all"},{type:"recurring,lifetime,one_time"});if(e.error){o(s,e.message,7500);return}C(e.list)}catch(e){console.error(e),o(s,e.message,7500,"error"),l(n,e.code)}},d=async()=>{try{const e=await c.getCustomerStripeSubscription(h.role);if(e.error){o(s,e.message,7500);return}k(e.customer)}catch(e){o(s,e.message,7500,"error"),l(n,e.code)}};return g.useEffect(()=>{s({type:"SETPATH",payload:{path:"billing"}}),x(),d()},[]),a.jsxs("div",{className:"my-4 rounded bg-brown-main-bg p-5 shadow-lg",children:[a.jsx("h2",{className:"mb-3 mt-0 text-xl font-medium leading-tight text-[#1f1d1a]",children:"Plans"}),a.jsx("div",{className:"flex  w-full flex-wrap justify-center ",children:S.map((e,i)=>a.jsx("div",{className:"flex w-full justify-center md:w-1/2 xl:w-1/4 ",children:a.jsxs("div",{className:"m-2 w-full max-w-sm rounded-lg border bg-brown-main-bg text-center shadow-lg",children:[a.jsx("div",{className:"border-b border-black/60 px-6 py-3",children:e.product_name??"n/a"}),a.jsxs("div",{className:"flex flex-col justify-center p-6",children:[a.jsx("h5",{className:"mb-2 text-xl font-medium text-gray-900",children:e.name}),a.jsxs("p",{className:"mb-4 text-base text-gray-700",children:["$",+e.amount]}),+(u==null?void 0:u.planId)==+e.id?a.jsx("button",{onClick:()=>v(u.subId),type:"button",className:" inline-block rounded bg-blue-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg",children:"Cancel"}):a.jsx(O,{onClick:()=>j(u.subId?"change":"checkout",{planId:e.id,planStripeId:e.stripe_id,planType:e.type}),type:"button",loading:b,disabled:b,className:" inline-block rounded bg-blue-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg",children:"Subscribe"})]}),a.jsx("div",{className:"border-t border-black/60 px-6 py-3 text-gray-600",children:+e.trial_days>0?`${+e.trial_days} Days`:"No trial"})]})},i))})]})};export{J as default};
