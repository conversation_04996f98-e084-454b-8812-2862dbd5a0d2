import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as l,b as _,L as T}from"./vendor-4cdf2bd1.js";import{u as E}from"./react-hook-form-a383372b.js";import{o as G}from"./yup-0917e80c.js";import{c as U,a as o}from"./yup-342a5df4.js";import{G as I,I as q,s as y,M as C}from"./index-f2ad9142.js";import{S as F}from"./spiral2-6c5dd995.js";import"./@nextui-org/theme-345a09ed.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const ce=()=>{var p,x,f,h,u,b,g,w,j,A,N;const v=U({first_name:o().required("This field is required"),last_name:o().required("This field is required"),email:o().email().required("This field is required"),password:o().required("This field is required"),company_website:o(),company_name:o().required("This field is required")}),[S,m]=l.useState(!1),{dispatch:c}=l.useContext(I),[i,k]=l.useState(!1),M=_(),{register:r,handleSubmit:L,setError:R,formState:{errors:s,isSubmitting:n}}=E({resolver:G(v),defaultValues:{first_name:"",last_name:"",email:"",password:"",company_name:"",company_website:""}}),z=async a=>{try{if(!S){y(c,"Agree to the Terms of Service and Privacy Policy",5e3,"error");return}await new C().callRawAPI("/v3/api/custom/goodbadugly/users/register-email",{first_name:a.first_name,last_name:a.last_name,email:a.email,password:a.password,role:"collaborator"},"POST"),M("/get-verified")}catch(t){console.log("Error",t),y(c,t.message,5e3,"error"),R("email",{type:"manual",message:t.message})}},d=async a=>{try{const t=new C;let B="stakeholder";const P=await t.oauthLoginApi(a,B);window.open(P,"_self")}catch{}};return e.jsx("div",{className:"min-h-screen bg-brown-main-bg pb-10 md:pb-0",children:e.jsx("div",{className:"mx-auto ",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-[.5fr_1fr]",children:[e.jsxs("div",{className:"relative hidden space-y-4 bg-[#1F1D1A] text-white md:block",children:[e.jsxs("div",{class:"flex max-w-[452px] flex-col p-8",children:[e.jsx("a",{href:"/",class:"cursor-pointer",children:e.jsx("img",{src:"/assets/updatestack_logo-invert2.png",class:"w-[150px] cursor-pointer lg:w-[208px]",alt:""})}),e.jsx("h4",{class:"mb-8 mt-[100px] rounded-md py-2 text-[24px] font-normal text-white",children:"Corporate Manager"}),e.jsx("div",{class:"my-10 mt-5 h-[1px] w-[100px] bg-brown-main-bg"}),e.jsx("h1",{class:"font-Inter text-3xl font-semibold text-white",children:"Be more productive and save time by automating your team and investor updates now."}),e.jsx("div",{class:"my-10 h-[1px] w-[100px] bg-brown-main-bg"}),e.jsxs("div",{class:"bg-[#292724] p-[20px] font-Inter text-white",children:[`"This tool saves me hours per week. Can't work without it now..."`,e.jsxs("div",{class:"mt-4 flex items-center gap-1",children:[e.jsx("img",{src:"/assets/elon.png",class:"h-[40px] w-[40px]",alt:""}),e.jsxs("div",{class:"flex flex-col gap-[2px]",children:[e.jsx("div",{class:"text-sm font-semibold",children:"Ben Nash"}),e.jsx("div",{class:"font-iowan-regular   text-sm",children:"CTO at Stack"})]})]})]})]}),e.jsx("div",{className:"",children:e.jsx("img",{src:F,className:"absolute bottom-0 h-[300px] w-full max-w-full",alt:"spiral"})})]}),e.jsxs("div",{className:"m-auto max-w-[600px] bg-brown-main-bg px-6 pt-[40px] md:py-[100px] ",children:[e.jsx("h3",{className:"text-[24px] font-[700] sm:text-[40px]",children:"Create an account"}),e.jsx("div",{className:"mt-3 block md:hidden",children:e.jsx("span",{className:"text-[18px] font-semibold sm:text-[27px] ",children:"Corporate Manager"})}),e.jsx("p",{className:"mt-4 font-medium text-gray-700",children:"Be more productive by sending automated updates free for 30 days!"}),e.jsxs("form",{onSubmit:L(z),children:[e.jsxs("div",{className:"mt-3 grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"",children:[e.jsx("label",{className:"mb-2 block font-iowan text-[16px] font-[700] capitalize capitalize text-gray-700",children:"First name"}),e.jsx("input",{type:"text",placeholder:"Enter First Name",autoComplete:"off",...r("first_name"),className:`w-full appearance-none rounded-sm  border-[2px] border-[#1f1d1a] bg-brown-main-bg px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(p=s.first_name)!=null&&p.message?"border-red-500":""}`}),e.jsx("p",{className:"text-sm italic text-red-500",children:(x=s.first_name)==null?void 0:x.message})]}),e.jsxs("div",{className:"",children:[e.jsx("label",{className:"mb-2 block font-iowan text-[16px] font-[700] capitalize capitalize text-gray-700",children:"Last name"}),e.jsx("input",{type:"text",autoComplete:"off",placeholder:"Enter last name",...r("last_name"),className:`w-full appearance-none rounded-sm  border-[2px] border-[#1f1d1a] bg-brown-main-bg px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(f=s.last_name)!=null&&f.message?"border-red-500":""}`}),e.jsx("p",{className:"text-sm italic text-red-500",children:(h=s.last_name)==null?void 0:h.message})]})]}),e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{className:"mb-2 block font-iowan text-[16px] font-[700] capitalize capitalize text-gray-700",children:"Email"}),e.jsx("input",{type:"text",placeholder:"Enter email",autoComplete:"off",...r("email"),className:`w-full appearance-none rounded-sm  border-[2px] border-[#1f1d1a] bg-brown-main-bg px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(u=s.email)!=null&&u.message?"border-red-500":""}`}),e.jsx("p",{className:"text-sm italic text-red-500",children:(b=s.email)==null?void 0:b.message})]}),e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{className:"mb-2 block font-iowan text-[16px] font-[700] capitalize capitalize text-gray-700",children:"Password"}),e.jsxs("div",{className:"flex h-[44px] items-center rounded-sm border-[2px] border-[#1f1d1a] bg-transparent px-2 py-1 text-[#1f1d1a]",children:[e.jsx("input",{className:"focus-visible::outline-none w-[95%] border-none bg-transparent p-1 shadow-[0] outline-none focus:border-none focus:shadow-none focus:outline-none",type:i?"text":"password",placeholder:"********",...r("password"),style:{boxShadow:"0 0 transparent"},autoComplete:"false"}),e.jsx("span",{className:"w-[5%] cursor-pointer",onClick:()=>k(!i),children:i?e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.99998 3.33337C13.5326 3.33335 16.9489 5.50937 19.0735 9.61715L19.2715 10L19.0735 10.3828C16.9489 14.4906 13.5326 16.6667 10 16.6667C6.46737 16.6667 3.05113 14.4907 0.926472 10.3829L0.728455 10.0001L0.926472 9.61724C3.05113 5.50946 6.46736 3.3334 9.99998 3.33337ZM7.08333 10C7.08333 8.38921 8.38917 7.08337 10 7.08337C11.6108 7.08337 12.9167 8.38921 12.9167 10C12.9167 11.6109 11.6108 12.9167 10 12.9167C8.38917 12.9167 7.08333 11.6109 7.08333 10Z",fill:"#A8A8A8"})}):e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.28033 2.21967C2.98744 1.92678 2.51256 1.92678 2.21967 2.21967C1.92678 2.51256 1.92678 2.98744 2.21967 3.28033L5.38733 6.44799C4.04329 7.533 2.8302 8.97021 1.81768 10.7471C1.37472 11.5245 1.37667 12.4782 1.81881 13.2539C3.74678 16.6364 6.40456 18.789 9.29444 19.6169C12.0009 20.3923 14.8469 19.9857 17.3701 18.4308L20.7197 21.7803C21.0126 22.0732 21.4874 22.0732 21.7803 21.7803C22.0732 21.4874 22.0732 21.0126 21.7803 20.7197L3.28033 2.21967ZM14.2475 15.3082L13.1559 14.2166C12.81 14.3975 12.4167 14.4995 11.9991 14.4995C10.6184 14.4995 9.49911 13.3802 9.49911 11.9995C9.49911 11.5819 9.60116 11.1886 9.78207 10.8427L8.69048 9.75114C8.25449 10.3917 7.99911 11.1662 7.99911 11.9995C7.99911 14.2087 9.78998 15.9995 11.9991 15.9995C12.8324 15.9995 13.6069 15.7441 14.2475 15.3082Z",fill:"#A8A8A8"}),e.jsx("path",{d:"M19.7234 16.5416C20.5189 15.7335 21.2556 14.7869 21.9145 13.7052C22.5512 12.66 22.5512 11.34 21.9145 10.2948C19.3961 6.16075 15.7432 4.00003 11.9999 4C10.6454 3.99999 9.30281 4.28286 8.02148 4.83974L19.7234 16.5416Z",fill:"#A8A8A8"})]})})]}),e.jsx("p",{className:"text-sm italic text-red-500",children:(g=s.password)==null?void 0:g.message})]}),e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{className:"mb-2 block font-iowan text-[16px] font-[700] capitalize capitalize text-gray-700",children:"Company Name"}),e.jsx("input",{type:"text",autoComplete:"off",placeholder:"ABC Enterprises",...r("company_name"),className:`w-full appearance-none rounded-sm  border-[2px] border-[#1f1d1a] bg-brown-main-bg px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(w=s.company_name)!=null&&w.message?"border-red-500":""}`}),e.jsx("p",{className:"text-sm italic text-red-500",children:(j=s.company_name)==null?void 0:j.message})]}),e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{className:"mb-2 block font-iowan text-[16px] font-[700] capitalize capitalize text-gray-700",children:"Company Website"}),e.jsx("input",{type:"text",placeholder:"www.abc.com",autoComplete:"off",...r("company_website"),className:`w-full appearance-none rounded-sm  border-[2px] border-[#1f1d1a] bg-brown-main-bg px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none ${(A=s.company_website)!=null&&A.message?"border-red-500":""}`}),e.jsx("p",{className:"text-sm italic text-red-500",children:(N=s.company_website)==null?void 0:N.message})]}),e.jsxs("p",{className:"mt-4  flex items-start font-semibold text-[#1f1d1a]",children:[e.jsx("input",{onChange:a=>{a.target.checked?m(!0):m(!1)},type:"checkbox",className:"mr-2 mt-[4px] h-[15px] w-[15px] border-[2px] bg-brown-main-bg text-[13px] sm:text-base"}),e.jsxs("span",{children:["By creating an account you are agreeing to the ",e.jsx("br",{}),e.jsx("a",{target:"_blank",className:"font-bold text-[#1f1d1a] underline",href:"https://updatestack.com/terms-of-use",children:"Terms of Service"})," ","and",e.jsxs("a",{target:"_blank",className:"font-bold text-[#1f1d1a] underline",href:"https://updatestack.com/privacy-policy",children:[" ","Privacy Policy"]})]})]}),e.jsx(q,{type:"submit",className:"my-5 flex h-[44px] w-full items-center justify-center rounded-sm bg-[#1f1d1a] py-2 tracking-wide text-white outline-none focus:outline-none",loading:n,disabled:n,children:e.jsx("span",{className:"capitalize",children:n?"Signing Up":"Sign Up"})})]}),e.jsxs("div",{className:"mt-4 flex min-w-[70%] flex-row  items-center",children:[e.jsx("hr",{className:"w-full border-[1px] border-[#1f1d1a]"}),e.jsxs("span",{className:"mx-2 w-full max-w-[100px] whitespace-nowrap whitespace-nowrap text-[16px] font-[500]",children:[" ","Or login with"]}),e.jsx("hr",{className:"w-full border-[1px] border-[#1f1d1a]"})]}),e.jsxs("div",{className:"oauth mt-4 flex min-w-[70%] grow flex-col gap-4 text-[#344054]",children:[e.jsxs("button",{onClick:()=>d("google"),className:"my-2 flex h-[44px] min-w-[70%] cursor-pointer items-center justify-center gap-3 rounded-sm border-2  border-[#1f1d1a] px-4",children:[e.jsx("img",{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAALpSURBVHgBtVbNTxNBFH8zuy3QoN0YJMEQs8QQP05LAsbEg4uRxMSD4AeaeLB6xEPhpIkm4MF4IsG/oODF4Edajgahy8UDxbAcjDEc2IORCIlUhVK6u/OcKbVpaZdWxN+lkzd9v9+b9968WQK7YEnXlYPSxm0GqCMQjZtUYScASUSw+NJwGU40GXOGFwfxIg7IqX6KGEYABSqCWBmKPc2TCbOiwEpXhwaMRAFQhb+Ei/i4aXpuyFNAkBMG8eqiLoVIG2N2Z5NhWiUCyxfPqLLtznuTYxKQWIRk869wT60SuYD8ZyHZrGzk3NGkCP3r6Cy0GGYyH5CuqRL1DXKhkBd5/gRrfa0h+7MSKQ0aRhqnEwOwC1YvtOuO41jlyPMCzpRvKT3boKbeNRdsYOzw1FwP/COoPSnriKjWdKxCsO8j0GAmm0/HdQZgHyADhXM8FdtqnPzArUVIv280gsOWVc5BH9xUoWrUJkWRi7pBiAQufRmF4fIukt+N8Hh0qAYsNUoBSztHRtmCfQASVCn8Z1BCiLXT6DJbg32CzPhFKpwXv9AHkY3jOoA5Uc6B53+Mn90o2SBi0mKo2MS5RZvyVVwYFp0g3P95GpbdQNJJuy3mnVgSqsT5JxuRnQKMQYj6uhyDr5Pjm8fg3o+zsMwCQlqR66RIteT6082S6LNw7BlJ/EpX22ufp1r1DEiF2yeOXDupfH396W0lcopMZKCoG/llNYzB4LN8+tvHr8zz3JYUl48MPkHJ0OyNN2NFxJFuZb1W7pfSp8J1K3cV6jQU+aHk1+IP/At5Ae3FTVWm9ny5e5FT4uMasi8WL7RKcs+nALUboO5bGKStozl2GJl+VD+w7VaAjpfXNRTHxb09OP61Hqj53m3GH9a35cUL/5DofWU6zNfGI7RgD9g6FI1hxu4stJV99LVotyJnaJjXZAiqAPI6Aa/Thx118hTIC/G6UMjolJLL2Y+AXBMgr4coPmc2CMVYojc648XxG0ZrPRAMMnAhAAAAAElFTkSuQmCC",className:"h-[18px] w-[18px]"}),e.jsx("span",{className:"text-[16px] font-[600]",children:"Sign up With Google"})]}),e.jsxs("button",{onClick:()=>d("microsoft"),className:"oauth flex h-[44px] items-center  justify-center gap-2 border-2 border-[#1f1d1a]",children:[e.jsx("img",{src:"https://companieslogo.com/img/orig/MSFT-a203b22d.png?t=**********",className:"h-[18px] w-[18px]"}),e.jsx("span",{className:"text-[16px] font-[600]",children:"Sign up With Microsoft"})]})]}),e.jsxs("div",{className:"mt-4 flex flex-row items-center justify-center text-sm lg:text-base",children:[e.jsxs("span",{className:"mr-1 font-medium text-[#1f1d1a]",children:["Already have an account?"," "]})," ",e.jsx(T,{to:"/collaborator/login",className:"font-bold text-[#1f1d1a] underline",children:"Sign in"})]})]})]})})})};export{ce as default};
