import{j as n}from"./@nextui-org/listbox-0f38ca19.js";import{r,b as y,i as b}from"./vendor-4cdf2bd1.js";import{M as g,A as k,G as w,u as j,Z as v,I as a,m as I}from"./index-f2ad9142.js";import{l as _}from"./AuthAction-52ee0934.js";import{u as C}from"./useLocalStorage-46cb237c.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";new g;const U=()=>{const o=r.useRef(null),{state:E,dispatch:m}=r.useContext(k),{dispatch:p}=r.useContext(w),[c,i]=r.useState(!1),l=y(),{company_member:d,company:f}=b();j();const{localStorageData:e,setLocalStorage:s,getLocalStorage:h}=C(["user","token"]),x=()=>{o.current=setInterval(()=>{h(["user","token"])},1e3)},u=async()=>{i(!1);const t=await I(p,m,{endpoint:`/v3/api/custom/goodbadugly/member/accept/${d}/${f}`,method:"POST"});t.error?i(!0):(s("company_email",t==null?void 0:t.company_email),s("token",t==null?void 0:t.token),s("user",t==null?void 0:t.user),s("role",t==null?void 0:t.role),_(m,{role:t.role,token:t.token,user_id:t.user}),x())};return r.useEffect(()=>{e!=null&&e.token&&(e!=null&&e.user)&&(console.log("localStorageData >>",e),l(`/${v[(e==null?void 0:e.role)||window.localStorage.getItem("role")]}/get-started?company_email=${(e==null?void 0:e.company_email)||window.localStorage.getItem("company_email")}`),o!=null&&o.current&&clearInterval(o==null?void 0:o.current))},[e==null?void 0:e.token,e==null?void 0:e.user]),r.useEffect(()=>{u()},[]),console.log("error >>",c),n.jsx("div",{className:"flex h-svh max-h-svh min-h-svh w-full items-center justify-center",children:c?n.jsx(a,{type:"button",className:"my-4 flex h-[2.75rem] w-fit items-center justify-center rounded-[.375rem] bg-[#1f1d1a] px-5 py-2 tracking-wide text-white outline-none focus:outline-none",color:"black",onClick:()=>{i(!1),u()},children:"Retry"}):n.jsx(a,{type:"button",className:"!broder-red-600 !border !bg-transparent",loading:!0,disabled:!0,color:"black",children:n.jsx("span",{children:"In Progress"})})})};export{U as default};
