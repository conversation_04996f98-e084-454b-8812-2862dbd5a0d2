import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{r as m}from"./vendor-4cdf2bd1.js";import{I as x}from"./index-f2ad9142.js";import{e as c}from"./lucide-react-0b94883e.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const a=({id:n=null,onClick:l,title:i="",paragraph:o,badge:t={show:!1,loading:!1,created:!1},active:r=!1})=>{const s=(t==null?void 0:t.show)&&(t==null?void 0:t.created);return e.jsxs("button",{className:`group relative flex w-full items-start gap-4 border-l-4 px-6 py-6 transition-all hover:bg-black/5 
      ${r?"border-l-black bg-black/5":"border-l-transparent"}`,onClick:()=>l&&l(),children:[e.jsx("div",{className:`flex h-10 w-10 shrink-0 items-center justify-center rounded-full border-2
        ${r?"text-white bg-black border-black":s?"text-white bg-green-600 border-green-600":"text-gray-600 border-gray-400 bg-brown-main-bg"}`,children:s?e.jsx(c,{className:"w-5 h-5"}):e.jsx("span",{className:"text-lg font-semibold",children:n})}),e.jsxs("div",{className:"flex-1 text-left",children:[e.jsxs("div",{className:"flex gap-3 items-center",children:[e.jsx("h3",{className:`text-lg font-semibold ${r?"text-black":"text-gray-800"}`,children:i}),(t==null?void 0:t.show)&&e.jsx(e.Fragment,{children:t!=null&&t.loading?e.jsx(x,{loading:t==null?void 0:t.loading,disabled:t==null?void 0:t.loading}):s?e.jsx("span",{className:"inline-flex items-center px-3 py-1 text-sm font-medium text-green-700 bg-green-50 rounded-full ring-1 ring-inset ring-green-600/20",children:"Completed"}):e.jsx("span",{className:"inline-flex items-center px-3 py-1 text-sm font-medium text-gray-600 bg-gray-50 rounded-full ring-1 ring-inset ring-gray-500/10",children:"Pending"})}),r&&e.jsx("span",{className:"inline-flex items-center px-3 py-1 text-sm font-medium text-white bg-black rounded-full",children:"Current Step"})]}),e.jsx("p",{className:`mt-2 text-sm ${r?"text-gray-900":"text-gray-600"}`,children:o})]}),r&&e.jsx("div",{className:"absolute right-4 top-1/2 text-black -translate-y-1/2",children:e.jsx("svg",{className:"w-6 h-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})},I=m.memo(a);export{I as default};
