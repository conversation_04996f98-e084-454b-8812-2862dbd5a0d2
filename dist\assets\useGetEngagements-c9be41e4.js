import{a as q,b as A}from"./index-f2ad9142.js";import{r as c,h as $}from"./vendor-4cdf2bd1.js";function O(j){const o=c.useRef(null),[b,d]=c.useState(!1),[h,E]=c.useState([]),[S,g]=c.useState({page:1,data:[],limit:0,pages:0,total:0,use:!0,reload:!1,canNextPage:!1,canPreviousPage:!1}),[i,x]=c.useState(0),[m,l]=$(),{tokenExpireError:y,showToast:C,custom:R}=q();A();const v=["date_from","date_to","update_title","section","commenter","comment"],u=m.get("limit")||30,n=m.get("page")||1;async function w(r,p,z){d(!0),o.current&&o.current.abort();const f=new AbortController;o.current=f;const _=f.signal;try{const a={limit:p,page:r};v.forEach(t=>{const s=m.get(t);s&&(a[t]=s)});const P=Object.entries(a).map(([t,s])=>s?`${t}=${s}`:"").join("&");console.log("queryParams",P);const e=await R({method:"GET",endpoint:`/v3/api/custom/goodbadugly/member/get-engagements?${P}`},null,!1,_);e!=null&&e.error||(E(e==null?void 0:e.data),g(t=>({...t,data:e==null?void 0:e.data,total:e==null?void 0:e.total,page:e==null?void 0:e.page,limit:e==null?void 0:e.limit,pages:e==null?void 0:e.pages,canPreviousPage:(e==null?void 0:e.page)>1,canNextPage:(e==null?void 0:e.page)+1<=(e==null?void 0:e.pages)})),x(e==null?void 0:e.total))}catch(a){y(a.message),C(a.message,5e3,"error"),a.name==="AbortError"?console.log("Fetch aborted"):console.error("ERROR",a)}finally{d(!1),g(a=>({...a,reload:!0})),o.current===f&&(console.info("abortControllerRef.current null"),o.current=null)}}const N=r=>{g(p=>({...p,...r}))},D=r=>{l({limit:r,page:1})},M=()=>{l({page:Math.max(1,n-1)})},T=()=>{l({page:Math.min(Math.ceil(i/u),n+1)})};return{loading:b,data:S,updateData:N,engagements:h,refetch:w,totalCount:i,currentPage:Number(n),pageSize:Number(u),updatePageSize:D,previousPage:M,nextPage:T,canPreviousPage:n>1,canNextPage:n<Math.ceil(i/u)}}export{O as u};
