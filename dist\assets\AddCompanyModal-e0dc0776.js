import{A as T,G as A,M as $,t as F,s as S}from"./index-f2ad9142.js";import{r as s,h as z}from"./vendor-4cdf2bd1.js";import{j as e}from"./@nextui-org/listbox-0f38ca19.js";import{o as E}from"./yup-0917e80c.js";import{InteractiveButton2 as I}from"./InteractiveButton-060359e0.js";import{u as q}from"./react-hook-form-a383372b.js";import{c as D,a as k}from"./yup-342a5df4.js";import{X as R}from"./XMarkIcon-cfb26fe7.js";import{t as C,S as N}from"./@headlessui/react-cdd9213e.js";function J(b){const[y,o]=s.useState(!1),[j,P]=s.useState([]),[u,v]=s.useState(0),[t,m]=z(),{dispatch:n}=s.useContext(T),{dispatch:x}=s.useContext(A),i=parseInt(t.get("limit")||"30"),a=parseInt(t.get("page")||"1"),r=t.get("name")||"";console.log("ooo-000");async function l(){o(!0);try{const w=await new $().callRawAPI(`/v4/api/records/company_member?join=companies|company_id&filter=member_id,eq,${b}&filter=member_role,in,'investor','cs'${r?`&filter=name,cs,${r}`:""}&page=${a??1},${i??10}&order=id,asc`);console.log(w,"dddddddddddddddddddddddddddddddddddddddddddddoi"),P(w.list.map(h=>{var _;return{id:h.company_id,logo:(_=h.companies)==null?void 0:_.logo,name:h.companies.name,user_id:h.companies.user_id}})),v(w.total||0)}catch(p){F(n,p.message),S(x,p.message,5e3,"error")}o(!1)}const d=Math.ceil(u/i),f=p=>{t.set("limit",p.toString()),t.set("page","1"),m(t)},g=()=>{a>1&&(t.set("page",(a-1).toString()),m(t))},c=()=>{a<d&&(t.set("page",(a+1).toString()),m(t))};return s.useEffect(()=>{l()},[i,a,r]),{loading:y,companies:j,refetch:l,currentPage:a,pageCount:d,pageSize:i,updatePageSize:f,previousPage:g,nextPage:c,canPreviousPage:a>1,canNextPage:a<d}}function Q({afterCreate:b,isOpen:y,closeModal:o}){var r,l,d,f;const{dispatch:j,state:P}=s.useContext(T),{dispatch:u}=s.useContext(A),v=D({email:k().email().required("This field is required"),company_name:k().required("This field is required")}),{register:t,handleSubmit:m,formState:{errors:n,isSubmitting:x},reset:i}=q({resolver:E(v),defaultValues:{email:"",company_name:""}});async function a(g){try{await new $().callRawAPI("/v3/api/custom/goodbadugly/investor/invite-startup",{email:g.email,company_name:g.company_name},"POST"),b(),o(),i(),S(u,"Company added")}catch(c){F(j,c.message),S(u,c.message,5e3,"error")}}return e.jsx(C,{appear:!0,show:y,as:s.Fragment,children:e.jsxs(N,{as:"div",className:"relative z-[50]",onClose:o,children:[e.jsx(C.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:"fixed inset-0 bg-[#1f1d1a] bg-opacity-25"})}),e.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:e.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:e.jsx(C.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:e.jsxs(N.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-md bg-brown-main-bg p-6 text-left align-middle text-base shadow-xl transition-all",as:"form",onSubmit:m(a),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(N.Title,{as:"h3",className:"text-xl font-semibold leading-6 text-gray-900",children:"Add a new company"}),e.jsx("button",{onClick:o,type:"button",children:e.jsx(R,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-7 max-w-xl text-lg",children:[e.jsxs("div",{className:"",children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Email Address"}),e.jsx("input",{type:"text",autoComplete:"off",...t("email"),className:`bg-brown-main-bg-3 w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent py-2 font-medium text-[#1f1d1a] focus:outline-none ${(r=n.email)!=null&&r.message?"border-red-500":""}`,placeholder:"Email"}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(l=n.email)==null?void 0:l.message})]}),e.jsxs("div",{className:"mt-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold capitalize capitalize text-[#1f1d1a]",children:"Company Name"}),e.jsx("input",{type:"text",autoComplete:"off",placeholder:"e.g., Acme Corporation",...t("company_name"),className:`bg-brown-main-bg-3 w-full appearance-none rounded-md border border-[#1f1d1a] bg-transparent py-2 font-medium text-[#1f1d1a] focus:outline-none ${(d=n.company_name)!=null&&d.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(f=n.company_name)==null?void 0:f.message})]})]}),e.jsxs("div",{className:"mt-6 flex justify-end gap-4",children:[e.jsx("button",{className:"h-[40px] rounded-md border border-[#1f1d1a] px-4 py-2 font-iowan text-[12px] font-medium sm:text-[14px]    xl:text-[16px]",type:"button",onClick:o,children:"Cancel"}),e.jsx(I,{loading:x,disabled:x,type:"submit",className:"whitespace-nowr disabled:bg-disabledblack rounded-md bg-primary-black px-6 py-2 text-center text-[10px] font-semibold font-semibold text-white transition-colors duration-100 sm:text-[12px] xl:text-sm",children:"Create company"})]})]})})})})]})})}export{Q as A,J as u};
