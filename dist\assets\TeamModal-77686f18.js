import{j as i}from"./@nextui-org/listbox-0f38ca19.js";import{a as f,L as n,o as h,a0 as u,T as p}from"./index-f2ad9142.js";import"./vendor-4cdf2bd1.js";import{R as w,A as b,a as g,b as x}from"./Teams-608c973b.js";import{h as j}from"./moment-a9aaa855.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";import"./yup-0917e80c.js";import"./@hookform/resolvers-b50d6e2a.js";import"./react-hook-form-a383372b.js";import"./yup-342a5df4.js";import"./index-d07d87ac.js";import"./MkdInput-d37679e9.js";import"./react-toggle-6478c5c4.js";import"./@uppy/dashboard-51133bb7.js";import"./@fullcalendar/core-085b11ae.js";import"./@uppy/core-a4ba4b97.js";import"./@uppy/aws-s3-a6b02742.js";import"./@craftjs/core-a2cdaeb4.js";import"./@uppy/compressor-4bcbc734.js";import"./AddButton-51d1b2cd.js";import"./MkdListTableV2-db78e8c5.js";import"./index-9dceff66.js";import"./ExportButton-eb4cf1f9.js";import"./index.esm-54e24cf9.js";import"./react-icons-36ae72b7.js";import"./lucide-react-0b94883e.js";import"./ClipboardDocumentIcon-f03b0627.js";const v={add:"Add Team Member",resend:"Resend Invite",accept:"Accept Invite",reject:"Reject Invite",delete:"Remove Team Member"},s={add:{modalDialog:"h-fit max-h-fit min-h-fit w-full min-w-full md:!w-fit !max-w-full md:!min-w-fit !gap-0 ",modalContent:"!bg-brown-main-bg !z-10 !mt-0 overflow-hidden !py-0"},resend:{modalDialog:"h-fit max-h-fit min-h-fit md:!w-fit !w-full",modalContent:"!bg-brown-main-bg !z-10 !mt-0 overflow-hidden !py-0"},accept:{modalDialog:"h-fit max-h-fit min-h-fit md:!w-fit !w-full",modalContent:"!bg-brown-main-bg !z-10 !mt-0 overflow-hidden !py-0"},reject:{modalDialog:"h-fit max-h-fit min-h-fit md:!w-fit !w-full",modalContent:"!bg-brown-main-bg !z-10 !mt-0 overflow-hidden !py-0"},delete:{modalDialog:"h-fit max-h-fit min-h-fit md:!w-fit !w-full",modalContent:"!bg-brown-main-bg !z-10 !mt-0 overflow-hidden !py-0"}},l=async(e,t,r,o)=>{new p;try{r(t==="accept"?"Invite accepted successfully":"Member removed successfully")}catch(m){console.error(`Error ${t}ing invitation:`,m)}finally{}},y=async(e,t,r)=>{const o=new p;try{console.log("id >>",e,"resending"),await o.update("company_invites",e,{invited_at:j().format("YYYY-MM-DD HH:mm:ss")}),t("Invite resent successfully"),r()}catch(m){console.error("Error resending invitation:",m)}},le=({ids:e,isOpen:t,onClose:r,modal:o="",onSuccess:m})=>{var d,c;const{showToast:a}=f();return console.log(t,"isopen",o,e,"companyMember"),i.jsx(n,{children:i.jsxs(h,{modalHeader:!0,title:v[o],isOpen:t,modalCloseClick:r,classes:{modalDialog:(d=s[o])==null?void 0:d.modalDialog,modalContent:(c=s[o])==null?void 0:c.modalContent,modal:"h-full"},children:[t&&["add"].includes(o)&&i.jsx(n,{children:i.jsx(u,{onSuccess:m})}),t&&["resend"].includes(o)&&i.jsx(n,{children:i.jsx(w,{id:e[0],onSuccess:()=>{y(e[0],a,m,e[1])},onClose:r})}),t&&["accept"].includes(o)&&i.jsx(n,{children:i.jsx(b,{id:e[0],memberId:e[1],onSuccess:()=>{m(),l(e[0],"accept",a,e[1])},onClose:r})}),t&&["reject"].includes(o)&&i.jsx(n,{children:i.jsx(g,{id:e[0],onSuccess:()=>{m(),l(e[0],"reject",a,e[1])},onClose:r})}),t&&["delete"].includes(o)&&i.jsx(n,{children:i.jsx(x,{id:e[0],onSuccess:()=>{l(e[0],"delete",a,e[1]),m()},onClose:r})})]})})};export{le as default};
