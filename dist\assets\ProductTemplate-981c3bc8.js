import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{r as o,b as m}from"./vendor-4cdf2bd1.js";import{A as p,G as l}from"./index-f2ad9142.js";import"./moment-a9aaa855.js";import{_ as n}from"./qr-scanner-cf010ec4.js";import{T as x}from"./index-590fd997.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const d=o.lazy(()=>n(()=>import("./Collapser-9a256a7f.js"),["assets/Collapser-9a256a7f.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"])),y=({template:e=null,onSelect:i=()=>{},openPreviewModal:s=()=>{},creating:a=!1})=>(m(),o.useContext(p),o.useContext(l),t.jsx(t.Fragment,{children:e?t.jsx("div",{className:"flex w-full flex-col space-y-5",children:e.categories.map(r=>t.jsx(d,{titleClasses:"!bg-transparent",className:"!border-b !border-b-[#1f1d1a]/20 !bg-transparent !pb-5",title:r.type,iconPosition:"right",children:t.jsx(x,{template:r,onSelect:i,creating:a,openPreviewModal:s})},r.id))}):null}));export{y as default};
