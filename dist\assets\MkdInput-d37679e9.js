import{j as t}from"./@nextui-org/listbox-0f38ca19.js";import{r as j}from"./vendor-4cdf2bd1.js";import{f as U,C as B,n as T,o as G}from"./index-f2ad9142.js";import{_ as J}from"./react-toggle-6478c5c4.js";import{_ as K}from"./qr-scanner-cf010ec4.js";const Q=j.lazy(()=>K(()=>import("./UpdateCalendar-900037e2.js"),["assets/UpdateCalendar-900037e2.js","assets/@nextui-org/listbox-0f38ca19.js","assets/vendor-4cdf2bd1.js","assets/@nextui-org/theme-345a09ed.js","assets/AddButton-51d1b2cd.js","assets/index-f2ad9142.js","assets/react-confirm-alert-1abd021a.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-cdd9213e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-5f217abb.js","assets/@fortawesome/react-fontawesome-205d7a0d.js","assets/@fortawesome/fontawesome-svg-core-1da0295f.js","assets/moment-a9aaa855.js","assets/@fortawesome/free-solid-svg-icons-88afae62.js","assets/@fortawesome/free-regular-svg-icons-b6baa1a7.js","assets/@fortawesome/free-brands-svg-icons-67e8b52a.js","assets/index-759278f3.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/@hassanmojab/react-modern-calendar-datepicker-b4938049.js","assets/UpdateCalendar-36ef3164.css"])),W=({type:a="text",page:h,cols:w="30",rows:$="50",labelStyle:A="",name:e,label:_,errors:n=null,register:u=null,className:s,placeholder:x,options:M=[],mapping:N=null,disabled:o=!1,value:d=null,onChange:c,loading:L=!1,required:m=!1,customField:f=!1,noneText:I="",readOnly:g=!1})=>{var S,q,D,v,P,V,C,E;const l=j.useId(),[z,F]=j.useState(null),[b,R]=j.useState({modal:null,showModal:!1}),k=(i,p)=>{R(H=>({...H,modal:i,showModal:p}))};return t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:`relative grow space-y-2 ${h==="list"?"w-full pl-2 pr-2 md:w-1/2":""}`,children:[["radio","checkbox","color","toggle"].includes(a)?null:t.jsx(t.Fragment,{children:_&&t.jsxs("label",{className:`block font-iowan text-[1rem] font-[700] capitalize leading-[1.5rem] text-[#1F1D1A] ${A}`,htmlFor:l,children:[_,m&&t.jsx("sup",{className:"z-[99999] text-[.825rem] text-red-600",children:"*"})]})}),L?t.jsx(U,{count:1,counts:[2],className:"!h-[3rem] !max-h-[3rem] !min-h-[3rem]  w-full appearance-none !gap-0 overflow-hidden rounded-sm border-[1px] border-[#1f1d1a] bg-brown-main-bg !p-0 px-3 py-2 text-sm font-normal text-[#1f1d1a] focus:outline-none"}):a==="textarea"?t.jsx(t.Fragment,{children:t.jsx("textarea",{maxLength:250,className:`focus:shadow-outline w-full  appearance-none  rounded-sm border-[1px] border-[#1f1d1a] bg-brown-main-bg p-[12px_16px_12px_16px] px-3 py-2 text-sm font-normal leading-tight  text-[#1f1d1a] shadow focus:outline-none ${s} ${n&&(n!=null&&n[e])&&((S=n==null?void 0:n[e])!=null&&S.message)?"!border-red-500":"border-soft-200"} ${o?"appearance-none bg-gray-200":""}`,disabled:o,id:l,readOnly:g,cols:w,name:`unique-${e}-${l}`,placeholder:x,rows:$,...d?{value:d}:null,...u?u(e,{...m&&f?{required:!0}:null}):{onChange:c}})}):["radio","checkbox","color","toggle"].includes(a)?t.jsxs("div",{className:"flex h-[1.875rem] items-center gap-2",children:[["toggle"].includes(a)?t.jsx(J,{className:`toggle_class ${s}`,disabled:o,icons:!1,...c?{onChange:c}:null,...[!0,!1].includes(d)?{checked:d}:null}):t.jsx("input",{autoComplete:"new-password","aria-autocomplete":"none",type:a,defaultValue:"",disabled:o,readOnly:g,id:l,name:`unique-${e}`,...d?{value:d}:null,placeholder:x,...u?u(e,{...m&&f?{required:!0}:null}):{onChange:c},className:`focus:shadow-outline h-6 w-4 cursor-pointer appearance-none rounded-md border-[1px] p-[12px_16px_12px_16px]  px-3 py-2 text-[.8125rem]  text-sm font-normal leading-tight text-black shadow  placeholder:font-normal placeholder:text-black focus:outline-none focus:ring-0  sm:!text-base ${s} ${n&&(n!=null&&n[e])&&((q=n==null?void 0:n[e])!=null&&q.message)?"!border-red-500":"border-black"} ${a==="color"?"min-h-[3.125rem] min-w-[6.25rem]":""} ${o?"appearance-none bg-gray-200":""}`}),_?t.jsxs("label",{className:"block font-iowan text-[1rem] font-[700] capitalize leading-[1.5rem] text-[#1F1D1A]",htmlFor:l,children:[_,m&&t.jsx("sup",{className:"z-[99999] text-[.825rem] text-red-600",children:"*"})]}):null]}):a==="dropdown"||a==="select"?t.jsxs("select",{type:a,defaultValue:"",id:l,name:`unique-${e}-${l}`,disabled:o,readOnly:g,placeholder:x,...u(e,{...m&&f?{required:!0}:null}),className:`focus:shadow-outline h-[3rem]   w-full appearance-none rounded-sm border-[1px] border-[#1f1d1a] bg-brown-main-bg p-[12px_16px_12px_16px]  text-sm font-normal  leading-tight  text-[#1f1d1a]  shadow focus:outline-none  focus:ring-0  ${s} ${n&&(n!=null&&n[e])&&((D=n==null?void 0:n[e])!=null&&D.message)?"!border-red-500":"border-soft-200"}  ${o?"appearance-none bg-gray-200":""}`,children:[t.jsx("option",{children:I}),M.map((i,p)=>t.jsx("option",{value:i,children:i},p+1))]}):a==="mapping"?t.jsx(t.Fragment,{children:N?t.jsxs("select",{id:l,name:`unique-${e}-${l}`,disabled:o,...d?{value:d}:null,placeholder:x,...u?u(e,{...m&&f?{required:!0}:null}):{onChange:c},className:`focus:shadow-outline h-[3rem]  w-full  appearance-none rounded-sm border-[1px] border-[#1f1d1a] bg-brown-main-bg p-[12px_16px_12px_16px]  text-sm font-normal leading-tight  text-[#1f1d1a] shadow focus:outline-none focus:ring-0  ${s} ${n&&(n!=null&&n[e])&&((v=n==null?void 0:n[e])!=null&&v.message)?"!border-red-500":"border-soft-200"} ${o?"appearance-none bg-gray-200":""}`,children:[t.jsx("option",{children:I}),M.map((i,p)=>t.jsx("option",{value:i,children:N[i]},p+1))]}):"Please Pass the mapping e.g {key:value}"}):["number","decimal"].includes(a)?t.jsx("input",{autoComplete:"new-password","aria-autocomplete":"none",type:a,defaultValue:"",id:l,readOnly:g,name:`unique-${e}-${l}`,disabled:o,placeholder:x,...d?{value:d}:null,...u?u(e,{...m&&f?{required:!0}:null}):{onChange:c},step:"0.01",min:"0.00",onInput:i=>{const p=i.target.value;p&&!/^\d+(\.\d{0,2})?$/.test(p)&&(i.target.value=p.slice(0,-1))},className:`focus:shadow-outline h-[3rem]  w-full appearance-none  rounded-sm border-[1px] border-[#1f1d1a] bg-brown-main-bg p-[.75rem_1rem_.75rem_1rem] text-sm  font-normal leading-tight text-[#1f1d1a] shadow placeholder:font-normal placeholder:text-black focus:outline-none focus:ring-0 ${s} ${n&&(n!=null&&n[e])&&((P=n==null?void 0:n[e])!=null&&P.message)?"!border-red-500":"border-soft-200"} ${o?"appearance-none bg-gray-200":""}`}):["custom_date"].includes(a)?t.jsxs("div",{onClick:()=>{o||k("custom_date",!0)},className:"relative cursor-pointer",children:[t.jsx("input",{autoComplete:"new-password","aria-autocomplete":"none",type:a,defaultValue:"",id:l,name:`unique-${e}-${l}`,disabled:!0,readOnly:g,placeholder:x,...d?{value:d}:null,...u?u(e,{...m&&f?{required:!0}:null}):{onChange:c},...a==="number"?{step:"0.01"}:null,min:a==="number"?"0.00":void 0,className:`focus:shadow-outline h-[3rem] w-full appearance-none rounded-sm border-[1px] border-[#1f1d1a] bg-brown-main-bg p-[.75rem_1rem_.75rem_1rem] text-center text-sm font-normal leading-tight text-[#1f1d1a] shadow placeholder:font-normal placeholder:text-black focus:outline-none focus:ring-0 ${s} ${n&&(n!=null&&n[e])&&((V=n==null?void 0:n[e])!=null&&V.message)?"!border-red-500":"border-soft-200"}`}),t.jsx(B,{className:"absolute right-3 top-1/2 -translate-y-1/2 transform cursor-pointer"})]}):t.jsx("input",{autoComplete:"new-password","aria-autocomplete":"none",type:a,readOnly:g,defaultValue:"",id:l,name:`unique-${e}-${l}`,disabled:o,placeholder:x,...d?{value:d}:null,...u?u(e,{...m&&f?{required:!0}:null}):{onChange:c},...a==="number"?{step:"0.01"}:null,min:a==="number"?"0.00":void 0,className:`focus:shadow-outline h-[3rem]  w-full appearance-none rounded-sm border-[1px] border-[#1f1d1a] bg-brown-main-bg p-[12px_16px_12px_16px] text-sm  font-normal leading-tight text-[#1f1d1a] shadow placeholder:font-normal placeholder:text-black focus:outline-none focus:ring-0 ${s} ${n&&(n!=null&&n[e])&&((C=n==null?void 0:n[e])!=null&&C.message)?"!border-red-500":"border-soft-200"} ${o?"appearance-none bg-gray-200":""}`}),n&&(n==null?void 0:n[e])&&t.jsx("p",{className:"text-field-error absolute inset-x-0 top-[90%] m-auto mt-2 text-[.8rem] italic text-red-500",children:T((E=n==null?void 0:n[e])==null?void 0:E.message,{casetype:"capitalize",separator:" "})})]}),t.jsx(G,{modalHeader:!0,title:b.modal?X(b==null?void 0:b.modal):"",isOpen:b.showModal,modalCloseClick:()=>k(null,!1),classes:{modalDialog:"!bg-brown-main-bg !px-0 !rounded-[.125rem] h-fit min-h-fit max-h-fit !w-full !max-w-full !min-w-full md:!w-[28.5rem] md:!max-w-[28.5rem] md:!min-w-[28.5rem] ",modalContent:"!bg-brown-main-bg !z-10 !mt-0 overflow-hidden !pt-0",modal:"h-full"},children:b.showModal&&["custom_date"].includes(b.modal)?t.jsx(Q,{selectedDay:z,setSelectedDay:F,onSave:()=>{c({target:{value:z}}),k(null,!1)}}):null})]})},nn=W;function X(a){const h=a.split("_"),w=h.includes("date"),$=h.includes("time");return w&&$?"Pick date & time":w?"Pick date":$?"Select Time":T(a,{casetype:"capitalize",separator:" "})}export{nn as M};
