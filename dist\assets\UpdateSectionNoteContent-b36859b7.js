import{j as n}from"./@nextui-org/listbox-0f38ca19.js";import{r as a}from"./vendor-4cdf2bd1.js";import{y as c,z as u}from"./index-f2ad9142.js";import"./@nextui-org/theme-345a09ed.js";import"./react-confirm-alert-1abd021a.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-cdd9213e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5f217abb.js";import"./@fortawesome/react-fontawesome-205d7a0d.js";import"./@fortawesome/fontawesome-svg-core-1da0295f.js";import"./moment-a9aaa855.js";import"./@fortawesome/free-solid-svg-icons-88afae62.js";import"./@fortawesome/free-regular-svg-icons-b6baa1a7.js";import"./@fortawesome/free-brands-svg-icons-67e8b52a.js";const M=({update:d=null,note:t=null})=>{const[e,s]=a.useState({html:"<p></p>",data:null});return a.useEffect(()=>{const p=c(t.content,{blocks:[{id:"zbGZFPM-iI",type:"paragraph",data:{text:""}}]}),{blocks:i}=p;let r=i.length!=0?u(i):null;r&&(r=r.replace(/>([^<]*)</g,(o,l)=>{const m=l.trim();return m?`>${m.charAt(0).toUpperCase()+m.slice(1)}<`:o})),s(o=>({...o,html:r,data:p}))},[t==null?void 0:t.content]),n.jsx(a.Fragment,{children:n.jsx("p",{className:"font-iowan !text-[1rem] !font-[500] !leading-[1.5rem]",dangerouslySetInnerHTML:{__html:e==null?void 0:e.html}})})};export{M as default};
